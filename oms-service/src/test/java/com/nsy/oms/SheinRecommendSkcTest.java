package com.nsy.oms;

import com.alibaba.fastjson.JSON;
import com.nsy.oms.business.domain.request.shein.SheinRecommendSaleSkcListRequest;
import com.nsy.oms.business.domain.request.shein.SheinRecommendSaleSkcManualRequest;
import com.nsy.oms.business.domain.request.shein.SheinRecommendSaleSkcPushRequest;
import com.nsy.oms.business.domain.request.upload.UploadRequest;
import com.nsy.oms.business.manage.omspublish.OmsPublishApiService;
import com.nsy.oms.business.service.shein.RecommendSaleSkcAdapter;
import com.nsy.oms.business.service.upload.UploadService;
import com.nsy.oms.utils.mp.LocationContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;

import java.util.Collections;

/**
 * @auth: qwf
 * @date: 2024年2月22日 0022
 * @description:
 */
@SpringBootTest
@ActiveProfiles("test")
public class SheinRecommendSkcTest {

    @Autowired
    private RecommendSaleSkcAdapter recommendSaleSkcAdapter;

    @MockBean
    private OmsPublishApiService omsPublishApiService;

    @Autowired
    private UploadService uploadService;

    SheinRecommendSaleSkcListRequest request;
    SheinRecommendSaleSkcListRequest temuRequest;
    @BeforeEach
    public void init() {
        String requestJson = "{\n" + "  \"sheinRecommendSaleSkcRequests\": [\n" + "    {\n" + "      \"recommendStoreIds\": \"9186\",\n" + "      \"recommendStoreNames\": \"shein测试店铺1\",\n" + "      \"productId\": 12,\n" + "      \"skc\": \"LC12345\",\n" + "      \"recommendType\": \"新品\"\n" + "    }\n" + "  ]\n" + "}";
        String temuRequestJson = "{\n" + "  \"sheinRecommendSaleSkcRequests\": [\n" + "    {\n" + "      \"recommendStoreIds\": \"5756\",\n" + "      \"recommendStoreNames\": \"拼多多测试\",\n" + "      \"productId\": 12,\n" + "      \"skc\": \"LC12345\",\n" + "      \"recommendType\": \"新品\"\n" + "    }\n" + "  ]\n" + "}";
        request = JSON.parseObject(requestJson, SheinRecommendSaleSkcListRequest.class);
        temuRequest = JSON.parseObject(temuRequestJson, SheinRecommendSaleSkcListRequest.class);
        Mockito.when(omsPublishApiService.getSpuPublishData(12, "shein")).thenReturn(Collections.emptyList());
        Mockito.when(omsPublishApiService.getSpuPublishData(12, "temu")).thenReturn(Collections.emptyList());
    }

    @Test
    @Disabled
    public void testSystemAdd() {
//        recommendSaleSkcAdapter.systemAdd(request);
        recommendSaleSkcAdapter.systemAdd(temuRequest);
    }

    @Test
    @Disabled
    public void testPush() {
        String testPushJson = "{\n" + "  \"websiteIds\": [\n" + "    359\n" + "  ],\n" + "  \"productId\": 12,\n" + "  \"colorList\": [\n" + "    \"LC123456\"\n" + "  ],\n" + "  \"isForcedPush\": false,\n" + "  \"createBy\": \"admin\"\n" + "}";
        SheinRecommendSaleSkcPushRequest request = JSON.parseObject(testPushJson, SheinRecommendSaleSkcPushRequest.class);
        recommendSaleSkcAdapter.push(request);
    }
    @Test
    @Disabled
    public void testManual() {
        String json = "{\n" + "  \"storeId\": 9213,\n" + "  \"sheinRecommendSaleSkcIdList\": [\n" + "    58341\n" + "  ]\n" + "}";
        SheinRecommendSaleSkcManualRequest request = JSON.parseObject(json, SheinRecommendSaleSkcManualRequest.class);
        recommendSaleSkcAdapter.manual(request);
    }
    @Test
    @Disabled
    public void testUpload() {
        String json = "{\"typeEnum\":\"OMS_SHEIN_RECOMMEND_SKC_CHANGE_STATUS\",\"dataJsonStr\":\"[{\\\"skc\\\":\\\"**********-P3\\\",\\\"publishStoreName\\\":\\\"内贸二级分销shein-test1\\\"}]\",\"createBy\":\"admin\",\"location\":\"QUANZHOU\",\"queueId\":9446,\"uploadParams\":\"{\\\"importType\\\":0,\\\"isAppendQty\\\":0,\\\"deptId\\\":3,\\\"userId\\\":645,\\\"userCode\\\":\\\"False\\\",\\\"companyCode\\\":\\\"201310302220027120\\\",\\\"uploadBatch\\\":\\\"1709607426829\\\"}\",\"fileName\":\"shein推荐.xls\",\"uploadFileUrl\":\"https://stage-nsy-products.oss-cn-hangzhou.aliyuncs.com/development/ajy/lgz/1709607427353.xls\",\"fileMd5\":\"E0E55AE41C7A736C2906DA81A328036E\",\"userId\":645,\"departmentId\":3,\"totalCount\":1,\"batchNum\":1}";
        try {
            LocationContext.setLocation("QUANZHOU");
            UploadRequest request = JSON.parseObject(json, UploadRequest.class);
            uploadService.distribution(request);
        } finally {
            LocationContext.clear();
        }
    }




}
