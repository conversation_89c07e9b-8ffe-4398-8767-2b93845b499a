<?xml version="1.0" encoding="UTF-8" ?>
<configuration>
    <springProperty scop="context" name="spring.application.name" source="spring.application.name" defaultValue=""/>
    <springProfile name="!production-job">
        <property name="app.name" value="${spring.application.name}"/>
    </springProfile>
    <springProfile name="production-job">
        <property name="app.name" value="${spring.application.name}-job"/>
    </springProfile>
    <property name="logFolder" value="/mnt/logs/${app.name}"/>

    <springProfile name="dev">
        <appender name="info" class="ch.qos.logback.core.ConsoleAppender">
            <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                <level>INFO</level>
            </filter>
            <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
                <pattern>%d [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>

        <root level="debug">
            <appender-ref ref="info"/>
        </root>
    </springProfile>

    <springProfile name="!dev">
        <appender name="info" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                <level>INFO</level>
            </filter>
            <file>${logFolder}/${app.name}-info.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>${logFolder}/${app.name}-info.%d{yyyy-MM-dd_HH}.log.gz</fileNamePattern>
            </rollingPolicy>
            <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
                <pattern>%X{EagleEye-TraceID} %d [%thread] %-5level %X{traceId} %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>

        <appender name="trace" class="com.nsy.api.core.apicore.log.TraceAppenderV2">
            <file>${logFolder}/${app.name}-trace.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>${logFolder}/${app.name}-trace.%d{yyyy-MM-dd_HH}.log.gz
                </fileNamePattern>
            </rollingPolicy>
            <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
                <pattern>%X{EagleEye-TraceID} %d [%thread] %-5level %X{traceId} %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>
        <appender name="action" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>${logFolder}/${app.name}-action.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>${logFolder}/${app.name}-action.%d{yyyy-MM-dd_HH}.log.gz
                </fileNamePattern>
            </rollingPolicy>
            <encoder class="com.nsy.api.core.apicore.log.FilterMessagePatternLayoutEncoder">
                <pattern> %X{EagleEye-TraceID} | %message | %X{traceId} %n</pattern>
            </encoder>
        </appender>

        <logger name="com.nsy.api.core.apicore.log.ActionLogger" additivity="false" level="INFO">
            <appender-ref ref="action"/>
        </logger>

        <logger name="springfox.documentation" level="INFO"/>
        <logger name="org.springframework" level="INFO"/>
        <logger name="org.apache.http" level="INFO"/>
        <logger name="org.hibernate" level="INFO"/>
        <logger name="org.apache.kafka" level="INFO"/>
        <logger name="com.alibaba.nacos" level="ERROR"/>


        <root level="DEBUG">
            <appender-ref ref="info"/>
            <appender-ref ref="trace"/>
        </root>
    </springProfile>
</configuration>