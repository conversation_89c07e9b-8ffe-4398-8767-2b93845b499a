<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.oms.repository.sql.mapper.platform.PlatformOrderExtendMapper">

    <resultMap id="BaseResultMap" type="com.nsy.oms.repository.entity.platform.PlatformOrderExtendEntity">
            <id property="platformOrderExtendId" column="platform_order_extend_id" jdbcType="INTEGER"/>
            <result property="platformOrderId" column="platform_order_id" jdbcType="INTEGER"/>
            <result property="platformOrderNo" column="platform_order_no" jdbcType="VARCHAR"/>
            <result property="orderCreateDateTimeZone" column="order_create_date_time_zone" jdbcType="TIMESTAMP"/>
            <result property="orderDeliverDateTimeZone" column="order_deliver_date_time_zone" jdbcType="TIMESTAMP"/>
            <result property="orderPaymentDateTimeZone" column="order_payment_date_time_zone" jdbcType="TIMESTAMP"/>
            <result property="orderCancelDateTimeZone" column="order_cancel_date_time_zone" jdbcType="TIMESTAMP"/>
            <result property="location" column="location" jdbcType="VARCHAR"/>
            <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="version" column="version" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        platform_order_extend_id,platform_order_id,platform_order_no,
        order_create_date_time_zone,order_deliver_date_time_zone,order_payment_date_time_zone,
        order_cancel_date_time_zone,location,create_date,
        create_by,update_date,update_by,
        version
    </sql>
</mapper>
