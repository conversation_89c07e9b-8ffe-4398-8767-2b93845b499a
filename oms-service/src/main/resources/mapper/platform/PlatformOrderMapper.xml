<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.oms.repository.sql.mapper.platform.PlatformOrderMapper">
    <sql id="baseColumnList">
        platform_order_id,platform_order_no,platform_original_order_no,
        order_status,payment_amount,product_discount_amount,
        product_total_amount,freight_fee,commission_fee,
        currency,shipping_type,platform_id,
        platform_name,store_id,store_name,
        market_code,market_name,buyer_remark,
        order_create_date,order_deliver_date,order_payment_date,
        order_cancel_date,push_stockout_order_status,push_cost_order_status,push_other_stockout_order_status,location,
        create_date,create_by,update_date,
        update_by,version
    </sql>

    <resultMap id="baseResultMap" type="com.nsy.oms.business.domain.response.platform.PlatformOrderResponse">
            <id property="platformOrderId" column="platform_order_id" jdbcType="INTEGER"/>
            <result property="platformOrderNo" column="platform_order_no" jdbcType="VARCHAR"/>
            <result property="platformOriginalOrderNo" column="platform_original_order_no" jdbcType="VARCHAR"/>
            <result property="orderStatus" column="order_status" jdbcType="TINYINT"/>
            <result property="paymentAmount" column="payment_amount" jdbcType="DECIMAL"/>
            <result property="productDiscountAmount" column="product_discount_amount" jdbcType="DECIMAL"/>
            <result property="productTotalAmount" column="product_total_amount" jdbcType="DECIMAL"/>
            <result property="freightFee" column="freight_fee" jdbcType="DECIMAL"/>
            <result property="commissionFee" column="commission_fee" jdbcType="DECIMAL"/>
            <result property="currency" column="currency" jdbcType="VARCHAR"/>
            <result property="shippingType" column="shipping_type" jdbcType="VARCHAR"/>
            <result property="platformId" column="platform_id" jdbcType="INTEGER"/>
            <result property="platformName" column="platform_name" jdbcType="VARCHAR"/>
            <result property="storeId" column="store_id" jdbcType="INTEGER"/>
            <result property="storeName" column="store_name" jdbcType="VARCHAR"/>
            <result property="marketCode" column="market_code" jdbcType="VARCHAR"/>
            <result property="marketName" column="market_name" jdbcType="VARCHAR"/>
            <result property="buyerRemark" column="buyer_remark" jdbcType="VARCHAR"/>
            <result property="orderCreateDate" column="order_create_date" jdbcType="TIMESTAMP"/>
            <result property="orderDeliverDate" column="order_deliver_date" jdbcType="TIMESTAMP"/>
            <result property="orderPaymentDate" column="order_payment_date" jdbcType="TIMESTAMP"/>
            <result property="orderCancelDate" column="order_cancel_date" jdbcType="TIMESTAMP"/>
            <result property="pushStockoutOrderStatus" column="push_stockout_order_status" jdbcType="TINYINT"/>
            <result property="pushCostOrderStatus" column="push_cost_order_status" jdbcType="TINYINT"/>
            <result property="pushOtherStockoutOrderStatus" column="push_other_stockout_order_status" jdbcType="TINYINT"/>
            <result property="location" column="location" jdbcType="VARCHAR"/>
            <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <collection property="itemList" ofType="com.nsy.oms.business.domain.response.platform.PlatformOrderItemResponse" javaType="java.util.List">
            <id property="platformOrderItemId" column="platform_order_item_id" jdbcType="INTEGER"/>
            <result property="platformOrderId" column="platform_order_id" jdbcType="INTEGER"/>
            <result property="itemStatus" column="item_status" jdbcType="TINYINT"/>
            <result property="sku" column="sku" jdbcType="VARCHAR"/>
            <result property="specId" column="spec_id" jdbcType="INTEGER"/>
            <result property="productId" column="product_id" jdbcType="INTEGER"/>
            <result property="qty" column="qty" jdbcType="INTEGER"/>
            <result property="unitPrice" column="unit_price" jdbcType="DECIMAL"/>
            <result property="totalAmount" column="total_amount" jdbcType="DECIMAL"/>
            <result property="paymentAmount" column="payment_amount" jdbcType="DECIMAL"/>
            <result property="unitDiscount" column="unit_discount" jdbcType="DECIMAL"/>
            <result property="sellerSku" column="seller_sku" jdbcType="VARCHAR"/>
            <result property="location" column="location" jdbcType="VARCHAR"/>
            <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <resultMap id="getSampleOrdersMap" type="com.nsy.api.oms.dto.response.order.SampleOrderResponse">
        <result property="storeId" column="store_id" jdbcType="INTEGER"/>
        <result property="storeName" column="store_name" jdbcType="VARCHAR"/>
        <result property="platformOrderNo" column="platform_order_no" jdbcType="VARCHAR"/>
        <result property="platformOriginalOrderNo" column="platform_original_order_no" jdbcType="VARCHAR"/>
        <result property="orderDeliverDate" column="order_deliver_date" jdbcType="TIMESTAMP"/>
        <result property="buyerNick" column="buyer_nick" jdbcType="VARCHAR"/>
        <result property="location" column="location" jdbcType="VARCHAR"/>
        <result property="orderCreateDate" column="order_create_date" jdbcType="TIMESTAMP"/>
        <result property="buyerUid" column="buyer_uid" jdbcType="VARCHAR"/>
        <collection property="orderItems" ofType="com.nsy.api.oms.dto.response.order.SampleOrderItemResponse" javaType="java.util.List">
            <result property="sku" column="sku" jdbcType="VARCHAR"/>
            <result property="sellerSku" column="seller_sku" jdbcType="VARCHAR"/>
            <result property="qty" column="qty" jdbcType="INTEGER"/>
            <result property="sellerSkuId" column="seller_sku_id" jdbcType="VARCHAR"/>
            <result property="sellerProductId" column="seller_product_id" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>



    <select id="getPlatformOrder" resultMap="baseResultMap">
        SELECT
            orders.platform_order_id,
            orders.platform_order_no,
            orders.platform_original_order_no,
            orders.order_status,
            orders.payment_amount,
            orders.product_discount_amount,
            orders.product_total_amount,
            orders.freight_fee,
            orders.commission_fee,
            orders.currency,
            orders.shipping_type,
            orders.platform_id,
            orders.platform_name,
            orders.store_id,
            orders.store_name,
            orders.market_code,
            orders.market_name,
            orders.buyer_remark,
            orders.order_create_date,
            orders.order_deliver_date,
            orders.order_payment_date,
            orders.order_cancel_date,
            orders.push_stockout_order_status,
            orders.push_cost_order_status,
            orders.push_other_stockout_order_status,
            orders.location,
            orders.create_date,
            orders.create_by,
            orders.update_date,
            orders.update_by,
            item.platform_order_item_id,
            item.item_status,
            item.sku,
            item.spec_id,
            item.product_id,
            item.qty,
            item.unit_price,
            item.total_amount,
            item.payment_amount,
            item.unit_discount,
            item.seller_sku,
            item.location,
            item.create_date,
            item.create_by,
            item.update_date,
            item.update_by
        FROM
        platform_order orders
        INNER JOIN platform_order_item item
        on orders.platform_order_id = item.platform_order_id
        where platform_order_no = #{platformOrderNo}
    </select>
    <select id="getFbaPushOrder" resultType="com.nsy.oms.repository.entity.platform.PlatformOrderEntity">
        SELECT
            a.*
        FROM
            `platform_order` a
        WHERE
          a.`update_date` BETWEEN #{startDate} AND #{endDate}
          and a.`order_status` = 21
          and a.order_type = 10
          AND a.push_stockout_order_status = 0
          AND a.`platform_id` >= 801
          limit #{fetchCount}
    </select>

    <select id="getSampleOrders" resultMap="getSampleOrdersMap">
        SELECT
            orders.platform_order_no,
            orders.platform_original_order_no,
            orders.store_id,
            orders.store_name,
            orders.order_deliver_date,
            orders.order_create_date,
            pr.buyer_nick,
            orders.location,
            item.sku,
            item.seller_sku,
            item.qty,
            item.seller_sku_id,
            item.seller_product_id,
            pr.buyer_uid
        FROM
            platform_order orders
                INNER JOIN platform_order_item item on orders.platform_order_id = item.platform_order_id
                left join platform_receiver pr on orders.platform_order_id = pr.platform_order_id
        <where>
            <if test="request.platformOriginalOrderNo != null and request.platformOriginalOrderNo != ''">
                and orders.platform_original_order_no = #{request.platformOriginalOrderNo}
            </if>
            <if test="request.isSampleOrder != null and request.isSampleOrder == 1">
                and orders.order_type in (20, 30)
                and orders.order_status = 21
            </if>
            <if test="request.platformOrderNos != null and request.platformOrderNos.size > 0">
                and orders.platform_order_no in
                <foreach collection="request.platformOrderNos" item="platformOrderNo" open="(" separator="," close=")">
                    #{platformOrderNo}
                </foreach>
            </if>
            <if test="request.orderCreateDateStart != null and request.orderCreateDateEnd != null">
                and orders.order_deliver_date between #{request.orderCreateDateStart} and #{request.orderCreateDateEnd}
            </if>
        </where>
    </select>

    <select id="getByPlatformOrderNos"
            resultType="string">
        SELECT
        platform_order_no
        FROM
        platform_order
        where
        platform_order_no in
        <foreach collection="platformOrderNos" item="platformOrderNo" open="(" separator="," close=")">
            #{platformOrderNo}
        </foreach>
    </select>


    <resultMap id="crmOrderPageMap" type="com.nsy.oms.business.domain.response.external.CrmOrderPageListResponse">
        <id property="orderId" column="order_id" jdbcType="INTEGER"/>
        <result property="storeId" column="store_id" jdbcType="INTEGER"/>
        <result property="marketCode" column="market_code" jdbcType="VARCHAR"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="orderStatus" column="order_status" jdbcType="INTEGER"/>
        <result property="paymentAmount" column="payment_amount" jdbcType="DECIMAL"/>
        <result property="shippingType" column="shipping_type" jdbcType="VARCHAR"/>
        <result property="currency" column="currency" jdbcType="VARCHAR"/>
        <result property="orderCreateDate" column="order_create_date" jdbcType="TIMESTAMP"/>
        <result property="orderDeliverDate" column="order_deliver_date" jdbcType="TIMESTAMP"/>
        <collection property="itemList" ofType="com.nsy.oms.business.domain.response.external.CrmOrderPageItemListResponse" javaType="java.util.List">
            <id property="orderItemId" column="order_item_id" jdbcType="INTEGER"/>
            <result property="sku" column="sku" jdbcType="VARCHAR"/>
            <result property="specId" column="spec_id" jdbcType="INTEGER"/>
            <result property="productId" column="product_id" jdbcType="INTEGER"/>
            <result property="qty" column="qty" jdbcType="INTEGER"/>
            <result property="sellerSku" column="seller_sku" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <select id="crmOrderPage" resultMap="crmOrderPageMap">
        SELECT
        o.store_id,
        o.market_code,
        o.platform_order_id as order_id,
        o.platform_original_order_no as order_no,
        o.order_status,
        o.payment_amount,
        o.shipping_type,
        o.currency,
        o.order_create_date,
        o.order_deliver_date,
        item.platform_order_item_id as order_item_id,
        item.sku,
        item.spec_id,
        item.product_id,
        item.qty,
        item.seller_sku
        FROM `platform_order` o
        INNER JOIN `platform_order_item` item
        ON o.platform_order_id = item.`platform_order_id`
        inner join platform_receiver receiver
        on o.platform_order_id = receiver.platform_order_id
        <where>
            receiver.buyer_email = #{request.buyerEmail}
            <if test="request.orderCreateDateStart != null and request.orderCreateDateEnd != null">
                and o.order_create_date BETWEEN #{request.orderCreateDateStart} AND #{request.orderCreateDateEnd}
            </if>
            <if test="request.storeIdList != null and request.storeIdList.size > 0">
                and o.store_id in
                <foreach collection="request.storeIdList" separator="," open="(" close=")" item="storeId">
                    #{storeId}
                </foreach>
            </if>
        </where>
        order by o.order_create_date desc
    </select>


    <resultMap id="crmOrderInfoMap" type="com.nsy.oms.business.domain.response.external.CrmOrderInoResponse">
        <id property="orderId" column="order_id" jdbcType="INTEGER"/>
        <result property="storeId" column="store_id" jdbcType="INTEGER"/>
        <result property="marketCode" column="market_code" jdbcType="VARCHAR"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="orderStatus" column="order_status" jdbcType="INTEGER"/>
        <result property="paymentAmount" column="payment_amount" jdbcType="DECIMAL"/>
        <result property="shippingType" column="shipping_type" jdbcType="VARCHAR"/>
        <result property="currency" column="currency" jdbcType="VARCHAR"/>
        <result property="orderCreateDate" column="order_create_date" jdbcType="TIMESTAMP"/>
        <result property="orderDeliverDate" column="order_deliver_date" jdbcType="TIMESTAMP"/>
        <collection property="itemList" ofType="com.nsy.oms.business.domain.response.external.CrmOrderItemResponse" javaType="java.util.List">
            <id property="orderItemId" column="order_item_id" jdbcType="INTEGER"/>
            <result property="sku" column="sku" jdbcType="VARCHAR"/>
            <result property="specId" column="spec_id" jdbcType="INTEGER"/>
            <result property="productId" column="product_id" jdbcType="INTEGER"/>
            <result property="qty" column="qty" jdbcType="INTEGER"/>
            <result property="sellerSku" column="seller_sku" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <select id="getCrmOrderInfo" resultMap="crmOrderInfoMap">
        SELECT
        o.store_id,
        o.market_code,
        o.platform_order_id as order_id,
        o.platform_original_order_no as order_no,
        o.order_status,
        o.payment_amount,
        o.shipping_type,
        o.currency,
        o.order_create_date,
        o.order_deliver_date,
        item.platform_order_item_id as order_item_id,
        item.sku,
        item.spec_id,
        item.product_id,
        item.qty,
        item.seller_sku
        FROM `platform_order` o
        INNER JOIN `platform_order_item` item
        ON o.platform_order_id = item.`platform_order_id`
        where o.platform_original_order_no = #{orderNo}
        and o.store_id in
        <foreach collection="storeIdList" separator="," open="(" close=")" item="storeId">
            #{storeId}
        </foreach>
    </select>

    <select id="getPlatformOrderPage" resultType="com.nsy.oms.business.domain.response.platform.PlatformOrderResponse">
        select distinct
               platform_order.platform_order_id,
               platform_order.platform_original_order_no,
               platform_order.order_status,
               platform_order.store_name,
               platform_order.platform_id,
               platform_order.product_total_amount,
               platform_order.payment_amount,
               platform_order.order_payment_date,
               platform_order.currency
        from nsy_oms.platform_order platform_order
                 left join nsy_oms.platform_order_item platform_order_item on platform_order_item.platform_order_id = platform_order.platform_order_id
        <where>
            <if test="permissionStoreIds != null and permissionStoreIds.size() > 0">
                and platform_order.store_id in
                <foreach collection="permissionStoreIds" separator="," index="index" item="permissionStoreId" open="(" close=")">
                    #{permissionStoreId}
                </foreach>
            </if>
            <if test="request.platformOriginalOrderNos != null and request.platformOriginalOrderNos.size() > 0">
                and platform_order.platform_original_order_no in
                <foreach collection="request.platformOriginalOrderNos" separator="," index="index" item="platformOriginalOrderNo" open="(" close=")">
                    #{platformOriginalOrderNo}
                </foreach>
            </if>
            <if test="request.skus != null and request.skus.size() > 0">
                and platform_order_item.sku in
                <foreach collection="request.skus" separator="," index="index" item="sku" open="(" close=")">
                    #{sku}
                </foreach>
            </if>
            <if test="request.sellerSkus != null and request.sellerSkus.size() > 0">
                and platform_order_item.seller_sku in
                <foreach collection="request.sellerSkus" separator="," index="index" item="sellerSku" open="(" close=")">
                    #{sellerSku}
                </foreach>
            </if>
            <if test="request.storeIds != null and request.storeIds.size() > 0">
                and platform_order.store_id in
                <foreach collection="request.storeIds" separator="," index="index" item="storeId" open="(" close=")">
                    #{storeId}
                </foreach>
            </if>
            <if test="request.orderStatusList != null and request.orderStatusList.size() > 0">
                and platform_order.order_status in
                <foreach collection="request.orderStatusList" separator="," index="index" item="orderStatus" open="(" close=")">
                    #{orderStatus}
                </foreach>
            </if>
            <if test="request.platformIds != null and request.platformIds.size() > 0">
                and platform_order.platform_id in
                <foreach collection="request.platformIds" separator="," index="index" item="platformId" open="(" close=")">
                    #{platformId}
                </foreach>
            </if>
            <if test="request.itemStatusList != null and request.itemStatusList.size() > 0">
                and platform_order_item.item_status in
                <foreach collection="request.itemStatusList" separator="," index="index" item="itemStatus" open="(" close=")">
                    #{itemStatus}
                </foreach>
            </if>
            <if test="request.orderPaymentDateStart != null and request.orderPaymentDateEnd != null">
                and platform_order.order_payment_date between #{request.orderPaymentDateStart} and #{request.orderPaymentDateEnd}
            </if>
        </where>
        order by platform_order.order_payment_date desc
    </select>

</mapper>
