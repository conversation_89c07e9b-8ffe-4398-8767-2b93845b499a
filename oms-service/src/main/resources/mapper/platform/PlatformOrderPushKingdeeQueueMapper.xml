<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.oms.repository.sql.mapper.platform.PlatformOrderPushKingdeeQueueMapper">

    <resultMap id="BaseResultMap" type="com.nsy.oms.repository.entity.platform.PlatformOrderPushKingdeeQueueEntity">
            <id property="pushKingdeeQueueId" column="push_kingdee_queue_id" jdbcType="INTEGER"/>
            <result property="platformOrderNo" column="platform_order_no" jdbcType="VARCHAR"/>
            <result property="orderStatus" column="order_status" jdbcType="TINYINT"/>
            <result property="orderType" column="order_type" jdbcType="TINYINT"/>
            <result property="freightFee" column="freight_fee" jdbcType="DECIMAL"/>
            <result property="commissionFee" column="commission_fee" jdbcType="DECIMAL"/>
            <result property="platformId" column="platform_id" jdbcType="INTEGER"/>
            <result property="platformName" column="platform_name" jdbcType="VARCHAR"/>
            <result property="storeId" column="store_id" jdbcType="INTEGER"/>
            <result property="storeName" column="store_name" jdbcType="VARCHAR"/>
            <result property="location" column="location" jdbcType="VARCHAR"/>
            <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="version" column="version" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        push_kingdee_queue_id,platform_order_no,order_status,
        order_type,freight_fee,commission_fee,
        platform_id,platform_name,store_id,
        store_name,location,create_date,
        create_by,update_date,update_by,
        version
    </sql>
</mapper>
