<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.oms.repository.sql.mapper.sa.SaStoreMapper">

    <select id="getStoreList" resultType="com.nsy.oms.repository.entity.sa.SaStoreEntity">
        select s.* from sa_store as s inner join sau_platform_auth_config as c on c.store_id=s.id
        <where>
            <choose>
                <when test="request.isCem==1">
                    s.platform_id in
                </when>
                <otherwise>
                    s.platform_id not in
                </otherwise>
            </choose>
            <foreach collection="request.platformIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>


    </select>

    <resultMap id="storeDetailResponseResultMap" type="com.nsy.api.oms.dto.response.store.StoreDetailResponse">

        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="erpStoreName" column="erp_store_name" jdbcType="VARCHAR"/>
        <result property="storeName" column="erp_store_name" jdbcType="VARCHAR"/>
        <result property="platformId" column="platform_id" jdbcType="INTEGER"/>
        <result property="platformName" column="platform_name" jdbcType="VARCHAR"/>
        <result property="retailId" column="retail_id" jdbcType="INTEGER"/>
        <result property="retailName" column="retail_name" jdbcType="VARCHAR"/>
        <result property="secondPlatformId" column="second_platform_id" jdbcType="INTEGER"/>
        <result property="secondPlatformName" column="second_platform_name" jdbcType="VARCHAR"/>
        <result property="storeUrl" column="store_url" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="nature" column="nature" jdbcType="TINYINT"/>
        <result property="associatedStoreDepartmentId" column="associated_store_department_id" jdbcType="INTEGER"/>
        <result property="associatedStoreDepartment" column="associated_store_department" jdbcType="VARCHAR"/>
        <result property="skuRule" column="sku_rule" jdbcType="VARCHAR"/>
        <result property="storeType" column="store_type" jdbcType="TINYINT"/>
        <result property="department" column="department" jdbcType="VARCHAR"/>
        <result property="departmentId" column="department_id" jdbcType="INTEGER"/>
        <result property="secondDepartmentId" column="second_department_id" jdbcType="INTEGER"/>
        <result property="secondDepartment" column="second_department" jdbcType="INTEGER"/>
        <result property="location" column="location" jdbcType="VARCHAR"/>

        <collection property="markets" columnPrefix="mk_" ofType="com.nsy.api.oms.dto.response.store.StoreMarketResponse">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="storeId" column="store_id" jdbcType="INTEGER"/>
            <result property="marketCode" column="market_code" jdbcType="VARCHAR"/>
            <result property="marketName" column="market_name" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
        </collection>

        <collection property="websites" columnPrefix="site_" ofType="com.nsy.api.oms.dto.response.store.StoreWebsiteMappingResponse">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="storeId" column="store_id" jdbcType="INTEGER"/>
            <result property="websiteId" column="website_id" jdbcType="INTEGER"/>
            <result property="websiteName" column="website_name" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
        </collection>

    </resultMap>


    <resultMap id="storeAmazonResponseResultMap" type="com.nsy.api.oms.dto.response.store.AmazonStoreListResponse">

        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="erpStoreName" column="erp_store_name" jdbcType="VARCHAR"/>
        <result property="storeName" column="erp_store_name" jdbcType="VARCHAR"/>
        <result property="platformId" column="platform_id" jdbcType="INTEGER"/>
        <result property="platformName" column="platform_name" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="skuRule" column="sku_rule" jdbcType="VARCHAR"/>
        <result property="department" column="department" jdbcType="VARCHAR"/>
        <result property="departmentId" column="department_id" jdbcType="INTEGER"/>
        <result property="secondDepartmentId" column="second_department_id" jdbcType="INTEGER"/>
        <result property="secondDepartment" column="second_department" jdbcType="INTEGER"/>
        <result property="openTransparency" column="open_transparency" jdbcType="INTEGER"/>
        <result property="openTransparency" column="open_transparency" jdbcType="INTEGER"/>
        <result property="fbaExamine" column="fba_examine" jdbcType="INTEGER"/>
        <result property="location" column="location" jdbcType="VARCHAR"/>

        <collection property="markets" columnPrefix="mk_" ofType="com.nsy.api.oms.dto.response.store.StoreMarketResponse">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="storeId" column="store_id" jdbcType="INTEGER"/>
            <result property="marketCode" column="market_code" jdbcType="VARCHAR"/>
            <result property="marketName" column="market_name" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
        </collection>
    </resultMap>

    <select id="getStoreByWebsiteId" resultMap="storeDetailResponseResultMap">
        SELECT
            store.id,
            store.erp_store_name,
            store.erp_store_name as store_name,
            store.platform_id,
            store.platform_name,
            store.retail_id,
            store.retail_name,
            store.nature,
            store.status,
            store.second_platform_id,
            store.second_platform_name,
            store.associated_store_id,
            store.sku_rule,
            store.store_type,
            store.store_url,
            store.department_id,
            store.department,
            store.second_department_id,
            store.second_department,
            store.location,
            store2.department_id as associated_store_department_id,
            store2.department as associated_store_department,
            website.id site_id,
            website.store_id site_store_id,
            website.website_id site_website_id,
            website.website_name site_website_name,
            website.status site_status,
            market.id mk_id,
            market.store_id mk_store_id,
            market.market_code mk_market_code,
            market.market_name mk_market_name,
            market.status mk_status
        FROM
            sa_store store
        INNER JOIN
            sa_store_website website
        on store.id = website.store_id and website.status = 1
        left join sa_store store2
            on store.associated_store_id = store2.id
        left join sa_store_market_set market
            on store.id = market.store_id and market.status = 1
        where website.website_id = #{websiteId}
            limit 1
    </select>

    <select id="getStoreDetailById" resultMap="storeDetailResponseResultMap">
        SELECT
            store.id,
            store.erp_store_name,
            store.erp_store_name as store_name,
            store.platform_id,
            store.platform_name,
            store.retail_id,
            store.retail_name,
            store.nature,
            store.status,
            store.second_platform_id,
            store.second_platform_name,
            store.associated_store_id,
            store.sku_rule,
            store.store_type,
            store.store_url,
            store.department_id,
            store.department,
            store.second_department_id,
            store.second_department,
            store.location,
            store2.department_id as associated_store_department_id,
            store2.department as associated_store_department,
            website.id site_id,
            website.store_id site_store_id,
            website.website_id site_website_id,
            website.website_name site_website_name,
            website.status site_status,
            market.id mk_id,
            market.store_id mk_store_id,
            market.market_code mk_market_code,
            market.market_name mk_market_name,
            market.status mk_status
        FROM
            sa_store store
                left join
            sa_store_website website
            on store.id = website.store_id and website.status = 1
                left join sa_store store2
                          on store.associated_store_id = store2.id
                left join sa_store_market_set market
                          on store.id = market.store_id and market.status = 1
        where store.id = #{storeId}
    </select>
    <select id="getStoreListByIds" resultMap="storeDetailResponseResultMap">
        SELECT
            store.id,
            store.erp_store_name,
            store.erp_store_name as store_name,
            store.platform_id,
            store.platform_name,
            store.retail_id,
            store.retail_name,
            store.nature,
            store.status,
            store.second_platform_id,
            store.second_platform_name,
            store.associated_store_id,
            store.sku_rule,
            store.store_type,
            store.store_url,
            store.department_id,
            store.department,
            store.second_department_id,
            store.second_department,
            store.location,
            store2.department_id as associated_store_department_id,
            store2.department as associated_store_department,
            website.id site_id,
            website.store_id site_store_id,
            website.website_id site_website_id,
            website.website_name site_website_name,
            website.status site_status,
            market.id mk_id,
            market.store_id mk_store_id,
            market.market_code mk_market_code,
            market.market_name mk_market_name,
            market.status mk_status
        FROM
            sa_store store
               left join
            sa_store_website website
            on store.id = website.store_id and website.status = 1
                left join sa_store store2
                          on store.associated_store_id = store2.id
                left join sa_store_market_set market
                          on store.id = market.store_id and market.status = 1
        where store.id in
        <foreach collection="storeIdList" item="storeId" separator="," open="(" close=")">
            #{storeId}
        </foreach>
    </select>

    <select id="getAllAmazonStore" resultMap="storeAmazonResponseResultMap">
        SELECT
            store.id,
            store.erp_store_name,
            store.erp_store_name as store_name,
            store.platform_id,
            store.platform_name,
            store.nature,
            store.status,
            store.second_platform_id,
            store.second_platform_name,
            store.associated_store_id,
            store.sku_rule,
            store.store_type,
            store.store_url,
            store.achievement_attribution,
            store.department_id,
            store.department,
            store.second_department_id,
            store.second_department,
            store.open_transparency,
            store.location,
            config.fba_examine,
            market.id mk_id,
            market.store_id mk_store_id,
            market.market_code mk_market_code,
            market.market_name mk_market_name,
            market.status mk_status
        FROM
            sa_store store
            left join sa_store_market_set market on store.id = market.store_id and market.status = 1
            left join sa_store_config config on store.id = config.store_id
        where store.platform_name like '亚马逊%' and store.status = 1
    </select>
    <select id="getStoreListByPlatformNames" resultType="com.nsy.api.oms.dto.response.store.StoreListResponse">
        SELECT
            store.id,
            store.erp_store_name,
            store.erp_store_name as store_name,
            store.platform_id,
            store.platform_name,
            store.nature,
            store.status,
            store.team_id,
            store.team,
            store.second_platform_id,
            store.second_platform_name,
            store.associated_store_id,
            store.sku_rule,
            store.store_type,
            store.store_url,
            store.achievement_attribution,
            store.department_id,
            store.department,
            store.second_department_id,
            store.second_department,
            store.location,
            store2.erp_store_name as associated_store_name
        FROM
            sa_store store
        left join sa_store store2 on store.associated_store_id = store2.id
        where store.platform_name in
        <foreach collection="platformNameList" item="platformName" open="(" close=")" separator=",">
            #{platformName}
        </foreach>

    </select>
    <select id="getStoreListByUserId" resultType="com.nsy.api.oms.dto.response.store.StoreListResponse">
        SELECT
        store.id,
        store.erp_store_name,
        store.erp_store_name as store_name,
        store.platform_id,
        store.platform_name,
        store.nature,
        store.status,
        store.team_id,
        store.team,
        store.second_platform_id,
        store.second_platform_name,
        store.associated_store_id,
        store.sku_rule,
        store.store_type,
        store.store_url,
        store.achievement_attribution,
        store.department_id,
        store.department,
        store.second_department_id,
        store.second_department,
        store.location,
        store2.erp_store_name as associated_store_name
        FROM
        sa_store store
        inner join sa_store_staffing staffing on store.id = staffing.store_id and staffing.staffing_id = #{userId} and staffing.status = 1
        left join sa_store store2 on store.associated_store_id = store2.id
    </select>

    <select id="getStoreListByCriteria" resultType="com.nsy.api.oms.dto.response.store.StoreListResponse">
        SELECT
            store.id,
            store.erp_store_name,
            store.erp_store_name as store_name,
            store.platform_id,
            store.platform_name,
            store.nature,
            store.status,
            store.team_id,
            store.team,
            store.second_platform_id,
            store.second_platform_name,
            store.associated_store_id,
            store.sku_rule,
            store.store_type,
            store.store_url,
            store.achievement_attribution,
            store.department_id,
            store.department,
            store.second_department_id,
            store.second_department,
            store.location,
            store2.erp_store_name as associated_store_name
        FROM
            sa_store store
                <if test="request.isSynchronizeOrder != null">
                    inner join sa_store_config config on store.id = config.store_id and config.prohibit_synchronized_order = #{request.isSynchronizeOrder}
                </if>
                left join sa_store store2 on store.associated_store_id = store2.id
        <where>
            <if test="request.id != null">
                and store.id = #{request.id}
            </if>
            <if test="request.idList != null and request.idList.size > 0">
                and store.id in
                <foreach collection="request.idList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="request.retailId != null">
                and store.retail_id = #{request.retailId}
            </if>
            <if test="request.retailIdList != null and request.retailIdList.size > 0">
                and store.retail_id in
                <foreach collection="request.retailIdList" item="retailId" open="(" close=")" separator=",">
                    #{retailId}
                </foreach>
            </if>
            <if test="request.platformId != null">
                and store.platform_id = #{request.platformId}
            </if>
            <if test="request.erpStoreName != null and request.erpStoreName != ''">
                and store.erp_store_name = #{request.erpStoreName}
            </if>
            <if test="request.erpStoreNameList != null and request.erpStoreNameList.size > 0">
                and store.erp_store_name in
                <foreach collection="request.erpStoreNameList" item="erpStoreName" open="(" close=")" separator=",">
                    #{erpStoreName}
                </foreach>
            </if>
            <if test="request.status != null">
                and store.status = #{request.status}
            </if>
            <if test="request.achievementAttribution != null and request.achievementAttribution != ''">
                and store.achievement_attribution = #{request.achievementAttribution}
            </if>
            <if test="request.department != null and request.department != ''">
                and store.department = #{request.department}
            </if>
            <if test="request.departmentList != null and request.departmentList.size > 0">
                and store.department in
                <foreach collection="request.departmentList" item="department" open="(" close=")" separator=",">
                    #{department}
                </foreach>
            </if>
            <if test="request.secondDepartment != null and request.secondDepartment != ''">
                and store.second_department = #{request.secondDepartment}
            </if>
            <if test="request.secondDepartmentList != null and request.secondDepartmentList.size > 0">
                and store.second_department in
                <foreach collection="request.secondDepartmentList" item="secondDepartment" open="(" close=")" separator=",">
                    #{secondDepartment}
                </foreach>
            </if>
            <if test="request.location != null and request.location != ''">
                and store.location = #{request.location}
            </if>
            <if test="request.locationList != null and request.locationList.size > 0">
                and store.location in
                <foreach collection="request.locationList" item="location" open="(" close=")" separator=",">
                    #{location}
                </foreach>
            </if>
            <if test="request.isAmazon != null">
                                                 
                <choose>
                    <when test="request.isAmazon == 1">
                        and store.platform_name like '亚马逊%' and store.achievement_attribution != '亚马逊海外仓'
                    </when>
                    <otherwise>
                        and store.platform_name not like '亚马逊%'
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>
    <select id="getlist" resultType="com.nsy.oms.business.domain.response.sa.StoreResponse">
          select s.*,p.status as paymentCardStatus,a.status as fincalAccountStatus from sa_store as s
        left join sa_store_finance_payment_account as p
        on p.store_id=s.id
        left join sa_store_finance_account as  a
        on a.store_id=s.id
        left join sa_sale_account as sale on s.sale_account_id=sale.id
          <where>
              <if test="request.status != null">
                  and s.status = #{request.status}
              </if>
              <if test="list != null and list.size > 0">
                  and s.id in
                  <foreach collection="list" item="item" open="(" close=")" separator=",">
                      #{item}
                  </foreach>
              </if>
              <if test="request.saSaleAccountList != null and request.saSaleAccountList.size > 0">
                  and s.sale_account_id in
                  <foreach collection="request.saSaleAccountList" item="item" open="(" close=")" separator=",">
                      #{item}
                  </foreach>
              </if>
              <if test="request.departmentId != null">
                  and s.department_id = #{request.departmentId}
              </if>
              <if test="request.saleAccountNum != null">
                  and s.sale_account_id =#{request.saleAccountNum}
              </if>

              <if test="request.platformId != null">
                  and s.platform_id = #{request.platformId}
              </if>
              <if test="request.secondPlatformId != null">
                  and s.second_platform_id = #{request.secondPlatformId}
              </if>
              <if test="request.isAuth != null">
                  and s.is_auth = #{request.isAuth}
              </if>
              <if test="request.grantStatus != null">
                  and  s.grant_status = #{request.grantStatus}
              </if>
              <if test="request.operateMethod != null">
                  and  s.operate_method = #{request.operateMethod}
              </if>
              <if test="request.erpStoreName != null and request.erpStoreName !=''">
                  and  s.erp_store_name like concat('%', #{request.erpStoreName}, '%')
              </if>

              <if test="request.storeName != null and request.storeName !=''">
                  and  s.store_name like   concat('%', #{request.storeName}, '%')
              </if>
              <if test="request.secondDepartmentId != null">
                  and s.second_department_id = #{request.secondDepartmentId}
              </if>
              <if test="request.paymentCardStatus !=null">
                  <choose>
                      <when test="request.paymentCardStatus ==2">
                          and p.id is null
                      </when>
                      <otherwise>
                          and p.status=#{request.paymentCardStatus}
                      </otherwise>
                  </choose>
              </if>
              <if test="request.fincalAccountStatus !=null">
                  <choose>
                      <when test="request.fincalAccountStatus ==2">
                          and a.id is null
                      </when>
                      <otherwise>
                          and a.status=#{request.fincalAccountStatus}
                      </otherwise>
                  </choose>
              </if>
              <if test="request.liveAccountId != null">
                  and s.live_account_id = #{request.liveAccountId}
              </if>
              <if test="request.adAccountId != null">
                  and  s.ad_account_id = #{request.adAccountId}
              </if>
              <if test="request.mediaAccountId != null">
                  and  s.media_account_id = #{request.mediaAccountId}
              </if>
          </where>
           order by s.id desc

    </select>

    <select id="getWebisteIdListByDepartment" resultType="integer">
        SELECT DISTINCT
            sw.`website_id`
        FROM
            sa_store s
                INNER JOIN sa_store_website sw ON s.`id` = sw.`store_id` AND sw.`status` = 1
        <where>
            <if test="department != null and department !=''">
                AND s.department = #{department}
            </if>
            <if test="location != null and location !=''">
                AND s.location = #{location}
            </if>
        </where>
    </select>

    <select id="getOrderGrabStoreIds" resultType="com.nsy.oms.business.domain.dto.StoreDTO">
        SELECT
        store.id as store_id,
        store.erp_store_name as store_name,
        store.platform_id,
        store.location
        FROM
        sa_store store
            inner join sa_store_config config on store.id = config.store_id and config.prohibit_synchronized_order = 0 and config.location = #{location}
        <where>
            store.location = #{location}
            and
            store.status = 1
            and store.achievement_attribution NOT IN ('亚马逊海外仓', '沃尔玛WFS', '海外代发')
            <if test="platformList != null and platformList.size > 0">
                and store.platform_id in
                <foreach collection="platformList" item="platformId" open="(" close=")" separator=",">
                    #{platformId}
                </foreach>
            </if>
            <if test="fetchCount != null">
                limit #{fetchCount}
            </if>
        </where>
    </select>

    <select id="getOrderGrabStoreIdList" resultType="com.nsy.oms.business.domain.dto.StoreDTO">
        SELECT
        store.id as store_id,
        store.erp_store_name as store_name,
        store.platform_id,
        store.location,
        config.current_catch_date as currentCatchDate
        FROM sa_store store
        inner join sa_store_config config on store.id = config.store_id and config.prohibit_synchronized_order = 0 and config.location = #{location}
        <where>
            store.location = #{location}
            and store.status = 1
            and store.achievement_attribution NOT IN ('亚马逊海外仓', '沃尔玛WFS', '海外代发')
            <if test="platformIdList != null and platformIdList.size > 0">
                and (store.platform_id in
                <foreach collection="platformIdList" item="platformId" open="(" close=")" separator=",">
                    #{platformId}
                </foreach>
                or store.second_platform_id in
                <foreach collection="platformIdList" item="platformId" open="(" close=")" separator=",">
                    #{platformId}
                </foreach>
                )
            </if>
            <if test="storeId != null and storeId > 0">
                and store.id = #{storeId}
            </if>
            <if test="notInStoreIdList != null and notInStoreIdList.size > 0">
                and store.id not in
                <foreach collection="notInStoreIdList" item="notInStoreId" open="(" close=")" separator=",">
                    #{notInStoreId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getStoreByWebsiteIds" resultType="com.nsy.api.oms.dto.response.store.StoreDetailResponse">
        SELECT
            store.id,
            store.erp_store_name,
            store.erp_store_name as store_name,
            store.platform_id,
            store.platform_name,
            store.retail_id,
            store.retail_name,
            store.nature,
            store.status,
            store.second_platform_id,
            store.second_platform_name,
            store.associated_store_id,
            store.sku_rule,
            store.store_type,
            store.store_url,
            store.department_id,
            store.department,
            store.second_department_id,
            store.second_department,
            store.location,
            website.id site_id,
            website.store_id site_store_id,
            website.website_id site_website_id,
            website.website_name site_website_name
        FROM
            sa_store store
                INNER JOIN
            sa_store_website website
            on store.id = website.store_id and website.status = 1
        <where>
            <if test="websiteIdList != null and websiteIdList.size > 0">
                and website.website_id  in
                <foreach collection="websiteIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

        </where>


    </select>
    <select id="getListByAccountSubjectId" resultType="com.nsy.oms.repository.entity.sa.SaStoreEntity">
        select o.*
        from nsy_oms.bd_account_subject as s
                 left join sa_sale_account as a on s.id=a.subject_id
                 left join sa_store as o on o.sale_account_id=a.id where s.id=#{subjectId}

    </select>

    <select id="getStoreException" resultType="com.nsy.oms.business.domain.dto.StoreExceptionDTO">
        SELECT
        store.id as store_id,
        store.erp_store_name as store_name,
        store.platform_id,
        store.location
        FROM
        sa_store store
        inner join sa_store_config config on store.id = config.store_id and config.prohibit_synchronized_order = 0
        where
            store.status = 1
            and store.achievement_attribution NOT IN ('亚马逊海外仓', '沃尔玛WFS', '海外代发')
    </select>

</mapper>
