<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.oms.repository.sql.mapper.celebrity.InternetCelebrityOrderItemPostMappingMapper">

    <select id="page"
            resultType="com.nsy.oms.repository.entity.celebrity.InternetCelebrityOrderItemPostMappingEntity">
        <include refid="search"></include>
    </select>
    <select id="count" resultType="java.lang.Long">
        select count(1) from (
        <include refid="search"></include>
        ) as a
    </select>
    <sql id="search">
        SELECT distinct m.* FROM internet_celebrity_order_item_post_mapping m
        LEFT JOIN internet_celebrity_sample_order_item icsoi ON icsoi.internet_celebrity_sample_order_item_id =
        m.internet_celebrity_sample_order_item_id
        LEFT JOIN internet_celebrity_sample_order_item_post icsoip ON
        icsoip.internet_celebrity_sample_order_item_post_id = m.internet_celebrity_sample_order_item_post_id
        LEFT JOIN internet_celebrity_sample_order icso ON
        icso.internet_celebrity_sample_order_id = icsoi.internet_celebrity_sample_order_id
        inner JOIN internet_celebrity_store_relation icsr ON icsr.id = m.store_relation_id
        LEFT JOIN internet_celebrity c ON icsr.internet_celebrity_id = c.id
        <where>
            <if test="query.ids!=null and query.ids.size() > 0 ">
                and m.id in
                <foreach collection="query.ids" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.internetCelebrityName!= null and query.internetCelebrityName!=''">
                and icsr.internet_celebrity_name like concat(#{query.internetCelebrityName},'%')
            </if>
            <if test="query.internetCelebrityNameList!=null and query.internetCelebrityNameList.size() > 0 ">
                and icsr.internet_celebrity_name in
                <foreach collection="query.internetCelebrityNameList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.videoCode!= null and query.videoCode!=''">
                and icsoip.video_code like concat(#{query.videoCode},'%')
            </if>
            <if test="query.videoCodeList!=null and query.videoCodeList.size() > 0 ">
                and icsoip.video_code in
                <foreach collection="query.videoCodeList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.platformOrderNo!= null and query.platformOrderNo!=''">
                and icso.platform_order_no like concat(#{query.platformOrderNo},'%')
            </if>
            <if test="query.platformOrderNoList!=null and query.platformOrderNoList.size() > 0 ">
                and icso.platform_order_no in
                <foreach collection="query.platformOrderNoList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.skc!= null and query.skc!=''">
                and icsoi.sku like concat(#{query.skc},'%')
            </if>
            <if test="query.hasAdCode!= null and query.hasAdCode == 1">
                and icsoip.ad_code != ''
            </if>
            <if test="query.hasAdCode!= null and query.hasAdCode == 0">
                and icsoip.ad_code = ''
            </if>
            <if test="query.adIntention!=null and query.adIntention.size() > 0 ">
                and icsoip.ad_intention in
                <foreach collection="query.adIntention" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.videoAuthorization!=null and query.videoAuthorization.size() > 0 ">
                and icsoip.video_authorization in
                <foreach collection="query.videoAuthorization" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.relationStatus!=null and query.relationStatus.size() > 0 ">
                and m.relation_status in
                <foreach collection="query.relationStatus" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.bdEmailList!=null and query.bdEmailList.size() > 0 ">
                and icsr.bd_email in
                <foreach collection="query.bdEmailList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.relateStoreIdList!=null and query.relateStoreIdList.size() > 0 ">
                and icsr.store_id in
                <foreach collection="query.relateStoreIdList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.remark!= null and query.remark!=''">
                and m.remark like concat(#{query.remark},'%')
            </if>
            <if test="query.internetCelebrityInfo!= null and query.internetCelebrityInfo!=''">
                and c.internet_celebrity_info like concat(#{query.internetCelebrityInfo},'%')
            </if>
            <if test="query.email!= null and query.email!=''">
                and c.email like concat(#{query.email},'%')
            </if>
            <if test="query.minGmv!= null">
                and icsoip.gmv &gt;= #{query.minGmv}
            </if>
            <if test="query.maxGmv!= null">
                and icsoip.gmv &lt;= #{query.maxGmv}
            </if>
            <if test="query.relationStartDate!= null">
                and m.relation_date &gt;= #{query.relationStartDate}
            </if>
            <if test="query.relationEndDate!= null">
                and m.relation_date &lt;= #{query.relationEndDate}
            </if>
            <if test="query.bdId!= null">
                and icsr.bd_id = #{query.bdId}
            </if>
            <if test="query.bdIdList!=null and query.bdIdList.size() > 0 ">
                and icsr.bd_id in
                <foreach collection="query.bdIdList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.bdNotCare!= null">
                and icsr.bd_not_care = #{query.bdNotCare}
            </if>
            <if test="query.orderCreateStartDate!= null">
                and icso.order_create_date &gt;= #{query.orderCreateStartDate}
            </if>
            <if test="query.orderCreateEndDate!= null">
                and icso.order_create_date &lt;= #{query.orderCreateEndDate}
            </if>
            <if test="query.orderCompromiseStartDate!= null">
                and icso.order_compromise_date &gt;= #{query.orderCompromiseStartDate}
            </if>
            <if test="query.orderCompromiseEndDate!= null">
                and icso.order_compromise_date &lt;= #{query.orderCompromiseEndDate}
            </if>
            <if test="query.quickSearchTypeList!=null and query.quickSearchTypeList.size() > 0 ">
                <if test="query.quickSearchTypeList.contains('已寄样衣')">
                    and icso.order_delivery_date is not null
                </if>
                <if test="query.quickSearchTypeList.contains('待签收样衣')">
                    and icso.order_delivery_date is not null and icso.order_compromise_date is null
                </if>
                <if test="query.quickSearchTypeList.contains('已签收样衣')">
                    and icso.order_compromise_date is not null
                </if>
                <if test="query.quickSearchTypeList.contains('同意投放广告')">
                    and icsoip.ad_intention = '同意'
                </if>
                <if test="query.quickSearchTypeList.contains('视频未提供')">
                    and m.internet_celebrity_sample_order_item_post_id = 0
                </if>
                <if test="query.quickSearchTypeList.contains('订单未匹配')">
                    and m.internet_celebrity_sample_order_item_id = 0
                </if>
                <if test="query.quickSearchTypeList.contains('未新建达人信息')">
                    and c.id is null
                </if>
                <if test="query.quickSearchTypeList.contains('未投放广告')">
                    and icsoip.ad_intention = '同意' and (ad_date is null or ad_date = '1970-01-01')
                </if>
                <if test="query.quickSearchTypeList.contains('订单视频已匹配')">
                    and m.internet_celebrity_sample_order_item_post_id > 0
                    and m.internet_celebrity_sample_order_item_id > 0
                </if>
            </if>
        </where>
        order by m.relation_date desc
    </sql>
</mapper>