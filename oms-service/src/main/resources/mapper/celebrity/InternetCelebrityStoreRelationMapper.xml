<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.oms.repository.sql.mapper.celebrity.InternetCelebrityStoreRelationMapper">

    <select id="findOrderRelationWithNoCreator" resultType="java.lang.Integer">
        select distinct r.id from internet_celebrity_store_relation r
        inner join internet_celebrity_order_item_post_mapping m on r.id = m.store_relation_id
        inner join internet_celebrity_sample_order_item i on i.internet_celebrity_sample_order_item_id = m.internet_celebrity_sample_order_item_id
        inner join internet_celebrity_sample_order o on i.internet_celebrity_sample_order_id = o.internet_celebrity_sample_order_id
        where r.internet_celebrity_id = 0 and ((o.internet_celebrity_no != '' and o.internet_celebrity_no = #{creatorNo}) or (o.internet_celebrity_nickname != '' and o.internet_celebrity_nickname = #{creatorName}))
    </select>

    <select id="findVideoRelationWithNoCreator" resultType="java.lang.Integer">
        select distinct r.id from internet_celebrity_store_relation r
        inner join internet_celebrity_order_item_post_mapping m on r.id = m.store_relation_id
        inner join internet_celebrity_sample_order_item_post p on m.internet_celebrity_sample_order_item_post_id = p.internet_celebrity_sample_order_item_post_id
        where r.internet_celebrity_id = 0 and ((p.internet_celebrity_no != '' and p.internet_celebrity_no = #{creatorNo}) or (p.internet_celebrity_name != '' and p.internet_celebrity_name = #{creatorName}))
    </select>
    <select id="findTopByOrderId"
            resultType="com.nsy.oms.repository.entity.celebrity.InternetCelebrityStoreRelationEntity">
        select r.*
        from internet_celebrity_store_relation r
                 inner join internet_celebrity_order_item_post_mapping m on r.id = m.store_relation_id
                 inner join internet_celebrity_sample_order_item item
                            on m.internet_celebrity_sample_order_item_id = item.internet_celebrity_sample_order_item_id
                 inner join internet_celebrity_sample_order o
                            on o.internet_celebrity_sample_order_id = item.internet_celebrity_sample_order_id
        where o.internet_celebrity_sample_order_id = #{orderId} limit 1
    </select>
</mapper>