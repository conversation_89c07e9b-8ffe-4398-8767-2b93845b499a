<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.oms.repository.sql.mapper.celebrity.InternetCelebritySampleOrderItemPostMapper">

    <select id="findAllByStoreIdAndVideoCodeIn"
            resultType="com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderItemPostEntity">
        select
        *
        from internet_celebrity_sample_order_item_post
        where
        store_id = #{storeId,jdbcType=NUMERIC}
        AND video_code in
        <foreach collection="videoCodeList" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="findVideoByCreatorIdWithNoOrder"
            resultType="com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderItemPostEntity">
        select p.*
        from internet_celebrity_sample_order_item_post p
                 left join internet_celebrity_order_item_post_mapping m
                            on p.internet_celebrity_sample_order_item_post_id =
                               m.internet_celebrity_sample_order_item_post_id
        where p.store_id = #{storeId}
          and p.seller_product_id = #{sellerProductId}
          and p.internet_celebrity_id = #{creatorId}
          and (m.internet_celebrity_sample_order_item_id = 0 or m.id is null)
        order by p.internet_celebrity_sample_order_item_post_id asc limit 1
    </select>
    <select id="findVideoByCreatorNoWithNoOrder"
            resultType="com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderItemPostEntity">
        select p.*
        from internet_celebrity_sample_order_item_post p
                 left join internet_celebrity_order_item_post_mapping m
                            on p.internet_celebrity_sample_order_item_post_id =
                               m.internet_celebrity_sample_order_item_post_id
        where p.store_id = #{storeId}
          and p.seller_product_id = #{sellerProductId}
          and p.internet_celebrity_no = #{creatorNo}
          and (m.internet_celebrity_sample_order_item_id = 0 or m.id is null)
        order by p.internet_celebrity_sample_order_item_post_id asc limit 1
    </select>
    <select id="findVideoByCreatorNameWithNoOrder"
            resultType="com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderItemPostEntity">
        select p.*
        from internet_celebrity_sample_order_item_post p
                 left join internet_celebrity_order_item_post_mapping m
                            on p.internet_celebrity_sample_order_item_post_id =
                               m.internet_celebrity_sample_order_item_post_id
        where p.store_id = #{storeId}
          and p.seller_product_id = #{sellerProductId}
          and p.internet_celebrity_name = #{creatorName}
          and (m.internet_celebrity_sample_order_item_id = 0 or m.id is null)
        order by p.internet_celebrity_sample_order_item_post_id asc limit 1
    </select>
    <select id="pageIdNoMapping" resultType="java.lang.Integer">
        select p.internet_celebrity_sample_order_item_post_id
        from internet_celebrity_sample_order_item_post p
                 left join internet_celebrity_order_item_post_mapping m
                           on p.internet_celebrity_sample_order_item_post_id =
                              m.internet_celebrity_sample_order_item_post_id
        where m.id is null
        and p.internet_celebrity_sample_order_item_post_id > #{maxId}
        limit #{limit}
    </select>
    <select id="getVideoViewsByPostIdIn"
            resultType="com.nsy.oms.business.domain.response.celebrity.InternetCelebrityPostViewNum">
        SELECT post_id, SUM(video_views) as videoViews FROM `internet_celebrity_sample_order_item_post_daily`
        where post_id in
        <foreach collection="postIds" separator="," index="index" item="item" open="(" close=")">
            #{item}
        </foreach>
        GROUP BY post_id
    </select>
    <select id="findByStoreIdAndVideoCodeAndSellerProductId" resultType="com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderItemPostEntity">
        select
         *
        from internet_celebrity_sample_order_item_post
        where
        store_id = #{storeId,jdbcType=NUMERIC}
        AND video_code = #{videoCode,jdbcType=VARCHAR}
        AND seller_product_id = #{sellerProductId,jdbcType=VARCHAR}
        limit 1
    </select>
</mapper>
