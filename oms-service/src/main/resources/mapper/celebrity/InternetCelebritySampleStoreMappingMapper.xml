<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.oms.repository.sql.mapper.celebrity.InternetCelebritySampleStoreMappingMapper">

    <resultMap id="BaseResultMap" type="com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleStoreMappingEntity">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="storeId" column="store_id" jdbcType="INTEGER"/>
            <result property="storeName" column="store_name" jdbcType="VARCHAR"/>
            <result property="sampleStoreId" column="sample_store_id" jdbcType="INTEGER"/>
            <result property="sampleStoreName" column="sample_store_name" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
            <result property="version" column="version" jdbcType="INTEGER"/>
            <result property="location" column="location" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,store_id,store_name,
        sample_store_id,sample_store_name,create_by,
        create_date,update_by,update_date,
        version,location
    </sql>
</mapper>
