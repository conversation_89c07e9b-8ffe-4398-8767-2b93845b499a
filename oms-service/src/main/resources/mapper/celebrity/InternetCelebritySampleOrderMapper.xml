<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.oms.repository.sql.mapper.celebrity.InternetCelebritySampleOrderMapper">

    <select id="getInternetCelebritySampleOrderList" resultType="com.nsy.oms.business.domain.response.celebrity.InternetCelebritySampleOrder">
        <include refid="getInternetCelebritySampleOrderSql"/>
    </select>

    <select id="getInternetCelebritySampleOrderPage" resultType="com.nsy.oms.business.domain.response.celebrity.InternetCelebritySampleOrder">
        <include refid="getInternetCelebritySampleOrderSql"/>
        order by sample_order.order_create_date desc
    </select>

    <select id="getCelebrityPerformanceRatePage" resultType="com.nsy.oms.business.domain.response.celebrity.InternetCelebritySampleOrder">
        <include refid="getCelebrityPerformanceRateSql"/>
        group by internet_celebrity_id
    </select>

    <select id="getCelebrityPerformanceTotalRate" resultType="com.nsy.oms.business.domain.response.celebrity.InternetCelebritySampleOrder">
        <include refid="getCelebrityPerformanceRateSql"/>
    </select>
    <select id="pageIdNoMapping" resultType="java.lang.Integer">
        select o.internet_celebrity_sample_order_id
        from internet_celebrity_sample_order o
                 inner join internet_celebrity_sample_order_item i
                            on i.internet_celebrity_sample_order_id = o.internet_celebrity_sample_order_id
                 left join internet_celebrity_order_item_post_mapping m
                           on m.internet_celebrity_sample_order_item_id = i.internet_celebrity_sample_order_item_id
        where m.id is null
          and o.store_id != 0
          and o.internet_celebrity_sample_order_id > #{maxId} limit #{limit}
    </select>

    <sql id="getInternetCelebritySampleOrderSql">
        select sample_order_item.internet_celebrity_sample_order_item_id,
               sample_order_item.sku,
               sample_order_item.qty,
               sample_order_item.seller_sku,
               sample_order_item.sku_picture_url,
               sample_order_item.location,
               sample_order.internet_celebrity_sample_order_id,
               sample_order.store_id,
               sample_order.store_name,
               sample_order_item.delivery_store_id,
               sample_order.platform_order_no,
               sample_order_item.platform_original_order_no,
               sample_order.order_status,
               sample_order.buyer_nick,
               sample_order.internet_celebrity_id,
               sample_order.internet_celebrity_nickname,
               sample_order.internet_celebrity_dept_id,
               sample_order.internet_celebrity_dept_name,
               sample_order.internet_celebrity_platform,
               sample_order.owner_code,
               sample_order.owner_name,
               sample_order_item.platform_package_id,
               sample_order_item.package_status,
               sample_order_item.tracking_number,
               sample_order.order_create_date,
               sample_order_item.order_delivery_date,
               sample_order_item.order_compromise_date,
               sample_order.order_type,
               sample_order_item.tracking_sync_error_message,
               sample_order_item.tracking_sync_status,
               sample_order.remark,
               internet_celebrity_sample_order_permission.internet_celebrity_sample_order_permission_id,
               internet_celebrity_sample_order_permission.owner_user_id,
               internet_celebrity_sample_order_permission.owner_dept_id,
               internet_celebrity_sample_order_permission.user_id,
               internet_celebrity_sample_order_permission.dept_id
        from internet_celebrity_sample_order_item sample_order_item
                 left join internet_celebrity_sample_order sample_order on sample_order_item.internet_celebrity_sample_order_id = sample_order.internet_celebrity_sample_order_id
                 left join internet_celebrity_sample_order_permission internet_celebrity_sample_order_permission FORCE INDEX(idx_internet_celebrity_sample_order_id) on internet_celebrity_sample_order_permission.internet_celebrity_sample_order_id = sample_order.internet_celebrity_sample_order_id
        <where>
            <include refid="internetCelebritySampleOrderWhereSql"/>
        </where>
    </sql>



    <sql id="getCelebrityPerformanceRateSql">
        select internet_celebrity_dept_name,
               internet_celebrity_platform,
               internet_celebrity_nickname,
               owner_name,
               internet_celebrity_id,
               count(1)                                                                                         as order_num,
               sum(IF(first_post_date is not null, 1, 0))                                                       as post_num,
               round(sum(IF(datediff(first_post_date, order_compromise_date) <![CDATA[ <= ]]> 7, 1, 0)) / count(1) * 100, 2)  as orders_within7_rate,
               round(sum(IF(datediff(first_post_date, order_compromise_date) <![CDATA[ <= ]]> 15, 1, 0)) / count(1) * 100, 2) as orders_within15_rate,
               round(sum(IF(datediff(first_post_date, order_compromise_date) <![CDATA[ <= ]]> 30, 1, 0)) / count(1) * 100, 2) as orders_within30_rate,
               round(sum(IF(first_post_date is not null, 1, 0)) / count(1) * 100,2)                             as post_rate,
               max (sample_order.order_create_date)                                                             as last_order_date ,
               min (sample_order.order_create_date)                                                             as first_order_date
        from nsy_oms.internet_celebrity_sample_order sample_order
                 left join internet_celebrity_sample_order_permission internet_celebrity_sample_order_permission on internet_celebrity_sample_order_permission.internet_celebrity_sample_order_id = sample_order.internet_celebrity_sample_order_id
        <where>
            <include refid="internetCelebritySampleOrderWhereSql"/>
            and order_type != 'NOT_ORDER'
        </where>
    </sql>

    <sql id="internetCelebritySampleOrderWhereSql">
            <if test="request.ids != null and request.ids.size() > 0">
                and sample_order_item.internet_celebrity_sample_order_item_id in
                <foreach collection="request.ids" separator="," index="index" item="id" open="(" close=")">
                    #{id}
                </foreach>
            </if>
            <if test="request.internetCelebritySampleOrderItemIds != null and request.internetCelebritySampleOrderItemIds.size() > 0">
                and sample_order_item.internet_celebrity_sample_order_item_id in
                <foreach collection="request.internetCelebritySampleOrderItemIds" separator="," index="index" item="internetCelebritySampleOrderItemId" open="(" close=")">
                    #{internetCelebritySampleOrderItemId}
                </foreach>
            </if>
            <if test="request.ownerName!=null and request.ownerName!=''">
                and sample_order.owner_name = #{request.ownerName}
            </if>
            <if test="request.internetCelebrityId!=null and request.internetCelebrityId!=''">
                and sample_order.internet_celebrity_id = #{request.internetCelebrityId}
            </if>
            <if test="request.storeId!=null">
                and sample_order.store_id = #{request.storeId}
            </if>
            <if test="request.platformOrderNo!=null and request.platformOrderNo!=''">
                and sample_order.platform_order_no like concat('%',#{request.platformOrderNo},'%')
            </if>
            <if test="request.packageStatus!=null and request.packageStatus!=''">
                and sample_order.package_status = #{request.packageStatus}
            </if>
            <if test="request.orderCreateDateStart != null and request.orderCreateDateEnd != null">
                AND sample_order.order_create_date BETWEEN #{request.orderCreateDateStart} AND #{request.orderCreateDateEnd}
            </if>
            <if test="request.orderCompromiseDateStart != null and request.orderCompromiseDateEnd != null">
                AND sample_order.order_compromise_date BETWEEN #{request.orderCompromiseDateStart} AND #{request.orderCompromiseDateEnd}
            </if>
            <if test="request.firstPostDateStart != null and request.firstPostDateEnd != null">
                AND sample_order.first_post_date BETWEEN #{request.firstPostDateStart} AND #{request.firstPostDateEnd}
            </if>
            <if test="request.internetCelebrityNickname!=null and request.internetCelebrityNickname!=''">
                and sample_order.internet_celebrity_nickname = #{request.internetCelebrityNickname}
            </if>
            <if test="request.internetCelebrityDeptName!=null and request.internetCelebrityDeptName!=''">
                and sample_order.internet_celebrity_dept_name = #{request.internetCelebrityDeptName}
            </if>
            <if test="request.sku!=null and request.sku!=''">
                and sample_order_item.sku = #{request.sku}
            </if>
            <if test="request.skus != null and request.skus.size() > 0">
                and sample_order_item.sku in
                <foreach collection="request.skus" separator="," index="index" item="sku" open="(" close=")">
                    #{sku}
                </foreach>
            </if>
            <if test="request.postStatus != null and request.postStatus == 1">
                and sample_order.first_post_date is not null
            </if>
            <if test="request.postStatus != null and request.postStatus == 0">
                and sample_order.first_post_date is null
            </if>
            <if test="request.syncCooperationNumType =='increment'
                          and request.createDateStart!=null
                          and request.createDateEnd!=null">
                and sample_order.create_date between #{request.createDateStart} and #{request.createDateEnd}
            </if>
    </sql>
</mapper>
