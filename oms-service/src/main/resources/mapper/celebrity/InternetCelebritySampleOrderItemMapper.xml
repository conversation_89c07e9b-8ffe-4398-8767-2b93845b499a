<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.oms.repository.sql.mapper.celebrity.InternetCelebritySampleOrderItemMapper">

    <select id="findTopByStoreIdAndSellerProductIdAndCreatorIdWithNoMapping"
            resultType="com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderItemEntity">
        select i.*
        from internet_celebrity_sample_order_item i
                 inner join internet_celebrity_sample_order o
                            on i.internet_celebrity_sample_order_id = o.internet_celebrity_sample_order_id
                 left join internet_celebrity_order_item_post_mapping m
                            on m.internet_celebrity_sample_order_item_id = i.internet_celebrity_sample_order_item_id
        where (m.id is null or m.internet_celebrity_sample_order_item_post_id = 0)
          and i.seller_product_id = #{sellerProductId}
          and o.store_id = #{storeId}
          and o.internet_celebrity_id = #{creatorId}
        order by i.internet_celebrity_sample_order_item_id asc limit 1
    </select>

    <select id="findTopByStoreIdAndSellerProductIdAndCreatorNoWithNoMapping"
            resultType="com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderItemEntity">
        select i.*
        from internet_celebrity_sample_order_item i
                 inner join internet_celebrity_sample_order o
                            on i.internet_celebrity_sample_order_id = o.internet_celebrity_sample_order_id
                 left join internet_celebrity_order_item_post_mapping m
                            on m.internet_celebrity_sample_order_item_id = i.internet_celebrity_sample_order_item_id
        where (m.id is null or m.internet_celebrity_sample_order_item_post_id = 0)
          and i.seller_product_id = #{sellerProductId}
          and o.store_id = #{storeId}
          and o.internet_celebrity_no = #{creatorNo}
        order by i.internet_celebrity_sample_order_item_id asc limit 1
    </select>

    <select id="findTopByStoreIdAndSellerProductIdAndCreatorNameWithNoMapping"
            resultType="com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderItemEntity">
        select i.*
        from internet_celebrity_sample_order_item i
                 inner join internet_celebrity_sample_order o
                            on i.internet_celebrity_sample_order_id = o.internet_celebrity_sample_order_id
                 left join internet_celebrity_order_item_post_mapping m
                            on m.internet_celebrity_sample_order_item_id = i.internet_celebrity_sample_order_item_id
        where (m.id is null or m.internet_celebrity_sample_order_item_post_id = 0)
          and i.seller_product_id = #{sellerProductId}
          and o.store_id = #{storeId}
          and o.internet_celebrity_nickname = #{creatorName}
        order by i.internet_celebrity_sample_order_item_id asc limit 1
    </select>


    <select id="findTopByStoreIdAndSkuInAndCreatorIdWithNoMapping"
            resultType="com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderItemEntity">
        select i.*
        from internet_celebrity_sample_order_item i
        inner join internet_celebrity_sample_order o
        on i.internet_celebrity_sample_order_id = o.internet_celebrity_sample_order_id
        left join internet_celebrity_order_item_post_mapping m
        on m.internet_celebrity_sample_order_item_id = i.internet_celebrity_sample_order_item_id
        where (m.id is null or m.internet_celebrity_sample_order_item_post_id = 0) and i.sku in
        <foreach collection="skuList" item="item"
                 open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and o.store_id = #{storeId}
        and o.internet_celebrity_id = #{creatorId}
        order by i.internet_celebrity_sample_order_item_id asc limit 1
    </select>

    <select id="findTopByStoreIdAndSkuInAndCreatorNoWithNoMapping"
            resultType="com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderItemEntity">
        select i.*
        from internet_celebrity_sample_order_item i
        inner join internet_celebrity_sample_order o
        on i.internet_celebrity_sample_order_id = o.internet_celebrity_sample_order_id
        left join internet_celebrity_order_item_post_mapping m
        on m.internet_celebrity_sample_order_item_id = i.internet_celebrity_sample_order_item_id
        where (m.id is null or m.internet_celebrity_sample_order_item_post_id = 0) and i.sku in
        <foreach collection="skuList" item="item"
                 open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and o.store_id = #{storeId}
        and o.internet_celebrity_no = #{creatorNo}
        order by i.internet_celebrity_sample_order_item_id asc limit 1
    </select>
    <select id="findTopByStoreIdAndSkuInAndCreatorNameWithNoMapping"
            resultType="com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderItemEntity">
        select i.*
        from internet_celebrity_sample_order_item i
        inner join internet_celebrity_sample_order o
        on i.internet_celebrity_sample_order_id = o.internet_celebrity_sample_order_id
        left join internet_celebrity_order_item_post_mapping m
        on m.internet_celebrity_sample_order_item_id = i.internet_celebrity_sample_order_item_id
        where (m.id is null or m.internet_celebrity_sample_order_item_post_id = 0) and i.sku in
        <foreach collection="skuList" item="item"
                 open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and o.store_id = #{storeId}
        and o.internet_celebrity_nickname = #{creatorName}
        order by i.internet_celebrity_sample_order_item_id asc limit 1
    </select>


</mapper>
