<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.oms.repository.sql.mapper.celebrity.InternetCelebrityMapper">

    <select id="page" resultType="com.nsy.oms.repository.entity.celebrity.InternetCelebrityEntity">
        select c.* from internet_celebrity c
        left join internet_celebrity_store_relation icsr on icsr.internet_celebrity_id = c.id
        <where>
            <if test="query.ids!=null and query.ids.size() > 0 ">
                and c.id in
                <foreach collection="query.ids" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.internetCelebrityName!= null and query.internetCelebrityName!=''">
                and c.internet_celebrity_name like concat(#{query.internetCelebrityName},'%')
            </if>
            <if test="query.internetCelebrityNameList!=null and query.internetCelebrityNameList.size() > 0 ">
                and c.internet_celebrity_name in
                <foreach collection="query.internetCelebrityNameList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.email!= null and query.email!=''">
                and c.email like concat(#{query.email},'%')
            </if>
            <if test="query.remark!= null and query.remark!=''">
                and icsr.remark like concat(#{query.remark},'%')
            </if>
            <if test="query.bdEmailList!=null and query.bdEmailList.size() > 0 ">
                and icsr.bd_email in
                <foreach collection="query.bdEmailList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.relateStoreIdList!=null and query.relateStoreIdList.size() > 0 ">
                and icsr.store_id in
                <foreach collection="query.relateStoreIdList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.unRelateStoreIdList!=null and query.unRelateStoreIdList.size() > 0 ">
                AND NOT EXISTS (
                SELECT 1
                FROM internet_celebrity_store_relation icsr
                WHERE icsr.internet_celebrity_id = c.id
                AND icsr.store_id in
                <foreach collection="query.unRelateStoreIdList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="query.relationStatus!=null and query.relationStatus.size() > 0 ">
                and icsr.relation_status in
                <foreach collection="query.relationStatus" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.levelList!=null and query.levelList.size() > 0 ">
                and c.level in
                <foreach collection="query.levelList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.shiyingFitnessList!=null and query.shiyingFitnessList.size() > 0 ">
                and icsr.shiying_fitness in
                <foreach collection="query.shiyingFitnessList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.videoNumIn14!= null">
                and icsr.video_num_in14 &gt;= #{query.videoNumIn14}
            </if>
            <if test="query.cooperateTimes!= null and query.cooperateTimesCondition != null and query.cooperateTimesCondition != '' ">
                and icsr.cooperate_times ${query.cooperateTimesCondition} #{query.cooperateTimes}
            </if>
            <if test="query.internetCelebrityInfo!= null and query.internetCelebrityInfo!=''">
                and c.internet_celebrity_info like concat(#{query.internetCelebrityInfo},'%')
            </if>
            <if test="query.instagram!= null and query.instagram!=''">
                and c.instagram like concat(#{query.instagram},'%')
            </if>
            <if test="query.facebook!= null and query.facebook!=''">
                and c.facebook like concat(#{query.facebook},'%')
            </if>
            <if test="query.whatsapp!= null and query.whatsapp!=''">
                and c.whatsapp like concat(#{query.whatsapp},'%')
            </if>
            <if test="query.phoneNumber!= null and query.phoneNumber!=''">
                and c.phone_number like concat(#{query.phoneNumber},'%')
            </if>
            <if test="query.minFollowers!= null">
                and c.followers &gt;= #{query.minFollowers}
            </if>
            <if test="query.maxFollowers!= null">
                and c.followers &lt;= #{query.maxFollowers}
            </if>
            <if test="query.createStartDate!= null">
                and c.create_date &gt;= #{query.createStartDate}
            </if>
            <if test="query.createEndDate!= null">
                and c.create_date &lt;= #{query.createEndDate}
            </if>
            <if test="query.bdId!= null">
                and icsr.bd_id = #{query.bdId}
            </if>
            <if test="query.bdIdList!=null and query.bdIdList.size() > 0 ">
                and icsr.bd_id in
                <foreach collection="query.bdIdList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.bdNotCare!= null">
                and icsr.bd_not_care = #{query.bdNotCare}
            </if>
        </where>
        GROUP BY c.`id`
        <if test="query.sortFiled != null and query.sortFiled == 'gmv'">
            order by c.gmv ${query.sortOrder}
        </if>
        <if test="query.sortFiled != null and query.sortFiled == 'videoGmvIn30'">
            order by SUM(icsr.video_gmv_in30) ${query.sortOrder}
        </if>
        <if test="query.sortFiled == null">
            ORDER BY
            CASE level
            WHEN 'V' THEN 1
            WHEN 'S' THEN 2
            WHEN 'A' THEN 3
            WHEN 'B' THEN 4
            WHEN 'C' THEN 5
            WHEN '' THEN 6
            ELSE 7
            END,
            c.create_date DESC
        </if>
    </select>
    <select id="findAllByInternetCelebrityNoIn" resultType="com.nsy.oms.repository.entity.celebrity.InternetCelebrityEntity">
        select
         *
        from internet_celebrity
        where
        internet_celebrity_no in
        <foreach collection="internetCelebrityNoList" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="findAllByInternetCelebrityNameIn" resultType="com.nsy.oms.repository.entity.celebrity.InternetCelebrityEntity">
        select
         *
        from internet_celebrity
        where
        internet_celebrity_name in
        <foreach collection="internetCelebrityNameList" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
</mapper>