<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.oms.repository.sql.mapper.rule.ConfigRecommendProductRuleItemMapper">

    <resultMap id="BaseResultMap" type="com.nsy.oms.repository.entity.rule.ConfigRecommendProductRuleItemEntity">
        <id property="configRecommendProductRuleItemId" column="config_recommend_product_rule_item_id"
            jdbcType="INTEGER"/>
        <result property="configRecommendProductRuleId" column="config_recommend_product_rule_id" jdbcType="INTEGER"/>
        <result property="ruleKey" column="rule_key" jdbcType="VARCHAR"/>
        <result property="ruleValue" column="rule_value" jdbcType="VARCHAR"/>
        <result property="isDefault" column="is_default" jdbcType="BOOLEAN"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="version" column="version" jdbcType="BIGINT"/>
        <result property="location" column="location" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        config_recommend_product_rule_item_id
        ,config_recommend_product_rule_id,rule_key,
        rule_value,is_default,
        remark,create_date,create_by,
        update_date,update_by,version,
        location
    </sql>

    <select id="getByRuleIdIn" resultType="com.nsy.oms.repository.entity.rule.ConfigRecommendProductRuleItemEntity">
        SELECT
        a.`config_recommend_product_rule_item_id`,
        a.`config_recommend_product_rule_id`,
        a.`rule_key`,
        a.`use_type`,
        a.`rule_value`,
        a.`is_default`,
        a.`remark`,
        a.`create_date`,
        a.`create_by`,
        a.`update_date`,
        a.`update_by`,
        a.`version`,
        a.`location`
        FROM
        nsy_oms.config_recommend_product_rule_item a
        <where>
            <if test="ruleIds != null and ruleIds.size() > 0">
                AND a.`config_recommend_product_rule_id` IN
                <foreach collection="ruleIds" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="useType != null and useType != ''">
                and a.`use_type` = #{useType}
            </if>
        </where>
    </select>
    <select id="findByConfigRecommendProductRuleId" resultMap="BaseResultMap">
        select
            a.`config_recommend_product_rule_item_id`,
            a.`config_recommend_product_rule_id`,
            a.`rule_key`,
            a.`use_type`,
            a.`rule_value`,
            a.`is_default`,
            a.`remark`,
            a.`create_date`,
            a.`create_by`,
            a.`update_date`,
            a.`update_by`,
            a.`version`,
            a.`location`
        from config_recommend_product_rule_item a
        where
        a.config_recommend_product_rule_id = #{configRecommendProductRuleId,jdbcType=NUMERIC}
    </select>
</mapper>
