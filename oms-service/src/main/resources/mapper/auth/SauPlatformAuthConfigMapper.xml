<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.oms.repository.sql.mapper.auth.SauPlatformAuthConfigMapper">


    <select id="getAuthInfoByWebsiteId"
            resultType="com.nsy.oms.business.domain.response.auth.SauPlatformAuthConfigResponse">
        select c.* from nsy_oms.sau_platform_auth_config as c
                            inner join nsy_oms.sa_store_website as w
                                       on c.store_id=w.store_id
                            inner join nsy_oms.sa_store as  a on  a.id=w.store_id and a.status=1
        where w.website_id=#{websiteId} and w.status=1 order by update_date desc limit 1
    </select>
</mapper>
