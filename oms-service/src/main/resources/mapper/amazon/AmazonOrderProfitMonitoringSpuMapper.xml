<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.oms.repository.sql.mapper.amazon.AmazonOrderProfitMonitoringSpuMapper">

    <select id="getPage" resultType="com.nsy.oms.repository.entity.amzon.AmazonOrderProfitMonitoringSpuEntity">
        select spu.* from amazon_order_profit_monitoring_spu spu
        <where>
            <if test="request.sku != null and request.sku != ''">
                and spu.spu like #{request.sku}
            </if>
            <if test="request.parentAsin != null and request.parentAsin != ''">
                and spu.parent_asin = #{request.parentAsin}
            </if>
            <if test="request.storeIds != null and request.storeIds.size() > 0">
                and spu.store_id in
                <foreach collection="request.storeIds" separator="," close=")" open="(" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="request.labelNames != null and request.labelNames.size() > 0">
                and exists (select 1 from amazon_order_profit_monitoring_label label
                where spu.spu = label.spu and spu.store_id = label.store_id and spu.parent_asin = label.parent_asin
                and label.label_name in
                <foreach collection="request.labelNames" separator="," close=")" open="(" item="item">
                    #{item}
                </foreach>
                )
            </if>
            <!-- 搜索对应没有销售标签下拉款的值 -->
            <if test="request.isNoLabel != null and request.isNoLabel == 1">
                and not exists (select 1 from amazon_order_profit_monitoring_label label
                where spu.spu = label.spu and spu.store_id = label.store_id and spu.parent_asin = label.parent_asin and label.label_type = 1
                )
            </if>
            <if test="request.suggestLabelNames != null and request.suggestLabelNames.size() > 0">
                and exists (select 1 from amazon_order_profit_monitoring_label label
                where spu.spu = label.spu and spu.store_id = label.store_id and spu.parent_asin = label.parent_asin
                and label.label_name in
                <foreach collection="request.suggestLabelNames" separator="," close=")" open="(" item="item">
                    #{item}
                </foreach>
                )
            </if>
            <if test="request.minProfitRate != null or request.maxProfitRate != null">
                and exists (select 1 from amazon_order_profit_monitoring_skc skc
                where spu.spu = skc.spu and spu.store_id = skc.store_id and spu.parent_asin = skc.parent_asin
                <if test="request.minProfitRate != null">
                    and skc.min_profit_rate_in_24_hours &gt;= #{request.minProfitRate}
                </if>
                <if test="request.maxProfitRate != null">
                    and skc.max_profit_rate_in_24_hours &lt;= #{request.maxProfitRate}
                </if>
                )
            </if>
            <if test="request.permissionStoreIds != null and request.permissionStoreIds.size() > 0">
                and spu.store_id in
                <foreach collection="request.permissionStoreIds" separator="," close=")" open="(" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="request.ids != null and request.ids.size() > 0">
                and spu.id in
                <foreach collection="request.ids" separator="," close=")" open="(" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="request.developSeasonList != null and request.developSeasonList.size() > 0">
                and spu.develop_season in
                <foreach collection="request.developSeasonList" separator="," close=")" open="(" item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
