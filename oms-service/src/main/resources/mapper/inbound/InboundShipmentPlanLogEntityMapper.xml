<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.oms.repository.sql.mapper.inbound.InboundPlanLogEntityMapper">

    <resultMap id="BaseResultMap" type="com.nsy.oms.repository.entity.inbound.InboundPlanLogEntity">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="plan_id" column="plan_id" jdbcType="INTEGER"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="plan_status" column="plan_status" jdbcType="VARCHAR"/>
            <result property="log_type" column="log_type" jdbcType="TINYINT"/>
            <result property="operate" column="operate" jdbcType="VARCHAR"/>
            <result property="create_date" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="create_by" column="create_by" jdbcType="VARCHAR"/>
            <result property="update_date" column="update_date" jdbcType="TIMESTAMP"/>
            <result property="update_by" column="update_by" jdbcType="VARCHAR"/>
            <result property="version" column="version" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,plan_id,description,
        plan_status,log_type,operate,
        create_date,create_by,update_date,
        update_by,version
    </sql>
</mapper>
