<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.oms.repository.sql.mapper.inbound.InboundShipmentItemEntityMapper">

    <resultMap id="BaseResultMap" type="com.nsy.oms.repository.entity.inbound.InboundShipmentItemEntity">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="shipment_id" column="shipment_id" jdbcType="VARCHAR"/>
            <result property="seller_sku" column="seller_sku" jdbcType="VARCHAR"/>
            <result property="erp_sku" column="erp_sku" jdbcType="VARCHAR"/>
            <result property="estimated_shipment_quantity" column="estimated_shipment_quantity" jdbcType="INTEGER"/>
            <result property="actual_shipment_quantity" column="actual_shipment_quantity" jdbcType="INTEGER"/>
            <result property="quantity_received" column="quantity_received" jdbcType="INTEGER"/>
            <result property="receive_difference_quantity" column="receive_difference_quantity" jdbcType="INTEGER"/>
            <result property="create_date" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="create_by" column="create_by" jdbcType="VARCHAR"/>
            <result property="update_date" column="update_date" jdbcType="TIMESTAMP"/>
            <result property="update_by" column="update_by" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,shipment_id,seller_sku,
        erp_sku,estimated_shipment_quantity,actual_shipment_quantity,
        quantity_received,receive_difference_quantity,create_date,
        create_by,update_date,update_by
    </sql>
</mapper>
