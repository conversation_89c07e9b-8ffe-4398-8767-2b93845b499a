<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.oms.repository.sql.mapper.inbound.InboundPlanEntityMapper">

    <resultMap id="BaseResultMap" type="com.nsy.oms.repository.entity.inbound.InboundPlanEntity">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="store_id" column="store_id" jdbcType="INTEGER"/>
            <result property="store_name" column="store_name" jdbcType="VARCHAR"/>
            <result property="platform" column="platform" jdbcType="VARCHAR"/>
            <result property="logistics_company_id" column="logistics_company_id" jdbcType="INTEGER"/>
            <result property="logistics_company_name" column="logistics_company_name" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="brand_id" column="brand_id" jdbcType="INTEGER"/>
            <result property="brand_name" column="brand_name" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="audit_remarks" column="audit_remarks" jdbcType="VARCHAR"/>
            <result property="from_warehouse" column="from_warehouse" jdbcType="VARCHAR"/>
            <result property="urls" column="urls" jdbcType="VARCHAR"/>
            <result property="space_id" column="space_id" jdbcType="INTEGER"/>
            <result property="space_name" column="space_name" jdbcType="VARCHAR"/>
            <result property="erp_tid" column="erp_tid" jdbcType="VARCHAR"/>
            <result property="create_by" column="create_by" jdbcType="VARCHAR"/>
            <result property="update_date" column="update_date" jdbcType="TIMESTAMP"/>
            <result property="update_by" column="update_by" jdbcType="VARCHAR"/>
            <result property="create_date" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="location" column="location" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,store_id,store_name,
        platform,logistics_company_id,logistics_company_name,
        remark,brand_id,brand_name,
        status,audit_remarks,from_warehouse,
        urls,space_id,space_name,
        erp_tid,create_by,update_date,
        update_by,create_date,location
    </sql>


    <select id="getInboundPlanPage" resultType="com.nsy.oms.repository.entity.inbound.InboundPlanEntity">
        select
            <if test="request.erpSku !=null and request.erpSku !=''
                      or request.sellerSku !=null and request.sellerSku !=''
                      or request.shipmentId !=null and request.shipmentId !=''
                      or request.platformStatusList != null and request.platformStatusList.size() > 0">
                distinct
            </if>
            plan.id,
            plan.store_id,
            plan.store_name,
            plan.platform,
            plan.logistics_company_id,
            plan.logistics_company_name,
            plan.remark,
            plan.brand_id,
            plan.brand_name,
            plan.status,
            plan.audit_remarks,
            plan.from_warehouse,
            plan.urls,
            plan.space_id,
            plan.space_name,
            plan.erp_tid,
            plan.box_count,
            plan.create_by,
            plan.update_date,
            plan.version,
            plan.update_by,
            plan.create_date,
            plan.location
        from nsy_oms.inbound_plan plan
                <if test="request.erpSku!=null and request.erpSku!='' or request.sellerSku!=null and request.sellerSku!=''">
                    left join inbound_plan_item plan_item on plan.id = plan_item.plan_id
                </if>
                <if test="request.shipmentId!=null and request.shipmentId!='' or request.platformStatusList != null and request.platformStatusList.size() > 0">
                    left join inbound_shipment shipment on plan.id = shipment.plan_id
                </if>
        <where>
            <if test="permissionStoreIds != null and permissionStoreIds.size() > 0">
                and plan.store_id in
                <foreach collection="permissionStoreIds" separator="," index="index" item="permissionStoreId" open="(" close=")">
                    #{permissionStoreId}
                </foreach>
            </if>
            <if test="request.storeIds != null and request.storeIds.size() > 0">
                and plan.store_id in
                <foreach collection="request.storeIds" separator="," index="index" item="storeId" open="(" close=")">
                    #{storeId}
                </foreach>
            </if>
            <if test="request.planIds != null and request.planIds.size() > 0">
                and plan.id in
                <foreach collection="request.planIds" separator="," index="index" item="planId" open="(" close=")">
                    #{planId}
                </foreach>
            </if>
            <if test="request.erpTid !=null and request.erpTid!=''">
                and instr(plan.erp_tid,#{request.erpTid})
            </if>
            <if test="request.statusList != null and request.statusList.size() > 0">
                and plan.status in
                <foreach collection="request.statusList" separator="," index="index" item="status" open="(" close=")">
                    #{status}
                </foreach>
            </if>
            <if test="request.brandIds != null and request.brandIds.size() > 0">
                and plan.brand_id in
                <foreach collection="request.brandIds" separator="," index="index" item="brandId" open="(" close=")">
                    #{brandId}
                </foreach>
            </if>
            <if test="request.logisticsCompanyIds != null and request.logisticsCompanyIds.size() > 0">
                and plan.logistics_company_id in
                <foreach collection="request.logisticsCompanyIds" separator="," index="index" item="logisticsCompanyId" open="(" close=")">
                    #{logisticsCompanyId}
                </foreach>
            </if>
            <if test="request.createBy !=null and request.createBy!=''">
                and instr(plan.create_by,#{request.createBy})
            </if>
            <if test="request.erpSku !=null and request.erpSku!=''">
                and instr(plan_item.erp_sku,#{request.erpSku})
            </if>
            <if test="request.sellerSku !=null and request.sellerSku!=''">
                and instr(plan_item.seller_sku,#{request.sellerSku})
            </if>
            <if test="request.shipmentId !=null and request.shipmentId!=''">
                and instr(shipment.shipment_id,#{request.shipmentId})
            </if>
            <if test="request.platformStatusList != null and request.platformStatusList.size() > 0">
                and shipment.platform_status in
                <foreach collection="request.platformStatusList" separator="," index="index" item="platformStatus" open="(" close=")">
                    #{platformStatus}
                </foreach>
            </if>
            <if test="request.createDateStart != null and request.createDateEnd != null">
                and plan.create_date between #{request.createDateStart } and #{request.createDateEnd}
            </if>
            <if test="request.updateDateStart != null and request.updateDateEnd != null">
                and plan.update_date between #{request.updateDateStart} and #{request.updateDateEnd}
            </if>
        </where>
        order by plan.id desc
    </select>


</mapper>
