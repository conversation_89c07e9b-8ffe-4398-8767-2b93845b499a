<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.oms.repository.sql.mapper.bd.BdOrderRuleMapper">

    <resultMap id="BaseResultMap" type="com.nsy.oms.repository.entity.bd.BdOrderRuleEntity">
            <id property="bdOrderRuleId" column="bd_order_rule_id" jdbcType="INTEGER"/>
            <result property="ruleName" column="rule_name" jdbcType="VARCHAR"/>
            <result property="ruleType" column="rule_type" jdbcType="INTEGER"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="storePlatform" column="store_platform" jdbcType="VARCHAR"/>
            <result property="storeId" column="store_id" jdbcType="INTEGER"/>
            <result property="storeName" column="store_name" jdbcType="VARCHAR"/>
            <result property="departmentName" column="department_name" jdbcType="VARCHAR"/>
            <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="version" column="version" jdbcType="BIGINT"/>
            <result property="location" column="location" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        bd_order_rule_id,rule_name,rule_type,
        status,store_platform,store_id,
        store_name,department_name,create_date,
        create_by,update_date,update_by,
        version,location
    </sql>
</mapper>
