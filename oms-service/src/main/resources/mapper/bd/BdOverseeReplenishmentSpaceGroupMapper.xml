<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.oms.repository.sql.mapper.bd.BdOverseeReplenishmentSpaceGroupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.nsy.oms.repository.entity.bd.BdOverseeReplenishmentSpaceGroupEntity">
        <id column="replenishment_space_group_id" property="replenishmentSpaceGroupId" />
        <result column="space_id" property="spaceId" />
        <result column="space_name" property="spaceName" />
        <result column="replenishment_group_id" property="replenishmentGroupId" />
        <result column="create_date" property="createDate" />
        <result column="create_by" property="createBy" />
        <result column="update_date" property="updateDate" />
        <result column="update_by" property="updateBy" />
        <result column="version" property="version" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        replenishment_space_group_id, space_id, space_name, replenishment_group_id,
        create_date, create_by, update_date, update_by, version
    </sql>

</mapper> 