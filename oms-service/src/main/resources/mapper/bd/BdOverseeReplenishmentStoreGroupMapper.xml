<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.oms.repository.sql.mapper.bd.BdOverseeReplenishmentStoreGroupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.nsy.oms.repository.entity.bd.BdOverseeReplenishmentStoreGroupEntity">
        <id column="replenishment_store_group_id" property="replenishmentStoreGroupId" />
        <result column="store_id" property="storeId" />
        <result column="store_name" property="storeName" />
        <result column="replenishment_group_id" property="replenishmentGroupId" />
        <result column="create_date" property="createDate" />
        <result column="create_by" property="createBy" />
        <result column="update_date" property="updateDate" />
        <result column="update_by" property="updateBy" />
        <result column="version" property="version" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        replenishment_store_group_id, store_id, store_name, replenishment_group_id,
        create_date, create_by, update_date, update_by, version
    </sql>

</mapper> 