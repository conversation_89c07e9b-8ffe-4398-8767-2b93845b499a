<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.oms.repository.sql.mapper.order.SaleOrderExtendMapper">

    <resultMap id="BaseResultMap" type="com.nsy.oms.repository.entity.order.SaleOrderExtendEntity">
            <id property="orderExtendId" column="order_extend_id" jdbcType="INTEGER"/>
            <result property="orderId" column="order_id" jdbcType="INTEGER"/>
            <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
            <result property="orderCreateDateTimeZone" column="order_create_date_time_zone" jdbcType="TIMESTAMP"/>
            <result property="earliestShipDateTimeZone" column="earliest_ship_date_time_zone" jdbcType="TIMESTAMP"/>
            <result property="latestShipDateTimeZone" column="latest_ship_date_time_zone" jdbcType="TIMESTAMP"/>
            <result property="earliestDeliveryDateTimeZone" column="earliest_delivery_date_time_zone" jdbcType="TIMESTAMP"/>
            <result property="latestDeliveryDateTimeZone" column="latest_delivery_date_time_zone" jdbcType="TIMESTAMP"/>
            <result property="orderPaymentDateTimeZone" column="order_payment_date_time_zone" jdbcType="TIMESTAMP"/>
            <result property="orderCancelDateTimeZone" column="order_cancel_date_time_zone" jdbcType="TIMESTAMP"/>
            <result property="location" column="location" jdbcType="VARCHAR"/>
            <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="version" column="version" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        order_extend_id,order_id,order_no,
        order_create_date_time_zone,earliest_ship_date_time_zone,latest_ship_date_time_zone,
        earliest_delivery_date_time_zone,latest_delivery_date_time_zone,order_payment_date_time_zone,
        order_cancel_date_time_zone,location,create_date,
        create_by,update_date,update_by,
        version
    </sql>
</mapper>
