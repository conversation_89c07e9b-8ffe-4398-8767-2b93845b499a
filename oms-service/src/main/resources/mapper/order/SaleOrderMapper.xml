<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.oms.repository.sql.mapper.order.SaleOrderMapper">

    <resultMap id="BaseResultMap" type="com.nsy.oms.repository.entity.order.SaleOrderEntity">
            <id property="orderId" column="order_id" jdbcType="INTEGER"/>
            <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
            <result property="platformOrderNo" column="platform_order_no" jdbcType="VARCHAR"/>
            <result property="orderStatus" column="order_status" jdbcType="TINYINT"/>
            <result property="paymentAmount" column="payment_amount" jdbcType="DECIMAL"/>
            <result property="productDiscountAmount" column="product_discount_amount" jdbcType="DECIMAL"/>
            <result property="productTotalAmount" column="product_total_amount" jdbcType="DECIMAL"/>
            <result property="freightFee" column="freight_fee" jdbcType="DECIMAL"/>
            <result property="commissionFee" column="commission_fee" jdbcType="DECIMAL"/>
            <result property="processFee" column="process_fee" jdbcType="DECIMAL"/>
            <result property="currency" column="currency" jdbcType="VARCHAR"/>
            <result property="shippingType" column="shipping_type" jdbcType="VARCHAR"/>
            <result property="platformId" column="platform_id" jdbcType="INTEGER"/>
            <result property="platformName" column="platform_name" jdbcType="VARCHAR"/>
            <result property="storeId" column="store_id" jdbcType="INTEGER"/>
            <result property="storeName" column="store_name" jdbcType="VARCHAR"/>
            <result property="marketCode" column="market_code" jdbcType="VARCHAR"/>
            <result property="marketName" column="market_name" jdbcType="VARCHAR"/>
            <result property="buyerRemark" column="buyer_remark" jdbcType="VARCHAR"/>
            <result property="orderCreateDate" column="order_create_date" jdbcType="TIMESTAMP"/>
            <result property="earliestShipDate" column="earliest_ship_date" jdbcType="TIMESTAMP"/>
            <result property="latestShipDate" column="latest_ship_date" jdbcType="TIMESTAMP"/>
            <result property="earliestDeliveryDate" column="earliest_delivery_date" jdbcType="TIMESTAMP"/>
            <result property="latestDeliveryDate" column="latest_delivery_date" jdbcType="TIMESTAMP"/>
            <result property="orderPaymentDate" column="order_payment_date" jdbcType="TIMESTAMP"/>
            <result property="orderCancelDate" column="order_cancel_date" jdbcType="TIMESTAMP"/>
            <result property="location" column="location" jdbcType="VARCHAR"/>
            <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="version" column="version" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        order_id,order_no,platform_order_no,
        order_status,payment_amount,product_discount_amount,
        product_total_amount,freight_fee,commission_fee,
        process_fee,currency,shipping_type,
        platform_id,platform_name,store_id,
        store_name,market_code,market_name,
        buyer_remark,order_create_date,earliest_ship_date,
        latest_ship_date,earliest_delivery_date,latest_delivery_date,
        order_payment_date,order_cancel_date,location,
        create_date,create_by,update_date,
        update_by,version
    </sql>

    <resultMap id="crmOrderPageMap" type="com.nsy.oms.business.domain.response.external.CrmOrderPageListResponse">
        <id property="orderId" column="order_id" jdbcType="INTEGER"/>
        <result property="storeId" column="store_id" jdbcType="INTEGER"/>
        <result property="marketCode" column="market_code" jdbcType="VARCHAR"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="orderStatus" column="order_status" jdbcType="INTEGER"/>
        <result property="paymentAmount" column="payment_amount" jdbcType="DECIMAL"/>
        <result property="shippingType" column="shipping_type" jdbcType="VARCHAR"/>
        <result property="currency" column="currency" jdbcType="VARCHAR"/>
        <result property="orderCreateDate" column="order_create_date" jdbcType="TIMESTAMP"/>
        <result property="orderDeliverDate" column="latest_delivery_date" jdbcType="TIMESTAMP"/>
        <collection property="itemList" ofType="com.nsy.oms.business.domain.response.external.CrmOrderPageItemListResponse" javaType="java.util.List">
            <id property="orderItemId" column="order_item_id" jdbcType="INTEGER"/>
            <result property="sku" column="sku" jdbcType="VARCHAR"/>
            <result property="specId" column="spec_id" jdbcType="INTEGER"/>
            <result property="productId" column="product_id" jdbcType="INTEGER"/>
            <result property="qty" column="qty" jdbcType="INTEGER"/>
            <result property="sellerSku" column="seller_sku" jdbcType="VARCHAR"/>
            <result property="asin" column="asin" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <select id="crmOrderPage" resultMap="crmOrderPageMap">
        SELECT
            o.store_id,
            o.market_code,
            o.order_id,
            o.order_no,
            o.order_status,
            o.payment_amount,
            o.shipping_type,
            o.currency,
            o.order_create_date,
            o.latest_delivery_date,
            item.order_item_id,
            item.sku,
            item.spec_id,
            item.product_id,
            item.qty,
            item.seller_sku,
            item.asin
        FROM `sale_order` o
            INNER JOIN  `sale_order_item` item
            ON o.order_id = item.`order_id`
        inner join sale_order_receiver receiver
            on o.order_id = receiver.order_id
        <where>
            receiver.buyer_email = #{request.buyerEmail}
            <if test="request.orderCreateDateStart != null and request.orderCreateDateEnd != null">
                and o.order_create_date BETWEEN #{request.orderCreateDateStart} AND #{request.orderCreateDateEnd}
            </if>
            <if test="request.storeIdList != null and request.storeIdList.size > 0">
                and o.store_id in
                <foreach collection="request.storeIdList" separator="," open="(" close=")" item="storeId">
                    #{storeId}
                </foreach>
            </if>
        </where>
        order by o.order_create_date desc
    </select>


    <resultMap id="crmOrderInoMap" type="com.nsy.oms.business.domain.response.external.CrmOrderInoResponse">
        <id property="orderId" column="order_id" jdbcType="INTEGER"/>
        <result property="storeId" column="store_id" jdbcType="INTEGER"/>
        <result property="marketCode" column="market_code" jdbcType="VARCHAR"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="orderStatus" column="order_status" jdbcType="INTEGER"/>
        <result property="paymentAmount" column="payment_amount" jdbcType="DECIMAL"/>
        <result property="shippingType" column="shipping_type" jdbcType="VARCHAR"/>
        <result property="currency" column="currency" jdbcType="VARCHAR"/>
        <result property="orderCreateDate" column="order_create_date" jdbcType="TIMESTAMP"/>
        <result property="orderDeliverDate" column="latest_delivery_date" jdbcType="TIMESTAMP"/>
        <collection property="itemList" ofType="com.nsy.oms.business.domain.response.external.CrmOrderItemResponse" javaType="java.util.List">
            <id property="orderItemId" column="order_item_id" jdbcType="INTEGER"/>
            <result property="sku" column="sku" jdbcType="VARCHAR"/>
            <result property="specId" column="spec_id" jdbcType="INTEGER"/>
            <result property="productId" column="product_id" jdbcType="INTEGER"/>
            <result property="qty" column="qty" jdbcType="INTEGER"/>
            <result property="sellerSku" column="seller_sku" jdbcType="VARCHAR"/>
            <result property="asin" column="asin" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <select id="getCrmOrderInfo" resultMap="crmOrderInoMap">
        SELECT
        o.store_id,
        o.market_code,
        o.order_id,
        o.order_no,
        o.order_status,
        o.payment_amount,
        o.shipping_type,
        o.currency,
        o.order_create_date,
        o.latest_delivery_date,
        item.order_item_id,
        item.sku,
        item.spec_id,
        item.product_id,
        item.qty,
        item.seller_sku,
        item.asin
        FROM `sale_order` o
        INNER JOIN  `sale_order_item` item
        ON o.order_id = item.`order_id`
        where o.order_no = #{orderNo}
        and o.store_id in
        <foreach collection="storeIdList" separator="," open="(" close=")" item="storeId">
            #{storeId}
        </foreach>
    </select>


</mapper>
