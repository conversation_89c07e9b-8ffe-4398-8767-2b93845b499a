<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.oms.repository.sql.mapper.order.SaleOrderReceiverMapper">

    <resultMap id="BaseResultMap" type="com.nsy.oms.repository.entity.order.SaleOrderReceiverEntity">
            <id property="receiverId" column="receiver_id" jdbcType="INTEGER"/>
            <result property="receiverName" column="receiver_name" jdbcType="VARCHAR"/>
            <result property="orderId" column="order_id" jdbcType="INTEGER"/>
            <result property="country" column="country" jdbcType="VARCHAR"/>
            <result property="province" column="province" jdbcType="VARCHAR"/>
            <result property="city" column="city" jdbcType="VARCHAR"/>
            <result property="area" column="area" jdbcType="VARCHAR"/>
            <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
            <result property="phone" column="phone" jdbcType="VARCHAR"/>
            <result property="address" column="address" jdbcType="VARCHAR"/>
            <result property="houseNumber" column="house_number" jdbcType="VARCHAR"/>
            <result property="postCode" column="post_code" jdbcType="VARCHAR"/>
            <result property="buyerEmail" column="buyer_email" jdbcType="VARCHAR"/>
            <result property="buyerNick" column="buyer_nick" jdbcType="VARCHAR"/>
            <result property="location" column="location" jdbcType="VARCHAR"/>
            <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="version" column="version" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        receiver_id,receiver_name,order_id,
        country,province,city,
        area,mobile,phone,
        address,house_number,post_code,
        buyer_email,buyer_nick,location,
        create_date,create_by,update_date,
        update_by,version
    </sql>
</mapper>
