<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.oms.repository.sql.mapper.order.SaleOrderCustomInfoMapper">

    <resultMap id="BaseResultMap" type="com.nsy.oms.repository.entity.order.SaleOrderCustomInfoEntity">
        <id property="orderCustomId" column="order_custom_id" jdbcType="INTEGER"/>
        <result property="orderId" column="order_id" jdbcType="INTEGER"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="customField" column="custom_field" jdbcType="VARCHAR"/>
        <result property="customValue" column="custom_value" jdbcType="VARCHAR"/>
        <result property="location" column="location" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="version" column="version" jdbcType="BIGINT"/>
    </resultMap>

</mapper> 