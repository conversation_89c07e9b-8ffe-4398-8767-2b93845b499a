<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.oms.repository.sql.mapper.order.SaleOrderItemMapper">

    <resultMap id="BaseResultMap" type="com.nsy.oms.repository.entity.order.SaleOrderItemEntity">
            <id property="orderItemId" column="order_item_id" jdbcType="INTEGER"/>
            <result property="orderId" column="order_id" jdbcType="INTEGER"/>
            <result property="platformItemId" column="platform_item_id" jdbcType="VARCHAR"/>
            <result property="itemStatus" column="item_status" jdbcType="TINYINT"/>
            <result property="sku" column="sku" jdbcType="VARCHAR"/>
            <result property="specId" column="spec_id" jdbcType="INTEGER"/>
            <result property="productId" column="product_id" jdbcType="INTEGER"/>
            <result property="qty" column="qty" jdbcType="INTEGER"/>
            <result property="refundId" column="refund_id" jdbcType="VARCHAR"/>
            <result property="refundStatus" column="refund_status" jdbcType="VARCHAR"/>
            <result property="unitPrice" column="unit_price" jdbcType="DECIMAL"/>
            <result property="totalAmount" column="total_amount" jdbcType="DECIMAL"/>
            <result property="paymentAmount" column="payment_amount" jdbcType="DECIMAL"/>
            <result property="unitDiscount" column="unit_discount" jdbcType="DECIMAL"/>
            <result property="sellerSku" column="seller_sku" jdbcType="VARCHAR"/>
            <result property="iossNumber" column="ioss_number" jdbcType="VARCHAR"/>
            <result property="isTransparency" column="is_transparency" jdbcType="TINYINT"/>
            <result property="asin" column="asin" jdbcType="VARCHAR"/>
            <result property="location" column="location" jdbcType="VARCHAR"/>
            <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="version" column="version" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        order_item_id,order_id,platform_item_id,
        item_status,sku,spec_id,
        product_id,qty,refund_id,
        refund_status,unit_price,total_amount,
        payment_amount,unit_discount,seller_sku,
        ioss_number,is_transparency, asin,location,create_date,
        create_by,update_date,update_by,
        version
    </sql>
</mapper>
