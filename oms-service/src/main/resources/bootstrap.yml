spring:
  application:
    name: ${appName:api-oms}
  cloud:
    bootstrap:
      enabled: true
    nacos:
      discovery:
        server-addr: ${nacosURL:************:8848}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        file-extension: yml
        shared-configs[0]:
          data-id: application-common.${spring.cloud.nacos.config.file-extension} # 配置文件名-Data Id
          refresh: false   # 是否动态刷新，默认为false
        shared-configs[1]:
          data-id: ${spring.application.name}-custom.${spring.cloud.nacos.config.file-extension}
          refresh: true
  profiles:
    active: dev
account:
  encrypt:
    key: 10billionnsy6666

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

nsy:
  mysql:
    host: ************
    port: 3306
    database: nsy_oms
    username: nsy_mysql
    password: t6DT6hKLSTbaI.
  redis:
    host: ************
    port: 6379
    password: jPaZbnLgK3zFYZIV
  kafka:
    broker: ************:9092
    bootstrap-server: ************:9092
  zookeeper:
    url: ************:2181
  web:
    syn:
      evaless:
        url: http://51028.cn01.dearlovertech.com
        token: core_51028_08f3be8aa9cca06f360a8c6364441640
      dearlover:
        url: http://43786.cn01.dearlovertech.com
        token: core_43786_87ef016805c88ca09eabfb894a788d92
  service:
    url:
      transfer: http://127.0.0.1:8899/transfer-service/   #鉴权中心URL
      auth-ng: http://*************:8907/
      erp: http://************:8080
      image-tool: http://************:32650/
      erp-intranet: http://************:8080/
      product: http://************:8081/
      selection: http://************:8082/
      amazon: http://************:8083/
      search: http://************:8084/
      bi: http://************:8085/
      supplier: http://************:8086/
      thirdparty: http://************:8087/
      user: http://************:8088/
      notify: http://************:8089/
      tms: http://************:8880/
      wms: http://************:8881/
      scm: http://************:8882/
      oms: http://************:8891/
      oms-publish: http://************:8883/
      pms: http://************:8884/
      settlement: http://************:8890/
      activiti: http://************:8891/
      report: http://************:8892/
      fms: http://************:8893/
      business: http://************:8894/
      image: http://************:8895/
      ads: http://************:8896/
      crawl: http://************:8897/
      shopxsybi: http://************:8898/
      kms: http://************:8901/
      etl: http://************:8902/
      crm: http://************:8903/
      mes: http://************:8904/
      oauth2-server: http://************:7777/
