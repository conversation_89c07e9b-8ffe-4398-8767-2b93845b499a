{"job": [{"name": "CheckStoreAuthIsExpireJob-WEIYUE", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.store.auth.CheckStoreAuthIsExpireJob", "cron": "0 0 8 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"WEIYUE\"}"}, {"name": "TemuPullAndCalGoodsSalesJob-WEIYUE", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.temu.TemuPullAndCalGoodsSalesJob", "cron": "0 15 3-23 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"WEIYUE\"}"}, {"name": "TemuGoodsSalesDataWarnJob-WEIYUE", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.temu.TemuGoodsSalesDataWarnJob", "cron": "0 0 8 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"WEIYUE\"}"}, {"name": "ProductBrandFetchJob-WEIYUE", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.temu.ProductBrandFetchJob", "cron": "0 0 21 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"WEIYUE\"}"}, {"name": "AutoGetErpScDataSellerListsJob-WEIYUE", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.lingxing.AutoGetErpScDataSellerListsJob", "cron": "0 19 17 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"WEIYUE\"}"}, {"name": "PlatformOrderItemMatchJob-WEIYUE", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.platform.PlatformOrderItemMatchJob", "cron": "0 0/30 1 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"WEIYUE\"}"}, {"name": "OrderItemMatchJob-WEIYUE", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.order.OrderItemMatchJob", "cron": "0 0 1 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"WEIYUE\"}"}, {"name": "OrderMissedJob-WEIYUE", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderMissedJob", "cron": "0 0/1 * * * ?", "sharding-total-count": 5, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4", "job-parameters": "{\"location\" : \"WEIYUE\", \"fetchCount\" : 1000}"}, {"name": "OrderGrabJob-AMAZON-WEIYUE", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderGrabJob", "cron": "0 0/5 * * * ?", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"platform\" : \"AMAZON\", \"location\" : \"WEIYUE\", \"fetchCount\" : 200}"}, {"name": "OrderItemGrabJob-WEIYUE-1", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderItemGrabJob", "cron": "0 0/1 * * * ?", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"platform\" : \"AMAZON\", \"location\" : \"WEIYUE\", \"fetchCount\" : 2000}"}, {"name": "OrderItemGrabJob-WEIYUE-2", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderItemGrabJob", "cron": "7 0/1 * * * ?", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"platform\" : \"AMAZON\", \"location\" : \"WEIYUE\", \"fetchCount\" : 2000}"}, {"name": "OrderItemGrabJob-WEIYUE-3", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderItemGrabJob", "cron": "14 0/1 * * * ?", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"platform\" : \"AMAZON\", \"location\" : \"WEIYUE\", \"fetchCount\" : 2000}"}, {"name": "OrderItemGrabJob-Big-Store-ME-US-WEIYUE-1", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderItemGrabJob", "cron": "0 0/1 * * * ?", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"platform\" : \"AMAZON\", \"storeId\" : 9429, \"location\" : \"WEIYUE\", \"fetchCount\" : 1000}"}, {"name": "OrderItemGrabJob-Big-Store-ME-US-WEIYUE-2", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderItemGrabJob", "cron": "8 0/1 * * * ?", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"platform\" : \"AMAZON\", \"storeId\" : 9429, \"location\" : \"WEIYUE\", \"fetchCount\" : 1000}"}, {"name": "OrderItemGrabJob-Big-Store-ME-US-WEIYUE-3", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderItemGrabJob", "cron": "16 0/1 * * * ?", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"platform\" : \"AMAZON\", \"storeId\" : 9429, \"location\" : \"WEIYUE\", \"fetchCount\" : 1000}"}, {"name": "OrderItemGrabJob-Big-Store-ZE-US-WEIYUE-1", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderItemGrabJob", "cron": "3 0/2 * * * ?", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"platform\" : \"AMAZON\", \"storeId\" : 9422, \"location\" : \"WEIYUE\", \"fetchCount\" : 1000}"}, {"name": "OrderItemGrabJob-Big-Store-ZE-US-WEIYUE-2", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderItemGrabJob", "cron": "9 0/2 * * * ?", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"platform\" : \"AMAZON\", \"storeId\" : 9422, \"location\" : \"WEIYUE\", \"fetchCount\" : 1000}"}, {"name": "OrderItemGrabJob-Big-Store-ZE-US-WEIYUE-3", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderItemGrabJob", "cron": "18 0/2 * * * ?", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"platform\" : \"AMAZON\", \"storeId\" : 9422, \"location\" : \"WEIYUE\", \"fetchCount\" : 1000}"}, {"name": "OrderItemGrabQueueRetryJob-WEIYUE", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.order.OrderItemGrabQueueRetryJob", "cron": "0 10 0/1 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"WEIYUE\", \"fetchCount\" : 2000}"}, {"name": "PushOrderToErpJob-WEIYUE", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.PushOrderToErpJob", "cron": "0 0/1 * * * ?", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"location\" : \"WEIYUE\", \"fetchCount\" : 1000}"}, {"name": "OrderGrabExceptionMonitorJob-store-WEIYUE", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.order.OrderGrabExceptionMonitorJob", "cron": "0 0 09,16 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"type\" : \"STORE_EXCEPTION\", \"location\" : \"WEIYUE\"}"}, {"name": "OrderGrabExceptionMonitorJob-orderItem-WEIYUE", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.order.OrderGrabExceptionMonitorJob", "cron": "0 0 09,16 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"type\" : \"ITEM_GRAB_EXCEPTION\", \"location\" : \"WEIYUE\"}"}, {"name": "FbaOrderItemMonitorJob-WEIYUE", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.order.FbaOrderItemMonitorJob", "cron": "3 15 3 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"WEIYUE\", \"fetchCount\" : 1000}"}, {"name": "OrderFetcherJob-TikTokLocalShipping-WEIYUE", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderFetcherJob", "cron": "39 1/10 * * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"platform\" : \"TikTokLocalShipping\", \"location\" : \"WEIYUE\"}"}, {"name": "OrderFetcherJob-TikTok-WEIYUE", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderFetcherJob", "cron": "39 2/10 * * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"platform\" : \"TikTok\", \"location\" : \"WEIYUE\"}"}, {"name": "OrderQueueFetcherJob-WEIYUE", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderQueueFetcherJob", "cron": "20 7/3 * * * ? 2099", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"location\" : \"WEIYUE\", \"fetchCount\" : 500}"}, {"name": "OrderItemQueueFetcherJob-WEIYUE", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderItemQueueFetcherJob", "cron": "40 7/3 * * * ? 2099", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"location\" : \"WEIYUE\", \"fetchCount\" : 500}"}, {"name": "OrderAddressFetcherJob-WEIYUE", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderAddressFetcherJob", "cron": "55 7/3 * * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"WEIYUE\", \"fetchCount\" : 500}"}, {"name": "SyncInboundShipmentsJob-WEIYUE", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.inbound.SyncInboundShipmentsJob", "cron": "0 0 0/6 * * ?", "sharding-total-count": 8, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4,5=5,6=6,7=7", "job-parameters": "{\"location\" : \"WEIYUE\"}"}, {"name": "PlatformStockSyncJob-WEIYUE", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.platform.PlatformStockSyncJob", "cron": "0 0 0/6 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"WEIYUE\"}"}, {"name": "ReportFbtInventoryRecordsJob-WEIYUE", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.report.ReportFbtInventoryRecordsJob", "cron": "0 13 0 * * ?", "sharding-total-count": 5, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4", "job-parameters": "{\"location\" : \"WEIYUE\", \"fetchCount\" : 20}"}, {"name": "ReportTkStatementRecordsJob-WEIYUE", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.report.ReportTkStatementRecordsJob", "cron": "0 16 0 * * ?", "sharding-total-count": 5, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4", "job-parameters": "{\"location\" : \"WEIYUE\", \"fetchCount\" : 20}"}, {"name": "OrderFetcherJob-JUSTFAB-WEIYUE", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderFetcherJob", "cron": "10 8/10 * * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"platform\" : \"JUSTFAB\", \"location\" : \"WEIYUE\"}"}, {"name": "ReportTkStatementTransactionRecordsJob-WEIYUE", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.report.ReportTkStatementTransactionRecordsJob", "cron": "0 7 5 * * ?", "sharding-total-count": 5, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4", "job-parameters": "{\"location\" : \"WEIYUE\", \"fetchCount\" : 50}"}, {"name": "ReportTkStatementOrderTransactionRecordsJob-WEIYUE", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.report.ReportTkStatementOrderTransactionRecordsJob", "cron": "0 10 0/1 * * ?", "sharding-total-count": 5, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4", "job-parameters": "{\"location\" : \"WEIYUE\", \"fetchCount\" : 50}"}, {"name": "ReportTkReturnRefundRecordsJob-WEIYUE", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.report.ReportTkReturnRefundRecordsJob", "cron": "0 13 5 * * ?", "sharding-total-count": 5, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4", "job-parameters": "{\"location\" : \"WEIYUE\", \"fetchCount\" : 50}"}]}