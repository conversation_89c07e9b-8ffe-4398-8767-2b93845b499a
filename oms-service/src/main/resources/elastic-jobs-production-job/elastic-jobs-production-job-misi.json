{"job": [{"name": "CheckStoreAuthIsExpireJob-MISI", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.store.auth.CheckStoreAuthIsExpireJob", "cron": "0 0 8 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"MISI\"}"}, {"name": "TemuPullAndCalGoodsSalesJob-MISI", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.temu.TemuPullAndCalGoodsSalesJob", "cron": "0 15 3-23 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"MISI\"}"}, {"name": "TemuGoodsSalesDataWarnJob-MISI", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.temu.TemuGoodsSalesDataWarnJob", "cron": "0 0 8 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"MISI\"}"}, {"name": "ProductBrandFetchJob-MISI", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.temu.ProductBrandFetchJob", "cron": "0 0 21 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"MISI\"}"}, {"name": "AutoGetErpScDataSellerListsJob-MISI", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.lingxing.AutoGetErpScDataSellerListsJob", "cron": "0 19 17 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"MISI\"}"}, {"name": "PlatformOrderItemMatchJob-MISI", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.platform.PlatformOrderItemMatchJob", "cron": "0 0/10 1 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"MISI\"}"}, {"name": "OrderItemMatchJob-MISI", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.order.OrderItemMatchJob", "cron": "0 0 1 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"MISI\"}"}, {"name": "OrderMissedJob-MISI", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderMissedJob", "cron": "0 0/1 * * * ?", "sharding-total-count": 5, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4", "job-parameters": "{\"location\" : \"MISI\", \"fetchCount\" : 1000}"}, {"name": "OrderGrabJob-AMAZON-MISI", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderGrabJob", "cron": "0 0/5 * * * ?", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"platform\" : \"AMAZON\", \"location\" : \"MISI\", \"fetchCount\" : 200}"}, {"name": "OrderItemGrabJob-MISI-1", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderItemGrabJob", "cron": "0 0/1 * * * ?", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"platform\" : \"AMAZON\", \"location\" : \"MISI\", \"fetchCount\" : 2000}"}, {"name": "OrderItemGrabJob-MISI-2", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderItemGrabJob", "cron": "5 0/1 * * * ?", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"platform\" : \"AMAZON\", \"location\" : \"MISI\", \"fetchCount\" : 2000}"}, {"name": "OrderItemGrabJob-MISI-3", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderItemGrabJob", "cron": "10 0/1 * * * ?", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"platform\" : \"AMAZON\", \"location\" : \"MISI\", \"fetchCount\" : 2000}"}, {"name": "OrderItemGrabJob-Big-Store-PR-MISI-1", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderItemGrabJob", "cron": "12 0/1 * * * ?", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"platform\" : \"AMAZON\", \"storeId\" : 9556, \"location\" : \"MISI\", \"fetchCount\" : 1000}"}, {"name": "OrderItemGrabJob-Big-Store-PR-MISI-2", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderItemGrabJob", "cron": "24 0/1 * * * ?", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"platform\" : \"AMAZON\", \"storeId\" : 9556, \"location\" : \"MISI\", \"fetchCount\" : 1000}"}, {"name": "OrderItemGrabJob-Big-Store-PR-MISI-3", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderItemGrabJob", "cron": "48 0/1 * * * ?", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"platform\" : \"AMAZON\", \"storeId\" : 9556, \"location\" : \"MISI\", \"fetchCount\" : 1000}"}, {"name": "OrderItemGrabQueueRetryJob-MISI", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.order.OrderItemGrabQueueRetryJob", "cron": "0 6 0/1 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"MISI\", \"fetchCount\" : 2000}"}, {"name": "PushOrderToErpJob-MISI", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.PushOrderToErpJob", "cron": "0 0/1 * * * ?", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"location\" : \"MISI\", \"fetchCount\" : 1000}"}, {"name": "OrderGrabExceptionMonitorJob-store-MISI", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.order.OrderGrabExceptionMonitorJob", "cron": "0 0 09,16 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"type\" : \"STORE_EXCEPTION\", \"location\" : \"MISI\"}"}, {"name": "OrderGrabExceptionMonitorJob-orderItem-MISI", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.order.OrderGrabExceptionMonitorJob", "cron": "0 0 09,16 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"type\" : \"ITEM_GRAB_EXCEPTION\", \"location\" : \"MISI\"}"}, {"name": "FbaOrderItemMonitorJob-MISI", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.order.FbaOrderItemMonitorJob", "cron": "3 6 3 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"MISI\", \"fetchCount\" : 1000}"}, {"name": "OrderFetcherJob-SheinSemiHosted-MISI", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderFetcherJob", "cron": "3 6/10 * * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"platform\" : \"SheinSemiHosted\", \"location\" : \"MISI\"}"}, {"name": "OrderFetcherJob-Shopify-MISI", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderFetcherJob", "cron": "3 8/10 * * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"platform\" : \"Shopify\", \"location\" : \"MISI\"}"}, {"name": "OrderFetcherJob-TemuPop-MISI", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderFetcherJob", "cron": "3 9/10 * * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"platform\" : \"TemuPop\", \"location\" : \"MISI\"}"}, {"name": "OrderFetcherJob-TikTok-MISI", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderFetcherJob", "cron": "33 2/10 * * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"platform\" : \"TikTok\", \"location\" : \"MISI\"}"}, {"name": "OrderFetcherJob-Walmart-MISI", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderFetcherJob", "cron": "33 4/10 * * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"platform\" : \"Walmart\", \"location\" : \"MISI\"}"}, {"name": "OrderQueueFetcherJob-MISI", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderQueueFetcherJob", "cron": "20 5/3 * * * ?", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"location\" : \"MISI\", \"fetchCount\" : 500}"}, {"name": "OrderItemQueueFetcherJob-MISI", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderItemQueueFetcherJob", "cron": "40 5/3 * * * ? 2099", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"location\" : \"GUANGZHOU\", \"fetchCount\" : 500}"}, {"name": "OrderAddressFetcherJob-MISI", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderAddressFetcherJob", "cron": "55 5/3 * * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"GUANGZHOU\", \"fetchCount\" : 500}"}, {"name": "SyncInboundShipmentsJob-MISI", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.inbound.SyncInboundShipmentsJob", "cron": "0 0 0/6 * * ?", "sharding-total-count": 8, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4,5=5,6=6,7=7", "job-parameters": "{\"location\" : \"MISI\"}"}, {"name": "PlatformStockSyncJob-MISI", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.platform.PlatformStockSyncJob", "cron": "0 0 0/6 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"MISI\"}"}, {"name": "ReportFbtInventoryRecordsJob-MISI", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.report.ReportFbtInventoryRecordsJob", "cron": "0 13 0 * * ?", "sharding-total-count": 5, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4", "job-parameters": "{\"location\" : \"MISI\", \"fetchCount\" : 20}"}, {"name": "ReportTkStatementRecordsJob-MISI", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.report.ReportTkStatementRecordsJob", "cron": "0 16 0 * * ?", "sharding-total-count": 5, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4", "job-parameters": "{\"location\" : \"MISI\", \"fetchCount\" : 20}"}, {"name": "OrderFetcherJob-JUSTFAB-MISI", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderFetcherJob", "cron": "10 8/10 * * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"platform\" : \"JUSTFAB\", \"location\" : \"MISI\"}"}, {"name": "ReportTkStatementTransactionRecordsJob-MISI", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.report.ReportTkStatementTransactionRecordsJob", "cron": "0 4 5 * * ?", "sharding-total-count": 5, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4", "job-parameters": "{\"location\" : \"MISI\", \"fetchCount\" : 50}"}, {"name": "ReportTkStatementOrderTransactionRecordsJob-MISI", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.report.ReportTkStatementOrderTransactionRecordsJob", "cron": "0 10 0/1 * * ?", "sharding-total-count": 5, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4", "job-parameters": "{\"location\" : \"MISI\", \"fetchCount\" : 50}"}, {"name": "ReportTkReturnRefundRecordsJob-MISI", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.report.ReportTkReturnRefundRecordsJob", "cron": "0 16 5 * * ?", "sharding-total-count": 5, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4", "job-parameters": "{\"location\" : \"MISI\", \"fetchCount\" : 50}"}]}