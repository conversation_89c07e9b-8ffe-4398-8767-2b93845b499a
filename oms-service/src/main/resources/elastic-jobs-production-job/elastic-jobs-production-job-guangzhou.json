{"job": [{"name": "CheckStoreAuthIsExpireJob-GUANGZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.store.auth.CheckStoreAuthIsExpireJob", "cron": "0 0 8 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"GUANGZHOU\"}"}, {"name": "TemuPullAndCalGoodsSalesJob-GUANGZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.temu.TemuPullAndCalGoodsSalesJob", "cron": "0 15 3-23 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"GUANGZHOU\"}"}, {"name": "TemuGoodsSalesDataWarnJob-GUANGZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.temu.TemuGoodsSalesDataWarnJob", "cron": "0 0 8 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"GUANGZHOU\"}"}, {"name": "ProductBrandFetchJob-GUANGZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.temu.ProductBrandFetchJob", "cron": "0 0 21 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"GUANGZHOU\"}"}, {"name": "AutoGetErpScDataSellerListsJob-GUANGZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.lingxing.AutoGetErpScDataSellerListsJob", "cron": "0 19 17 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"GUANGZHOU\"}"}, {"name": "PlatformOrderItemMatchJob-GUANGZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.platform.PlatformOrderItemMatchJob", "cron": "0 0/5 1 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"GUANGZHOU\"}"}, {"name": "OrderItemMatchJob-GUANGZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.order.OrderItemMatchJob", "cron": "0 0 1 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"GUANGZHOU\"}"}, {"name": "OrderMissedJob-GUANGZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderMissedJob", "cron": "0 0/1 * * * ?", "sharding-total-count": 5, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4", "job-parameters": "{\"location\" : \"GUANGZHOU\", \"fetchCount\" : 1000}"}, {"name": "OrderGrabJob-AMAZON-GUANGZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderGrabJob", "cron": "0 0/5 * * * ?", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"platform\" : \"AMAZON\", \"location\" : \"GUANGZHOU\", \"fetchCount\" : 200}"}, {"name": "OrderItemGrabJob-GUANGZHOU-1", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderItemGrabJob", "cron": "0 0/1 * * * ?", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"platform\" : \"AMAZON\", \"location\" : \"GUANGZHOU\", \"fetchCount\" : 2000}"}, {"name": "OrderItemGrabJob-GUANGZHOU-2", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderItemGrabJob", "cron": "10 0/1 * * * ?", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"platform\" : \"AMAZON\", \"location\" : \"GUANGZHOU\", \"fetchCount\" : 2000}"}, {"name": "OrderItemGrabJob-GUANGZHOU-3", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderItemGrabJob", "cron": "20 0/1 * * * ?", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"platform\" : \"AMAZON\", \"location\" : \"GUANGZHOU\", \"fetchCount\" : 2000}"}, {"name": "OrderItemGrabQueueRetryJob-GUANGZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.order.OrderItemGrabQueueRetryJob", "cron": "0 4 0/1 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"GUANGZHOU\", \"fetchCount\" : 2000}"}, {"name": "PushOrderToErpJob-GUANGZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.PushOrderToErpJob", "cron": "0 0/1 * * * ?", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"location\" : \"GUANGZHOU\", \"fetchCount\" : 1000}"}, {"name": "OrderGrabExceptionMonitorJob-store-GUANGZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.order.OrderGrabExceptionMonitorJob", "cron": "0 0 09,16 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"type\" : \"STORE_EXCEPTION\", \"location\" : \"GUANGZHOU\"}"}, {"name": "OrderGrabExceptionMonitorJob-orderItem-GUANGZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.order.OrderGrabExceptionMonitorJob", "cron": "0 0 09,16 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"type\" : \"ITEM_GRAB_EXCEPTION\", \"location\" : \"GUANGZHOU\"}"}, {"name": "FbaOrderItemMonitorJob-GUANGZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.order.FbaOrderItemMonitorJob", "cron": "1 3 3 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"GUANGZHOU\", \"fetchCount\" : 1000}"}, {"name": "OrderFetcherJob-Shopify-GUANGZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderFetcherJob", "cron": "5 8/10 * * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"platform\" : \"Shopify\", \"location\" : \"GUANGZHOU\"}"}, {"name": "OrderFetcherJob-TikTok-GUANGZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderFetcherJob", "cron": "35 2/10 * * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"platform\" : \"TikTok\", \"location\" : \"GUANGZHOU\"}"}, {"name": "OrderQueueFetcherJob-GUANGZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderQueueFetcherJob", "cron": "20 3/3 * * * ?", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"location\" : \"GUANGZHOU\", \"fetchCount\" : 500}"}, {"name": "OrderItemQueueFetcherJob-GUANGZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderItemQueueFetcherJob", "cron": "40 3/3 * * * ? 2099", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"location\" : \"GUANGZHOU\", \"fetchCount\" : 500}"}, {"name": "OrderAddressFetcherJob-GUANGZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderAddressFetcherJob", "cron": "55 3/3 * * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"GUANGZHOU\", \"fetchCount\" : 500}"}, {"name": "SyncInboundShipmentsJob-GUANGZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.inbound.SyncInboundShipmentsJob", "cron": "0 0 0/6 * * ?", "sharding-total-count": 8, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4,5=5,6=6,7=7", "job-parameters": "{\"location\" : \"GUANGZHOU\"}"}, {"name": "PlatformStockSyncJob-GUANGZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.platform.PlatformStockSyncJob", "cron": "0 0 0/6 * * ?", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"GUANGZHOU\"}"}, {"name": "ReportFbtInventoryRecordsJob-GUANGZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.report.ReportFbtInventoryRecordsJob", "cron": "0 13 0 * * ?", "sharding-total-count": 5, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4", "job-parameters": "{\"location\" : \"GUANGZHOU\", \"fetchCount\" : 20}"}, {"name": "ReportTkStatementRecordsJob-GUANGZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.report.ReportTkStatementRecordsJob", "cron": "0 16 0 * * ?", "sharding-total-count": 5, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4", "job-parameters": "{\"location\" : \"GUANGZHOU\", \"fetchCount\" : 20}"}, {"name": "ReportTkStatementTransactionRecordsJob-GUANGZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.report.ReportTkStatementTransactionRecordsJob", "cron": "0 5 5 * * ?", "sharding-total-count": 5, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4", "job-parameters": "{\"location\" : \"GUANGZHOU\", \"fetchCount\" : 50}"}, {"name": "ReportTkStatementOrderTransactionRecordsJob-GUANGZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.report.ReportTkStatementOrderTransactionRecordsJob", "cron": "0 10 0/1 * * ?", "sharding-total-count": 5, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4", "job-parameters": "{\"location\" : \"GUANGZHOU\", \"fetchCount\" : 50}"}, {"name": "ReportTkReturnRefundRecordsJob-GUANGZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.report.ReportTkReturnRefundRecordsJob", "cron": "0 17 5 * * ?", "sharding-total-count": 5, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4", "job-parameters": "{\"location\" : \"GUANGZHOU\", \"fetchCount\" : 50}"}]}