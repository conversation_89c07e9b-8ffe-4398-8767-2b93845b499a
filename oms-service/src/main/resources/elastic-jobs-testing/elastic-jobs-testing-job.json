{"job": [{"name": "KafkaExceptionRetryJob", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.mq.KafkaExceptionRetryJob", "cron": "0 0 0/3 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"fetchCount\" : 200}"}, {"name": "QMessageExceptionRetryJob", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.mq.QMessageExceptionRetryJob", "cron": "0 0 0/3 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"fetchCount\" : 200}"}, {"name": "AmazonOrderProfitMonitoringJob", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.amazon.AmazonOrderProfitMonitoringJob", "cron": "0 0 8-18 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": ""}]}