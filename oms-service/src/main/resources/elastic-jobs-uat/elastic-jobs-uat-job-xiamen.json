{"job": [{"name": "CheckStoreAuthIsExpireJob-XIAMEN", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.store.auth.CheckStoreAuthIsExpireJob", "cron": "0 0 8 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"XIAMEN\"}"}, {"name": "TemuPullAndCalGoodsSalesJob-XIAMEN", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.temu.TemuPullAndCalGoodsSalesJob", "cron": "0 15 3-23 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"XIAMEN\"}"}, {"name": "TemuGoodsSalesDataWarnJob-XIAMEN", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.temu.TemuGoodsSalesDataWarnJob", "cron": "0 0 8 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"XIAMEN\"}"}, {"name": "ProductBrandFetchJob-XIAMEN", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.temu.ProductBrandFetchJob", "cron": "0 0 21 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"XIAMEN\"}"}, {"name": "AutoGetErpScDataSellerListsJob-XIAMEN", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.lingxing.AutoGetErpScDataSellerListsJob", "cron": "0 19 17 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"XIAMEN\"}"}, {"name": "PlatformOrderItemMatchJob-XIAMEN", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.platform.PlatformOrderItemMatchJob", "cron": "0 0 1 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"XIAMEN\"}"}, {"name": "OrderItemMatchJob-XIAMEN", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.order.OrderItemMatchJob", "cron": "0 0 1 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"XIAMEN\"}"}, {"name": "OrderMissedJob-XIAMEN", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderMissedJob", "cron": "0 0/1 * * * ? 2099", "sharding-total-count": 5, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4", "job-parameters": "{\"location\" : \"XIAMEN\", \"fetchCount\" : 1000}"}, {"name": "OrderGrabJob-AMAZON-XIAMEN", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderGrabJob", "cron": "0 0/5 * * * ? 2099", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"platform\" : \"AMAZON\", \"location\" : \"XIAMEN\", \"fetchCount\" : 200}"}, {"name": "OrderItemGrabJob-XIAMEN", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderItemGrabJob", "cron": "0 0/1 * * * ? 2099", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"platform\" : \"AMAZON\", \"location\" : \"XIAMEN\", \"fetchCount\" : 2000}"}, {"name": "OrderItemGrabQueueRetryJob-XIAMEN", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.order.OrderItemGrabQueueRetryJob", "cron": "0 0 0/1 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"XIAMEN\", \"fetchCount\" : 2000}"}, {"name": "PushOrderToErpJob-XIAMEN", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.PushOrderToErpJob", "cron": "0 0/1 * * * ? 2099", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"location\" : \"XIAMEN\", \"fetchCount\" : 1000}"}, {"name": "OrderGrabExceptionMonitorJob-store-XIAMEN", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.order.OrderGrabExceptionMonitorJob", "cron": "0 0 09,16 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"type\" : \"STORE_EXCEPTION\", \"location\" : \"XIAMEN\"}"}, {"name": "OrderGrabExceptionMonitorJob-orderItem-XIAMEN", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.order.OrderGrabExceptionMonitorJob", "cron": "0 0 09,16 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"type\" : \"ITEM_GRAB_EXCEPTION\", \"location\" : \"XIAMEN\"}"}, {"name": "FbaOrderItemMonitorJob-XIAMEN", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.order.FbaOrderItemMonitorJob", "cron": "3 3 3 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"XIAMEN\", \"fetchCount\" : 1000}"}, {"name": "OrderFetcherJob-TikTokLocalShipping-XIAMEN", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderFetcherJob", "cron": "39 1/10 * * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"platform\" : \"TikTokLocalShipping\", \"location\" : \"XIAMEN\"}"}, {"name": "OrderFetcherJob-TikTok-XIAMEN", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderFetcherJob", "cron": "39 2/10 * * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"platform\" : \"TikTok\", \"location\" : \"XIAMEN\"}"}, {"name": "OrderQueueFetcherJob-XIAMEN", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderQueueFetcherJob", "cron": "20 7/3 * * * ? 2099", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"location\" : \"XIAMEN\", \"fetchCount\" : 500}"}, {"name": "OrderItemQueueFetcherJob-XIAMEN", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderItemQueueFetcherJob", "cron": "40 7/3 * * * ? 2099", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"location\" : \"XIAMEN\", \"fetchCount\" : 500}"}, {"name": "OrderAddressFetcherJob-XIAMEN", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderAddressFetcherJob", "cron": "55 7/3 * * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"XIAMEN\", \"fetchCount\" : 500}"}, {"name": "SyncInboundShipmentsJob-XIAMEN", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.inbound.SyncInboundShipmentsJob", "cron": "0 0 0/6 * * ? 2099", "sharding-total-count": 8, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4,5=5,6=6,7=7", "job-parameters": "{\"location\" : \"XIAMEN\"}"}, {"name": "PlatformStockSyncJob-XIAMEN", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.platform.PlatformStockSyncJob", "cron": "0 0 0/6 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"XIAMEN\"}"}, {"name": "ReportFbtInventoryRecordsJob-XIAMEN", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.report.ReportFbtInventoryRecordsJob", "cron": "0 0/1 * * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"XIAMEN\", \"fetchCount\" : 100}"}, {"name": "ReportTkStatementRecordsJob-XIAMEN", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.report.ReportTkStatementRecordsJob", "cron": "0 0/1 * * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"XIAMEN\", \"fetchCount\" : 100}"}, {"name": "ReportTkStatementTransactionRecordsJob-XIAMEN", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.report.ReportTkStatementTransactionRecordsJob", "cron": "0 6 5 * * ? 2099", "sharding-total-count": 5, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4", "job-parameters": "{\"location\" : \"XIAMEN\", \"fetchCount\" : 100}"}, {"name": "ReportTkStatementOrderTransactionRecordsJob-XIAMEN", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.report.ReportTkStatementOrderTransactionRecordsJob", "cron": "0 6 5 * * ? 2099", "sharding-total-count": 5, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4", "job-parameters": "{\"location\" : \"XIAMEN\", \"fetchCount\" : 100}"}, {"name": "ReportTkReturnRefundRecordsJob-XIAMEN", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.report.ReportTkReturnRefundRecordsJob", "cron": "0 6 5 * * ? 2099", "sharding-total-count": 5, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4", "job-parameters": "{\"location\" : \"XIAMEN\", \"fetchCount\" : 100}"}]}