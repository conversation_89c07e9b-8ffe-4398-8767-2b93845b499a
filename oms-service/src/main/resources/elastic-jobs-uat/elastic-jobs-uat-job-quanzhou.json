{"job": [{"name": "CheckStoreAuthIsExpireJob-QUANZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.store.auth.CheckStoreAuthIsExpireJob", "cron": "0 0 8 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"QUANZHOU\"}"}, {"name": "TemuPullAndCalGoodsSalesJob-QUANZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.temu.TemuPullAndCalGoodsSalesJob", "cron": "0 15 3-23 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"QUANZHOU\"}"}, {"name": "TemuGoodsSalesDataWarnJob-QUANZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.temu.TemuGoodsSalesDataWarnJob", "cron": "0 0 8 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"QUANZHOU\"}"}, {"name": "ProductBrandFetchJob-QUANZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.temu.ProductBrandFetchJob", "cron": "0 0 21 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"QUANZHOU\"}"}, {"name": "AutoGetErpScDataSellerListsJob-QUANZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.lingxing.AutoGetErpScDataSellerListsJob", "cron": "0 19 17 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"QUANZHOU\"}"}, {"name": "PlatformOrderItemMatchJob-QUANZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.platform.PlatformOrderItemMatchJob", "cron": "0 0 1 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"QUANZHOU\"}"}, {"name": "OrderItemMatchJob-QUANZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.order.OrderItemMatchJob", "cron": "0 0 1 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"QUANZHOU\"}"}, {"name": "OrderMissedJob-QUANZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderMissedJob", "cron": "0 0/1 * * * ? 2099", "sharding-total-count": 5, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4", "job-parameters": "{\"location\" : \"QUANZHOU\", \"fetchCount\" : 1000}"}, {"name": "OrderGrabJob-AMAZON-QUANZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderGrabJob", "cron": "0 0/5 * * * ? 2099", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"platform\" : \"AMAZON\", \"location\" : \"QUANZHOU\", \"fetchCount\" : 200}"}, {"name": "OrderItemGrabJob-QUANZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderItemGrabJob", "cron": "0 0/1 * * * ? 2099", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"platform\" : \"AMAZON\", \"location\" : \"QUANZHOU\", \"fetchCount\" : 2000}"}, {"name": "OrderItemGrabQueueRetryJob-QUANZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.order.OrderItemGrabQueueRetryJob", "cron": "0 0 0/1 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"QUANZHOU\", \"fetchCount\" : 2000}"}, {"name": "PushOrderToErpJob-QUANZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.PushOrderToErpJob", "cron": "0 0/1 * * * ? 2099", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"location\" : \"QUANZHOU\", \"fetchCount\" : 1000}"}, {"name": "OrderGrabExceptionMonitorJob-store-QUANZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.order.OrderGrabExceptionMonitorJob", "cron": "0 0 09,16 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"type\" : \"STORE_EXCEPTION\", \"location\" : \"QUANZHOU\"}"}, {"name": "OrderGrabExceptionMonitorJob-orderItem-QUANZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.order.OrderGrabExceptionMonitorJob", "cron": "0 0 09,16 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"type\" : \"ITEM_GRAB_EXCEPTION\", \"location\" : \"QUANZHOU\"}"}, {"name": "OrderItemGrabJob-Big-Store-US09-QUANZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderItemGrabJob", "cron": "0 0/1 * * * ? 2099", "sharding-total-count": 10, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4,5=5,6=6,7=7,8=8,9=9", "job-parameters": "{\"platform\" : \"AMAZON\", \"storeId\" : 5009, \"location\" : \"QUANZHOU\", \"fetchCount\" : 500}"}, {"name": "PushKingDeeStockoutOrderJob-FBA-QUANZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.order.PushKingDeeStockoutOrderJob", "cron": "0 5 0/4 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"QUANZHOU\", \"fetchCount\" : 1000, \"orderType\" : \"FBA_OVERSEA_STOCK_OUT_ORDER\"}"}, {"name": "PushKingDeeStockoutOrderJob-WFS-QUANZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.order.PushKingDeeStockoutOrderJob", "cron": "0 5 0/4 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"QUANZHOU\", \"fetchCount\" : 1000, \"orderType\" : \"WFS_OVERSEA_STOCK_OUT_ORDER\"}"}, {"name": "PushKingDeeStockoutOrderJob-TIKTOK-QUANZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.order.PushKingDeeStockoutOrderJob", "cron": "0 5 0/4 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"QUANZHOU\", \"fetchCount\" : 1000, \"orderType\" : \"FBT_SALE_STOCK_OUT_ORDER\"}"}, {"name": "PushKingDeeCostOrderJob-QUANZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.order.PushKingDeeCostOrderJob", "cron": "0 5 0/4 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"QUANZHOU\", \"fetchCount\" : 1000, \"orderType\" : \"RECEIVABLE_COST_ORDER\"}"}, {"name": "PushKingDeeOtherStockoutOrderJob-QUANZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.order.PushKingDeeOtherStockoutOrderJob", "cron": "0 3 0/4 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"QUANZHOU\", \"fetchCount\" : 1000, \"orderType\" : \"OTHER_STOCK_OUT_ORDER\"}"}, {"name": "PushFbtReturnOrderToKingDeeJob-QUANZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.PushFbtReturnOrderToKingDeeJob", "cron": "0 0/7 * * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"QUANZHOU\", \"fetchCount\" : 100}"}, {"name": "SyncInternetCelebritySampleOrderJob-QUANZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.celebrity.SyncInternetCelebritySampleOrderJob", "cron": "1 0 3,13,23 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"QUANZHOU\"}"}, {"name": "SyncOrderCompromiseDateJob-QUANZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.celebrity.SyncOrderCompromiseDateJob", "cron": "0 0 4 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"QUANZHOU\"}"}, {"name": "CompromiseDateOverdueWarningJob-QUANZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.celebrity.CompromiseDateOverdueWarningJob", "cron": "0 0 5 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"QUANZHOU\"}"}, {"name": "InternetCelebrityCreatorRelationUpdateJob-QUANZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.celebrity.InternetCelebrityCreatorRelationUpdateJob", "cron": "0 0 5 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"QUANZHOU\"}"}, {"name": "InternetCelebrityInitRelationJob-QUANZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.celebrity.InternetCelebrityInitRelationJob", "cron": "0 0 5 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"QUANZHOU\"}"}, {"name": "SyncInternetCelebritySampleCooperationJob-QUANZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.celebrity.SyncInternetCelebritySampleCooperationJob", "cron": "0 0 6 * * ? 2099", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"type\" : \"increment\", \"location\" : \"QUANZHOU\"}"}, {"name": "FbaOrderItemMonitorJob-QUANZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.order.FbaOrderItemMonitorJob", "cron": "3 3 3 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"QUANZHOU\", \"fetchCount\" : 1000}"}, {"name": "CheckEmailIsExpireJob", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.sa.CheckEmailIsExpireJob", "cron": "0 0 8 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"QUANZHOU\"}"}, {"name": "OrderFetcherJob-AlibabaInternation-QUANZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderFetcherJob", "cron": "1 1/10 * * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"platform\" : \"AlibabaInternation\", \"location\" : \"QUANZHOU\"}"}, {"name": "OrderFetcherJob-Alibaba-QUANZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderFetcherJob", "cron": "1 2/10 * * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"platform\" : \"Alibaba\", \"location\" : \"QUANZHOU\"}"}, {"name": "OrderFetcherJob-Faire-QUANZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderFetcherJob", "cron": "1 3/10 * * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"platform\" : \"Faire\", \"location\" : \"QUANZHOU\"}"}, {"name": "OrderFetcherJob-FashionGo-QUANZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderFetcherJob", "cron": "1 4/10 * * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"platform\" : \"FashionGo\", \"location\" : \"QUANZHOU\"}"}, {"name": "OrderFetcherJob<PERSON><PERSON>-QUANZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderFetcherJob", "cron": "1 5/10 * * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"platform\" : \"Otto\", \"location\" : \"QUANZHOU\"}"}, {"name": "OrderFetcherJob-SheinSemiHosted-QUANZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderFetcherJob", "cron": "1 6/10 * * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"platform\" : \"SheinSemiHosted\", \"location\" : \"QUANZHOU\"}"}, {"name": "OrderF<PERSON>cher<PERSON><PERSON><PERSON><PERSON><PERSON>-QUANZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderFetcherJob", "cron": "1 7/10 * * * ? 2099", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"platform\" : \"Shein\", \"location\" : \"QUANZHOU\"}"}, {"name": "OrderFetcherJob-Shopify-QUANZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderFetcherJob", "cron": "1 8/10 * * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"platform\" : \"Shopify\", \"location\" : \"QUANZHOU\"}"}, {"name": "OrderFetcherJob-TemuPop-QUANZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderFetcherJob", "cron": "1 9/10 * * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"platform\" : \"TemuPop\", \"location\" : \"QUANZHOU\"}"}, {"name": "OrderFetcherJob-Temu-QUANZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderFetcherJob", "cron": "1 0/10 * * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"platform\" : \"Temu\", \"location\" : \"QUANZHOU\"}"}, {"name": "OrderFetcherJob-TikTokLocalShipping-QUANZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderFetcherJob", "cron": "30 1/10 * * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"platform\" : \"TikTokLocalShipping\", \"location\" : \"QUANZHOU\"}"}, {"name": "OrderFetcherJob-TikTok-QUANZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderFetcherJob", "cron": "30 2/10 * * * ? 2099", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"platform\" : \"TikTok\", \"location\" : \"QUANZHOU\"}"}, {"name": "OrderFetcherJob-TikTokShop-QUANZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderFetcherJob", "cron": "30 3/10 * * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"platform\" : \"TikTokShop\", \"location\" : \"QUANZHOU\"}"}, {"name": "OrderFetcherJob-Walmart-QUANZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderFetcherJob", "cron": "30 4/10 * * * ? 2099", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"platform\" : \"Walmart\", \"location\" : \"QUANZHOU\"}"}, {"name": "OrderQueueFetcherJob-QUANZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderQueueFetcherJob", "cron": "20 1/3 * * * ? 2099", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"location\" : \"QUANZHOU\", \"fetchCount\" : 500}"}, {"name": "OrderItemQueueFetcherJob-QUANZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderItemQueueFetcherJob", "cron": "40 1/3 * * * ? 2099", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"location\" : \"QUANZHOU\", \"fetchCount\" : 500}"}, {"name": "OrderAddressFetcherJob-QUANZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.order.OrderAddressFetcherJob", "cron": "55 1/3 * * * ? 2099", "sharding-total-count": 2, "sharding-item-parameters": "0=0,1=1", "job-parameters": "{\"location\" : \"QUANZHOU\", \"fetchCount\" : 500}"}, {"name": "SyncInboundShipmentsJob-QUANZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.inbound.SyncInboundShipmentsJob", "cron": "0 0 0/6 * * ? 2099", "sharding-total-count": 8, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4,5=5,6=6,7=7", "job-parameters": "{\"location\" : \"QUANZHOU\"}"}, {"name": "PlatformStockSyncJob-QUANZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.platform.PlatformStockSyncJob", "cron": "0 0 0/6 * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"QUANZHOU\"}"}, {"name": "SyncAffirmWaitConfirmJob-QUANZHOU", "elastic-job-type": "simple-job", "type": "com.nsy.oms.elasticjob.points.SyncAffirmWaitConfirmJob", "cron": "0 0/5 * * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"QUANZHOU\"}"}, {"name": "ReportFbtInventoryRecordsJob-QUANZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.report.ReportFbtInventoryRecordsJob", "cron": "0 0/1 * * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"QUANZHOU\", \"fetchCount\" : 100}"}, {"name": "ReportTkStatementRecordsJob-QUANZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.report.ReportTkStatementRecordsJob", "cron": "0 0/1 * * * ? 2099", "sharding-total-count": 1, "sharding-item-parameters": "0=0", "job-parameters": "{\"location\" : \"QUANZHOU\", \"fetchCount\" : 100}"}, {"name": "ReportTkStatementTransactionRecordsJob-QUANZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.report.ReportTkStatementTransactionRecordsJob", "cron": "0 3 5 * * ? 2099", "sharding-total-count": 5, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4", "job-parameters": "{\"location\" : \"QUANZHOU\", \"fetchCount\" : 100}"}, {"name": "ReportTkStatementOrderTransactionRecordsJob-QUANZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.report.ReportTkStatementOrderTransactionRecordsJob", "cron": "0 3 5 * * ? 2099", "sharding-total-count": 5, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4", "job-parameters": "{\"location\" : \"QUANZHOU\", \"fetchCount\" : 100}"}, {"name": "ReportTkReturnRefundRecordsJob-QUANZHOU", "elastic-job-type": "dataflow-job", "streaming_process": 0, "type": "com.nsy.oms.elasticjob.report.ReportTkReturnRefundRecordsJob", "cron": "0 3 5 * * ? 2099", "sharding-total-count": 5, "sharding-item-parameters": "0=0,1=1,2=2,3=3,4=4", "job-parameters": "{\"location\" : \"QUANZHOU\", \"fetchCount\" : 100}"}]}