package com.nsy.oms.enumstable;

public enum InternetCelebritySampleOrderDeliveryTypeEnum {
    FBA(1, "FBA", "FBA"),
    FBT(2, "FBT", "FBT"),
    OVERSEAS_WAREHOUSE(3, "NOT_FBA", "海外仓"),
    UNKNOWN(4, "UNKNOWN", "未知"),
    NOT_ORDER(5, "NOT_ORDER", "无订单号");

    private final Integer code;
    private final String desc;
    private final String description;

    InternetCelebritySampleOrderDeliveryTypeEnum(Integer code, String desc, String description) {
        this.code = code;
        this.desc = desc;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getDescription() {
        return description;
    }
}
