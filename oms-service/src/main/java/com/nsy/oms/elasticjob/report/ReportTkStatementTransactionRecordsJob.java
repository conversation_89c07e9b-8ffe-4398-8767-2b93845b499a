package com.nsy.oms.elasticjob.report;

import cn.hutool.core.date.DateUtil;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.DateUtils;
import com.nsy.api.core.apicore.util.NsyJacksonUtils;
import com.nsy.api.transfer.domain.common.TiktokAuthResponse;
import com.nsy.oms.business.domain.dto.BaseParameterDTO;
import com.nsy.oms.business.service.auth.SauPlatformAuthConfigService;
import com.nsy.oms.business.service.report.ReportTkStatementService;
import com.nsy.oms.elasticjob.base.BaseDataFlowJob;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class ReportTkStatementTransactionRecordsJob extends BaseDataFlowJob<TiktokAuthResponse> {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReportTkStatementTransactionRecordsJob.class);

    @Autowired
    private ReportTkStatementService reportTkStatementService;
    @Autowired
    private SauPlatformAuthConfigService sauPlatformAuthConfigService;

    @Override
    protected List<TiktokAuthResponse> doFetchData(ShardingContext shardingContext) throws Exception {
        LOGGER.info("ReportTkStatementRecordsJob start, shardingItem: {}, jobParameter: {}", shardingContext.getShardingItem(), DateUtil.format(new Date(), DateUtils.DATE_FORMAT_DATE4));
        // 获取所有tiktok授权列表
        List<Integer> list = new ArrayList<>();
        list.add(63);
        list.add(68);
        list.add(71);
        List<TiktokAuthResponse> authList = sauPlatformAuthConfigService.getTiktokAuthList(list);
        if (CollectionUtils.isEmpty(authList)) {
            return Collections.emptyList();
        }
        return authList.stream().filter(queue -> queue.getStoreId() % shardingContext.getShardingTotalCount() == shardingContext.getShardingItem()).collect(Collectors.toList());
    }

    @Override
    protected void doProcessData(ShardingContext shardingContext, List<TiktokAuthResponse> data) {
        String jobParameter = shardingContext.getJobParameter();
        BaseParameterDTO baseParameterDTO = NsyJacksonUtils.toObj(jobParameter, BaseParameterDTO.class);
        checkParameter(baseParameterDTO);
        try {
            reportTkStatementService.saveTransactionRecords(data, baseParameterDTO);
            LOGGER.info("ReportTkStatementRecordsJob end");
        } catch (Exception e) {
            LOGGER.error("ReportTkStatementRecordsJob error", e);
        }
    }

    private void checkParameter(BaseParameterDTO baseParameterDTO) {
        if (Objects.isNull(baseParameterDTO)) {
            throw new BusinessServiceException("ReportTkStatementRecordsJob参数有误!");
        }
        if (Objects.isNull(baseParameterDTO.getFetchCount())) {
            throw new BusinessServiceException("ReportTkStatementRecordsJob：fetchCount参数为空!");
        }
        if (StringUtils.isBlank(baseParameterDTO.getLocation())) {
            throw new BusinessServiceException("ReportTkStatementRecordsJob：location参数为空!");
        }
    }
}
