package com.nsy.oms.elasticjob.inbound;

import com.nsy.api.core.apicore.util.ExceptionUtils;
import com.nsy.oms.business.manage.thirdparty.ThirdPartyApiService;
import com.nsy.oms.business.manage.thirdparty.auth.TiktokAuth;
import com.nsy.oms.business.manage.thirdparty.response.InboundOrder;
import com.nsy.oms.business.manage.thirdparty.response.InboundOrdersResponse;
import com.nsy.oms.business.manage.thirdparty.response.OrderOperationLog;
import com.nsy.oms.business.manage.thirdparty.response.PlannedGood;
import com.nsy.oms.business.service.auth.SauPlatformAuthConfigService;
import com.nsy.oms.business.service.inbound.InboundPlanService;
import com.nsy.oms.business.service.inbound.InboundShipmentItemService;
import com.nsy.oms.business.service.inbound.InboundShipmentService;
import com.nsy.oms.business.service.sa.SaStoreService;
import com.nsy.oms.elasticjob.base.BaseSimpleJob;
import com.nsy.oms.enums.inbound.InboundPlanStatusEnum;
import com.nsy.oms.enums.inbound.ShipmentStatusEnum;
import com.nsy.oms.enums.inbound.TiktokPlatformShipmentStatusEnum;
import com.nsy.oms.enums.sa.PlatformEnum;
import com.nsy.oms.repository.dao.sa.SaStoreDao;
import com.nsy.oms.repository.entity.inbound.InboundShipmentEntity;
import com.nsy.oms.repository.entity.inbound.InboundShipmentItemEntity;
import com.nsy.oms.repository.entity.sa.SaStoreEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: linCheng
 * @create: 2025-04-07 15:29
 **/
@Slf4j
@Component
public class SyncInboundShipmentsJob extends BaseSimpleJob {

    @Autowired
    private SaStoreService storeService;
    @Autowired
    private InboundShipmentService inboundShipmentService;
    @Autowired
    private InboundShipmentItemService inboundShipmentItemService;
    @Autowired
    private InboundPlanService inboundPlanService;
    @Autowired
    private SauPlatformAuthConfigService sauPlatformAuthConfigService;
    @Autowired
    private ThirdPartyApiService thirdPartyApiService;
    @Resource
    private SaStoreDao saStoreDao;
    private static final Integer REQUEST_SHIPMENT_ID_SIZE = 50;

    @Override
    protected void run(Map<String, Object> jobDataMap) throws Exception {
        int shardingTotalCount = (int) jobDataMap.get("shardingTotalCount");
        int shardingItem = (int) jobDataMap.get("shardingItem");
        this.syncInboundShipments(shardingTotalCount, shardingItem);
    }

    public void syncInboundShipments(int shardingTotalCount, int shardingItem) {
        List<SaStoreEntity> storeEntities = getShardingStores(shardingTotalCount, shardingItem);
        List<String> exceptions = new ArrayList<>();

        // 1. sync shipments from TikTok
        storeEntities.forEach(storeEntity -> {
            try {
                this.bulkSyncShipments(storeEntity);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                exceptions.add(String.format("%s, exception=%s", storeEntity.getErpStoreName(), ExceptionUtils.stackTrace(e)));
            }
        });

        // 2. sync shipment plan
        try {
            inboundPlanService.getListByStatus(InboundPlanStatusEnum.SHIPPED.getStatus())
                    .stream()
                    .filter(inboundPlan -> inboundShipmentService.getByPlanIds(Collections.singletonList(inboundPlan.getId()))
                            .stream()
                            .map(InboundShipmentEntity::getStatus)
                            .allMatch(x -> x.equals(ShipmentStatusEnum.CLOSED.status())))
                    .peek(shipmentPlan -> shipmentPlan.setStatus(InboundPlanStatusEnum.DONE.status()))
                    .forEach(inboundPlanEntity -> {
                        inboundPlanService.updateById(inboundPlanEntity);
                    });
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            exceptions.add(String.format("syncShipmentPlan, exception=%s", ExceptionUtils.stackTrace(e)));
        }
        if (CollectionUtils.isNotEmpty(exceptions)) {
            throw new IllegalStateException(exceptions.toString());
        }
    }

    private void bulkSyncShipments(SaStoreEntity storeEntity) {
        Integer storeId = storeEntity.getId();
        List<InboundShipmentEntity> inboundShipmentEntities = inboundShipmentService.getListByStoreIdAndStatus(storeId, Collections.singletonList(ShipmentStatusEnum.SHIPPED.status()));
        if (CollectionUtils.isEmpty(inboundShipmentEntities)) {
            log.info("storeName={}, shipmentEntities is empty.", storeEntity.getErpStoreName());
            return;
        }
        TiktokAuth tiktokAuth = sauPlatformAuthConfigService.getTikTokToken(saStoreDao.getAssociatedStoreIdWithThrowEx(storeId));
        List<List<InboundShipmentEntity>> shipmentPartitions = ListUtils.partition(inboundShipmentEntities, REQUEST_SHIPMENT_ID_SIZE);
        // sync shipment
        shipmentPartitions.forEach(shipmentPartition -> self(getClass()).bulkSyncShipmentInfo(storeId, shipmentPartition, tiktokAuth));
    }


    @Transactional
    public void bulkSyncShipmentInfo(Integer storeId, List<InboundShipmentEntity> shipmentPartition, TiktokAuth tiktokAuth) {
        List<String> shipmentIds = shipmentPartition.stream().map(InboundShipmentEntity::getShipmentId).collect(Collectors.toList());
        InboundOrdersResponse inboundOrdersResponse = thirdPartyApiService.getInboundOrders(tiktokAuth, shipmentIds);
        if (!Optional.ofNullable(inboundOrdersResponse).isPresent() || !Optional.ofNullable(inboundOrdersResponse.getInboundOrderDate()).isPresent() || CollectionUtils.isEmpty(inboundOrdersResponse.getInboundOrderDate().getInboundOrders())) {
            return;
        }
        List<InboundOrder> inboundOrders = inboundOrdersResponse.getInboundOrderDate().getInboundOrders();
        if (!Objects.equals(inboundOrders.size(), shipmentIds.size())) {
            List<String> returnShipmentIds = inboundOrders.stream().map(InboundOrder::getId).collect(Collectors.toList());
            log.warn("storeId={} bulkSyncShipmentInfo diffShipmentIds={}", storeId, CollectionUtils.disjunction(returnShipmentIds, shipmentIds));
        }

        inboundOrders.forEach(inboundOrder -> {
            InboundShipmentEntity inboundShipment = inboundShipmentService.getInboundShipment(inboundOrder.getId());
            if (!ShipmentStatusEnum.SHIPPED.status().equals(inboundShipment.getStatus())) {
                return;
            }
            log.info("start process shipmentId={}", inboundShipment.getShipmentId());
            Optional<OrderOperationLog> maxTimeLog = inboundOrder.getOrderOperationLogs().stream().max(Comparator.comparingLong(OrderOperationLog::getOperateTime));
            inboundShipment.setPlatformStatus(maxTimeLog.isPresent() ? maxTimeLog.get().getOrderStatus() : "未知");

            if (TiktokPlatformShipmentStatusEnum.RECEIVED.name().equalsIgnoreCase(inboundShipment.getPlatformStatus())) {
                inboundShipment.setStatus(ShipmentStatusEnum.CLOSED.status());
            }

            setShipmentItemInfo(inboundOrder.getPlannedGoods(), inboundShipment.getShipmentId());

            inboundShipmentService.updateById(inboundShipment);
            log.info("end process shipmentId={}", inboundShipment.getShipmentId());
        });
    }

    private void setShipmentItemInfo(List<PlannedGood> plannedGoods, String shipmentId) {
        List<InboundShipmentItemEntity> inboundShipmentItemEntities = inboundShipmentItemService.getByShipmentIds(Collections.singletonList(shipmentId));
        Map<String, InboundShipmentItemEntity> inboundShipmentItemMap = inboundShipmentItemEntities.stream().collect(Collectors.toMap(InboundShipmentItemEntity::getGoodsId, a -> a, (k1, k2) -> k1));

        List<InboundShipmentItemEntity> inboundShipmentItemEntityList = new ArrayList<>();
        plannedGoods.forEach(plannedGood -> {
            InboundShipmentItemEntity inboundShipmentItemEntity = inboundShipmentItemMap.get(plannedGood.getId());
            if (!Optional.ofNullable(inboundShipmentItemEntity).isPresent()) {
                return;
            }
            inboundShipmentItemEntity.setUpdateDate(new Date());
            inboundShipmentItemEntity.setUpdateBy(this.getJobName());
            inboundShipmentItemEntity.setQuantityReceived(plannedGood.getQuantity());
            inboundShipmentItemEntity.setReceiveDifferenceQuantity((Optional.ofNullable(inboundShipmentItemEntity.getQuantityReceived()).isPresent() ? inboundShipmentItemEntity.getQuantityReceived() : 0) - (Optional.ofNullable(inboundShipmentItemEntity.getActualShipmentQuantity()).isPresent() ? inboundShipmentItemEntity.getActualShipmentQuantity() : 0));
            inboundShipmentItemEntityList.add(inboundShipmentItemEntity);
        });
        inboundShipmentItemService.updateBatchById(inboundShipmentItemEntityList);
    }


    private List<SaStoreEntity> getShardingStores(int shardingTotalCount, int shardingItem) {
        return storeService.getSaleStore(PlatformEnum.TIKTOK.name())
                .stream().filter(store -> store.getId() % shardingTotalCount == shardingItem)
                .collect(Collectors.toList());
    }


}
