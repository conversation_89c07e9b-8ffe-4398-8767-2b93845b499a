package com.nsy.oms.elasticjob.points;

import com.nsy.oms.business.manage.erp.ErpApiService;
import com.nsy.oms.business.manage.erp.request.inbound.GetPaymentInfoRequest;
import com.nsy.oms.business.manage.erp.response.inbound.PaymentInfoResponse;
import com.nsy.oms.business.service.points.IB2bPointsRefundItemService;
import com.nsy.oms.business.service.points.IB2bPointsRefundService;
import com.nsy.oms.elasticjob.base.BaseSimpleJob;
import com.nsy.oms.enums.points.B2bPointsRefundStateEnum;
import com.nsy.oms.repository.entity.points.B2bPointsRefundEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @description:
 * @author: linCheng
 * @create: 2025-05-09 14:24
 **/
@Slf4j
@Component
public class SyncAffirmWaitConfirmJob extends BaseSimpleJob {

    @Resource
    private ErpApiService erpApiService;
    @Resource
    private IB2bPointsRefundService b2bPointsRefundService;
    @Resource
    private IB2bPointsRefundItemService b2bPointsRefundItemService;

    @Override
    protected void run(Map<String, Object> jobDataMap) throws Exception {
        List<B2bPointsRefundEntity> b2bPointsRefundEntityList = b2bPointsRefundService.getListByState(B2bPointsRefundStateEnum.WAIT_DISTRIBUTE.getStatus());
        b2bPointsRefundEntityList.forEach(entity -> {
            try {
                B2bPointsRefundEntity b2bPointsRefundEntity = b2bPointsRefundService.getByRefundId(entity.getRefundId());
                if (!Optional.ofNullable(b2bPointsRefundEntity).isPresent() || !B2bPointsRefundStateEnum.WAIT_DISTRIBUTE.getStatus().equals(b2bPointsRefundEntity.getState())) {
                    log.info("SyncAffirmWaitConfirmJob.同步明细数据异常,refundId:{},state:{}", entity.getRefundId(), Optional.ofNullable(b2bPointsRefundEntity).isPresent() ? b2bPointsRefundEntity.getState() : "");
                    return;
                }
                PaymentInfoResponse paymentInfo = erpApiService.getPaymentInfo(new GetPaymentInfoRequest(b2bPointsRefundEntity.getRefundId(), b2bPointsRefundEntity.getStoreId(), b2bPointsRefundEntity.getAmountRefunded(), b2bPointsRefundEntity.getCustomerEmail()));

                b2bPointsRefundItemService.syncItems(b2bPointsRefundEntity.getRefundId(), paymentInfo.getTradePaymentInfoList());
                b2bPointsRefundEntity.setState(B2bPointsRefundStateEnum.WAIT_PROCESS.getStatus());
                b2bPointsRefundEntity.setUpdateBy("SyncAffirmWaitConfirmJob");
                b2bPointsRefundEntity.setUpdateDate(new Date());
                b2bPointsRefundService.updateById(b2bPointsRefundEntity);
            } catch (Exception e) {
                log.error("SyncAffirmWaitConfirmJob.syncB2bPointsRefundItem.refundId:{}.error:{}", entity.getRefundId(), e.getMessage());
            }

        });

    }
}
