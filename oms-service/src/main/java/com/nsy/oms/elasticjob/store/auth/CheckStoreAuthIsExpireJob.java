package com.nsy.oms.elasticjob.store.auth;

import cn.hutool.core.collection.CollectionUtil;
import com.nsy.oms.business.manage.notify.NotifyApiService;
import com.nsy.oms.business.service.auth.SauAliInterConfigService;
import com.nsy.oms.business.service.auth.SauAmazonConfigService;
import com.nsy.oms.business.service.auth.SauPddConfigService;
import com.nsy.oms.business.service.auth.SauShopifyConfigService;
import com.nsy.oms.business.service.auth.SauTaobaoConfigService;
import com.nsy.oms.business.service.auth.SauWholesaleConfigService;
import com.nsy.oms.business.service.sa.SaStorePrincipalService;
import com.nsy.oms.elasticjob.base.BaseSimpleJob;
import com.nsy.oms.repository.entity.auth.SauAliInterConfigEntity;
import com.nsy.oms.repository.entity.auth.SauAmazonConfigEntity;
import com.nsy.oms.repository.entity.auth.SauPddConfigEntity;
import com.nsy.oms.repository.entity.auth.SauShopifyConfigEntity;
import com.nsy.oms.repository.entity.auth.SauTaobaoConfigEntity;
import com.nsy.oms.repository.entity.auth.SauWholesaleConfigEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 检查店铺授权是否过期
 *
 * <AUTHOR>
 * @date 2023/2/15 14:36
 */
@Component
public class CheckStoreAuthIsExpireJob extends BaseSimpleJob {
    private static final Logger LOGGER = LoggerFactory.getLogger(CheckStoreAuthIsExpireJob.class);
    @Autowired
    private NotifyApiService notifyApiService;
    @Autowired
    private SauAmazonConfigService sauAmazonConfigService;
    @Autowired
    private SauShopifyConfigService sauShopifyConfigService;
    @Autowired
    private SauTaobaoConfigService sauTaobaoConfigService;
    @Autowired
    private SauPddConfigService sauPddConfigService;
    @Autowired
    private SauWholesaleConfigService sauWholesaleConfigService;
    @Autowired
    private SauAliInterConfigService sauAliInterConfigService;
    @Autowired
    private SaStorePrincipalService saStorePrincipalService;
    /**
     * 提前通知时间
     */
    private static final Integer NOTIFY_DAY = 5;
    private static final String AUTH_VALID_MESSAGE = "授权即将到期,请前去更换";

    @Override
    protected void run(Map<String, Object> jobDataMap) {
        LOGGER.info("CheckStoreAuthIsExpireJob -------检查店铺是否授权过期------start");
        List<SauAmazonConfigEntity> sauAmazonConfigEntities = sauAmazonConfigService.getExpireStores(NOTIFY_DAY);
        LOGGER.info("CheckStoreAuthIsExpireJob  sauAmazonConfigEntities 的size -- {}", sauAmazonConfigEntities.size());
        sauAmazonConfigEntities.forEach(item ->
                sendMessageToDingTalk(item.getStoreId(), String.format("<<亚马逊>> %s %s", item.getStoreName(), AUTH_VALID_MESSAGE))
        );
        List<SauShopifyConfigEntity> sauShopifyConfigEntities = sauShopifyConfigService.getExpireStores(NOTIFY_DAY);
        sauShopifyConfigEntities.forEach(item ->
                sendMessageToDingTalk(item.getStoreId(), String.format("<<shopify>> %s %s", item.getStoreName(), AUTH_VALID_MESSAGE))
        );
        List<SauTaobaoConfigEntity> sauTaobaoConfigEntities = sauTaobaoConfigService.getExpireStores(NOTIFY_DAY);
        sauTaobaoConfigEntities.forEach(item ->
                sendMessageToDingTalk(item.getStoreId(), String.format("<<淘宝>> %s %s", item.getStoreName(), AUTH_VALID_MESSAGE))
        );
        List<SauPddConfigEntity> sauPddConfigEntities = sauPddConfigService.getExpireStores(NOTIFY_DAY);
        sauPddConfigEntities.forEach(item ->
                sendMessageToDingTalk(item.getStoreId(), String.format("<<拼多多>> %s %s", item.getStoreName(), AUTH_VALID_MESSAGE))
        );
        List<SauWholesaleConfigEntity> sauWholesaleConfigEntities = sauWholesaleConfigService.getExpireStores(NOTIFY_DAY);
        sauWholesaleConfigEntities.forEach(item ->
                sendMessageToDingTalk(item.getStoreId(), String.format("<<1688>> %s %s", item.getStoreName(), AUTH_VALID_MESSAGE))
        );
        List<SauAliInterConfigEntity> sauAliInterConfigEntities = sauAliInterConfigService.getExpireStores(NOTIFY_DAY);
        sauAliInterConfigEntities.forEach(item ->
                sendMessageToDingTalk(item.getStoreId(), String.format("<<阿里国际>> %s %s", item.getStoreName(), AUTH_VALID_MESSAGE))
        );
        LOGGER.info("CheckStoreAuthIsExpireJob -------检查店铺是否授权过期------end");
    }

    public void sendMessageToDingTalk(Integer storeId, String message) {
        LOGGER.info("sendMessageToDingTalk << storeId{} << {}", storeId, message);
        List<String> userNameList = saStorePrincipalService.getUserNameList(storeId);
        LOGGER.info("sendMessageToDingTalk << storeId{} << {}", storeId, userNameList.size());
        if (!CollectionUtil.isEmpty(userNameList)) {
            notifyApiService.sendTextDingTalkMessage(userNameList, message);
        }
    }


}
