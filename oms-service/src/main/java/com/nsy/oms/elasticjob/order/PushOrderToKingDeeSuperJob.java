package com.nsy.oms.elasticjob.order;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.DateUtils;
import com.nsy.api.core.apicore.util.NsyCollUtil;
import com.nsy.api.core.apicore.util.NsyJacksonUtils;
import com.nsy.oms.business.domain.dto.PushKingDeeParameterDTO;
import com.nsy.oms.business.factory.PlatformOrderPushKingDeeServiceFactory;
import com.nsy.oms.business.service.platform.PlatformOrderPushKingDeeService;
import com.nsy.oms.business.service.platform.PlatformOrderService;
import com.nsy.oms.constants.JobConstant;
import com.nsy.oms.elasticjob.base.BaseSimpleJob;
import com.nsy.oms.repository.entity.platform.PlatformOrderEntity;
import com.nsy.oms.repository.entity.platform.PlatformOrderPushKingdeeQueueEntity;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public abstract class PushOrderToKingDeeSuperJob extends BaseSimpleJob {
    private static final Logger LOGGER = LoggerFactory.getLogger(PushOrderToKingDeeSuperJob.class);
    @Autowired
    private PlatformOrderPushKingDeeServiceFactory platformOrderPushKingDeeServiceFactory;
    @Autowired
    private PlatformOrderService platformOrderService;

    @Override
    protected void run(Map<String, Object> jobDataMap) throws Exception {
        String jobParameter = (String) jobDataMap.get(JobConstant.JOB_PARAMETER);
        PushKingDeeParameterDTO parameterDTO = NsyJacksonUtils.toObj(jobParameter, PushKingDeeParameterDTO.class);

        if (Objects.isNull(parameterDTO)) {
            throw new BusinessServiceException("parameters is empty");
        }
        if (StringUtils.isBlank(parameterDTO.getOrderType())) {
            throw new BusinessServiceException("orderType is empty");
        }
        processOrder(parameterDTO);
    }

    protected void processOrder(PushKingDeeParameterDTO parameterDTO) {
        Integer fetchCount = Objects.nonNull(parameterDTO.getFetchCount()) ? parameterDTO.getFetchCount() : 1000;
        String startDate = parameterDTO.getStartDate();
        String orderType = parameterDTO.getOrderType();
        Date currentDate = DateUtil.beginOfDay(DateUtil.offset(new Date(), DateField.DAY_OF_MONTH, 1));

        PlatformOrderPushKingDeeService platformOrderPushKingDeeService = platformOrderPushKingDeeServiceFactory.getService(orderType);

        Date date;
        String cacheDate = platformOrderPushKingDeeService.getCacheDate();

        if (StringUtils.isBlank(startDate) && StringUtils.isNotBlank(cacheDate)) {
            date = DateUtil.parseDate(cacheDate);
        } else if (StringUtils.isNotBlank(startDate)) {
            date = DateUtil.parseDate(startDate);
        } else {
            date = DateUtil.parseDate(platformOrderPushKingDeeService.getControlTime());
        }

        Date businessStarDate = DateUtil.beginOfDay(date);
        Date businessEndDate = DateUtil.endOfDay(date);
        while (businessStarDate.before(currentDate)) {
            List<PlatformOrderPushKingdeeQueueEntity> pushQueueEntityList = platformOrderPushKingDeeService.listData(businessStarDate, businessEndDate, fetchCount);
            List<String> platformOrderNoList = pushQueueEntityList.stream().map(PlatformOrderPushKingdeeQueueEntity::getPlatformOrderNo).collect(Collectors.toList());
            try {
                if (NsyCollUtil.isNotEmpty(pushQueueEntityList) && NsyCollUtil.isNotEmpty(platformOrderNoList)) {
                    List<PlatformOrderEntity> platformOrderEntityList = platformOrderService.getByPlatformOrderNoList(platformOrderNoList);
                    if (NsyCollUtil.isNotEmpty(platformOrderEntityList)) {
                        pushOrder(platformOrderPushKingDeeService, platformOrderEntityList);
                    }
                } else {
                    businessStarDate = DateUtil.beginOfDay(DateUtil.offset(businessStarDate, DateField.DAY_OF_MONTH, 1));
                    businessEndDate = DateUtil.endOfDay(DateUtil.offset(businessEndDate, DateField.DAY_OF_MONTH, 1));
                }
            } catch (Exception e) {
                LOGGER.error(String.format("推送金蝶订单失败，pushType: %s，pushOrderList: %s, msg: %s", platformOrderPushKingDeeService.pushKingDeeTypeEnum().name(), NsyJacksonUtils.toJson(platformOrderNoList), e.getMessage()), e);
                businessStarDate = DateUtil.beginOfDay(DateUtil.offset(businessStarDate, DateField.DAY_OF_MONTH, 1));
                businessEndDate = DateUtil.endOfDay(DateUtil.offset(businessEndDate, DateField.DAY_OF_MONTH, 1));
            }
        }

        // 更新缓存时间
        platformOrderPushKingDeeService.setCacheDate(DateUtil.format(DateUtil.compare(businessStarDate, new Date()) > 0 ? new Date() : businessStarDate, DateUtils.DATE_FORMAT_DATE4));
    }

    protected abstract void pushOrder(PlatformOrderPushKingDeeService platformOrderPushKingDeeService, List<PlatformOrderEntity> orderList) throws Exception;

}
