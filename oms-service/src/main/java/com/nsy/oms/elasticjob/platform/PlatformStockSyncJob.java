package com.nsy.oms.elasticjob.platform;

import com.nsy.api.core.apicore.exception.InvalidRequestException;
import com.nsy.oms.business.service.platform.stock.TikTokSyncService;
import com.nsy.oms.business.service.platform.stock.base.PlatformSyncBaseService;
import com.nsy.oms.elasticjob.base.BaseSimpleJob;
import com.nsy.oms.enums.sa.PlatformEnum;
import com.nsy.oms.repository.dao.sa.SaStoreDao;
import com.nsy.oms.repository.entity.sa.SaStoreEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: linCheng
 * @create: 2025-04-07 15:29
 **/
@Slf4j
@Component
public class Platform<PERSON>tockSyncJob extends BaseSimpleJob {

    @Autowired
    private TikTokSyncService tikTokSyncService;
    @Autowired
    private SaStoreDao saStoreDao;


    @Override
    protected void run(Map<String, Object> jobDataMap) throws Exception {
        int shardingTotalCount = (int) jobDataMap.get("shardingTotalCount");
        int shardingItem = (int) jobDataMap.get("shardingItem");
        this.sync(shardingTotalCount, shardingItem);
    }

    public void sync(int shardingTotalCount, int shardingItem) {
        List<SaStoreEntity> storeEntities = getShardingStores(shardingTotalCount, shardingItem);
        storeEntities.forEach(storeEntity -> {
            try {
                PlatformSyncBaseService baseService = getRealService(PlatformEnum.valueOf(storeEntity.getPlatformName().toUpperCase(Locale.ROOT)));
                baseService.syncStock(storeEntity);
            } catch (Exception e) {
                log.error("PlatformStockSyncJob.syncStock.error.storeId:{}.errorMessage:{}", storeEntity.getId(), e.getMessage(), e);
            }
        });
    }

    private PlatformSyncBaseService getRealService(PlatformEnum type) {
        PlatformSyncBaseService baseService;
        switch (type) {
            case TIKTOK:
                baseService = tikTokSyncService;
                break;
            case AMAZON_AMERICA:
                baseService = tikTokSyncService;
                break;
            default:
                throw new InvalidRequestException("无效的请求类型");
        }
        return baseService;
    }


    private List<SaStoreEntity> getShardingStores(int shardingTotalCount, int shardingItem) {
        return saStoreDao.getByPlatformNameList(Collections.singletonList(PlatformEnum.TIKTOK.name()))
                .stream()
                .filter(store -> Integer.valueOf(1).equals(store.getStatus()))
                .filter(store -> store.getId() % shardingTotalCount == shardingItem)
                .collect(Collectors.toList());
    }


}
