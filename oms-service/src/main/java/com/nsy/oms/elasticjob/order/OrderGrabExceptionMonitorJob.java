package com.nsy.oms.elasticjob.order;

import cn.hutool.core.date.DateUtil;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.DateUtils;
import com.nsy.api.core.apicore.util.NsyCollUtil;
import com.nsy.api.core.apicore.util.NsyJacksonUtils;
import com.nsy.oms.business.domain.dto.ItemGrabExceptionDTO;
import com.nsy.oms.business.domain.dto.StoreExceptionDTO;
import com.nsy.oms.business.domain.dto.OrderGrabExceptionParameterDTO;
import com.nsy.oms.business.domain.response.auth.AmazonConfigResponse;
import com.nsy.oms.business.manage.notify.NotifyService;
import com.nsy.oms.business.service.auth.SauAmazonConfigService;
import com.nsy.oms.business.service.order.OrderItemGrabQueueService;
import com.nsy.oms.business.service.sa.SaStoreService;
import com.nsy.oms.constants.DingDingRobotConstant;
import com.nsy.oms.constants.JobConstant;
import com.nsy.oms.constants.StringConstant;
import com.nsy.oms.elasticjob.base.BaseSimpleJob;
import com.nsy.oms.enums.order.OrderTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class OrderGrabExceptionMonitorJob extends BaseSimpleJob {
    public static final String FBM_GRAB_TIME = "2小时前";
    public static final String FBA_GRAB_TIME = "1天前";
    private static final Logger LOGGER = LoggerFactory.getLogger(OrderGrabExceptionMonitorJob.class);
    @Autowired
    private SaStoreService saStoreService;
    @Autowired
    private SauAmazonConfigService sauAmazonConfigService;
    @Autowired
    private OrderItemGrabQueueService orderItemGrabQueueService;
    @Autowired
    private NotifyService notifyService;

    @Override
    protected void run(Map<String, Object> jobDataMap) throws Exception {
        LOGGER.info("OrderGrabExceptionMonitorJob start: {}", DateUtil.format(new Date(), DateUtils.DATE_FORMAT_DATE4));
        String jobParameter = (String) jobDataMap.get(JobConstant.JOB_PARAMETER);
        OrderGrabExceptionParameterDTO parameterDTO = NsyJacksonUtils.toObj(jobParameter, OrderGrabExceptionParameterDTO.class);
        checkParameter(parameterDTO);
        Date now = new Date();
        if (OrderGrabExceptionParameterDTO.STORE_EXCEPTION.equals(parameterDTO.getType())) {
            // 推送钉钉异常机器人
            // key: SEC7c1733268bb8b76570affea9086be4947b2932dd8d640060fe8b109bdeb197e7
            // token: https://oapi.dingtalk.com/robot/send?access_token=fb2180539f694ef904021d7e58b2615d58ac6c999d5df9a13acf8b0ee05c359b
            notifyService.sendStoreExceptionMsg(DingDingRobotConstant.API_OMS_STORE_GRAB_ORDER_EXCEPTION, getStoreException(now));
        } else if (OrderGrabExceptionParameterDTO.ITEM_GRAB_EXCEPTION.equals(parameterDTO.getType())) {
            notifyService.sendStoreExceptionMsg(DingDingRobotConstant.API_OMS_STORE_GRAB_ORDER_EXCEPTION, getFbmItemGrabException(now));
            notifyService.sendStoreExceptionMsg(DingDingRobotConstant.API_OMS_STORE_GRAB_ORDER_EXCEPTION, getFbaItemGrabException(now));
        }
    }

    private String getFbmItemGrabException(Date now) {
        Date fbmDate = DateUtil.offsetHour(now, -2);
        List<ItemGrabExceptionDTO> fbmGrabExceptionList = orderItemGrabQueueService.getItemFbmGrabException(fbmDate);
        if (NsyCollUtil.isEmpty(fbmGrabExceptionList)) {
            return null;
        }
        return getExceptionMsg(now, FBM_GRAB_TIME, OrderTypeEnum.FBM.getDesc(), fbmGrabExceptionList);
    }

    private String getFbaItemGrabException(Date now) {
        Date fbaDate = DateUtil.endOfDay(DateUtil.offsetDay(now, -2));
        List<ItemGrabExceptionDTO> fbaGrabExceptionList = orderItemGrabQueueService.getItemFbaGrabException(fbaDate);
        if (NsyCollUtil.isEmpty(fbaGrabExceptionList)) {
            return null;
        }
        return getExceptionMsg(now, FBA_GRAB_TIME, OrderTypeEnum.FBA.getDesc(), fbaGrabExceptionList);
    }

    private String getExceptionMsg(Date now, String grabTime, String type, List<ItemGrabExceptionDTO> grabExceptionList) {
        StringBuilder sb = new StringBuilder(2000);
        sb.append(DateUtil.format(now, DateUtils.DATE_FORMAT_DATE))
            .append(type)
            .append(grabTime)
            .append("未抓取情况：\n\n异常店铺数：")
            .append(grabExceptionList.size())
            .append("\n\n| 店铺Id | item未抓取的数量 | 最早item时间 |\n\n");

        grabExceptionList.forEach(exception ->
            sb.append(exception.getStoreId()).append(StringConstant.COMMA)
                .append(exception.getTotal()).append(StringConstant.COMMA)
                .append(DateUtil.format(exception.getMinDate(), DateUtils.DATE_FORMAT_DATE4))
                .append("\n\n")
        );
        return sb.toString();
    }


    private String getStoreException(Date now) {
        List<StoreExceptionDTO> storeList = saStoreService.getStoreException();
        if (NsyCollUtil.isEmpty(storeList)) {
            return null;
        }
        StringBuilder sb = new StringBuilder(2000);
        List<StoreExceptionDTO> exceptionStoreList = new ArrayList<>();
        Date halfHour = DateUtil.offsetMinute(now, -30);
        storeList.forEach(store -> {
            AmazonConfigResponse response = sauAmazonConfigService.getInfoResponse(store.getStoreId());
            if (Objects.nonNull(response) && Objects.nonNull(response.getCurrentCatchDate()) && response.getCurrentCatchDate().before(halfHour)) {
                store.setCurrentCatchDate(response.getCurrentCatchDate());
                exceptionStoreList.add(store);
            }
        });
        sb.append(DateUtil.format(now, DateUtils.DATE_FORMAT_DATE))
                .append(" 近半小时店铺抓单时间异常：\n\n异常店铺数：")
                .append(exceptionStoreList.size());
        if (NsyCollUtil.isNotEmpty(exceptionStoreList)) {
            sb.append("\n\n| 区域 | 平台 | 店铺Id | 店铺 | 最后抓单时间 |\n\n");
            exceptionStoreList.forEach(exceptionStore ->
                sb.append(exceptionStore.getLocation()).append(StringConstant.COMMA)
                    .append(exceptionStore.getPlatformId()).append(StringConstant.COMMA)
                    .append(exceptionStore.getStoreId()).append(StringConstant.COMMA)
                    .append(exceptionStore.getStoreName()).append(StringConstant.COMMA)
                    .append(DateUtil.format(exceptionStore.getCurrentCatchDate(), DateUtils.DATE_FORMAT_DATE4)).append("\n\n")
            );
        }
        return sb.toString();
    }

    private void checkParameter(OrderGrabExceptionParameterDTO parameterDTO) {
        if (Objects.isNull(parameterDTO)) {
            throw new BusinessServiceException("StoreExceptionMonitorJob参数有误!");
        }
        if (StringUtils.isBlank(parameterDTO.getLocation())) {
            throw new BusinessServiceException("OrderGrabExceptionMonitorJob：location参数为空!");
        }
        if (StringUtils.isBlank(parameterDTO.getType())) {
            throw new BusinessServiceException("OrderGrabExceptionMonitorJob：type参数为空!");
        }
    }
}
