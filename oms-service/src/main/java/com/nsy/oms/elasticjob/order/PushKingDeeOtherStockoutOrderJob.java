package com.nsy.oms.elasticjob.order;

import com.nsy.api.core.apicore.util.NsyJacksonUtils;
import com.nsy.oms.business.service.platform.PlatformOrderPushKingDeeService;
import com.nsy.oms.business.service.platform.PlatformOrderPushKingdeeQueueService;
import com.nsy.oms.enums.order.OrderPushStatusEnum;
import com.nsy.oms.repository.entity.platform.PlatformOrderEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class PushKingDeeOtherStockoutOrderJob extends PushOrderToKingDeeSuperJob {
    private static final Logger LOGGER = LoggerFactory.getLogger(PushKingDeeOtherStockoutOrderJob.class);
    @Autowired
    private PlatformOrderPushKingdeeQueueService pushKingdeeQueueService;

    @Override
    protected void pushOrder(PlatformOrderPushKingDeeService platformOrderPushKingDeeService, List<PlatformOrderEntity> orderList) {
        orderList.forEach(orderEntity -> {
            try {
                // 推送单据到金蝶
                platformOrderPushKingDeeService.pushOrderToKingDee(orderEntity);
                pushKingdeeQueueService.updateStatusByPlatformOrderNo(orderEntity.getPlatformOrderNo(), OrderPushStatusEnum.PUSHED.getCode());
            } catch (Exception e) {
                LOGGER.error(String.format("PushKingDeeOtherStockoutOrderJob, 推送金蝶其他出库单失败，storeId: %s，pushType: %s，pushOrder: %s，msg: %s", orderEntity.getStoreId(), platformOrderPushKingDeeService.pushKingDeeTypeEnum().name(), NsyJacksonUtils.toJson(orderEntity), e.getMessage()), e);
            }
        });
    }
}
