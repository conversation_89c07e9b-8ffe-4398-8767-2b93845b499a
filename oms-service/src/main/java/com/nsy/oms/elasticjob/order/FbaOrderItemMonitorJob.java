package com.nsy.oms.elasticjob.order;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.nsy.api.core.apicore.constant.enums.QueueStatusEnum;
import com.nsy.api.core.apicore.util.NsyCollUtil;
import com.nsy.api.core.apicore.util.NsyJacksonUtils;
import com.nsy.api.core.apicore.util.PageUtils;
import com.nsy.oms.business.domain.dto.BaseParameterDTO;
import com.nsy.oms.business.service.order.OrderItemGrabQueueService;
import com.nsy.oms.constants.JobConstant;
import com.nsy.oms.constants.MybatisQueryConstant;
import com.nsy.oms.elasticjob.base.BaseSimpleJob;
import com.nsy.oms.enums.order.OrderTypeEnum;
import com.nsy.oms.repository.entity.order.OrderItemGrabQueueEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class FbaOrderItemMonitorJob extends BaseSimpleJob {
    private static final Logger LOGGER = LoggerFactory.getLogger(FbaOrderItemMonitorJob.class);
    @Autowired
    private OrderItemGrabQueueService orderItemGrabQueueService;

    @Override
    protected void run(Map<String, Object> jobDataMap) throws Exception {
        String jobParameter = (String) jobDataMap.get(JobConstant.JOB_PARAMETER);
        BaseParameterDTO parameterDTO = NsyJacksonUtils.toObj(jobParameter, BaseParameterDTO.class);
        // 昨天
        Date date = DateUtil.endOfDay(DateUtil.offsetDay(new Date(), -1));

        LambdaQueryWrapper<OrderItemGrabQueueEntity> queryWrapper = Wrappers.<OrderItemGrabQueueEntity>lambdaQuery()
            .eq(OrderItemGrabQueueEntity::getOrderType, OrderTypeEnum.FBA.getCode())
            .le(OrderItemGrabQueueEntity::getCreateDate, date)
            .and(wrapper ->
                wrapper
                    .eq(OrderItemGrabQueueEntity::getQueueStatus, QueueStatusEnum.INIT.getCode())
                    .or(wrapper1 -> wrapper1
                        .eq(OrderItemGrabQueueEntity::getQueueStatus, QueueStatusEnum.EXECUTE_FAIL.getCode())
                        .ge(OrderItemGrabQueueEntity::getRetryCount, 3)
                    )
            );

        Long count = orderItemGrabQueueService.count(queryWrapper);

        if (count == 0) {
            return;
        }

        Integer maxId = 0;
        int intCount = count == null ? 0 : count.intValue();
        for (int i = 0; i < PageUtils.calculatePageCount(intCount, parameterDTO.getFetchCount()); i++) {
            List<OrderItemGrabQueueEntity> list = orderItemGrabQueueService.list(
                    queryWrapper
                    .gt(OrderItemGrabQueueEntity::getItemGrabQueueId, maxId)
                    .orderByAsc(OrderItemGrabQueueEntity::getItemGrabQueueId)
                    .last(String.format(MybatisQueryConstant.LIMIT, parameterDTO.getFetchCount()))
            );
            if (NsyCollUtil.isEmpty(list)) {
                return;
            }

            maxId = list.get(list.size() - 1).getItemGrabQueueId();

            try {
                list.forEach(queue -> {
                    if (queue.getRetryCount() >= 3) {
                        queue.setRetryCount(2);
                    }
                    queue.setQueueStatus(QueueStatusEnum.EXECUTE_FAIL.getCode());
                    queue.setUpdateBy(this.getJobName());
                });
                orderItemGrabQueueService.updateBatchById(list);
            } catch (Exception e) {
                LOGGER.error(String.format("FbaOrderItemMonitorJob error：maxId：%s, msg：%s", maxId, e.getMessage()));
            }
        }
    }
}
