package com.nsy.oms.elasticjob.sa;


import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.NsyCollUtil;
import com.nsy.api.core.apicore.util.NsyJacksonUtils;
import com.nsy.oms.business.domain.dto.EmailParameterDTO;
import com.nsy.oms.business.manage.notify.NotifyApiService;
import com.nsy.oms.business.service.bd.BdEmailService;
import com.nsy.oms.elasticjob.base.BaseSimpleJob;
import com.nsy.oms.enums.bd.MediaAccountNoticeUserEnum;
import com.nsy.oms.repository.entity.bd.BdEmailEntity;
import com.nsy.oms.utils.mp.LocationContext;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/1/10 13:54
 */
@Component
public class CheckEmailIsExpireJob extends BaseSimpleJob {
    private static final Logger LOGGER = LoggerFactory.getLogger(CheckEmailIsExpireJob.class);
    @Autowired
    private BdEmailService bdEmailService;
    @Autowired
    private NotifyApiService notifyApiService;


    private void checkParameter(EmailParameterDTO emailParameterDTO) {
        if (StringUtils.isBlank(emailParameterDTO.getLocation())) {
            throw new BusinessServiceException("CheckEmailIsExpireJob：location参数为空!");
        }
    }

    @Override
    protected void run(Map<String, Object> jobDataMap) {
        LOGGER.info("CheckEmailIsExpireJob ---------start,{}", JSONUtil.toJsonStr(jobDataMap));
        EmailParameterDTO emailParameterDTO = NsyJacksonUtils.toObj(jobDataMap.get("jobParameter").toString(), EmailParameterDTO.class);
        checkParameter(emailParameterDTO);

        // 提醒：XX邮箱 续费还有XX天 即将到期，请注意续费。钉钉推送给：林珈羽。（到期7天前提醒）
        LocationContext.setLocation(emailParameterDTO.getLocation());
        Date endDate = DateUtil.offsetDay(DateUtil.beginOfDay(new Date()), 7);
        List<BdEmailEntity> emailEntities = bdEmailService.getEmailListByDate(DateUtil.beginOfDay(new Date()), endDate);
        if (!NsyCollUtil.isEmpty(emailEntities)) {
            emailEntities.forEach(item -> {
                String content = String.format("提醒：%s邮箱,还有%s天即将到期,请注意续费.", item.getEmail(), DateUtil.between(new Date(), item.getExpiryDate(), DateUnit.DAY));
                List<String> userList = new ArrayList<>();
                userList.add(MediaAccountNoticeUserEnum.LIANZHEN.getCode());
                notifyApiService.sendMarkdownDingTalkMessage("邮件到期提醒", content, userList);
            });
        }

        LOGGER.info("CheckEmailIsExpireJob -----------end");
    }


}
