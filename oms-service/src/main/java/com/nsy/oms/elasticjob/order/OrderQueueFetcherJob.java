package com.nsy.oms.elasticjob.order;

import cn.hutool.core.date.DateUtil;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.nsy.api.core.apicore.constant.enums.QueueStatusEnum;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.DateUtils;
import com.nsy.api.core.apicore.util.NsyCollUtil;
import com.nsy.api.core.apicore.util.NsyJacksonUtils;
import com.nsy.api.transfer.constants.AmazonPlatformConstant;
import com.nsy.oms.business.domain.dto.OrderGrabParameterDTO;
import com.nsy.oms.business.service.order.OrderMissedQueueService;
import com.nsy.oms.business.service.platform.PlatformBaseService;
import com.nsy.oms.business.service.sa.SaStoreService;
import com.nsy.oms.elasticjob.base.BaseDataFlowJob;
import com.nsy.oms.repository.entity.order.OrderMissedQueueEntity;
import com.nsy.oms.repository.entity.sa.SaStoreEntity;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 平台抓单Job--按订单号抓单
 */
@Component
public class OrderQueueFetcherJob extends BaseDataFlowJob<OrderMissedQueueEntity> {
    private static final Logger LOGGER = LoggerFactory.getLogger(OrderQueueFetcherJob.class);
    @Autowired
    private PlatformBaseService platformBaseService;
    @Autowired
    private SaStoreService saStoreService;
    @Autowired
    private OrderMissedQueueService orderMissedQueueService;
    @Override
    protected List<OrderMissedQueueEntity> doFetchData(ShardingContext shardingContext) throws Exception {
        LOGGER.info("OrderQueueFetcherJob start, shardingItem: {}, jobParameter: {}", shardingContext.getShardingItem(), DateUtil.format(new Date(), DateUtils.DATE_FORMAT_DATE4));
        String jobParameter = shardingContext.getJobParameter();
        OrderGrabParameterDTO orderGrabParameterDTO = NsyJacksonUtils.toObj(jobParameter, OrderGrabParameterDTO.class);
        checkParameter(orderGrabParameterDTO);
        List<Integer> notInStoreIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(orderGrabParameterDTO.getNotInStoreIds())) {
            notInStoreIdList = Arrays.stream(StringUtils.splitByWholeSeparator(orderGrabParameterDTO.getNotInStoreIds(), ","))
                    .filter(StringUtils::isNotBlank) // 过滤掉空字符串
                    .map(Integer::parseInt) // 将字符串转换为整数
                    .collect(Collectors.toList());
        }
        List<OrderMissedQueueEntity> orderMissedQueueEntityList = orderMissedQueueService.getOrderQueueList(orderGrabParameterDTO.getPlatform(), orderGrabParameterDTO.getStoreId(), orderGrabParameterDTO.getLocation(), notInStoreIdList, orderGrabParameterDTO.getFetchCount()); // 根据平台名称和区域查询符合条件的抓单店铺
        if (NsyCollUtil.isEmpty(orderMissedQueueEntityList)) {
            return Collections.emptyList();
        }
        // 过滤掉非亚马逊平台的队列
        orderMissedQueueEntityList = orderMissedQueueEntityList.stream()
                .filter(queue -> {
                    SaStoreEntity storeEntity = saStoreService.getById(queue.getStoreId());
                    if (storeEntity == null) {
                        return false;
                    }
                    Integer platformId = storeEntity.getSecondPlatformId() > 0 ? storeEntity.getSecondPlatformId() : storeEntity.getPlatformId();
                    return !AmazonPlatformConstant.AMAZON_PLATFORM_ID_LIST.contains(platformId);
                })
                .collect(Collectors.toList());
        if (NsyCollUtil.isEmpty(orderMissedQueueEntityList)) {
            return null;
        }
        List<OrderMissedQueueEntity> partitionList = orderMissedQueueEntityList.stream()
                .filter(orderMissedQueueEntity -> orderMissedQueueEntity.getOrderMissedId() % shardingContext.getShardingTotalCount() == shardingContext.getShardingItem())
                .collect(Collectors.toList());
        if (NsyCollUtil.isEmpty(partitionList)) {
            return Collections.emptyList();
        }
        partitionList.forEach(orderMissedQueueEntity -> {
            orderMissedQueueEntity.setQueueStatus(QueueStatusEnum.EXECUTING.getCode());
            orderMissedQueueEntity.setUpdateBy("OrderQueueFetcherJob");
        });
        orderMissedQueueService.updateBatchById(partitionList);
        return partitionList;
    }

    @Override
    protected void doProcessData(ShardingContext shardingContext, List<OrderMissedQueueEntity> data) {
        if (NsyCollUtil.isEmpty(data)) {
            return;
        }
        try {
            List<Integer> storeIds = data.stream().map(OrderMissedQueueEntity::getStoreId).distinct().collect(Collectors.toList());
            List<SaStoreEntity> storeList = saStoreService.listByIds(storeIds);
            Map<Integer, List<OrderMissedQueueEntity>> storeOrderQueueMap = data.stream().collect(Collectors.groupingBy(OrderMissedQueueEntity::getStoreId)); //按店铺分组抓订单信息
            storeOrderQueueMap.forEach((storeId, queueList) -> {
                SaStoreEntity storeEntity = storeList.stream().filter(store -> storeId.equals(store.getId())).findFirst().orElseThrow(() -> new BusinessServiceException(String.format("查询不到平台ID, 店铺id: %s", storeId)));
                platformBaseService.getOrderListByIdList(storeEntity, queueList); //抓订单信息
            });
        } catch (BusinessServiceException bse) {
            LOGGER.error(String.format("OrderQueueFetcherJob business error, shardingItem: %s, msg: %s", shardingContext.getShardingItem(), bse.getMessage()), bse);
            throw bse;
        } catch (Exception e) {
            LOGGER.error(String.format("OrderQueueFetcherJob unexpected error, shardingItem: %s, msg: %s", shardingContext.getShardingItem(), e.getMessage()), e);
            throw new BusinessServiceException(e.getMessage());
        } finally {
            LOGGER.info("OrderQueueFetcherJob end, shardingItem: {}, endTime: {}", shardingContext.getShardingItem(), DateUtil.format(new Date(), DateUtils.DATE_FORMAT_DATE4));
        }
    }

    /**
     * 参数检查
     */
    private void checkParameter(OrderGrabParameterDTO orderGrabParameterDTO) {
        if (Objects.isNull(orderGrabParameterDTO)) {
            throw new BusinessServiceException("OrderQueueFetcherJob 参数有误!");
        }
        if (StringUtils.isBlank(orderGrabParameterDTO.getLocation())) {
            throw new BusinessServiceException("OrderQueueFetcherJob：location参数为空!");
        }
        if (Objects.isNull(orderGrabParameterDTO.getFetchCount()) || orderGrabParameterDTO.getFetchCount() <= 0) {
            throw new BusinessServiceException("OrderQueueFetcherJob：fetchCount参数不合法!");
        }
    }
}
