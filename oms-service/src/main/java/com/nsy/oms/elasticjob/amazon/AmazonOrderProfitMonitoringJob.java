package com.nsy.oms.elasticjob.amazon;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.nsy.oms.business.manage.bi.BiApiService;
import com.nsy.oms.business.manage.bi.domain.BiDataBackFlowRecord;
import com.nsy.oms.business.manage.bi.request.BiBackFlowDataRequest;
import com.nsy.oms.business.manage.bi.response.fact.FactAmazonProfitabilityAnalysisSpuResponse;
import com.nsy.oms.business.manage.pms.PmsApiService;
import com.nsy.oms.business.manage.pms.response.ProductSeasonInfo;
import com.nsy.oms.business.manage.search.request.BaseListRequest;
import com.nsy.oms.business.service.amazon.AmazonOrderProfitMonitoringSyncService;
import com.nsy.oms.elasticjob.base.BaseSimpleJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 亚马逊订单利润率监控数据计算
 */
@Slf4j
@Component
public class AmazonOrderProfitMonitoringJob extends BaseSimpleJob {
    private static final Logger LOGGER = LoggerFactory.getLogger(AmazonOrderProfitMonitoringJob.class);
    private static final Integer LIMIT_PAGE = 2000;
    private static final List<String> TABLE_NAME_LIST = Lists.newArrayList("fact_amazon_profitability_analysis_spu", "fact_amazon_profitability_analysis_skc", "fact_amazon_profitability_analysis_sku");
    @Autowired
    private BiApiService biApiService;
    @Autowired
    private AmazonOrderProfitMonitoringSyncService amazonOrderProfitMonitoringSyncService;
    @Autowired
    private PmsApiService pmsApiService;

    @Override
    protected void run(Map<String, Object> jobDataMap) throws Exception {
        BiBackFlowDataRequest biBackFlowDataRequest = new BiBackFlowDataRequest();
        biBackFlowDataRequest.setType("amazon_order_profit_monitoring");
        biBackFlowDataRequest.setTableNames(TABLE_NAME_LIST);
        List<BiDataBackFlowRecord> recordList = biApiService.getBiDataBackFlowRecordList(biBackFlowDataRequest);
        // 业务表最新的数据时间
        Date maxDataUpdateDate = amazonOrderProfitMonitoringSyncService.getMaxUpdateDate();
        // 业务表最新的数据时间为空（第一次跑），或者3个bi表的回流时间都大于业务表最新的数据时间，则认为可以重跑数据
        boolean canUpdate = maxDataUpdateDate == null || TABLE_NAME_LIST.stream()
                .allMatch(t -> recordList.stream().filter(e -> t.equals(e.getTablename()))
                        .max(Comparator.comparing(BiDataBackFlowRecord::getCreateDate)).filter(e -> maxDataUpdateDate.before(e.getCreateDate())).isPresent());
        if (!canUpdate) {
            return;
        }
        String batchNo = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        BaseListRequest request = new BaseListRequest();
        request.setPageSize(100);
        for (int pageNumber = 1; pageNumber <= LIMIT_PAGE; pageNumber++) {
            request.setPageIndex(pageNumber);
            List<FactAmazonProfitabilityAnalysisSpuResponse> responseList = biApiService.getFactAmazonProfitabilityAnalysisSpuList(request);
            if (CollectionUtils.isEmpty(responseList)) {
                break;
            }
            Map<String, ProductSeasonInfo> seasonMap = pmsApiService.getProductSeasonLabelInfoList(responseList.stream().map(e -> e.getSpu().getSpu()).collect(Collectors.toList()))
                    .stream().collect(Collectors.toMap(ProductSeasonInfo::getSpu, Function.identity(), (k1, k2) -> k1));
            for (FactAmazonProfitabilityAnalysisSpuResponse response : responseList) {
                try {
                    amazonOrderProfitMonitoringSyncService.syncSpu(response.getSpu(), seasonMap, batchNo);
                    amazonOrderProfitMonitoringSyncService.syncSkcAndSku(response);
                } catch (Exception e) {
                    LOGGER.error(response.getSpu().getSpu() + "亚马逊订单利润率监控数据计算失败,错误信息:" + e.getMessage(), e);
                }
            }
        }
        // spu级别全部处理完之后，删除非本批次的spu
        amazonOrderProfitMonitoringSyncService.removeNotCurrentBatchSpu(batchNo);
    }


}
