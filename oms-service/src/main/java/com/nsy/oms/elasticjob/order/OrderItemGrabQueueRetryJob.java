package com.nsy.oms.elasticjob.order;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.nsy.api.core.apicore.constant.enums.QueueStatusEnum;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.DateUtils;
import com.nsy.api.core.apicore.util.NsyCollUtil;
import com.nsy.api.core.apicore.util.NsyJacksonUtils;
import com.nsy.oms.business.domain.dto.ItemRetryParameterDTO;
import com.nsy.oms.business.manage.amazon.AmazonApiService;
import com.nsy.oms.business.manage.amazon.request.ReportGetFlatFileAllOrdersDataRequest;
import com.nsy.oms.business.manage.amazon.response.ReportGetFlatFileAllOrdersDataResponse;
import com.nsy.oms.business.service.order.OrderGrabStatusService;
import com.nsy.oms.business.service.order.OrderItemGrabQueueService;
import com.nsy.oms.business.service.order.OrderMissedQueueService;
import com.nsy.oms.business.service.platform.PlatformOrderItemService;
import com.nsy.oms.business.service.platform.PlatformOrderService;
import com.nsy.oms.constants.JobConstant;
import com.nsy.oms.constants.MybatisQueryConstant;
import com.nsy.oms.elasticjob.base.BaseSimpleJob;
import com.nsy.oms.enums.CommonStateEnum;
import com.nsy.oms.enums.order.ItemStatusEnum;
import com.nsy.oms.enums.order.OrderTypeEnum;
import com.nsy.oms.enums.platform.PlatformOrderStatusEnum;
import com.nsy.oms.repository.entity.order.OrderGrabStatusEntity;
import com.nsy.oms.repository.entity.order.OrderItemGrabQueueEntity;
import com.nsy.oms.repository.entity.platform.PlatformOrderEntity;
import com.nsy.oms.repository.entity.platform.PlatformOrderItemEntity;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @description: 亚马逊抓单改造-获取详情失败重试JOB
 * @author: linCheng
 * @create: 2024-06-21 10:55
 **/
@Component
public class OrderItemGrabQueueRetryJob extends BaseSimpleJob {
    private static final Logger LOGGER = LoggerFactory.getLogger(OrderItemGrabQueueRetryJob.class);
    @Autowired
    private PlatformOrderService platformOrderService;
    @Autowired
    private PlatformOrderItemService platformOrderItemService;
    @Autowired
    private OrderItemGrabQueueService orderItemGrabQueueService;
    @Autowired
    private OrderGrabStatusService orderGrabStatusService;
    @Autowired
    private AmazonApiService amazonApiService;
    @Autowired
    private OrderMissedQueueService orderMissedQueueService;

    @Override
    protected void run(Map<String, Object> jobDataMap) throws Exception {
        String jobParameter = (String) jobDataMap.get(JobConstant.JOB_PARAMETER);
        ItemRetryParameterDTO parameterDTO = NsyJacksonUtils.toObj(jobParameter, ItemRetryParameterDTO.class);
        Date now = new Date();
        Date currentDate = DateUtil.beginOfDay(DateUtil.offset(now, DateField.DAY_OF_MONTH, 1));

        Date date = DateUtil.offset(now, DateField.DAY_OF_MONTH, -7);
        if (StringUtils.isNotBlank(parameterDTO.getStartDate())) {
            date = DateUtil.parseDate(parameterDTO.getStartDate());
        }

        Date businessStarDate = DateUtil.beginOfDay(date);
        Date businessEndDate = DateUtil.endOfDay(date);

        while (businessStarDate.before(currentDate)) {
            List<OrderItemGrabQueueEntity> list = orderItemGrabQueueService
                    .list(Wrappers.<OrderItemGrabQueueEntity>lambdaQuery()
                            .in(OrderItemGrabQueueEntity::getQueueStatus, QueueStatusEnum.EXECUTE_FAIL.getCode())
                            .eq(OrderItemGrabQueueEntity::getOrderType, OrderTypeEnum.FBA.getCode())
                            .between(OrderItemGrabQueueEntity::getCreateDate, businessStarDate, businessEndDate)
                            .lt(OrderItemGrabQueueEntity::getRetryCount, 3)
                            .last(String.format(MybatisQueryConstant.LIMIT, parameterDTO.getFetchCount())));
            try {
                if (NsyCollUtil.isNotEmpty(list)) {
                    processingData(list);
                } else {
                    businessStarDate = DateUtil.beginOfDay(DateUtil.offset(businessStarDate, DateField.DAY_OF_MONTH, 1));
                    businessEndDate = DateUtil.endOfDay(DateUtil.offset(businessEndDate, DateField.DAY_OF_MONTH, 1));
                }
            } catch (Exception e) {
                LOGGER.error(String.format("OrderItemGrabQueueRetryJob，error: businessDate: %s, msg: %s", DateUtil.format(businessStarDate, DateUtils.DATE_FORMAT_DATE4), e.getMessage()), e);
                businessStarDate = DateUtil.beginOfDay(DateUtil.offset(businessStarDate, DateField.DAY_OF_MONTH, 1));
                businessEndDate = DateUtil.endOfDay(DateUtil.offset(businessEndDate, DateField.DAY_OF_MONTH, 1));
            }
        }
    }

    public void processingData(List<OrderItemGrabQueueEntity> list) {
        Map<Integer, Map<Integer, List<OrderItemGrabQueueEntity>>> orderItemGrabQueueMap = list.stream()
                .collect(Collectors.groupingBy(OrderItemGrabQueueEntity::getStoreId, Collectors.groupingBy(OrderItemGrabQueueEntity::getOrderType)));

        // 处理数据
        orderItemGrabQueueMap.forEach((storeId, orderItemGrabQueueByOrderTypeMap) -> {
            orderItemGrabQueueByOrderTypeMap.forEach((orderType, orderItemGrabQueueEntities) -> {
                try {
                    retry(storeId, orderType, orderItemGrabQueueEntities);
                } catch (Exception e) {
                    String errorMsg = String.format("店铺ID: %s，抓取详情重试失败, 请求体：%s，错误：%s", storeId, NsyJacksonUtils.toJson(orderItemGrabQueueEntities), e.getMessage());
                    LOGGER.error(errorMsg, e);
                }
            });
        });
    }


    public void retry(Integer storeId, Integer orderType, List<OrderItemGrabQueueEntity> orderItemGrabQueueEntities) {

        List<String> orderNos = orderItemGrabQueueEntities.stream().map(OrderItemGrabQueueEntity::getOrderNo).distinct().collect(Collectors.toList());

        List<PlatformOrderEntity> platformOrderEntities = platformOrderService.getListByStoreIdAndOrderNos(storeId, orderNos);
        Map<String, PlatformOrderEntity> orderMap = platformOrderEntities.stream().collect(Collectors.toMap(PlatformOrderEntity::getPlatformOriginalOrderNo, a -> a, (k1, k2) -> k1));

        List<PlatformOrderItemEntity> platformOrderItemServiceListByOrderIds = platformOrderItemService.getListByOrderIds(platformOrderEntities.stream().map(PlatformOrderEntity::getPlatformOrderId).collect(Collectors.toList()));
        Map<Integer, List<PlatformOrderItemEntity>> orderItemMap = platformOrderItemServiceListByOrderIds.stream().collect(Collectors.groupingBy(PlatformOrderItemEntity::getPlatformOrderId));

        List<OrderGrabStatusEntity> orderGrabStatusEntities = orderGrabStatusService.getListBystoreIdAndOrderNos(storeId, orderNos);
        Map<String, OrderGrabStatusEntity> orderGrabStatusMap = orderGrabStatusEntities.stream().collect(Collectors.toMap(OrderGrabStatusEntity::getOrderNo, a -> a, (k1, k2) -> k1));

        List<ReportGetFlatFileAllOrdersDataResponse> reportGetFlatFileAllOrdersData = amazonApiService.getReportGetFlatFileAllOrdersData(new ReportGetFlatFileAllOrdersDataRequest(storeId, orderType, orderNos));
        Map<String, List<ReportGetFlatFileAllOrdersDataResponse>> reportGetFlatFileAllOrdersDataMap = reportGetFlatFileAllOrdersData
                .stream()
                .collect(Collectors.groupingBy(ReportGetFlatFileAllOrdersDataResponse::getAmazonOrderId));

        for (OrderItemGrabQueueEntity orderItemGrabQueueEntity : orderItemGrabQueueEntities) {
            try {
                PlatformOrderEntity platformOrderEntity = Optional.ofNullable(orderMap.get(orderItemGrabQueueEntity.getOrderNo())).orElseThrow(() -> new BusinessServiceException("订单:" + orderItemGrabQueueEntity.getOrderNo() + "订单表数据不存在"));
                OrderGrabStatusEntity orderGrabStatusEntity = Optional.ofNullable(orderGrabStatusMap.get(orderItemGrabQueueEntity.getOrderNo())).orElseThrow(() -> new BusinessServiceException("订单:" + orderItemGrabQueueEntity.getOrderNo() + "抓单状态表数据不存在"));
                List<PlatformOrderItemEntity> platformOrderItemEntities = orderItemMap.get(platformOrderEntity.getPlatformOrderId());
                if (PlatformOrderStatusEnum.CANCEL.getCode().equals(platformOrderEntity.getOrderStatus())) {
                    if (CollectionUtils.isNotEmpty(platformOrderItemEntities)) {
                        platformOrderItemEntities.forEach(platformOrderItemEntity -> {
                            platformOrderItemEntity.setItemStatus(ItemStatusEnum.DELETE.getCode());
                            platformOrderItemEntity.setUpdateDate(new Date());
                            platformOrderItemEntity.setUpdateBy(this.getJobName());
                        });
                    }
                    setOrderGrabStatusEntity(orderGrabStatusEntity, CommonStateEnum.YES.getCode());
                    setOrderItemGrabQueueEntity(orderItemGrabQueueEntity, String.format("订单状态为取消,状态改成:%s", QueueStatusEnum.IGNORE.getDescription()), QueueStatusEnum.IGNORE.getCode());
                    saveOrUpdate(orderItemGrabQueueEntity, orderGrabStatusEntity, null, platformOrderItemEntities);
                    orderMissedQueueService.updateQueueStatusByOrderId(QueueStatusEnum.EXECUTE_SUCCESS.getCode(), orderItemGrabQueueEntity.getOrderNo(), orderItemGrabQueueEntity.getStoreId());
                } else if (NsyCollUtil.isNotEmpty(reportGetFlatFileAllOrdersDataMap) && NsyCollUtil.isNotEmpty(reportGetFlatFileAllOrdersDataMap.get(orderItemGrabQueueEntity.getOrderNo()))) {
                    List<ReportGetFlatFileAllOrdersDataResponse> reportGetFlatFileAllOrdersDataResponses = reportGetFlatFileAllOrdersDataMap.get(orderItemGrabQueueEntity.getOrderNo());
                    List<PlatformOrderItemEntity> platformOrderItemEntityList = new ArrayList<>();

                    if (NsyCollUtil.isNotEmpty(platformOrderItemEntities)) {
                        platformOrderItemEntities.forEach(existItem ->
                                reportGetFlatFileAllOrdersDataResponses.stream().filter(item ->
                                        StringUtils.isNotBlank(item.getSku()) && item.getSku().equals(existItem.getSellerSku())
                                ).findFirst().ifPresent(item -> {
                                    platformOrderItemEntityList.add(platformOrderItemService.buildPlatformOrderItemEntity(platformOrderEntity, existItem, item));
                                })
                        );
                    } else {
                        reportGetFlatFileAllOrdersDataResponses.forEach(reportGetFlatFileAllOrdersDataResponse -> {
                            platformOrderItemEntityList.add(platformOrderItemService.buildPlatformOrderItemEntity(platformOrderEntity, new PlatformOrderItemEntity(), reportGetFlatFileAllOrdersDataResponse));
                        });
                    }
                    boolean result = reportGetFlatFileAllOrdersDataResponses.stream().allMatch(response -> PlatformOrderStatusEnum.DELIVERY.getOutCode().equals(response.getOrderStatus()) || PlatformOrderStatusEnum.CANCEL.getOutCode().equals(response.getOrderStatus()));

                    setOrderGrabStatusEntity(orderGrabStatusEntity, CommonStateEnum.YES.getCode());
                    setOrderItemGrabQueueEntity(orderItemGrabQueueEntity, "已同步订单明细", result ? QueueStatusEnum.EXECUTE_SUCCESS.getCode() : QueueStatusEnum.EXECUTE_FAIL.getCode());
                    orderMissedQueueService.updateQueueStatusByOrderId(QueueStatusEnum.EXECUTE_SUCCESS.getCode(), orderItemGrabQueueEntity.getOrderNo(), orderItemGrabQueueEntity.getStoreId());
                    platformOrderService.buildPlatformOrderPrice(platformOrderEntity, reportGetFlatFileAllOrdersDataResponses);
                    saveOrUpdate(orderItemGrabQueueEntity, orderGrabStatusEntity, platformOrderEntity, platformOrderItemEntityList);
                } else {
                    setOrderItemGrabQueueEntity(orderItemGrabQueueEntity, "report is not data", QueueStatusEnum.EXECUTE_FAIL.getCode());
                    orderMissedQueueService.updateQueueStatusByOrderId(QueueStatusEnum.EXECUTE_FAIL.getCode(), orderItemGrabQueueEntity.getOrderNo(), orderItemGrabQueueEntity.getStoreId());
                    orderItemGrabQueueService.saveOrUpdate(orderItemGrabQueueEntity);
                }
            } catch (Exception e) {
                LOGGER.error(String.format("OrderItemGrabQueueRetryJob error: storeId:%s, orderNo:%s, msg: %s", storeId, orderItemGrabQueueEntity.getOrderNo(), e.getMessage()), e);
                setOrderItemGrabQueueEntity(orderItemGrabQueueEntity, e.getMessage(), QueueStatusEnum.EXECUTE_FAIL.getCode());
                orderMissedQueueService.updateQueueStatusByOrderId(QueueStatusEnum.EXECUTE_FAIL.getCode(), orderItemGrabQueueEntity.getOrderNo(), orderItemGrabQueueEntity.getStoreId());
                orderItemGrabQueueService.saveOrUpdate(orderItemGrabQueueEntity);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(OrderItemGrabQueueEntity orderItemGrabQueueEntity, OrderGrabStatusEntity orderGrabStatusEntity, PlatformOrderEntity platformOrderEntity, List<PlatformOrderItemEntity> platformOrderItemEntities) {
        orderItemGrabQueueService.saveOrUpdate(orderItemGrabQueueEntity);
        orderGrabStatusService.saveOrUpdate(orderGrabStatusEntity);
        if (CollectionUtils.isNotEmpty(platformOrderItemEntities)) {
            platformOrderItemService.saveOrUpdateBatch(platformOrderItemEntities);
        }
        if (Objects.nonNull(platformOrderEntity)) {
            platformOrderService.updateById(platformOrderEntity);
        }
    }


    private void setOrderItemGrabQueueEntity(OrderItemGrabQueueEntity orderItemGrabQueueEntity, String remark, Integer queueStatus) {
        orderItemGrabQueueEntity.setUpdateDate(new Date());
        orderItemGrabQueueEntity.setUpdateBy(this.getJobName());
        orderItemGrabQueueEntity.setQueueStatus(queueStatus);
        orderItemGrabQueueEntity.setRetryCount(orderItemGrabQueueEntity.getRetryCount() + 1);
        orderItemGrabQueueEntity.setRemark(StringUtils.isNotBlank(remark) && remark.length() > 200 ? remark.substring(0, 200) : remark);
    }

    private void setOrderGrabStatusEntity(OrderGrabStatusEntity orderGrabStatusEntity, Integer isGrabItem) {
        orderGrabStatusEntity.setUpdateDate(new Date());
        orderGrabStatusEntity.setUpdateBy(this.getJobName());
        orderGrabStatusEntity.setIsGrabItem(isGrabItem);
    }


}
