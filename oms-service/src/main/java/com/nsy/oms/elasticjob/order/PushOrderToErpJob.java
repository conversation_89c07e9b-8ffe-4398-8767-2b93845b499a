package com.nsy.oms.elasticjob.order;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.nsy.api.core.apicore.constant.enums.IsEnum;
import com.nsy.api.core.apicore.util.NsyCollUtil;
import com.nsy.api.core.apicore.util.NsyJacksonUtils;
import com.nsy.oms.business.domain.dto.PushOrderToErpDto;
import com.nsy.oms.business.domain.mq.StoreMpResponse;
import com.nsy.oms.business.domain.response.order.PlatformOrderItemResponse;
import com.nsy.oms.business.domain.response.order.PlatformOrderListResponse;
import com.nsy.oms.business.domain.response.order.PlatformOrderResponse;
import com.nsy.oms.business.service.order.SaleOrderExtendService;
import com.nsy.oms.business.service.order.OrderGrabStatusService;
import com.nsy.oms.business.service.order.SaleOrderItemService;
import com.nsy.oms.business.service.order.SaleOrderReceiverService;
import com.nsy.oms.business.service.order.SaleOrderService;
import com.nsy.oms.constants.MybatisQueryConstant;
import com.nsy.oms.elasticjob.base.BaseDataFlowJob;
import com.nsy.oms.enums.platform.PlatformOrderItemStatusEnum;
import com.nsy.oms.enums.platform.PlatformOrderStatusEnum;
import com.nsy.oms.mq.producer.MessageProducer;
import com.nsy.oms.repository.entity.order.SaleOrderEntity;
import com.nsy.oms.repository.entity.order.SaleOrderExtendEntity;
import com.nsy.oms.repository.entity.order.OrderGrabStatusEntity;
import com.nsy.oms.repository.entity.order.SaleOrderItemEntity;
import com.nsy.oms.repository.entity.order.SaleOrderReceiverEntity;
import jodd.util.StringUtil;
import org.apache.kafka.clients.admin.NewTopic;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/20 15:26
 */
@Component
public class PushOrderToErpJob extends BaseDataFlowJob<OrderGrabStatusEntity> {
    private static final Logger LOGGER = LoggerFactory.getLogger(PushOrderToErpJob.class);

    @Autowired
    private OrderGrabStatusService orderGrabStatusService;

    @Autowired
    private SaleOrderService saleOrderService;
    @Autowired
    private SaleOrderReceiverService saleOrderReceiverService;
    @Autowired
    private SaleOrderItemService saleOrderItemService;

    @Autowired
    @Qualifier("orderItemTopic")
    private NewTopic orderItemTopic;
    @Autowired
    private MessageProducer<StoreMpResponse> messageProducer;

    @Autowired
    private SaleOrderExtendService saleOrderExtendService;


    @Override
    protected List<OrderGrabStatusEntity> doFetchData(ShardingContext shardingContext) throws Exception {
        String jobParameter = shardingContext.getJobParameter();
        PushOrderToErpDto pushOrderToErpDto = null;
        if (StringUtil.isNotBlank(jobParameter)) {
            pushOrderToErpDto = NsyJacksonUtils.toObj(jobParameter, PushOrderToErpDto.class);
        }

        List<OrderGrabStatusEntity> list = orderGrabStatusService.list(Wrappers.<OrderGrabStatusEntity>lambdaQuery()
                .eq(OrderGrabStatusEntity::getOrderType, 1)
                .eq(OrderGrabStatusEntity::getIsGrabItem, 1)
                .eq(OrderGrabStatusEntity::getIsPushSyncOrderMq, 0)
                .last(String.format(MybatisQueryConstant.LIMIT, Objects.nonNull(pushOrderToErpDto) && Objects.nonNull(pushOrderToErpDto.getFetchCount()) ? pushOrderToErpDto.getFetchCount() : MybatisQueryConstant.FIVE_HUNDRED)));

        if (NsyCollUtil.isEmpty(list)) {
            return null;
        }

        return list.stream()
                .filter(orderGrabStatusEntity -> orderGrabStatusEntity.getGrabStatusId() % shardingContext.getShardingTotalCount() == shardingContext.getShardingItem())
                .collect(Collectors.toList());
    }


    @Override
    protected void doProcessData(ShardingContext shardingContext, List<OrderGrabStatusEntity> data) {
        if (NsyCollUtil.isEmpty(data)) {
            return;
        }

        data.forEach(item -> {
            try {
                PlatformOrderListResponse platformOrderListResponse = new PlatformOrderListResponse();
                PlatformOrderResponse platformOrderResponse = buildPlatFormOrderResponse(item);
                platformOrderResponse.setOrderItemInfos(buildPlatformOrderItemResponse(item, platformOrderResponse));
                if (!platformOrderResponse.getOrderItemInfos().isEmpty()) {
                    PlatformOrderItemResponse firstItem = platformOrderResponse.getOrderItemInfos().get(0);
                    if (firstItem != null) {
                        platformOrderResponse.setIossNumer(firstItem.getIossNumber());
                    } else {
                        platformOrderResponse.setIossNumer(StringUtils.EMPTY);
                    }
                } else {
                    platformOrderResponse.setIossNumer(StringUtils.EMPTY);
                }
                platformOrderListResponse.setOrderInfo(platformOrderResponse);
                messageProducer.sendMessage("订单同步", orderItemTopic.name(), platformOrderListResponse);
                item.setIsPushSyncOrderMq(1);
                orderGrabStatusService.updateById(item);
            } catch (Exception e) {
                LOGGER.info(String.format("PushOrderToErpJob推送失败，orderNo: %s msg: %s", item.getOrderNo(), e.getMessage()), e);
            }

        });


    }

    private List<PlatformOrderItemResponse> buildPlatformOrderItemResponse(OrderGrabStatusEntity orderGrabStatusEntity, PlatformOrderResponse platformOrderResponse) {
        List<SaleOrderItemEntity> orderItemEntities = saleOrderItemService.list(Wrappers.<SaleOrderItemEntity>lambdaQuery().eq(SaleOrderItemEntity::getOrderId, orderGrabStatusEntity.getOrderId()));
        return Optional.ofNullable(orderItemEntities).orElse(new ArrayList<>()).stream().map(item -> {
            PlatformOrderItemResponse platformOrderItemResponse = new PlatformOrderItemResponse();
            platformOrderItemResponse.setPrice(item.getUnitPrice());
            platformOrderItemResponse.setSku(item.getSellerSku());
            platformOrderItemResponse.setQty(item.getQty());
            platformOrderItemResponse.setTransparency(IsEnum.IS.getCode().equals(item.getIsTransparency()));
            platformOrderItemResponse.setAsin(item.getAsin());
            platformOrderItemResponse.setTotalDiscount(item.getUnitDiscount().multiply(BigDecimal.valueOf(item.getQty())));
            platformOrderItemResponse.setStatus(item.getItemStatus().equals(PlatformOrderItemStatusEnum.DELETE.getCode()) ? PlatformOrderStatusEnum.CANCEL.getOutCode() : StringUtils.EMPTY);
            platformOrderItemResponse.setRefundId(item.getRefundId());
            platformOrderItemResponse.setRefundStatus(item.getRefundStatus());
            platformOrderItemResponse.setOrderItemId(item.getPlatformItemId());
            platformOrderResponse.setIossNumer(item.getIossNumber());
            platformOrderItemResponse.setIossNumber(item.getIossNumber());
            platformOrderItemResponse.setShippingPriceAmount(item.getFreightFee());
            platformOrderItemResponse.setShippingDiscountAmount(item.getFreightFeeDiscount());
            platformOrderItemResponse.setCustomerSkc(item.getSellerSku());
            return platformOrderItemResponse;
        }).collect(Collectors.toList());
    }


    private PlatformOrderResponse buildPlatFormOrderResponse(OrderGrabStatusEntity item) {

        PlatformOrderResponse platformOrderResponse = new PlatformOrderResponse();
        SaleOrderEntity saleOrderEntity = saleOrderService.getByOrderNo(item.getStoreId(), item.getOrderNo());
        if (ObjectUtil.isNotNull(saleOrderEntity)) {
            BeanUtils.copyProperties(saleOrderEntity, platformOrderResponse);
            platformOrderResponse.setStoreId(item.getStoreId());
            platformOrderResponse.setOrderId(saleOrderEntity.getOrderNo());
            platformOrderResponse.setStatus(PlatformOrderStatusEnum.getOutCodeByCode(saleOrderEntity.getOrderStatus()));
            platformOrderResponse.setTotalFee(saleOrderEntity.getProductTotalAmount());
            platformOrderResponse.setPaymentFee(saleOrderEntity.getPaymentAmount());
            platformOrderResponse.setDiscountFee(saleOrderEntity.getProductDiscountAmount());
            platformOrderResponse.setPostFee(saleOrderEntity.getFreightFee());
            platformOrderResponse.setMarketplaceId(saleOrderEntity.getMarketCode());
            platformOrderResponse.setCreateDate(saleOrderEntity.getOrderCreateDate());
            platformOrderResponse.setPaymentDate(saleOrderEntity.getOrderPaymentDate());
            platformOrderResponse.setCancelledDate(saleOrderEntity.getOrderCancelDate());

            // 设值付款时间（平台时区）
            SaleOrderExtendEntity saleOrderExtendEntity = saleOrderExtendService.getByOrderNo(saleOrderEntity.getOrderNo());
            if (Objects.nonNull(saleOrderExtendEntity)) {
                platformOrderResponse.setOrderPaymentDateTimeZone(saleOrderExtendEntity.getOrderPaymentDateTimeZone());
            }
            platformOrderResponse.setBuyerMemo(saleOrderEntity.getBuyerRemark());
            SaleOrderReceiverEntity receiverEntity = saleOrderReceiverService.getByOrderId(saleOrderEntity.getOrderId());
            platformOrderResponse.setReceiverName(receiverEntity.getReceiverName());
            platformOrderResponse.setReceiverCountry(receiverEntity.getCountry());
            platformOrderResponse.setReceiverState(receiverEntity.getProvince());
            platformOrderResponse.setReceiverCity(receiverEntity.getCity());
            platformOrderResponse.setReceiverDistrict(receiverEntity.getArea());
            platformOrderResponse.setReceiverAddress(receiverEntity.getAddress());
            platformOrderResponse.setReceiverMobile(receiverEntity.getMobile());
            platformOrderResponse.setReceiverPhone(receiverEntity.getPhone());
            platformOrderResponse.setReceiverZip(receiverEntity.getPostCode());
            platformOrderResponse.setBuyerNick(receiverEntity.getBuyerNick());
            platformOrderResponse.setBuyerEmail(receiverEntity.getBuyerEmail());
            platformOrderResponse.setReceiverHouseNumber(receiverEntity.getHouseNumber());
        } else {
            LOGGER.info("PushOrderToErpJob orderNo {}, 查询不到订单", item.getOrderNo());
        }

        return platformOrderResponse;
    }
}
