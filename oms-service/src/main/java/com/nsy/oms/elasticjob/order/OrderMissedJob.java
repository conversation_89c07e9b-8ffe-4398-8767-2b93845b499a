package com.nsy.oms.elasticjob.order;

import cn.hutool.core.date.DateUtil;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.constant.enums.QueueStatusEnum;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.DateUtils;
import com.nsy.api.core.apicore.util.NsyCollUtil;
import com.nsy.api.core.apicore.util.NsyJacksonUtils;
import com.nsy.api.transfer.constants.AmazonPlatformConstant;
import com.nsy.oms.business.domain.dto.OrderMissedParamterDTO;
import com.nsy.oms.business.domain.request.order.BaseAuthRequest;
import com.nsy.oms.business.domain.request.order.GetOrderListByOrderIdsAuthRequest;
import com.nsy.oms.business.domain.response.order.GetPlatformOrderListResponse;
import com.nsy.oms.business.factory.PlatformOrderGrabServiceFactory;
import com.nsy.oms.business.manage.thirdparty.ThirdPartyApiService;
import com.nsy.oms.business.service.order.OrderGrabCommonService;
import com.nsy.oms.business.service.order.OrderGrabService;
import com.nsy.oms.business.service.order.OrderGrabStatusService;
import com.nsy.oms.business.service.order.OrderMissedQueueService;
import com.nsy.oms.business.service.order.SaleOrderService;
import com.nsy.oms.business.service.platform.PlatformOrderService;
import com.nsy.oms.business.service.sa.SaStoreService;
import com.nsy.oms.constants.MybatisQueryConstant;
import com.nsy.oms.constants.ThirdPartyExceptionConstant;
import com.nsy.oms.elasticjob.base.BaseDataFlowJob;
import com.nsy.oms.enums.order.OrderGrabPlatformEnum;
import com.nsy.oms.enums.order.OrderTypeEnum;
import com.nsy.oms.enums.platform.PlatformOrderStatusEnum;
import com.nsy.oms.enums.platform.PlatformShippingTypeEnum;
import com.nsy.oms.repository.entity.order.SaleOrderEntity;
import com.nsy.oms.repository.entity.order.OrderGrabStatusEntity;
import com.nsy.oms.repository.entity.order.OrderMissedQueueEntity;
import com.nsy.oms.repository.entity.platform.PlatformOrderEntity;
import com.nsy.oms.repository.entity.sa.SaStoreEntity;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/19 9:39
 */
@Component
public class OrderMissedJob extends BaseDataFlowJob<OrderMissedQueueEntity> {
    private static final Logger LOGGER = LoggerFactory.getLogger(OrderMissedJob.class);

    @Autowired
    private OrderMissedQueueService orderMissedQueueService;
    @Autowired
    private OrderGrabCommonService orderGrabCommonService;
    @Autowired
    private SaStoreService storeService;
    @Autowired
    private PlatformOrderGrabServiceFactory platformOrderGrabServiceFactory;
    @Autowired
    private ThirdPartyApiService thirdPartyApiService;
    @Autowired
    private SaleOrderService saleOrderService;
    @Autowired
    private PlatformOrderService platformOrderService;
    @Autowired
    private OrderGrabStatusService orderGrabStatusService;

    @Override
    protected List<OrderMissedQueueEntity> doFetchData(ShardingContext shardingContext) throws Exception {
        LOGGER.info("order missed job start: {}", DateUtil.format(new Date(), DateUtils.DATE_FORMAT_DATE4));
        String jobParameter = shardingContext.getJobParameter();
        OrderMissedParamterDTO orderMissedParamterDTO = null;
        if (StringUtil.isNotBlank(jobParameter)) {
            orderMissedParamterDTO = NsyJacksonUtils.toObj(jobParameter, OrderMissedParamterDTO.class);
        }
        List<OrderMissedQueueEntity> list = orderMissedQueueService.getList(Objects.nonNull(orderMissedParamterDTO) && Objects.nonNull(orderMissedParamterDTO.getFetchCount()) ? orderMissedParamterDTO.getFetchCount() : MybatisQueryConstant.FIVE_HUNDRED);

        if (NsyCollUtil.isEmpty(list)) {
            return null;
        }
        // 取亚马逊平台的队列
        list = list.stream()
                .filter(queue -> {
                    SaStoreEntity storeEntity = storeService.getById(queue.getStoreId());
                    if (storeEntity == null) {
                        return false;
                    }
                    Integer platformId = storeEntity.getSecondPlatformId() > 0 ? storeEntity.getSecondPlatformId() : storeEntity.getPlatformId();
                    return AmazonPlatformConstant.AMAZON_PLATFORM_ID_LIST.contains(platformId);
                })
                .collect(Collectors.toList());
        if (NsyCollUtil.isEmpty(list)) {
            return null;
        }
        List<OrderMissedQueueEntity> partitionList = list.stream()
                .filter(orderMissedQueueEntity -> orderMissedQueueEntity.getOrderMissedId() % shardingContext.getShardingTotalCount() == shardingContext.getShardingItem())
                .collect(Collectors.toList());

        if (NsyCollUtil.isNotEmpty(partitionList)) {
            partitionList.forEach(queue -> queue.setQueueStatus(QueueStatusEnum.EXECUTING.getCode()));
            orderMissedQueueService.updateBatchById(partitionList);
        }
        return partitionList;
    }


    @Override
    protected void doProcessData(ShardingContext shardingContext, List<OrderMissedQueueEntity> data) {
        if (NsyCollUtil.isEmpty(data)) {
            return;
        }
        List<Integer> storeIds = data.stream().map(OrderMissedQueueEntity::getStoreId).distinct().collect(Collectors.toList());
        List<SaStoreEntity> storeList = storeService.listByIds(storeIds);

        // 按店铺分组抓单
        Map<Integer, List<OrderMissedQueueEntity>> storeMissedQueueMap =
                data.stream().collect(Collectors.groupingBy(OrderMissedQueueEntity::getStoreId));

        storeMissedQueueMap.forEach((storeId, queueList) -> {
            try {
                SaStoreEntity storeEntity = storeList.stream().filter(store -> storeId.equals(store.getId())).findFirst().orElseThrow(() -> new BusinessServiceException(String.format("查询不到平台ID, 店铺id: %s", storeId)));
                Integer platformId = storeEntity.getSecondPlatformId() > 0 ? storeEntity.getSecondPlatformId() : storeEntity.getPlatformId();
                String platform = OrderGrabPlatformEnum.getPlatform(platformId);
                if (OrderGrabPlatformEnum.AMAZON.name().equalsIgnoreCase(platform)) {
                    OrderGrabService orderGrabService = platformOrderGrabServiceFactory.getOrderGrabService(platform);
                    replacementOrder(platform, storeId, queueList, orderGrabService);
                }
            } catch (Exception e) {
                LOGGER.error(String.format("order missed job error: storeId: %s, queueList: %s, msg: %s", storeId, queueList, e.getMessage()), e);
            }
        });
    }

    private void replacementOrder(String platform, Integer storeId, List<OrderMissedQueueEntity> queueList, OrderGrabService orderGrabService) {
        if (NsyCollUtil.isEmpty(queueList)) {
            return;
        }
        // 订单校验
        List<OrderMissedQueueEntity> needProcessQueues = checkOrders(storeId, queueList);
        if (NsyCollUtil.isEmpty(needProcessQueues)) {
            return;
        }
        List<List<OrderMissedQueueEntity>> partitionQueueList = Lists.partition(needProcessQueues, 50);

        partitionQueueList.forEach(queues -> {
            List<String> orderNoList = queues.stream().map(OrderMissedQueueEntity::getOrderNo).collect(Collectors.toList());
            try {
                GetOrderListByOrderIdsAuthRequest request = new GetOrderListByOrderIdsAuthRequest();

                BaseAuthRequest storeAuth = orderGrabService.getStoreAuth(storeId);
                BeanUtils.copyProperties(storeAuth, request);

                request.setPlatform(platform);
                request.setStoreId(storeId);
                request.setOrderIdList(orderNoList);

                // 调用三方网关进行抓单
                GetPlatformOrderListResponse response = thirdPartyApiService.getOrderListByIds(request);
                if (Objects.isNull(response) || NsyCollUtil.isEmpty(response.getOrderList())) {
                    queues.forEach(queue -> {
                        queue.setRemark("订单为空");
                        queue.setQueueStatus(QueueStatusEnum.EXECUTE_FAIL.getCode());
                    });
                    return;
                }

                queues.forEach(queue ->
                        response.getOrderList().stream()
                                .filter(orderResponse -> queue.getOrderNo().equals(orderResponse.getOrderId())
                                        && PlatformOrderStatusEnum.PENDING.getOutCode().equals(orderResponse.getStatus())
                                        && !orderResponse.getShippingType().contains(PlatformShippingTypeEnum.AFN.name())
                                )
                                .findFirst()
                                .ifPresent(orderResponse -> {
                                    queue.setRemark("订单未付款，不抓单到系统");
                                    queue.setQueueStatus(QueueStatusEnum.EXECUTE_FAIL.getCode());
                                })
                );
                // 保存订单
                orderGrabCommonService.doSaveOrder(storeId, orderGrabService, response);

            } catch (Exception e) {
                LOGGER.info(String.format("order missed job error: storeId: %s, orderNoList: %s, msg: %s", storeId, orderNoList, e.getMessage()), e);
                if (StringUtils.isNotBlank(e.getMessage()) && e.getMessage().contains(ThirdPartyExceptionConstant.TOO_MANY_REQUESTS)) {
                    queues.forEach(queue -> {
                        queue.setQueueStatus(QueueStatusEnum.INIT.getCode());
                        queue.setRetryCount(queue.getRetryCount() + 1);
                        queue.setRemark(StringUtils.isNotBlank(e.getMessage()) && e.getMessage().length() > 200 ? e.getMessage().substring(0, 200) : e.getMessage());
                    });
                } else {
                    queues.forEach(queue -> {
                        queue.setQueueStatus(QueueStatusEnum.EXECUTE_FAIL.getCode());
                        queue.setRetryCount(queue.getRetryCount() + 1);
                        queue.setRemark(StringUtils.isNotBlank(e.getMessage()) && e.getMessage().length() > 200 ? e.getMessage().substring(0, 200) : e.getMessage());
                    });
                }
            } finally {
                orderMissedQueueService.updateBatchById(queues);
                LOGGER.info("order missed job end: {}", DateUtil.format(new Date(), DateUtils.DATE_FORMAT_DATE4));
            }
        });
    }

    /**
     * 1）手工抓单，新增校验逻辑，判断抓单状态表是否存在改订单，有，则更新为忽略，备注订单已存在
     * 2）手工抓单，查询订单是否取消，是，手工抓单表状态更新为成功。
     *
     * @param storeId
     * @param
     * @return
     */
    private List<OrderMissedQueueEntity> checkOrders(Integer storeId, List<OrderMissedQueueEntity> queueList) {
        List<String> orderNoList = queueList.stream().map(OrderMissedQueueEntity::getOrderNo).collect(Collectors.toList());
        List<OrderMissedQueueEntity> notProcessQueueList = new ArrayList<>();

        List<SaleOrderEntity> cancelOrderList = saleOrderService.cancelOrders(storeId, orderNoList);
        if (NsyCollUtil.isNotEmpty(cancelOrderList)) {
            queueList.forEach(queue -> {
                boolean result = cancelOrderList.stream()
                        .anyMatch(order -> order.getStoreId().equals(queue.getStoreId()) && order.getOrderNo().equals(queue.getOrderNo()));

                if (result) {
                    queue.setQueueStatus(QueueStatusEnum.EXECUTE_SUCCESS.getCode());
                    queue.setRemark("自发货订单取消");
                    notProcessQueueList.add(queue);
                }
            });
        }

        List<PlatformOrderEntity> platformCancelOrderList = platformOrderService.cancelOrders(storeId, orderNoList);
        if (NsyCollUtil.isNotEmpty(platformCancelOrderList)) {
            queueList.forEach(queue -> {
                boolean result = platformCancelOrderList.stream()
                        .anyMatch(order -> order.getStoreId().equals(queue.getStoreId()) && order.getPlatformOriginalOrderNo().equals(queue.getOrderNo()));

                if (result) {
                    queue.setQueueStatus(QueueStatusEnum.EXECUTE_SUCCESS.getCode());
                    queue.setRemark("平台订单取消");
                    notProcessQueueList.add(queue);
                }
            });
        }

        List<OrderGrabStatusEntity> grabStatusEntityList = orderGrabStatusService.existOrders(storeId, orderNoList);

        if (NsyCollUtil.isNotEmpty(grabStatusEntityList)) {
            queueList.forEach(queue -> {
                grabStatusEntityList.forEach(grabStatus -> {
                    if (grabStatus.getStoreId().equals(queue.getStoreId()) && grabStatus.getOrderNo().equals(queue.getOrderNo())) {
                        queue.setQueueStatus(QueueStatusEnum.IGNORE.getCode());
                        queue.setRemark(String.format("订单类型：%s, 订单已存在!", OrderTypeEnum.getDescByCode(grabStatus.getOrderType())));
                        notProcessQueueList.add(queue);
                    }
                });
            });
        }

        if (NsyCollUtil.isNotEmpty(notProcessQueueList)) {
            orderMissedQueueService.updateBatchById(notProcessQueueList);
            return queueList.stream().filter(queue ->
                    notProcessQueueList.stream().noneMatch(notProcessQueue -> notProcessQueue.getOrderMissedId().equals(queue.getOrderMissedId()))
            ).collect(Collectors.toList());
        } else {
            return queueList;
        }
    }

}
