package com.nsy.oms.elasticjob.order;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.nsy.api.core.apicore.util.NsyCollUtil;
import com.nsy.api.core.apicore.util.NsyJacksonUtils;
import com.nsy.api.core.apicore.util.PageUtils;
import com.nsy.oms.business.service.order.SaleOrderItemService;
import com.nsy.oms.business.service.order.SaleOrderService;
import com.nsy.oms.business.service.sync.AsyncService;
import com.nsy.oms.constants.MybatisQueryConstant;
import com.nsy.oms.elasticjob.base.BaseSimpleJob;
import com.nsy.oms.repository.entity.order.SaleOrderEntity;
import com.nsy.oms.repository.entity.order.SaleOrderItemEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class OrderItemMatchJob extends BaseSimpleJob {
    private static final Logger LOGGER = LoggerFactory.getLogger(BaseSimpleJob.class);

    @Autowired
    private SaleOrderService saleOrderService;
    @Autowired
    private SaleOrderItemService saleOrderItemService;
    @Autowired
    private AsyncService asyncService;

    @Override
    protected void run(Map<String, Object> jobDataMap) throws Exception {
        matchSku();
    }

    public void matchSku() {
        Long count = saleOrderItemService
                .count(Wrappers.<SaleOrderItemEntity>lambdaQuery()
                        .eq(SaleOrderItemEntity::getSpecId, 0));

        if (count == 0) {
            return;
        }
        Integer maxId = 0;
        int intCount = count == null ? 0 : count.intValue();
        for (int i = 0; i < PageUtils.calculatePageCount(intCount, 100); i++) {

            List<SaleOrderItemEntity> list = saleOrderItemService
                    .list(Wrappers.<SaleOrderItemEntity>lambdaQuery()
                            .eq(SaleOrderItemEntity::getSpecId, 0)
                            .gt(maxId > 0, SaleOrderItemEntity::getOrderItemId, maxId)
                            .orderByAsc(SaleOrderItemEntity::getOrderItemId)
                            .last(MybatisQueryConstant.QUERY_HUNDRED));

            if (NsyCollUtil.isEmpty(list)) {
                return;
            }

            maxId = list.get(list.size() - 1).getOrderItemId();

            try {
                doingMatch(list);
            } catch (Exception e) {
                LOGGER.error(String.format("订单明细每天定时匹配商品系统sku失败：%s", e.getMessage()), e);
            }
        }
    }

    public void doingMatch(List<SaleOrderItemEntity> list) {
        Map<Integer, List<SaleOrderItemEntity>> orderItemGroupList = list.stream().collect(Collectors.groupingBy(SaleOrderItemEntity::getOrderId));
        orderItemGroupList.forEach((k, v) -> {
            try {
                if (NsyCollUtil.isNotEmpty(v)) {
                    SaleOrderEntity saleOrderEntity = saleOrderService.getById(k);
                    asyncService.orderMatchLocalSku(saleOrderEntity.getStoreId(), v);
                    saleOrderItemService.updateBatchById(v);
                }
            } catch (Exception e) {
                LOGGER.error("订单明细每天定时匹配sku失败，订单id: {}, 订单详情: {}, 错误信息: {}", k, NsyJacksonUtils.toJson(v), e.getMessage(), e);
            }
        });
    }
}
