package com.nsy.oms.mq.message;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/15 11:22
 */
public class OmsShipmentBoxRelationMessage {
    /**
     * erp补货计划单id
     */
    @JsonProperty("erpTid")
    private String erpTid;

    @JsonProperty("location")
    private String location;

    /**
     * Shipment跟Box的关系列表
     */
    @JsonProperty("staShipmentBoxRelationDtoList")
    private List<OmsShipmentBoxRelationDto> omsShipmentBoxRelationDtoList;

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getErpTid() {
        return erpTid;
    }

    public void setErpTid(String erpTid) {
        this.erpTid = erpTid;
    }

    public List<OmsShipmentBoxRelationDto> ********************************() {
        return omsShipmentBoxRelationDtoList;
    }

    public void ********************************(List<OmsShipmentBoxRelationDto> omsShipmentBoxRelationDtoList) {
        this.omsShipmentBoxRelationDtoList = omsShipmentBoxRelationDtoList;
    }
}
