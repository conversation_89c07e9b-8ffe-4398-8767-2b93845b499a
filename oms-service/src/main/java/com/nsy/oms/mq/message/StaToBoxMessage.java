package com.nsy.oms.mq.message;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 消息体：
 * public static final String SYNC_BOX_TO_AMAZON_TOPIC = "sync-box-to-amazon-topic";
 * public static final String SYNC_BOX_TO_AMAZON_TOPIC_NAME = "同步箱子到亚马逊";
 * messageProducer.sendMessage(KafkaConstant.SYNC_BOX_TO_AMAZON_TOPIC_NAME, KafkaConstant.SYNC_BOX_TO_AMAZON_TOPIC, StaBoxInfoMessage);
 *
 * <AUTHOR>
 * @date 2022/8/11 11:11
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class StaToBoxMessage {
    @JsonProperty("orderNo")
    private String orderNo;
    @JsonProperty("location")
    private String location;

}
