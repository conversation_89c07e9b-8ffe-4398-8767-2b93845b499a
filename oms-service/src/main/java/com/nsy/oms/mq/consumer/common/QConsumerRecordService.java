package com.nsy.oms.mq.consumer.common;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.util.NsyJacksonUtils;
import com.nsy.api.oms.mq.QMessage;
import com.nsy.oms.constants.JobConstant;
import com.nsy.oms.repository.entity.QConsumerRecordEntity;
import com.nsy.oms.repository.sql.mapper.QConsumerRecordMapper;
import com.nsy.oms.utils.JsonMapper;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.Objects;

/**
 * <p>
 * 消息队列--消费者记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-24
 */
@Service
public class QConsumerRecordService extends ServiceImpl<QConsumerRecordMapper, QConsumerRecordEntity> {

    public QConsumerRecordEntity findByMessageId(String messageId) {
        return this.getOne(new QueryWrapper<QConsumerRecordEntity>().lambda().eq(QConsumerRecordEntity::getMessageId, messageId).last("limit 1"));
    }

    @Transactional(rollbackOn = Exception.class)
    public QConsumerRecordEntity saveQConsumerRecord(QMessage receiveMessage) {
        QConsumerRecordEntity qConsumerRecordEntity = new QConsumerRecordEntity();
        qConsumerRecordEntity.setMessageId(receiveMessage.getMessageId());
        qConsumerRecordEntity.setTopic(receiveMessage.getDestination());
        qConsumerRecordEntity.setBusinessMark(receiveMessage.getBusinessMark());
        qConsumerRecordEntity.setMessageContent(NsyJacksonUtils.toJson(receiveMessage));
        qConsumerRecordEntity.setMessageCreateTimestamp(receiveMessage.getMessageCreateTimestamp());
        qConsumerRecordEntity.setStatus(QConsumerRecordStatusEnum.RECEIVED.getCode());
        this.save(qConsumerRecordEntity);
        return qConsumerRecordEntity;
    }

    @Transactional(rollbackOn = Exception.class)
    public void updateQConsumerRecord(QConsumerRecordEntity qConsumerRecordEntity) {
        this.updateById(qConsumerRecordEntity);
    }

    @Transactional
    public QConsumerRecordEntity writeQConsumerRecord(QMessage receiveMessage) {
        QConsumerRecordEntity qConsumerRecordEntity = this.findByMessageId(receiveMessage.getMessageId());
        if (Objects.isNull(qConsumerRecordEntity)) {
            qConsumerRecordEntity = new QConsumerRecordEntity();
            qConsumerRecordEntity.setRetryCount(0);
        } else {
            qConsumerRecordEntity.setRetryCount(qConsumerRecordEntity.getRetryCount() + 1);
        }
        qConsumerRecordEntity.setRecordType(JobConstant.RECORD_TYPE_CONSUMER);
        qConsumerRecordEntity.setMessageId(receiveMessage.getMessageId());
        qConsumerRecordEntity.setTopic(receiveMessage.getDestination());
        qConsumerRecordEntity.setBusinessMark(receiveMessage.getBusinessMark());
        qConsumerRecordEntity.setMessageContent(JsonMapper.toJson(receiveMessage.getMessageContent()));
        qConsumerRecordEntity.setMessageCreateTimestamp(receiveMessage.getMessageCreateTimestamp());
        qConsumerRecordEntity.setRecordKey(receiveMessage.getRecordKey());
        qConsumerRecordEntity.setStatus(QConsumerRecordStatusEnum.RECEIVED.getCode());
        this.saveOrUpdate(qConsumerRecordEntity);
        return qConsumerRecordEntity;
    }

    @Transactional
    public void persistQConsumerRecord(QConsumerRecordEntity qConsumerRecordEntity) {
        this.saveOrUpdate(qConsumerRecordEntity);
    }
}
