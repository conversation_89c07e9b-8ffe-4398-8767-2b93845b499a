package com.nsy.oms.mq.message;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class FbtOrderShipShipmentInfoDto {
    private String fbtShipmentId;

    private List<String> forwarderChannelList;

    private List<String> logisticsNoList;

    private List<String> logisticsCompanyList;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryTime;
}
