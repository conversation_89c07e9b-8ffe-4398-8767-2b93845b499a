package com.nsy.oms.mq.producer;

import com.nsy.oms.enums.tkcreator.InternetCelebrityMessageType;
import com.nsy.oms.mq.KafkaConstant;
import com.nsy.oms.mq.message.InternetCelebrityMessage;
import org.springframework.stereotype.Service;

import javax.inject.Inject;

/**
 * <AUTHOR>
 * @date 2021-11-17
 */
@Service
public class InternetCelebrityMessageSendService {
    @Inject
    MessageProducer<InternetCelebrityMessage> messageProducer;

    public void sendMessage(Integer id, InternetCelebrityMessageType type) {
        InternetCelebrityMessage message = InternetCelebrityMessage.builder().id(id).type(type.name()).build();
        messageProducer.sendMessage("TK达人", KafkaConstant.OMS_INTERNET_CELEBRITY, message);
    }


}
