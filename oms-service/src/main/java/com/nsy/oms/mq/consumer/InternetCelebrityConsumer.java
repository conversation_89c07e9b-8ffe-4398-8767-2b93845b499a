package com.nsy.oms.mq.consumer;

import com.nsy.api.core.apicore.util.NsyJacksonUtils;
import com.nsy.api.oms.mq.QMessage;
import com.nsy.oms.business.service.celebrity.InternetCelebrityConsumerMessageService;
import com.nsy.oms.mq.KafkaConstant;
import com.nsy.oms.mq.consumer.common.CommonConsumer;
import com.nsy.oms.mq.consumer.common.message.InternetCelebrityConsumerMessage;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import javax.inject.Inject;

@Service
public class InternetCelebrityConsumer extends CommonConsumer<InternetCelebrityConsumerMessage.Message> {

    private static final Logger LOGGER = LoggerFactory.getLogger(InternetCelebrityConsumer.class);

    @Inject
    InternetCelebrityConsumerMessageService celebrityConsumerMessageService;

    @KafkaListener(topics = KafkaConstant.OMS_INTERNET_CELEBRITY)
    public void nsyProductProductLabelConsumer(ConsumerRecord<?, ?> record, Acknowledgment ack) {
        QMessage<InternetCelebrityConsumerMessage.Message> receiveMessage = NsyJacksonUtils.toObj(record.value().toString(), InternetCelebrityConsumerMessage.class);
        LOGGER.debug("InternetCelebrityConsumer receive message: {}", record.value());
        processMessage(receiveMessage, ack);
    }

    @Override
    protected void doProcessMessage(QMessage<InternetCelebrityConsumerMessage.Message> receiveMessage) {
        InternetCelebrityConsumerMessage.Message messageContent = receiveMessage.getMessageContent();
        celebrityConsumerMessageService.consumerMessage(messageContent.getId(), messageContent.getType());
    }

}
