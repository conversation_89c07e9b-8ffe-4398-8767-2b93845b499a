package com.nsy.oms.mq.consumer.common.message;

import com.nsy.api.oms.mq.QMessage;
import com.nsy.oms.enums.tkcreator.InternetCelebrityMessageType;

/**
 * <AUTHOR>
 * @date 2021-11-17
 */
public class InternetCelebrityConsumerMessage extends QMessage<InternetCelebrityConsumerMessage.Message> {

    public static class Message {
        /**
         * 店铺id
         */
        private Integer id;

        private InternetCelebrityMessageType type;

        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public InternetCelebrityMessageType getType() {
            return type;
        }

        public void setType(InternetCelebrityMessageType type) {
            this.type = type;
        }
    }

}
