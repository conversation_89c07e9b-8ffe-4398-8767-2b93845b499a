package com.nsy.oms.repository.entity.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.oms.repository.entity.base.BaseMpEntity;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单扩展表
 * <AUTHOR>
 * @TableName order_extend
 */
@TableName("sale_order_extend")
public class SaleOrderExtendEntity extends BaseMpEntity implements Serializable {
    private static final long serialVersionUID = -6630204694539630515L;
    /**
     * 订单扩展ID
     */
    @TableId(value = "order_extend_id", type = IdType.AUTO)
    private Integer orderExtendId;

    /**
     * 订单ID
     */
    @TableField("order_id")
    private Integer orderId;

    /**
     * 订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 订单创建时间（平台时区）
     */
    @TableField("order_create_date_time_zone")
    private Date orderCreateDateTimeZone;

    /**
     * 最早发货时间（平台时区）
     */
    @TableField("earliest_ship_date_time_zone")
    private Date earliestShipDateTimeZone;

    /**
     * 最晚发货时间（平台时区）
     */
    @TableField("latest_ship_date_time_zone")
    private Date latestShipDateTimeZone;

    /**
     * 最早交货时间（平台时区）
     */
    @TableField("earliest_delivery_date_time_zone")
    private Date earliestDeliveryDateTimeZone;

    /**
     * 最晚交货时间（平台时区）
     */
    @TableField("latest_delivery_date_time_zone")
    private Date latestDeliveryDateTimeZone;

    /**
     * 订单付款时间（平台时区）
     */
    @TableField("order_payment_date_time_zone")
    private Date orderPaymentDateTimeZone;

    /**
     * 订单取消时间（平台时区）
     */
    @TableField("order_cancel_date_time_zone")
    private Date orderCancelDateTimeZone;

    
    /**
     * 订单扩展ID
     */
    public Integer getOrderExtendId() {
        return orderExtendId;
    }

    /**
     * 订单扩展ID
     */
    public void setOrderExtendId(Integer orderExtendId) {
        this.orderExtendId = orderExtendId;
    }

    /**
     * 订单ID
     */
    public Integer getOrderId() {
        return orderId;
    }

    /**
     * 订单ID
     */
    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    /**
     * 订单号
     */
    public String getOrderNo() {
        return orderNo;
    }

    /**
     * 订单号
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    /**
     * 订单创建时间（平台时区）
     */
    public Date getOrderCreateDateTimeZone() {
        return orderCreateDateTimeZone;
    }

    /**
     * 订单创建时间（平台时区）
     */
    public void setOrderCreateDateTimeZone(Date orderCreateDateTimeZone) {
        this.orderCreateDateTimeZone = orderCreateDateTimeZone;
    }

    /**
     * 最早发货时间（平台时区）
     */
    public Date getEarliestShipDateTimeZone() {
        return earliestShipDateTimeZone;
    }

    /**
     * 最早发货时间（平台时区）
     */
    public void setEarliestShipDateTimeZone(Date earliestShipDateTimeZone) {
        this.earliestShipDateTimeZone = earliestShipDateTimeZone;
    }

    /**
     * 最晚发货时间（平台时区）
     */
    public Date getLatestShipDateTimeZone() {
        return latestShipDateTimeZone;
    }

    /**
     * 最晚发货时间（平台时区）
     */
    public void setLatestShipDateTimeZone(Date latestShipDateTimeZone) {
        this.latestShipDateTimeZone = latestShipDateTimeZone;
    }

    /**
     * 最早交货时间（平台时区）
     */
    public Date getEarliestDeliveryDateTimeZone() {
        return earliestDeliveryDateTimeZone;
    }

    /**
     * 最早交货时间（平台时区）
     */
    public void setEarliestDeliveryDateTimeZone(Date earliestDeliveryDateTimeZone) {
        this.earliestDeliveryDateTimeZone = earliestDeliveryDateTimeZone;
    }

    /**
     * 最晚交货时间（平台时区）
     */
    public Date getLatestDeliveryDateTimeZone() {
        return latestDeliveryDateTimeZone;
    }

    /**
     * 最晚交货时间（平台时区）
     */
    public void setLatestDeliveryDateTimeZone(Date latestDeliveryDateTimeZone) {
        this.latestDeliveryDateTimeZone = latestDeliveryDateTimeZone;
    }

    /**
     * 订单付款时间（平台时区）
     */
    public Date getOrderPaymentDateTimeZone() {
        return orderPaymentDateTimeZone;
    }

    /**
     * 订单付款时间（平台时区）
     */
    public void setOrderPaymentDateTimeZone(Date orderPaymentDateTimeZone) {
        this.orderPaymentDateTimeZone = orderPaymentDateTimeZone;
    }

    /**
     * 订单取消时间（平台时区）
     */
    public Date getOrderCancelDateTimeZone() {
        return orderCancelDateTimeZone;
    }

    /**
     * 订单取消时间（平台时区）
     */
    public void setOrderCancelDateTimeZone(Date orderCancelDateTimeZone) {
        this.orderCancelDateTimeZone = orderCancelDateTimeZone;
    }
    
}