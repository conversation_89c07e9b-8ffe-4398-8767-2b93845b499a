package com.nsy.oms.repository.entity.report;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.oms.repository.entity.base.BaseMpEntity;

import java.util.Date;

/**
 * FBT库存变动报表实体类
 */
@TableName("report_fbt_inventory_records")
public class ReportFbtInventoryRecordsEntity extends BaseMpEntity {

    @TableId(value = "inventory_records_id", type = IdType.AUTO)
    private Integer inventoryRecordsId;

    private String orderId;

    private String orderType;

    private String fbtWarehouseId;

    private Date orderCreateTime;

    private Integer changedQuantity;

    private Integer finalOnHandQuantity;

    private Integer initialOnHandQuantity;

    private Integer storeId;

    private String storeName;

    private String goodsId;

    private String goodsName;

    private String goodsReferenceCode;

    private String inventoryGoodsType;

    private String department;

    public Integer getInventoryRecordsId() {
        return inventoryRecordsId;
    }

    public void setInventoryRecordsId(Integer inventoryRecordsId) {
        this.inventoryRecordsId = inventoryRecordsId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getFbtWarehouseId() {
        return fbtWarehouseId;
    }

    public void setFbtWarehouseId(String fbtWarehouseId) {
        this.fbtWarehouseId = fbtWarehouseId;
    }

    public Date getOrderCreateTime() {
        return orderCreateTime;
    }

    public void setOrderCreateTime(Date orderCreateTime) {
        this.orderCreateTime = orderCreateTime;
    }

    public Integer getChangedQuantity() {
        return changedQuantity;
    }

    public void setChangedQuantity(Integer changedQuantity) {
        this.changedQuantity = changedQuantity;
    }

    public Integer getFinalOnHandQuantity() {
        return finalOnHandQuantity;
    }

    public void setFinalOnHandQuantity(Integer finalOnHandQuantity) {
        this.finalOnHandQuantity = finalOnHandQuantity;
    }

    public Integer getInitialOnHandQuantity() {
        return initialOnHandQuantity;
    }

    public void setInitialOnHandQuantity(Integer initialOnHandQuantity) {
        this.initialOnHandQuantity = initialOnHandQuantity;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsReferenceCode() {
        return goodsReferenceCode;
    }

    public void setGoodsReferenceCode(String goodsReferenceCode) {
        this.goodsReferenceCode = goodsReferenceCode;
    }

    public String getInventoryGoodsType() {
        return inventoryGoodsType;
    }

    public void setInventoryGoodsType(String inventoryGoodsType) {
        this.inventoryGoodsType = inventoryGoodsType;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }
}