package com.nsy.oms.repository.entity.amzon;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.oms.repository.entity.base.BaseMpEntity;

/**
 * 亚马逊订单利润率监控标签
 */
@TableName("amazon_order_profit_monitoring_label")
public class AmazonOrderProfitMonitoringLabelEntity extends BaseMpEntity {

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * spu
     */
    @TableField("spu")
    private String spu;

    /**
     * spu
     */
    @TableField("skc")
    private String skc;

    /**
     * 店铺id
     */
    @TableField("store_id")
    private Integer storeId;

    /**
     * 店铺名称
     */
    @TableField("store_name")
    private String storeName;

    @TableField("marketplace_code")
    private String marketplaceCode;

    @TableField("parent_asin")
    private String parentAsin;

    /**
     * 标签类型
     */
    @TableField("label_type")
    private Integer labelType;

    /**
     * 标签名称
     */
    @TableField("label_name")
    private String labelName;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSpu() {
        return spu;
    }

    public void setSpu(String spu) {
        this.spu = spu;
    }

    public String getSkc() {
        return skc;
    }

    public void setSkc(String skc) {
        this.skc = skc;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getMarketplaceCode() {
        return marketplaceCode;
    }

    public void setMarketplaceCode(String marketplaceCode) {
        this.marketplaceCode = marketplaceCode;
    }

    public String getParentAsin() {
        return parentAsin;
    }

    public void setParentAsin(String parentAsin) {
        this.parentAsin = parentAsin;
    }

    public Integer getLabelType() {
        return labelType;
    }

    public void setLabelType(Integer labelType) {
        this.labelType = labelType;
    }

    public String getLabelName() {
        return labelName;
    }

    public void setLabelName(String labelName) {
        this.labelName = labelName;
    }

    public String getKey() {
        return String.format("%s-%s-%s", this.spu, this.storeId, this.parentAsin);
    }
}