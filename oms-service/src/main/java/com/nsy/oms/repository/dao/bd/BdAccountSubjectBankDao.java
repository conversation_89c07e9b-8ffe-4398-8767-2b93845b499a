package com.nsy.oms.repository.dao.bd;

import com.nsy.oms.repository.entity.bd.BdAccountSubjectBankEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【bd_account_subject_bank(账号主体开户行信息表)】的数据库操作Service
* @createDate 2023-02-10 11:29:49
*/
public interface BdAccountSubjectBankDao extends IService<BdAccountSubjectBankEntity> {

    List<BdAccountSubjectBankEntity> getList(Integer subjectId);
}
