package com.nsy.oms.repository.sql.mapper.celebrity;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.oms.business.domain.request.celebrity.InternetCelebrityRelationPageRequest;
import com.nsy.oms.repository.entity.celebrity.InternetCelebrityOrderItemPostMappingEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface InternetCelebrityOrderItemPostMappingMapper extends BaseMapper<InternetCelebrityOrderItemPostMappingEntity> {
    Page<InternetCelebrityOrderItemPostMappingEntity> page(IPage page, @Param("query") InternetCelebrityRelationPageRequest request);

    long count(@Param("query") InternetCelebrityRelationPageRequest request);
}