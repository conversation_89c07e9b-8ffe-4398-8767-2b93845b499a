package com.nsy.oms.repository.sql.mapper.stock;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nsy.oms.business.domain.stock.StockInfo;
import com.nsy.oms.repository.entity.stock.StockEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: caishao<PERSON>
 * @version: v1.0
 * @description: 库存表Mapper
 * @date: 2025-04-02 14:40
 */
@org.apache.ibatis.annotations.Mapper
public interface StockMapper extends BaseMapper<StockEntity> {

    List<StockInfo> searchSkuStockInfo(@Param("spaceId") Integer spaceId, @Param("sku") String sku, @Param("isPreLabel") Integer isPreLabel);

    List<StockInfo> searchSkuStockInfoBySpaceIdListAndSpecIdList(@Param("spaceIdList") List<Integer> spaceIdList, @Param("specIdList") List<Integer> specIdList);
}
