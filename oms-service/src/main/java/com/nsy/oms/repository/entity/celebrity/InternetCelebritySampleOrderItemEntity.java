package com.nsy.oms.repository.entity.celebrity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.oms.repository.entity.base.BaseMpEntity;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * 网红样衣订单明细
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-29
 */
@Getter
@Setter
@TableName("internet_celebrity_sample_order_item")
public class InternetCelebritySampleOrderItemEntity extends BaseMpEntity {


    /**
     * 主键id
     */
    @TableId(value = "internet_celebrity_sample_order_item_id", type = IdType.AUTO)
    private Integer internetCelebritySampleOrderItemId;

    /**
     * 网红样衣订单id
     */
    @TableField("internet_celebrity_sample_order_id")
    private Integer internetCelebritySampleOrderId;

    /**
     * sku
     */
    @TableField("sku")
    private String sku;

    /**
     * 数量
     */
    @TableField("qty")
    private Integer qty;

    /**
     * sku
     */
    @TableField("seller_sku")
    private String sellerSku;

    /**
     * 平台sku id
     */
    @TableField("seller_sku_id")
    private String sellerSkuId;

    /**
     * 平台product id
     */
    @TableField("seller_product_id")
    private String sellerProductId;

    /**
     * sku图片
     */
    @TableField("sku_picture_url")
    private String skuPictureUrl;


    /**
     * 发货店铺id
     */
    @TableField("delivery_store_id")
    private Integer deliveryStoreId;

    /**
     * 平台原始订单号
     */
    @TableField("platform_original_order_no")
    private String platformOriginalOrderNo;

    /**
     * 平台包裹id
     */
    @TableField("platform_package_id")
    private String platformPackageId;

    /**
     * 包裹状态
     */
    @TableField("package_status")
    private String packageStatus;

    /**
     * 物流单号
     */
    @TableField("tracking_number")
    private String trackingNumber;

    /**
     * 物流轨迹同步状态(0：异常，1：成功)
     */
    @TableField("tracking_sync_status")
    private Integer trackingSyncStatus;

    /**
     * 物流轨迹同步状态信息
     */
    @TableField("tracking_sync_error_message")
    private String trackingSyncErrorMessage;

    /**
     * 订单发货类型
     */
    @TableField("order_delivery_type")
    private String orderDeliveryType;

    /**
     * 订单妥投时间
     */
    @TableField("order_compromise_date")
    private Date orderCompromiseDate;

    /**
     * 订单发货时间
     */
    @TableField("order_delivery_date")
    private Date orderDeliveryDate;

}
