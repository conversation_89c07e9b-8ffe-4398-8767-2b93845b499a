package com.nsy.oms.repository.dao.bd.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.oms.business.domain.request.bd.AccountSubjectPageRequest;
import com.nsy.oms.business.domain.request.bd.AssociateAccountSubjectRequest;
import com.nsy.oms.business.domain.response.bd.AssociateAccountSubjectResponse;
import com.nsy.oms.business.domain.response.bd.BdAccountSubjectResponse;
import com.nsy.oms.repository.dao.bd.BdAccountSubjectDao;
import com.nsy.oms.repository.entity.bd.BdAccountSubjectEntity;
import com.nsy.oms.repository.sql.mapper.bd.BdAccountSubjectMapper;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2022/11/15 15:50
 */
@Service
public class BdAccountSubjectDaoImpl extends ServiceImpl<BdAccountSubjectMapper, BdAccountSubjectEntity> implements BdAccountSubjectDao {


    @Override
    public Page<BdAccountSubjectResponse> getList(AccountSubjectPageRequest request, String location) {
        return baseMapper.selectPageByCondition(new Page<>(request.getPageIndex(), request.getPageSize()), request, location);
    }


    @Override
    public List<BdAccountSubjectEntity> getAccountSubjectList() {
        LambdaQueryWrapper<BdAccountSubjectEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.ne(BdAccountSubjectEntity::getEnable, 0);
        return this.list(lambdaQueryWrapper);
    }

    @Override
    public List<BdAccountSubjectEntity> getAccountSubjectListByCompanyClassification(List<String> companyClassification) {
        LambdaQueryWrapper<BdAccountSubjectEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.ne(BdAccountSubjectEntity::getEnable, 0);
        lambdaQueryWrapper.in(BdAccountSubjectEntity::getCompanyClassification, companyClassification);
        return this.list(lambdaQueryWrapper);
    }

    @Override
    public Page<AssociateAccountSubjectResponse> associateList(AssociateAccountSubjectRequest request) {
        return baseMapper.associateList(new Page<>(request.getPageIndex(), request.getPageSize()), request);
    }
}
