package com.nsy.oms.repository.dao.sa;

import com.nsy.oms.business.domain.response.sa.ErpStoreMarketResponse;
import com.nsy.oms.business.domain.response.sa.StoreMarketResponse;
import com.nsy.oms.repository.entity.sa.SaStoreMarketSetupEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【sa_store_market(店铺市场表)】的数据库操作Service
* @createDate 2022-12-15 15:01:48
*/
public interface SaStoreMarketSetupDao extends IService<SaStoreMarketSetupEntity> {

    List<StoreMarketResponse> getList(Integer storeId);

    SaStoreMarketSetupEntity getInfo(String marketCode, Integer storeId);

    List<ErpStoreMarketResponse> getResponseList(Integer storeId);

    SaStoreMarketSetupEntity getInfoByStoreId(Integer storeId);

    List<SaStoreMarketSetupEntity> getByStoreId(Integer storeId);
}
