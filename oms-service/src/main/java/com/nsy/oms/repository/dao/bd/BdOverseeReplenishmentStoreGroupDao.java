package com.nsy.oms.repository.dao.bd;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nsy.api.oms.dto.request.store.OverseaReplenishmentGetGroupStoreRequest;
import com.nsy.oms.repository.entity.bd.BdOverseeReplenishmentStoreGroupEntity;

import java.util.List;

/**
 * <p>
 * 海外仓补货店铺分组 Dao 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-19
 */
public interface BdOverseeReplenishmentStoreGroupDao extends IService<BdOverseeReplenishmentStoreGroupEntity> {
    List<BdOverseeReplenishmentStoreGroupEntity> getGroupStoreList(OverseaReplenishmentGetGroupStoreRequest request);
}