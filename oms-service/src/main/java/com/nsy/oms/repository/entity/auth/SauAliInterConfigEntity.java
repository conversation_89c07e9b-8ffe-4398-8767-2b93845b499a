package com.nsy.oms.repository.entity.auth;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.oms.annotation.FieldDesc;
import com.nsy.oms.repository.entity.base.BaseMpEntity;

import java.util.Date;

/**
 * 店铺授权-阿里国际授权表
 *
 * @TableName sau_ali_inter_config
 */
@TableName("sau_ali_inter_config")
public class SauAliInterConfigEntity extends BaseMpEntity {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 店铺名称
     */
    @TableField("store_name")
    private String storeName;

    /**
     * 店铺ID
     */
    @TableField("store_id")
    private Integer storeId;

    /**
     * 市场ID
     */
    @TableField("market_id")
    private Integer marketId;

    /**
     * 市场
     */
    @FieldDesc("市场")
    @TableField("market")
    private String market;

    /**
     * redirect_uri
     */
    @FieldDesc("redirect_url")
    @TableField("redirect_url")
    private String redirectUrl;

    /**
     * appkey
     */
    @FieldDesc("appkey")
    @TableField("appkey")
    private String appkey;

    /**
     * appsecret
     */
    @FieldDesc("appsecret")
    @TableField("appsecret")
    private String appsecret;

    /**
     * authorization_code
     */
    @FieldDesc("authorization_code")
    @TableField("authorization_code")
    private String authorizationCode;

    /**
     * refresh_token
     */
    @FieldDesc("refresh_token")
    private String refreshToken;

    /**
     * access_token
     */
    @FieldDesc("access_token")
    private String accessToken;

    /**
     * extended_attributes_one
     */
    @FieldDesc("extended_attributes_one")
    @TableField("extended_attributes_one")
    private String extendedAttributesOne;

    /**
     * access_token
     */
    @FieldDesc("extended_attributes_two")
    @TableField("extended_attributes_two")
    private String extendedAttributesTwo;

    /**
     * token状态 0:异常  1:正常
     */
    @FieldDesc("token状态")
    @TableField("token_status")
    private Integer tokenStatus;

    /**
     * 授权状态 0:关闭  1:开启
     */
    @FieldDesc("授权状态")
    @TableField("grant_auth_status")
    private Integer grantAuthStatus;

    /**
     * token有效起始时间
     */
    @FieldDesc("token有效起始时间")
    @TableField("token_validity_date_start")
    private Date tokenValidityDateStart;

    /**
     * token有效终止时间
     */
    @FieldDesc("token有效终止时间")
    @TableField("token_validity_date_end")
    private Date tokenValidityDateEnd;


    /**
     * 授权日期
     */
    @TableField("grant_date")
    private Date grantDate;


    /**
     * 有效期
     */
    @TableField("validity_date")
    private Date validityDate;


    /**
     * 开始抓单时间
     */
    @TableField("catch_date")
    private Date catchDate;


    /**
     * 当前抓单时间
     */
    @TableField("current_catch_date")
    private Date currentCatchDate;


    /**
     * token重新刷新时间
     */
    @TableField("refresh_token_timeout")
    private Date refreshTokenTimeout;



    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    public Integer getId() {
        return id;
    }

    /**
     *
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 店铺名称
     */
    public String getStoreName() {
        return storeName;
    }

    /**
     * 店铺名称
     */
    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    /**
     * 店铺ID
     */
    public Integer getStoreId() {
        return storeId;
    }

    /**
     * 店铺ID
     */
    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    /**
     * 市场
     */
    public String getMarket() {
        return market;
    }

    /**
     * 市场
     */
    public void setMarket(String market) {
        this.market = market;
    }

    /**
     * redirect_uri
     */
    public String getRedirectUrl() {
        return redirectUrl;
    }

    /**
     * redirect_uri
     */
    public void setRedirectUrl(String redirectUrl) {
        this.redirectUrl = redirectUrl;
    }

    /**
     * appkey
     */
    public String getAppkey() {
        return appkey;
    }

    /**
     * appkey
     */
    public void setAppkey(String appkey) {
        this.appkey = appkey;
    }

    /**
     * appsecret
     */
    public String getAppsecret() {
        return appsecret;
    }

    /**
     * appsecret
     */
    public void setAppsecret(String appsecret) {
        this.appsecret = appsecret;
    }

    /**
     * authorization_code
     */
    public String getAuthorizationCode() {
        return authorizationCode;
    }

    /**
     * authorization_code
     */
    public void setAuthorizationCode(String authorizationCode) {
        this.authorizationCode = authorizationCode;
    }

    /**
     * refresh_token
     */
    public String getRefreshToken() {
        return refreshToken;
    }

    /**
     * refresh_token
     */
    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    /**
     * access_token
     */
    public String getAccessToken() {
        return accessToken;
    }

    /**
     * access_token
     */
    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    /**
     * token状态 0:异常  1:正常
     */
    public Integer getTokenStatus() {
        return tokenStatus;
    }

    /**
     * token状态 0:异常  1:正常
     */
    public void setTokenStatus(Integer tokenStatus) {
        this.tokenStatus = tokenStatus;
    }

    /**
     * 授权状态 0:关闭  1:开启
     */
    public Integer getGrantAuthStatus() {
        return grantAuthStatus;
    }

    /**
     * 授权状态 0:关闭  1:开启
     */
    public void setGrantAuthStatus(Integer grantAuthStatus) {
        this.grantAuthStatus = grantAuthStatus;
    }

    /**
     * token有效起始时间
     */
    public Date getTokenValidityDateStart() {
        return tokenValidityDateStart;
    }

    /**
     * token有效起始时间
     */
    public void setTokenValidityDateStart(Date tokenValidityDateStart) {
        this.tokenValidityDateStart = tokenValidityDateStart;
    }

    /**
     * token有效终止时间
     */
    public Date getTokenValidityDateEnd() {
        return tokenValidityDateEnd;
    }

    /**
     * token有效终止时间
     */
    public void setTokenValidityDateEnd(Date tokenValidityDateEnd) {
        this.tokenValidityDateEnd = tokenValidityDateEnd;
    }

    public String getExtendedAttributesOne() {
        return extendedAttributesOne;
    }

    public void setExtendedAttributesOne(String extendedAttributesOne) {
        this.extendedAttributesOne = extendedAttributesOne;
    }

    public String getExtendedAttributesTwo() {
        return extendedAttributesTwo;
    }

    public void setExtendedAttributesTwo(String extendedAttributesTwo) {
        this.extendedAttributesTwo = extendedAttributesTwo;
    }

    public Integer getMarketId() {
        return marketId;
    }

    public void setMarketId(Integer marketId) {
        this.marketId = marketId;
    }

    public Date getGrantDate() {
        return grantDate;
    }

    public void setGrantDate(Date grantDate) {
        this.grantDate = grantDate;
    }

    public Date getValidityDate() {
        return validityDate;
    }

    public void setValidityDate(Date validityDate) {
        this.validityDate = validityDate;
    }

    public Date getCatchDate() {
        return catchDate;
    }

    public void setCatchDate(Date catchDate) {
        this.catchDate = catchDate;
    }

    public Date getCurrentCatchDate() {
        return currentCatchDate;
    }

    public void setCurrentCatchDate(Date currentCatchDate) {
        this.currentCatchDate = currentCatchDate;
    }

    public Date getRefreshTokenTimeout() {
        return refreshTokenTimeout;
    }

    public void setRefreshTokenTimeout(Date refreshTokenTimeout) {
        this.refreshTokenTimeout = refreshTokenTimeout;
    }
}