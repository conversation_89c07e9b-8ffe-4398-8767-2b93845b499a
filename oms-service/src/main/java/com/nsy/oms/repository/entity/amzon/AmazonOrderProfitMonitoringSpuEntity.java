package com.nsy.oms.repository.entity.amzon;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.oms.repository.entity.base.BaseMpEntity;

import java.math.BigDecimal;

/**
 * 亚马逊订单利润率监控spu
 */
@TableName("amazon_order_profit_monitoring_spu")
public class AmazonOrderProfitMonitoringSpuEntity extends BaseMpEntity {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("spu")
    private String spu;

    @TableField("season")
    private String season;

    @TableField("develop_season")
    private String developSeason;

    @TableField("image_url")
    private String imageUrl;

    @TableField("marketplace_code")
    private String marketplaceCode;

    @TableField("parent_asin")
    private String parentAsin;

    @TableField("first_category_name")
    private String firstCategoryName;

    @TableField("second_category_name")
    private String secondCategoryName;

    @TableField("third_category_name")
    private String thirdCategoryName;

    @TableField("department")
    private String department;

    @TableField("store_id")
    private Integer storeId;

    @TableField("store_name")
    private String storeName;

    /**
     * 近7天销量
     */
    @TableField("last_7_date_sale_qty")
    private Integer last7DateSaleQty;

    /**
     * 近14天销量
     */
    @TableField("last_14_date_sale_qty")
    private Integer last14DateSaleQty;

    /**
     * 近30天销量
     */
    @TableField("last_30_date_sale_qty")
    private Integer last30DateSaleQty;

    @TableField("profit_rate_in_7_days")
    private BigDecimal profitRateIn7Days;

    @TableField("profit_rate_in_14_days")
    private BigDecimal profitRateIn14Days;

    @TableField("profit_rate_in_30_days")
    private BigDecimal profitRateIn30Days;

    /**
     * 总库存
     */
    @TableField("total_inv")
    private Integer totalInv;

    /**
     * 泉州仓
     */
    @TableField("quanzhou_inv")
    private Integer quanzhouInv;

    /**
     * 海外可售
     */
    @TableField("overseas_inv")
    private Integer overseasInv;

    /**
     * 海外预留
     */
    @TableField("overseas_in_reserved_inv")
    private Integer overseasInReservedInv;

    /**
     * 采购在途
     */
    @TableField("purchase_in_transit_inv")
    private Integer purchaseInTransitInv;

    /**
     * 退货率
     */
    @TableField("return_rate")
    private BigDecimal returnRate;

    /**
     * 广告费率
     */
    @TableField("ad_cost_rate")
    private BigDecimal adCostRate;

    /**
     * 活动价($)
     */
    @TableField("sale_price")
    private BigDecimal salePrice;

    /**
     * 原价($)
     */
    @TableField("your_price")
    private BigDecimal yourPrice;

    /**
     * 近24H最低成交价
     */
    @TableField("min_order_price_in_24_hours")
    private BigDecimal minOrderPriceIn24Hours;

    /**
     * 近24H最高成交价
     */
    @TableField("max_order_price_in_24_hours")
    private BigDecimal maxOrderPriceIn24Hours;

    @TableField("min_profit_rate_in_24_hours")
    private BigDecimal minProfitRateIn24Hours;

    @TableField("max_profit_rate_in_24_hours")
    private BigDecimal maxProfitRateIn24Hours;

    /**
     * 当前档佣金率
     */
    @TableField("current_level_commission_rate")
    private BigDecimal currentLevelCommissionRate;

    /**
     * 当前档盈亏平衡价
     */
    @TableField("current_level_balance_price")
    private BigDecimal currentLevelBalancePrice;

    @TableField("batch_no")
    private String batchNo;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSpu() {
        return spu;
    }

    public void setSpu(String spu) {
        this.spu = spu;
    }

    public String getSeason() {
        return season;
    }

    public void setSeason(String season) {
        this.season = season;
    }

    public String getDevelopSeason() {
        return developSeason;
    }

    public void setDevelopSeason(String developSeason) {
        this.developSeason = developSeason;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getMarketplaceCode() {
        return marketplaceCode;
    }

    public void setMarketplaceCode(String marketplaceCode) {
        this.marketplaceCode = marketplaceCode;
    }

    public String getParentAsin() {
        return parentAsin;
    }

    public void setParentAsin(String parentAsin) {
        this.parentAsin = parentAsin;
    }

    public String getFirstCategoryName() {
        return firstCategoryName;
    }

    public void setFirstCategoryName(String firstCategoryName) {
        this.firstCategoryName = firstCategoryName;
    }

    public String getSecondCategoryName() {
        return secondCategoryName;
    }

    public void setSecondCategoryName(String secondCategoryName) {
        this.secondCategoryName = secondCategoryName;
    }

    public String getThirdCategoryName() {
        return thirdCategoryName;
    }

    public void setThirdCategoryName(String thirdCategoryName) {
        this.thirdCategoryName = thirdCategoryName;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public Integer getLast7DateSaleQty() {
        return last7DateSaleQty;
    }

    public void setLast7DateSaleQty(Integer last7DateSaleQty) {
        this.last7DateSaleQty = last7DateSaleQty;
    }

    public Integer getLast14DateSaleQty() {
        return last14DateSaleQty;
    }

    public void setLast14DateSaleQty(Integer last14DateSaleQty) {
        this.last14DateSaleQty = last14DateSaleQty;
    }

    public Integer getLast30DateSaleQty() {
        return last30DateSaleQty;
    }

    public void setLast30DateSaleQty(Integer last30DateSaleQty) {
        this.last30DateSaleQty = last30DateSaleQty;
    }

    public BigDecimal getProfitRateIn7Days() {
        return profitRateIn7Days;
    }

    public void setProfitRateIn7Days(BigDecimal profitRateIn7Days) {
        this.profitRateIn7Days = profitRateIn7Days;
    }

    public BigDecimal getProfitRateIn14Days() {
        return profitRateIn14Days;
    }

    public void setProfitRateIn14Days(BigDecimal profitRateIn14Days) {
        this.profitRateIn14Days = profitRateIn14Days;
    }

    public BigDecimal getProfitRateIn30Days() {
        return profitRateIn30Days;
    }

    public void setProfitRateIn30Days(BigDecimal profitRateIn30Days) {
        this.profitRateIn30Days = profitRateIn30Days;
    }

    public Integer getTotalInv() {
        return totalInv;
    }

    public void setTotalInv(Integer totalInv) {
        this.totalInv = totalInv;
    }

    public Integer getQuanzhouInv() {
        return quanzhouInv;
    }

    public void setQuanzhouInv(Integer quanzhouInv) {
        this.quanzhouInv = quanzhouInv;
    }

    public Integer getOverseasInv() {
        return overseasInv;
    }

    public void setOverseasInv(Integer overseasInv) {
        this.overseasInv = overseasInv;
    }

    public Integer getOverseasInReservedInv() {
        return overseasInReservedInv;
    }

    public void setOverseasInReservedInv(Integer overseasInReservedInv) {
        this.overseasInReservedInv = overseasInReservedInv;
    }

    public Integer getPurchaseInTransitInv() {
        return purchaseInTransitInv;
    }

    public void setPurchaseInTransitInv(Integer purchaseInTransitInv) {
        this.purchaseInTransitInv = purchaseInTransitInv;
    }

    public BigDecimal getReturnRate() {
        return returnRate;
    }

    public void setReturnRate(BigDecimal returnRate) {
        this.returnRate = returnRate;
    }

    public BigDecimal getAdCostRate() {
        return adCostRate;
    }

    public void setAdCostRate(BigDecimal adCostRate) {
        this.adCostRate = adCostRate;
    }

    public BigDecimal getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(BigDecimal salePrice) {
        this.salePrice = salePrice;
    }

    public BigDecimal getYourPrice() {
        return yourPrice;
    }

    public void setYourPrice(BigDecimal yourPrice) {
        this.yourPrice = yourPrice;
    }

    public BigDecimal getMinOrderPriceIn24Hours() {
        return minOrderPriceIn24Hours;
    }

    public void setMinOrderPriceIn24Hours(BigDecimal minOrderPriceIn24Hours) {
        this.minOrderPriceIn24Hours = minOrderPriceIn24Hours;
    }

    public BigDecimal getMaxOrderPriceIn24Hours() {
        return maxOrderPriceIn24Hours;
    }

    public void setMaxOrderPriceIn24Hours(BigDecimal maxOrderPriceIn24Hours) {
        this.maxOrderPriceIn24Hours = maxOrderPriceIn24Hours;
    }

    public BigDecimal getMinProfitRateIn24Hours() {
        return minProfitRateIn24Hours;
    }

    public void setMinProfitRateIn24Hours(BigDecimal minProfitRateIn24Hours) {
        this.minProfitRateIn24Hours = minProfitRateIn24Hours;
    }

    public BigDecimal getMaxProfitRateIn24Hours() {
        return maxProfitRateIn24Hours;
    }

    public void setMaxProfitRateIn24Hours(BigDecimal maxProfitRateIn24Hours) {
        this.maxProfitRateIn24Hours = maxProfitRateIn24Hours;
    }

    public BigDecimal getCurrentLevelCommissionRate() {
        return currentLevelCommissionRate;
    }

    public void setCurrentLevelCommissionRate(BigDecimal currentLevelCommissionRate) {
        this.currentLevelCommissionRate = currentLevelCommissionRate;
    }

    public BigDecimal getCurrentLevelBalancePrice() {
        return currentLevelBalancePrice;
    }

    public void setCurrentLevelBalancePrice(BigDecimal currentLevelBalancePrice) {
        this.currentLevelBalancePrice = currentLevelBalancePrice;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }
}