package com.nsy.oms.repository.entity.auth;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.oms.annotation.FieldDesc;
import com.nsy.oms.repository.entity.base.BaseMpEntity;

import java.util.Date;

/**
 * 店铺授权-1688授权表
 *
 * @TableName sau_wholesale_config
 */
@TableName("sau_wholesale_config")
public class SauWholesaleConfigEntity extends BaseMpEntity {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 店铺名称
     */
    @TableField("store_name")
    private String storeName;

    /**
     * 店铺ID
     */
    @TableField("store_id")
    private Integer storeId;

    /**
     * Appkey
     */
    @FieldDesc("Appkey")
    @TableField("appkey")
    private String appkey;

    /**
     * AppSecret
     */
    @FieldDesc("AppSecret")
    @TableField("appsecret")
    private String appsecret;

    /**
     * refresh_token
     */
    @TableField("refresh_token")
    private String refreshToken;

    /**
     * access_token
     */
    @TableField("access_token")
    private String accessToken;

    /**
     * memberId
     */
    @FieldDesc("memberId")
    @TableField("member_id")
    private String memberId;

    /**
     * 回调地址
     */
    @FieldDesc("回调地址")
    @TableField("callback_url")
    private String callbackUrl;

    /**
     * 扩展属性1
     */
    @FieldDesc("扩展属性1")
    @TableField("extended_attributes_one")
    private String extendedAttributesOne;

    /**
     * 扩展属性2
     */
    @FieldDesc("扩展属性2")
    @TableField("extended_attributes_two")
    private String extendedAttributesTwo;

    /**
     * token状态 0:正常  1:异常
     */
    @FieldDesc("token状态")
    @TableField("token_status")
    private Integer tokenStatus;

    /**
     * 授权状态 0:关闭  1:开启
     */
    @FieldDesc("授权状态")
    @TableField("grant_auth_status")
    private Integer grantAuthStatus;

    /**
     * token有效起始时间
     */
    @FieldDesc("token有效起始时间")
    @TableField("token_validity_date_start")
    private Date tokenValidityDateStart;

    /**
     * token有效终止时间
     */
    @FieldDesc("token有效终止时间")
    @TableField("token_validity_date_end")
    private Date tokenValidityDateEnd;


    /**
     * 授权日期
     */
    @TableField("grant_date")
    private Date grantDate;


    /**
     * 有效期
     */
    @TableField("validity_date")
    private Date validityDate;


    /**
     * 开始抓单时间
     */
    @TableField("catch_date")
    private Date catchDate;


    /**
     * 当前抓单时间
     */
    @TableField("current_catch_date")
    private Date currentCatchDate;


    /**
     * token重新刷新时间
     */
    @TableField("refresh_token_timeout")
    private Date refreshTokenTimeout;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    public Integer getId() {
        return id;
    }

    /**
     *
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 店铺名称
     */
    public String getStoreName() {
        return storeName;
    }

    /**
     * 店铺名称
     */
    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    /**
     * 店铺ID
     */
    public Integer getStoreId() {
        return storeId;
    }

    /**
     * 店铺ID
     */
    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    /**
     * Appkey
     */
    public String getAppkey() {
        return appkey;
    }

    /**
     * Appkey
     */
    public void setAppkey(String appkey) {
        this.appkey = appkey;
    }

    /**
     * AppSecret
     */
    public String getAppsecret() {
        return appsecret;
    }

    /**
     * AppSecret
     */
    public void setAppsecret(String appsecret) {
        this.appsecret = appsecret;
    }

    /**
     * refresh_token
     */
    public String getRefreshToken() {
        return refreshToken;
    }

    /**
     * refresh_token
     */
    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    /**
     * access_token
     */
    public String getAccessToken() {
        return accessToken;
    }

    /**
     * access_token
     */
    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    /**
     * memberId
     */
    public String getMemberId() {
        return memberId;
    }

    /**
     * memberId
     */
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    /**
     * 回调地址
     */
    public String getCallbackUrl() {
        return callbackUrl;
    }

    /**
     * 回调地址
     */
    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }

    /**
     * token状态 0:正常  1:异常
     */
    public Integer getTokenStatus() {
        return tokenStatus;
    }

    /**
     * token状态 0:正常  1:异常
     */
    public void setTokenStatus(Integer tokenStatus) {
        this.tokenStatus = tokenStatus;
    }

    /**
     * 授权状态 0:关闭  1:开启
     */
    public Integer getGrantAuthStatus() {
        return grantAuthStatus;
    }

    /**
     * 授权状态 0:关闭  1:开启
     */
    public void setGrantAuthStatus(Integer grantAuthStatus) {
        this.grantAuthStatus = grantAuthStatus;
    }

    /**
     * token有效起始时间
     */
    public Date getTokenValidityDateStart() {
        return tokenValidityDateStart;
    }

    /**
     * token有效起始时间
     */
    public void setTokenValidityDateStart(Date tokenValidityDateStart) {
        this.tokenValidityDateStart = tokenValidityDateStart;
    }

    /**
     * token有效终止时间
     */
    public Date getTokenValidityDateEnd() {
        return tokenValidityDateEnd;
    }

    /**
     * token有效终止时间
     */
    public void setTokenValidityDateEnd(Date tokenValidityDateEnd) {
        this.tokenValidityDateEnd = tokenValidityDateEnd;
    }

    public String getExtendedAttributesOne() {
        return extendedAttributesOne;
    }

    public void setExtendedAttributesOne(String extendedAttributesOne) {
        this.extendedAttributesOne = extendedAttributesOne;
    }

    public String getExtendedAttributesTwo() {
        return extendedAttributesTwo;
    }

    public void setExtendedAttributesTwo(String extendedAttributesTwo) {
        this.extendedAttributesTwo = extendedAttributesTwo;
    }

    public Date getGrantDate() {
        return grantDate;
    }

    public void setGrantDate(Date grantDate) {
        this.grantDate = grantDate;
    }

    public Date getValidityDate() {
        return validityDate;
    }

    public void setValidityDate(Date validityDate) {
        this.validityDate = validityDate;
    }

    public Date getCatchDate() {
        return catchDate;
    }

    public void setCatchDate(Date catchDate) {
        this.catchDate = catchDate;
    }

    public Date getCurrentCatchDate() {
        return currentCatchDate;
    }

    public void setCurrentCatchDate(Date currentCatchDate) {
        this.currentCatchDate = currentCatchDate;
    }

    public Date getRefreshTokenTimeout() {
        return refreshTokenTimeout;
    }

    public void setRefreshTokenTimeout(Date refreshTokenTimeout) {
        this.refreshTokenTimeout = refreshTokenTimeout;
    }
}