package com.nsy.oms.repository.mongo.repository.tiktok;

import com.nsy.oms.repository.mongo.document.tiktok.ReportTkStatementTransactionDocument;
import com.nsy.oms.repository.mongo.repository.DocumentRepository;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ReportTkStatementTransactionDocumentRepository extends DocumentRepository<ReportTkStatementTransactionDocument, String> {

    ReportTkStatementTransactionDocument findAllByStoreIdAndStatementIdAndTransactionIdAndOrderIdAndAdjustmentId(Integer storeId, String statementId, String transactionId, String orderId, String adjustmentId);

    List<ReportTkStatementTransactionDocument> findAllByOrderIdStatusOrderByCreateDateDesc(Integer orderIdStatus, Pageable pageable);

}
