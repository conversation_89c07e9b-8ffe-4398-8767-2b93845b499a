package com.nsy.oms.repository.dao.auth;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.oms.business.domain.request.auth.PlatformAuthConfigDescriptionPageRequest;
import com.nsy.oms.repository.entity.auth.SauPlatformAuthConfigDescriptionEntity;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【sau_platform_auth_config_description(通用平台授权配置说明)】的数据库操作Service
* @createDate 2023-03-22 13:44:20
*/
public interface SauPlatformAuthConfigDescriptionDao extends IService<SauPlatformAuthConfigDescriptionEntity> {

    Page<SauPlatformAuthConfigDescriptionEntity> getList(PlatformAuthConfigDescriptionPageRequest request);

    SauPlatformAuthConfigDescriptionEntity getPlatformDesc(String platformId);

    boolean exist(String platformId);
}
