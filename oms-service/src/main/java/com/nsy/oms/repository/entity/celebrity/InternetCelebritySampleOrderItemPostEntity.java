package com.nsy.oms.repository.entity.celebrity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.oms.repository.entity.base.BaseMpEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 网红样衣订单明细发帖信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-29
 */
@Getter
@Setter
@TableName("internet_celebrity_sample_order_item_post")
public class InternetCelebritySampleOrderItemPostEntity extends BaseMpEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "internet_celebrity_sample_order_item_post_id", type = IdType.AUTO)
    private Integer internetCelebritySampleOrderItemPostId;

    /**
     * 网红样衣订单明细ID
     */
    @TableField("internet_celebrity_sample_order_item_id")
    private Integer internetCelebritySampleOrderItemId;

    /**
     * 视频code
     */
    @TableField("video_code")
    private String videoCode;


    /**
     * 店铺id
     */
    @TableField("store_id")
    private Integer storeId;

    /**
     * 店铺名
     */
    @TableField("store_name")
    private String storeName;

    /**
     * tk平台达人id
     */
    @TableField("internet_celebrity_no")
    private String internetCelebrityNo;

    /**
     * tk平台达人id
     */
    @TableField("internet_celebrity_id")
    private Integer internetCelebrityId;

    /**
     * tk平台达人名
     */
    @TableField("internet_celebrity_name")
    private String internetCelebrityName;

    /**
     * gmv
     */
    @TableField("gmv")
    private BigDecimal gmv;

    /**
     * 播放量
     */
    @TableField("video_views")
    private Integer videoViews;
    /**
     * 视频授权
     */
    private String videoAuthorization;

    /**
     * 平台product id
     */
    @TableField("seller_product_id")
    private String sellerProductId;

    /**
     * 视频链接
     */
    @TableField("video_url")
    private String videoUrl;

    /**
     * 发帖日期
     */
    @TableField("post_date")
    private Date postDate;

    @TableField("ad_date")
    private Date adDate;
    // 广告意图
    @TableField("ad_code")
    private String adCode;
    // 广告名称
    @TableField("ad_name")
    private String adName;
    // 广告意图
    @TableField("ad_intention")
    private String adIntention;
    // 广告反馈
    @TableField("ad_feedback")
    private String adFeedback;

    @TableField("ad_amount_in1_year")
    private BigDecimal adAmountIn1Year;

    @TableField("ad_roas_in1_year")
    private BigDecimal adRoasIn1Year;

}

