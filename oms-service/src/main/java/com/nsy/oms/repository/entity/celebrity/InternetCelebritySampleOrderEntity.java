package com.nsy.oms.repository.entity.celebrity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.oms.repository.entity.base.BaseMpEntity;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * 网红样衣订单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-29
 */
@Getter
@Setter
@TableName("internet_celebrity_sample_order")
public class InternetCelebritySampleOrderEntity extends BaseMpEntity {


    /**
     * 主键id
     */
    @TableId(value = "internet_celebrity_sample_order_id", type = IdType.AUTO)
    private Integer internetCelebritySampleOrderId;

    /**
     * 店铺id
     */
    @TableField("store_id")
    private Integer storeId;

    /**
     * 店铺名
     */
    @TableField("store_name")
    private String storeName;

    /**
     * 样衣店铺id
     */
    @TableField("sample_store_id")
    private Integer sampleStoreId;

    @TableField("sample_store_name")
    private String sampleStoreName;

    /**
     * 平台订单号
     */
    @TableField("platform_order_no")
    private String platformOrderNo;

    /**
     * 订单状态
     */
    @TableField("order_status")
    private String orderStatus;

    /**
     * internet_celebrity_store_relation表主键0表示未建联
     */
    @TableField("store_relation_id")
    private Integer storeRelationId;

    /**
     * tk平台达人id
     */
    @TableField("internet_celebrity_no")
    private String internetCelebrityNo;

    /**
     * 订单买家昵称
     */
    @TableField("buyer_nick")
    private String buyerNick;

    /**
     * 网红id
     */
    @TableField("internet_celebrity_id")
    private Integer internetCelebrityId;

    /**
     * 网红昵称
     */
    @TableField("internet_celebrity_nickname")
    private String internetCelebrityNickname;

    /**
     * 网红部门id(网红负责人部门的一级部门ID)
     */
    @TableField("internet_celebrity_dept_id")
    private Integer internetCelebrityDeptId;

    /**
     * 网红部门(网红负责人部门的一级部门)
     */
    @TableField("internet_celebrity_dept_name")
    private String internetCelebrityDeptName;

    /**
     * 网红平台
     */
    @TableField("internet_celebrity_platform")
    private String internetCelebrityPlatform;

    /**
     * 网红负责人编号
     */
    @TableField("owner_code")
    private String ownerCode;

    /**
     * 网红负责人名称
     */
    @TableField("owner_name")
    private String ownerName;

    /**
     * 订单创建时间
     */
    @TableField("order_create_date")
    private Date orderCreateDate;

    /**
     * 订单最早发货时间(有可能部分发货)
     */
    @TableField("order_delivery_date")
    private Date orderDeliveryDate;

    /**
     * 订单类型
     */
    @TableField("order_type")
    private String orderType;

    /**
     * 订单最早妥投时间(有可能部分发货)
     */
    @TableField("order_compromise_date")
    private Date orderCompromiseDate;

    /**
     * 首次发帖日期
     */
    @TableField(value = "first_post_date", updateStrategy = FieldStrategy.IGNORED)
    private Date firstPostDate;

    /**
     * 待办任务类型：0未生成，1待办，2已完成
     */
    @TableField("task_type")
    private Integer taskType;


    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    //  需要删除字段，这些字段已下沉到item级别。这些字段还未删除，但是不要再继续使用
    /**
     * 发货店铺id
     */
    @TableField("delivery_store_id")
    private Integer deliveryStoreId = 0;

//    /**
//     * 平台原始订单号
//     */
//    @TableField("platform_original_order_no")
//    private String platformOriginalOrderNo;
//
//    /**
//     * 平台包裹id
//     */
//    @TableField("platform_package_id")
//    private String platformPackageId;
//
//    /**
//     * 包裹状态
//     */
//    @TableField("package_status")
//    private String packageStatus;
//
//    /**
//     * 物流单号
//     */
//    @TableField("tracking_number")
//    private String trackingNumber;

//    /**
//     * 物流轨迹同步状态(0：异常，1：成功)
//     */
//    @TableField("tracking_sync_status")
//    private Integer trackingSyncStatus;
//
//    /**
//     * 物流轨迹同步状态信息
//     */
//    @TableField("tracking_sync_error_message")
//    private String trackingSyncErrorMessage;

    //  需要删除字段，这些字段已下沉到item级别。这些字段还未删除，但是不要再继续使用

}
