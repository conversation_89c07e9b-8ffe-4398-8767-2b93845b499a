package com.nsy.oms.repository.entity.points;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.oms.repository.entity.base.BaseMpEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * B2B积分退款
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("b2b_points_refund")
public class B2bPointsRefundEntity extends BaseMpEntity {

    /**
     * 退款ID
     */
    @TableId(value = "b2b_points_refund_id", type = IdType.AUTO)
    private Integer b2bPointsRefundId;

    /**
     * 退款请求ID
     */
    @TableField("refund_id")
    private Integer refundId;

    /**
     * 账期创建时间
     */
    @TableField("account_create_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date accountCreateTime;

    /**
     * 客户名称
     */
    @TableField("customer_name")
    private String customerName;

    /**
     * 客户邮箱
     */
    @TableField("customer_email")
    private String customerEmail;

    /**
     * 店铺id
     */
    @TableField("store_id")
    private Integer storeId;

    /**
     * 店铺名称
     */
    @TableField("store_name")
    private String storeName;

    /**
     * 业务员
     */
    @TableField("salesman")
    private String salesman;

    /**
     * 最后一次下单时间
     */
    @TableField("last_order_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date lastOrderTime;

    /**
     * 待退积分
     */
    @TableField("points_refunded")
    private Integer pointsRefunded;

    /**
     * 待退金额
     */
    @TableField("amount_refunded")
    private BigDecimal amountRefunded;

    /**
     * 状态(待确认,待分配,待处理,已处理)
     */
    @TableField("state")
    private String state;



}
