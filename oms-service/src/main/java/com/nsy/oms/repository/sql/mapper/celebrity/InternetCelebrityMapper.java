package com.nsy.oms.repository.sql.mapper.celebrity;
import java.util.List;
import java.util.Collection;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.oms.business.domain.request.celebrity.InternetCelebrityPageRequest;
import com.nsy.oms.repository.entity.celebrity.InternetCelebrityEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface InternetCelebrityMapper extends BaseMapper<InternetCelebrityEntity> {
    Page<InternetCelebrityEntity> page(IPage page, @Param("query") InternetCelebrityPageRequest request);

    List<InternetCelebrityEntity> findAllByInternetCelebrityNoIn(@Param("internetCelebrityNoList") List<String> internetCelebrityNoList);

    List<InternetCelebrityEntity> findAllByInternetCelebrityNameIn(@Param("internetCelebrityNameList") Collection<String> internetCelebrityNameList);

}