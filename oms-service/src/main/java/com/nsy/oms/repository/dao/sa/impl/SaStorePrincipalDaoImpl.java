package com.nsy.oms.repository.dao.sa.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.oms.business.domain.response.sa.StorePrincipalResponse;
import com.nsy.oms.repository.dao.sa.SaStorePrincipalDao;
import com.nsy.oms.repository.entity.sa.SaStorePrincipalEntity;
import com.nsy.oms.repository.sql.mapper.sa.SaStorePrincipalMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【sa_store_principal(店铺负责人表)】的数据库操作Service实现
 * @createDate 2022-12-15 16:21:43
 */
@Service
public class SaStorePrincipalDaoImpl extends ServiceImpl<SaStorePrincipalMapper, SaStorePrincipalEntity>
        implements SaStorePrincipalDao {

    @Override
    public List<StorePrincipalResponse> getList(Integer storeId) {
        LambdaQueryWrapper<SaStorePrincipalEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SaStorePrincipalEntity::getStoreId, storeId);
        lambdaQueryWrapper.eq(SaStorePrincipalEntity::getStatus, 1);
        return Optional.of(this.list(lambdaQueryWrapper)).orElse(new ArrayList<>()).stream().map(item -> {
            StorePrincipalResponse storePrincipalResponse = new StorePrincipalResponse();
            BeanUtils.copyProperties(item, storePrincipalResponse);
            storePrincipalResponse.setStorePrincipalId(String.valueOf(item.getStorePrincipalId()));
            return storePrincipalResponse;
        }).collect(Collectors.toList());
    }

    @Override
    public SaStorePrincipalEntity getInfo(Integer storePrincipalId, Integer fbaStoreId) {
        LambdaQueryWrapper<SaStorePrincipalEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SaStorePrincipalEntity::getStoreId, fbaStoreId);
        lambdaQueryWrapper.eq(SaStorePrincipalEntity::getStorePrincipalId, storePrincipalId);
        return this.getOne(lambdaQueryWrapper);
    }

    @Override
    public List<SaStorePrincipalEntity> getBatchList(List<Integer> storeIds) {
        LambdaQueryWrapper<SaStorePrincipalEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(SaStorePrincipalEntity::getStoreId, storeIds);
        return this.list(lambdaQueryWrapper);
    }
}




