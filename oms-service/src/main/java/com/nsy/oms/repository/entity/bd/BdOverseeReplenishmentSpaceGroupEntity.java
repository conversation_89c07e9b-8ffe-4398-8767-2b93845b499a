package com.nsy.oms.repository.entity.bd;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 海外仓补货仓库分组
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-19
 */
@TableName("bd_oversea_replenishment_space_group")
public class BdOverseeReplenishmentSpaceGroupEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，无业务含义
     */
    @TableId(value = "replenishment_space_group_id", type = IdType.AUTO)
    private Integer replenishmentSpaceGroupId;

    /**
     * 仓库ID
     */
    @TableField("space_id")
    private Integer spaceId;

    /**
     * 仓库名称
     */
    @TableField("space_name")
    private String spaceName;

    /**
     * 补货分组ID
     */
    @TableField("replenishment_group_id")
    private Integer replenishmentGroupId;

    /**
     * 创建时间
     */
    @TableField(value = "create_date", fill = FieldFill.INSERT)
    private Date createDate;


    /**
     * 更新时间
     */
    @TableField(value = "update_date", fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 版本号
     */
    @Version
    @JsonIgnore
    @TableField("version")
    private Integer version;

    public Integer getReplenishmentSpaceGroupId() {
        return replenishmentSpaceGroupId;
    }

    public void setReplenishmentSpaceGroupId(Integer replenishmentSpaceGroupId) {
        this.replenishmentSpaceGroupId = replenishmentSpaceGroupId;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public Integer getReplenishmentGroupId() {
        return replenishmentGroupId;
    }

    public void setReplenishmentGroupId(Integer replenishmentGroupId) {
        this.replenishmentGroupId = replenishmentGroupId;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }
}