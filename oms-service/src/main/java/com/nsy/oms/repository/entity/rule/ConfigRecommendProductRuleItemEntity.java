package com.nsy.oms.repository.entity.rule;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

/**
 * 上架规则详情
 *
 * @TableName config_recommend_product_rule_item
 */

@TableName("config_recommend_product_rule_item")
public class ConfigRecommendProductRuleItemEntity {
    /**
     * 主键id
     */
    @TableId(value = "config_recommend_product_rule_item_id", type = IdType.AUTO)
    private Integer configRecommendProductRuleItemId;

    /**
     * 规则id
     */
    private Integer configRecommendProductRuleId;

    /**
     * 商品，标签，尺码库存，销量
     */
    private String ruleKey;
    /**
     * 使用类型：上架:SHELF  库存:STOCK_UPDATE  补货:REPLENISHMENT
     */
    private String useType;

    /**
     * 值
     */
    private String ruleValue;

    /**
     * 默认
     */
    private Integer isDefault;

    /**
     * 说明
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 版本号
     */
    private Long version;

    /**
     * 地区
     */
    private String location;

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    public Integer getConfigRecommendProductRuleItemId() {
        return configRecommendProductRuleItemId;
    }

    /**
     * 主键id
     */
    public void setConfigRecommendProductRuleItemId(Integer configRecommendProductRuleItemId) {
        this.configRecommendProductRuleItemId = configRecommendProductRuleItemId;
    }

    /**
     * 规则id
     */
    public Integer getConfigRecommendProductRuleId() {
        return configRecommendProductRuleId;
    }

    /**
     * 规则id
     */
    public void setConfigRecommendProductRuleId(Integer configRecommendProductRuleId) {
        this.configRecommendProductRuleId = configRecommendProductRuleId;
    }

    /**
     * 商品，标签，尺码库存，销量
     */
    public String getRuleKey() {
        return ruleKey;
    }

    /**
     * 商品，标签，尺码库存，销量
     */
    public void setRuleKey(String ruleKey) {
        this.ruleKey = ruleKey;
    }

    /**
     * 值
     */
    public String getRuleValue() {
        return ruleValue;
    }

    /**
     * 值
     */
    public void setRuleValue(String ruleValue) {
        this.ruleValue = ruleValue;
    }

    /**
     * 说明
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 说明
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 创建时间
     */
    public Date getCreateDate() {
        return createDate;
    }

    /**
     * 创建时间
     */
    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    /**
     * 创建人
     */
    public String getCreateBy() {
        return createBy;
    }

    /**
     * 创建人
     */
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    /**
     * 更新时间
     */
    public Date getUpdateDate() {
        return updateDate;
    }

    /**
     * 更新时间
     */
    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    /**
     * 更新人
     */
    public String getUpdateBy() {
        return updateBy;
    }

    /**
     * 更新人
     */
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 版本号
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 版本号
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    /**
     * 地区
     */
    public String getLocation() {
        return location;
    }

    /**
     * 地区
     */
    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }

    public String getUseType() {
        return useType;
    }

    public void setUseType(String useType) {
        this.useType = useType;
    }
}