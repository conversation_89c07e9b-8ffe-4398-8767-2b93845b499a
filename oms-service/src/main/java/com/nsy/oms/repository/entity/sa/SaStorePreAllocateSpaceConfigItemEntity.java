package com.nsy.oms.repository.entity.sa;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.oms.repository.entity.base.BaseMpEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 店铺预配仓配置子表
 *
 * @TableName sa_store_pre_allocate_space_config_item
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("sa_store_pre_allocate_space_config_item")
public class SaStorePreAllocateSpaceConfigItemEntity extends BaseMpEntity {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 主表id
     */
    private Integer saStorePreAllocateSpaceConfigId;

    /**
     * 仓库id
     */
    private Integer spaceId;

    /**
     * 仓库名称
     */
    private String spaceName;

    /**
     * 是否主预配仓库
     */
    private Integer primaryType;

    /**
     * 预配顺序(0最靠前)
     */
    private Integer sort;


    private static final long serialVersionUID = 1L;
}