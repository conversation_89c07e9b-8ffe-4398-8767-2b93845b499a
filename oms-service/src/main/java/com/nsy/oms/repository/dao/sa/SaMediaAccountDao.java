package com.nsy.oms.repository.dao.sa;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nsy.oms.business.domain.request.sa.SaMediaAccountPageRequest;
import com.nsy.oms.business.domain.response.sa.SaMediaAccountResponse;
import com.nsy.oms.repository.entity.sa.SaMediaAccountEntity;


/**
* <AUTHOR>
* @description 针对表【sa_media_account(社媒账号-社媒账号表)】的数据库操作Service
* @createDate 2024-10-30 10:48:42
*/
public interface SaMediaAccountDao extends IService<SaMediaAccountEntity> {

    Page<SaMediaAccountResponse> getList(Page page, SaMediaAccountPageRequest request);
}
