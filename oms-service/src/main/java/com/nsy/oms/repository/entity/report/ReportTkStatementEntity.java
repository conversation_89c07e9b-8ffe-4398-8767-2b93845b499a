package com.nsy.oms.repository.entity.report;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.oms.repository.entity.base.BaseMpEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * TK账单表
 *
 * @TableName report_tk_statement
 */
@TableName("report_tk_statement")
public class ReportTkStatementEntity extends BaseMpEntity {
    /**
     * 主键ID
     */
    @TableId(value = "tk_statement_id", type = IdType.AUTO)
    private Integer tkStatementId;

    /**
     * 店铺ID
     */
    @TableField("store_id")
    private Integer storeId;

    /**
     * 店铺名称
     */
    @TableField("store_name")
    private String storeName;

    /**
     * 账单ID
     */
    @TableField("statement_id")
    private String statementId;

    /**
     * 结算时间
     */
    @TableField("statement_time")
    private Date statementTime;

    /**
     * 结算金额
     */
    @TableField("settlement_amount")
    private BigDecimal settlementAmount;

    /**
     * 货币类型
     */
    @TableField("currency")
    private String currency;

    /**
     * 收入金额
     */
    @TableField("revenue_amount")
    private BigDecimal revenueAmount;

    /**
     * 费用金额
     */
    @TableField("fee_amount")
    private BigDecimal feeAmount;

    /**
     * 调整金额
     */
    @TableField("adjustment_amount")
    private BigDecimal adjustmentAmount;

    /**
     * 支付状态
     */
    @TableField("payment_status")
    private String paymentStatus;

    /**
     * 支付ID
     */
    @TableField("payment_id")
    private Long paymentId;

    /**
     * 净销售额
     */
    @TableField("net_sales_amount")
    private BigDecimal netSalesAmount;

    /**
     * 运费金额
     */
    @TableField("shipping_cost_amount")
    private BigDecimal shippingCostAmount;

    /**
     * 总收入金额
     */
    @TableField("total_revenue_amount")
    private BigDecimal totalRevenueAmount;

    /**
     * 总运费金额
     */
    @TableField("total_shipping_cost_amount")
    private BigDecimal totalShippingCostAmount;

    /**
     * 总费用税金金额
     */
    @TableField("total_fee_tax_amount")
    private BigDecimal totalFeeTaxAmount;

    /**
     * 总调整金额
     */
    @TableField("total_adjustment_amount")
    private BigDecimal totalAdjustmentAmount;

    /**
     * 总结算金额
     */
    @TableField("total_settlement_amount")
    private BigDecimal totalSettlementAmount;

    /**
     * 状态
     */
    @TableField("status")
    private String status;

    /**
     * 总数
     */
    @TableField("total_count")
    private Integer totalCount;

    /**
     * 拉取数据状态（0未拉取，1已拉取）
     */
    @TableField("is_pull_status")
    private Integer isPullStatus;


    public Integer getTkStatementId() {
        return tkStatementId;
    }

    public void setTkStatementId(Integer tkStatementId) {
        this.tkStatementId = tkStatementId;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getStatementId() {
        return statementId;
    }

    public void setStatementId(String statementId) {
        this.statementId = statementId;
    }

    public Date getStatementTime() {
        return statementTime;
    }

    public void setStatementTime(Date statementTime) {
        this.statementTime = statementTime;
    }

    public BigDecimal getSettlementAmount() {
        return settlementAmount;
    }

    public void setSettlementAmount(BigDecimal settlementAmount) {
        this.settlementAmount = settlementAmount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public BigDecimal getRevenueAmount() {
        return revenueAmount;
    }

    public void setRevenueAmount(BigDecimal revenueAmount) {
        this.revenueAmount = revenueAmount;
    }

    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public BigDecimal getAdjustmentAmount() {
        return adjustmentAmount;
    }

    public void setAdjustmentAmount(BigDecimal adjustmentAmount) {
        this.adjustmentAmount = adjustmentAmount;
    }

    public String getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(String paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public Long getPaymentId() {
        return paymentId;
    }

    public void setPaymentId(Long paymentId) {
        this.paymentId = paymentId;
    }

    public BigDecimal getNetSalesAmount() {
        return netSalesAmount;
    }

    public void setNetSalesAmount(BigDecimal netSalesAmount) {
        this.netSalesAmount = netSalesAmount;
    }

    public BigDecimal getShippingCostAmount() {
        return shippingCostAmount;
    }

    public void setShippingCostAmount(BigDecimal shippingCostAmount) {
        this.shippingCostAmount = shippingCostAmount;
    }

    public BigDecimal getTotalRevenueAmount() {
        return totalRevenueAmount;
    }

    public void setTotalRevenueAmount(BigDecimal totalRevenueAmount) {
        this.totalRevenueAmount = totalRevenueAmount;
    }

    public BigDecimal getTotalShippingCostAmount() {
        return totalShippingCostAmount;
    }

    public void setTotalShippingCostAmount(BigDecimal totalShippingCostAmount) {
        this.totalShippingCostAmount = totalShippingCostAmount;
    }

    public BigDecimal getTotalFeeTaxAmount() {
        return totalFeeTaxAmount;
    }

    public void setTotalFeeTaxAmount(BigDecimal totalFeeTaxAmount) {
        this.totalFeeTaxAmount = totalFeeTaxAmount;
    }

    public BigDecimal getTotalAdjustmentAmount() {
        return totalAdjustmentAmount;
    }

    public void setTotalAdjustmentAmount(BigDecimal totalAdjustmentAmount) {
        this.totalAdjustmentAmount = totalAdjustmentAmount;
    }

    public BigDecimal getTotalSettlementAmount() {
        return totalSettlementAmount;
    }

    public void setTotalSettlementAmount(BigDecimal totalSettlementAmount) {
        this.totalSettlementAmount = totalSettlementAmount;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Integer getIsPullStatus() {
        return isPullStatus;
    }

    public void setIsPullStatus(Integer isPullStatus) {
        this.isPullStatus = isPullStatus;
    }
}