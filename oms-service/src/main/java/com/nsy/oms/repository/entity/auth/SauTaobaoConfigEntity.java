package com.nsy.oms.repository.entity.auth;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.oms.annotation.FieldDesc;
import com.nsy.oms.repository.entity.base.BaseMpEntity;

import java.util.Date;

/**
 * 店铺授权-淘宝授权表
 *
 * @TableName sau_taobao_config
 */
@TableName("sau_taobao_config")
public class SauTaobaoConfigEntity extends BaseMpEntity {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 店铺名称
     */
    @TableField("store_name")
    private String storeName;

    /**
     * 店铺ID
     */
    @TableField("store_id")
    private Integer storeId;

    /**
     * 店铺URL
     */
    @FieldDesc("店铺URL")
    @TableField("store_url")
    private String storeUrl;

    /**
     * 淘宝Appkey
     */
    @FieldDesc("淘宝Appkey")
    @TableField("appkey")
    private String appkey;

    /**
     * 淘宝AppSecret
     */
    @FieldDesc("淘宝AppSecret")
    @TableField("appsecret")
    private String appsecret;

    /**
     * refresh_token
     */
    @TableField("refresh_token")
    @FieldDesc("refresh_token")
    private String refreshToken;

    /**
     * access_token
     */
    @TableField("access_token")
    @FieldDesc("access_token")
    private String accessToken;

    /**
     * token状态 0:正常  1:异常
     */
    @FieldDesc("token状态")
    @TableField("token_status")
    private Integer tokenStatus;

    /**
     * 授权状态 0:关闭  1:开启
     */
    @FieldDesc("授权状态")
    @TableField("grant_auth_status")
    private Integer grantAuthStatus;

    /**
     * token有效起始时间
     */
    @FieldDesc("token有效起始时间")
    @TableField("token_validity_date_start")
    private Date tokenValidityDateStart;

    /**
     * token有效终止时间
     */
    @FieldDesc("token有效终止时间")
    @TableField("token_validity_date_end")
    private Date tokenValidityDateEnd;

    /**
     * 扩展属性1
     */
    @FieldDesc("扩展属性1")
    @TableField("extended_attributes_one")
    private String extendedAttributesOne;

    /**
     * 扩展属性2
     */
    @FieldDesc("扩展属性2")
    @TableField("extended_attributes_two")
    private String extendedAttributesTwo;

    /**
     * 扩展属性3
     */
    @FieldDesc("扩展属性3")
    @TableField("extended_attributes_three")
    private String extendedAttributesThree;


    /**
     * 授权日期
     */
    @TableField("grant_date")
    private Date grantDate;


    /**
     * 有效期
     */
    @TableField("validity_date")
    private Date validityDate;


    /**
     * 开始抓单时间
     */
    @TableField("catch_date")
    private Date catchDate;

    /**
     * 当前抓单时间
     */
    @TableField("current_catch_date")
    private Date currentCatchDate;


    /**
     * token重新刷新时间
     */
    @TableField("refresh_token_timeout")
    private Date refreshTokenTimeout;


    /**
     * 权限编码
     */
    @TableField("auth_code")
    private String authCode;


    /**
     * 随机数
     */
    @TableField("random_number")
    private String randomNumber;


    /**
     * 淘宝类型: 淘宝：C, 天猫：B
     */
    @TableField("taobao_type")
    private String taoBaoType;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    public Integer getId() {
        return id;
    }

    /**
     *
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 店铺名称
     */
    public String getStoreName() {
        return storeName;
    }

    /**
     * 店铺名称
     */
    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    /**
     * 店铺ID
     */
    public Integer getStoreId() {
        return storeId;
    }

    /**
     * 店铺ID
     */
    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    /**
     * 店铺URL
     */
    public String getStoreUrl() {
        return storeUrl;
    }

    /**
     * 店铺URL
     */
    public void setStoreUrl(String storeUrl) {
        this.storeUrl = storeUrl;
    }

    /**
     * 淘宝Appkey
     */
    public String getAppkey() {
        return appkey;
    }

    /**
     * 淘宝Appkey
     */
    public void setAppkey(String appkey) {
        this.appkey = appkey;
    }

    /**
     * 淘宝AppSecret
     */
    public String getAppsecret() {
        return appsecret;
    }

    /**
     * 淘宝AppSecret
     */
    public void setAppsecret(String appsecret) {
        this.appsecret = appsecret;
    }

    /**
     * refresh_token
     */
    public String getRefreshToken() {
        return refreshToken;
    }

    /**
     * refresh_token
     */
    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    /**
     * access_token
     */
    public String getAccessToken() {
        return accessToken;
    }

    /**
     * access_token
     */
    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    /**
     * token状态 0:正常  1:异常
     */
    public Integer getTokenStatus() {
        return tokenStatus;
    }

    /**
     * token状态 0:正常  1:异常
     */
    public void setTokenStatus(Integer tokenStatus) {
        this.tokenStatus = tokenStatus;
    }

    /**
     * 授权状态 0:关闭  1:开启
     */
    public Integer getGrantAuthStatus() {
        return grantAuthStatus;
    }

    /**
     * 授权状态 0:关闭  1:开启
     */
    public void setGrantAuthStatus(Integer grantAuthStatus) {
        this.grantAuthStatus = grantAuthStatus;
    }

    /**
     * token有效起始时间
     */
    public Date getTokenValidityDateStart() {
        return tokenValidityDateStart;
    }

    /**
     * token有效起始时间
     */
    public void setTokenValidityDateStart(Date tokenValidityDateStart) {
        this.tokenValidityDateStart = tokenValidityDateStart;
    }

    /**
     * token有效终止时间
     */
    public Date getTokenValidityDateEnd() {
        return tokenValidityDateEnd;
    }

    /**
     * token有效终止时间
     */
    public void setTokenValidityDateEnd(Date tokenValidityDateEnd) {
        this.tokenValidityDateEnd = tokenValidityDateEnd;
    }

    /**
     * 扩展属性1
     */
    public String getExtendedAttributesOne() {
        return extendedAttributesOne;
    }

    /**
     * 扩展属性1
     */
    public void setExtendedAttributesOne(String extendedAttributesOne) {
        this.extendedAttributesOne = extendedAttributesOne;
    }

    /**
     * 扩展属性2
     */
    public String getExtendedAttributesTwo() {
        return extendedAttributesTwo;
    }

    /**
     * 扩展属性2
     */
    public void setExtendedAttributesTwo(String extendedAttributesTwo) {
        this.extendedAttributesTwo = extendedAttributesTwo;
    }

    public String getExtendedAttributesThree() {
        return extendedAttributesThree;
    }

    public void setExtendedAttributesThree(String extendedAttributesThree) {
        this.extendedAttributesThree = extendedAttributesThree;
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    public String getRandomNumber() {
        return randomNumber;
    }

    public void setRandomNumber(String randomNumber) {
        this.randomNumber = randomNumber;
    }

    public Date getGrantDate() {
        return grantDate;
    }

    public void setGrantDate(Date grantDate) {
        this.grantDate = grantDate;
    }

    public Date getValidityDate() {
        return validityDate;
    }

    public void setValidityDate(Date validityDate) {
        this.validityDate = validityDate;
    }

    public Date getCatchDate() {
        return catchDate;
    }

    public void setCatchDate(Date catchDate) {
        this.catchDate = catchDate;
    }

    public Date getCurrentCatchDate() {
        return currentCatchDate;
    }

    public void setCurrentCatchDate(Date currentCatchDate) {
        this.currentCatchDate = currentCatchDate;
    }

    public Date getRefreshTokenTimeout() {
        return refreshTokenTimeout;
    }

    public void setRefreshTokenTimeout(Date refreshTokenTimeout) {
        this.refreshTokenTimeout = refreshTokenTimeout;
    }

    public String getTaoBaoType() {
        return taoBaoType;
    }

    public void setTaoBaoType(String taoBaoType) {
        this.taoBaoType = taoBaoType;
    }
}