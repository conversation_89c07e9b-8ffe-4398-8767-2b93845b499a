package com.nsy.oms.repository.entity.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.oms.repository.entity.base.BaseMpEntity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单表
 *
 * <AUTHOR>
 * @TableName order
 */
@TableName("sale_order")
public class SaleOrderEntity extends BaseMpEntity implements Serializable {
    private static final long serialVersionUID = -7094148483607663226L;
    /**
     * 订单ID
     */
    @TableId(value = "order_id", type = IdType.AUTO)
    private Integer orderId;

    /**
     * 订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 平台订单号
     */
    @TableField("platform_order_no")
    private String platformOrderNo;

    /**
     * 订单状态：
     * 10-付款未发货
     * 20-部分发货
     * 21-已发货
     * 30-已取消
     */
    @TableField("order_status")
    private Integer orderStatus;

    /**
     * 实付金额
     */
    @TableField("payment_amount")
    private BigDecimal paymentAmount;

    /**
     * 商品折扣金额
     */
    @TableField("product_discount_amount")
    private BigDecimal productDiscountAmount;

    /**
     * 商品总金额
     */
    @TableField("product_total_amount")
    private BigDecimal productTotalAmount;

    /**
     * 运费
     */
    @TableField("freight_fee")
    private BigDecimal freightFee;

    /**
     * 平台手续费
     */
    @TableField("commission_fee")
    private BigDecimal commissionFee;

    /**
     * 加工费
     */
    @TableField("process_fee")
    private BigDecimal processFee;

    /**
     * 币种
     */
    @TableField("currency")
    private String currency;

    /**
     * 物流方式
     */
    @TableField("shipping_type")
    private String shippingType;

    /**
     * 平台id
     */
    @TableField("platform_id")
    private Integer platformId;

    /**
     * 平台名称
     */
    @TableField("platform_name")
    private String platformName;

    /**
     * 店铺id
     */
    @TableField("store_id")
    private Integer storeId;

    /**
     * 店铺名称
     */
    @TableField("store_name")
    private String storeName;

    /**
     * 市场编码
     */
    @TableField("market_code")
    private String marketCode;

    /**
     * 市场名称
     */
    @TableField("market_name")
    private String marketName;
    /**
     * 业务员
     */
    @TableField("sales_man")
    private String salesMan;

    /**
     * 买家备注
     */
    @TableField("buyer_remark")
    private String buyerRemark;

    /**
     * 订单创建时间
     */
    @TableField("order_create_date")
    private Date orderCreateDate;

    /**
     * 最早发货时间
     */
    @TableField("earliest_ship_date")
    private Date earliestShipDate;

    /**
     * 最晚发货时间
     */
    @TableField("latest_ship_date")
    private Date latestShipDate;

    /**
     * 最早交货时间
     */
    @TableField("earliest_delivery_date")
    private Date earliestDeliveryDate;
    /**
     * 运费券
     */
    @TableField("freight_voucher")
    private BigDecimal freightVoucher;
    /**
     * 最晚交货时间
     */
    @TableField("latest_delivery_date")
    private Date latestDeliveryDate;

    /**
     * 订单付款时间
     */
    @TableField("order_payment_date")
    private Date orderPaymentDate;

    /**
     * 订单取消时间
     */
    @TableField("order_cancel_date")
    private Date orderCancelDate;

    /**
     * 亚马逊：是否业务买方的订单(亚马逊):  0-否，1-是
     */
    @TableField("is_business_order")
    private Boolean isBusinessOrder;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getPlatformOrderNo() {
        return platformOrderNo;
    }

    public void setPlatformOrderNo(String platformOrderNo) {
        this.platformOrderNo = platformOrderNo;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public BigDecimal getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(BigDecimal paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public BigDecimal getProductDiscountAmount() {
        return productDiscountAmount;
    }

    public void setProductDiscountAmount(BigDecimal productDiscountAmount) {
        this.productDiscountAmount = productDiscountAmount;
    }

    public BigDecimal getProductTotalAmount() {
        return productTotalAmount;
    }

    public void setProductTotalAmount(BigDecimal productTotalAmount) {
        this.productTotalAmount = productTotalAmount;
    }

    public BigDecimal getFreightFee() {
        return freightFee;
    }

    public void setFreightFee(BigDecimal freightFee) {
        this.freightFee = freightFee;
    }

    public BigDecimal getCommissionFee() {
        return commissionFee;
    }

    public void setCommissionFee(BigDecimal commissionFee) {
        this.commissionFee = commissionFee;
    }

    public BigDecimal getProcessFee() {
        return processFee;
    }

    public void setProcessFee(BigDecimal processFee) {
        this.processFee = processFee;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getShippingType() {
        return shippingType;
    }

    public void setShippingType(String shippingType) {
        this.shippingType = shippingType;
    }

    public Integer getPlatformId() {
        return platformId;
    }

    public void setPlatformId(Integer platformId) {
        this.platformId = platformId;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getMarketCode() {
        return marketCode;
    }

    public void setMarketCode(String marketCode) {
        this.marketCode = marketCode;
    }

    public String getMarketName() {
        return marketName;
    }

    public void setMarketName(String marketName) {
        this.marketName = marketName;
    }

    public String getBuyerRemark() {
        return buyerRemark;
    }

    public void setBuyerRemark(String buyerRemark) {
        this.buyerRemark = buyerRemark;
    }

    public Date getOrderCreateDate() {
        return orderCreateDate;
    }

    public void setOrderCreateDate(Date orderCreateDate) {
        this.orderCreateDate = orderCreateDate;
    }

    public Date getEarliestShipDate() {
        return earliestShipDate;
    }

    public void setEarliestShipDate(Date earliestShipDate) {
        this.earliestShipDate = earliestShipDate;
    }

    public Date getLatestShipDate() {
        return latestShipDate;
    }

    public void setLatestShipDate(Date latestShipDate) {
        this.latestShipDate = latestShipDate;
    }

    public Date getEarliestDeliveryDate() {
        return earliestDeliveryDate;
    }

    public void setEarliestDeliveryDate(Date earliestDeliveryDate) {
        this.earliestDeliveryDate = earliestDeliveryDate;
    }

    public Date getLatestDeliveryDate() {
        return latestDeliveryDate;
    }

    public void setLatestDeliveryDate(Date latestDeliveryDate) {
        this.latestDeliveryDate = latestDeliveryDate;
    }

    public Date getOrderPaymentDate() {
        return orderPaymentDate;
    }

    public void setOrderPaymentDate(Date orderPaymentDate) {
        this.orderPaymentDate = orderPaymentDate;
    }

    public Date getOrderCancelDate() {
        return orderCancelDate;
    }

    public void setOrderCancelDate(Date orderCancelDate) {
        this.orderCancelDate = orderCancelDate;
    }

    public Boolean getBusinessOrder() {
        return isBusinessOrder;
    }

    public void setBusinessOrder(Boolean businessOrder) {
        isBusinessOrder = businessOrder;
    }

    public BigDecimal getFreightVoucher() {
        return freightVoucher;
    }

    public void setFreightVoucher(BigDecimal freightVoucher) {
        this.freightVoucher = freightVoucher;
    }

    public String getSalesMan() {
        return salesMan;
    }

    public void setSalesMan(String salesMan) {
        this.salesMan = salesMan;
    }
}