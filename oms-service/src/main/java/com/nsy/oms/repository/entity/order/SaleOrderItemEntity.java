package com.nsy.oms.repository.entity.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.oms.repository.entity.base.BaseMpEntity;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单详情表
 * <AUTHOR>
 * @TableName order_item
 */
@TableName("sale_order_item")
public class SaleOrderItemEntity extends BaseMpEntity implements Serializable {
    private static final long serialVersionUID = 4764410344511789673L;
    /**
     * 订单明细ID
     */
    @TableId(value = "order_item_id", type = IdType.AUTO)
    private Integer orderItemId;

    /**
     * 订单ID
     */
    @TableField("order_id")
    private Integer orderId;

    /**
     * 平台订单明细ID
     */
    @TableField("platform_item_id")
    private String platformItemId;

    /**
     * 明细状态：0-正常，1-删除
     */
    @TableField("item_status")
    private Integer itemStatus;

    /**
     * sku
     */
    @TableField("sku")
    private String sku;

    /**
     * 商品规格ID
     */
    @TableField("spec_id")
    private Integer specId;

    /**
     * 商品ID
     */
    @TableField("product_id")
    private Integer productId;

    /**
     * 数量
     */
    @TableField("qty")
    private Integer qty;

    /**
     * 退款ID
     */
    @TableField("refund_id")
    private String refundId;

    /**
     * 退款状态:WAIT_SELLER_AGREE(买家已经申请退款,等待卖家同意),WAIT_BUYER_RETURN_GOODS(卖家已经同意退款,等待买家退货),WAIT_SELLER_CONFIRM_GOODS5(买家已经退货,等待卖家确认收货),CLOSED(退款关闭),SUCCESS(退款成功),SELLER_REFUSE_BUYER(卖家拒绝退款),NO_REFUND(没有退款) 
     */
    @TableField("refund_status")
    private String refundStatus;

    /**
     * 销售单价(折前)
     */
    @TableField("unit_price")
    private BigDecimal unitPrice;

    /**
     * 商品总金额(折前)
     */
    @TableField("total_amount")
    private BigDecimal totalAmount;

    /**
     * 商品实付金额(折后)
     */
    @TableField("payment_amount")
    private BigDecimal paymentAmount;

    /**
     * 商品单件折扣
     */
    @TableField("unit_discount")
    private BigDecimal unitDiscount;

    /**
     * 运费
     */
    @TableField("freight_fee")
    private BigDecimal freightFee;

    /**
     * 运费折扣
     */
    @TableField("freight_fee_discount")
    private BigDecimal freightFeeDiscount;

    /**
     * 卖方SKU
     */
    @TableField("seller_sku")
    private String sellerSku;

    /**
     * 卖方sku_id
     */
    @TableField("seller_sku_id")
    private String sellerSkuId;

    /**
     * 卖方product_id
     */
    @TableField("seller_product_id")
    private String sellerProductId;

    /**
     * 进口一站式服务编号
     */
    @TableField("ioss_number")
    private String iossNumber;

    /**
     * 是否加入透明计划：0-否，1-是
     */
    @TableField("is_transparency")
    private Integer isTransparency;

    /**
     * asin
     */
    @TableField("asin")
    private String asin;


    public Integer getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Integer orderItemId) {
        this.orderItemId = orderItemId;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getPlatformItemId() {
        return platformItemId;
    }

    public void setPlatformItemId(String platformItemId) {
        this.platformItemId = platformItemId;
    }

    public Integer getItemStatus() {
        return itemStatus;
    }

    public void setItemStatus(Integer itemStatus) {
        this.itemStatus = itemStatus;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getSpecId() {
        return specId;
    }

    public void setSpecId(Integer specId) {
        this.specId = specId;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Integer getQty() {
        return qty;
    }

    public void setQty(Integer qty) {
        this.qty = qty;
    }

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public String getRefundStatus() {
        return refundStatus;
    }

    public void setRefundStatus(String refundStatus) {
        this.refundStatus = refundStatus;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(BigDecimal paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public BigDecimal getUnitDiscount() {
        return unitDiscount;
    }

    public void setUnitDiscount(BigDecimal unitDiscount) {
        this.unitDiscount = unitDiscount;
    }

    public String getSellerSku() {
        return sellerSku;
    }

    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    public String getIossNumber() {
        return iossNumber;
    }

    public void setIossNumber(String iossNumber) {
        this.iossNumber = iossNumber;
    }

    public Integer getIsTransparency() {
        return isTransparency;
    }

    public void setIsTransparency(Integer isTransparency) {
        this.isTransparency = isTransparency;
    }

    public String getAsin() {
        return asin;
    }

    public void setAsin(String asin) {
        this.asin = asin;
    }

    public BigDecimal getFreightFee() {
        return freightFee;
    }

    public void setFreightFee(BigDecimal freightFee) {
        this.freightFee = freightFee;
    }

    public BigDecimal getFreightFeeDiscount() {
        return freightFeeDiscount;
    }

    public void setFreightFeeDiscount(BigDecimal freightFeeDiscount) {
        this.freightFeeDiscount = freightFeeDiscount;
    }

    public String getSellerSkuId() {
        return sellerSkuId;
    }

    public void setSellerSkuId(String sellerSkuId) {
        this.sellerSkuId = sellerSkuId;
    }

    public String getSellerProductId() {
        return sellerProductId;
    }

    public void setSellerProductId(String sellerProductId) {
        this.sellerProductId = sellerProductId;
    }
}