package com.nsy.oms.repository.entity.bd;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.oms.repository.entity.base.BaseMpEntity;


/**
 * 账号主体开户行信息表
 *
 * @TableName bd_account_subject_bank
 */
@TableName("bd_account_subject_bank")
public class BdAccountSubjectBankEntity extends BaseMpEntity {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 主体id
     */
    @TableField("subject_id")
    private Integer subjectId;

    /**
     * 开户行名称(数据字典名称)
     */
    @TableField("bank_name")
    private String bankName;

    /**
     * 开户行id
     */
    @TableField("bank_id")
    private String bankId;

    /**
     * 户名
     */
    @TableField("account_name")
    private String accountName;

    /**
     * 银行账号
     */
    @TableField("bank_account")
    private String bankAccount;
    /**
     * 状态
     */
    @TableField("status")
    private Integer status;

    /**
     * 银行性质
     */
    @TableField("nature")
    private String nature;
    /**
     * 币种
     */
    @TableField("currency")
    private String currency;


    public String getNature() {
        return nature;
    }

    public void setNature(String nature) {
        this.nature = nature;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getSubjectId() {
        return subjectId;
    }

    public void setSubjectId(Integer subjectId) {
        this.subjectId = subjectId;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankId() {
        return bankId;
    }

    public void setBankId(String bankId) {
        this.bankId = bankId;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }
}