package com.nsy.oms.repository.entity.celebrity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.oms.repository.entity.base.BaseMpEntity;

import java.math.BigDecimal;
import java.util.Date;

@TableName("internet_celebrity_ad_daily")
public class InternetCelebrityAdDailyEntity extends BaseMpEntity {
    
    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer postId;

    private Integer storeId;

    private String storeName;

    private String videoNo;

    private String adNo;

    private Date adDate;

    private BigDecimal adAmount;

    private BigDecimal saleAmount;

    private String currency;

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getVideoNo() {
        return videoNo;
    }

    public void setVideoNo(String videoNo) {
        this.videoNo = videoNo;
    }

    public String getAdNo() {
        return adNo;
    }

    public void setAdNo(String adNo) {
        this.adNo = adNo;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getPostId() {
        return postId;
    }

    public void setPostId(Integer postId) {
        this.postId = postId;
    }

    public Date getAdDate() {
        return adDate;
    }

    public void setAdDate(Date adDate) {
        this.adDate = adDate;
    }

    public BigDecimal getAdAmount() {
        return adAmount;
    }

    public void setAdAmount(BigDecimal adAmount) {
        this.adAmount = adAmount;
    }

    public BigDecimal getSaleAmount() {
        return saleAmount;
    }

    public void setSaleAmount(BigDecimal saleAmount) {
        this.saleAmount = saleAmount;
    }
}