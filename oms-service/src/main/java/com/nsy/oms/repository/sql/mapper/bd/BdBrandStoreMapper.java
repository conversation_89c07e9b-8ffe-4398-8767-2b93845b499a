package com.nsy.oms.repository.sql.mapper.bd;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.oms.business.domain.request.bd.brand.BdBrandCommonPageRequest;
import com.nsy.oms.business.domain.response.bd.brand.BdBrandStore;
import com.nsy.oms.repository.entity.bd.BdBrandStoreEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 品牌店铺配置表
 *
 * <AUTHOR>
 * @date 2023-09-21
 */
public interface BdBrandStoreMapper extends BaseMapper<BdBrandStoreEntity> {
    IPage<BdBrandStore> page(IPage<BdBrandStoreEntity> page, @Param("request") BdBrandCommonPageRequest request);

    List<Integer> getAllByStoreId();

    List<BdBrandStore> listByStoreIds(@Param("storeIds") List<Integer> storeIds);
}