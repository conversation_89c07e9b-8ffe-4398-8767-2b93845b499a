package com.nsy.oms.repository.mongo.repository.tiktok;

import com.nsy.oms.repository.mongo.document.tiktok.ReportTkStatementOrderTransactionDocument;
import com.nsy.oms.repository.mongo.repository.DocumentRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ReportTkStatementOrderTransactionDocumentRepository extends DocumentRepository<ReportTkStatementOrderTransactionDocument, String> {
    ReportTkStatementOrderTransactionDocument findAllByStoreIdAndOrderId(Integer storeId, String orderId);
}
