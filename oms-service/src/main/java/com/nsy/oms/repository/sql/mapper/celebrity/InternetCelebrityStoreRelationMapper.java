package com.nsy.oms.repository.sql.mapper.celebrity;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nsy.oms.repository.entity.celebrity.InternetCelebrityStoreRelationEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InternetCelebrityStoreRelationMapper extends BaseMapper<InternetCelebrityStoreRelationEntity> {


    List<Integer> findOrderRelationWithNoCreator(@Param("creatorNo") String creatorNo, @Param("creatorName") String creatorName);

    List<Integer> findVideoRelationWithNoCreator(@Param("creatorNo") String creatorNo, @Param("creatorName") String creatorName);


    InternetCelebrityStoreRelationEntity findTopByOrderId(@Param("orderId") Integer orderId);
}