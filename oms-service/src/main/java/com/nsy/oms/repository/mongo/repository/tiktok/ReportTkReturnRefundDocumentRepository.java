package com.nsy.oms.repository.mongo.repository.tiktok;

import com.nsy.oms.repository.mongo.document.tiktok.ReportTkReturnRefundDocument;
import com.nsy.oms.repository.mongo.repository.DocumentRepository;
import org.springframework.stereotype.Repository;

/**
 * tk退货账单
 */
@Repository
public interface ReportTkReturnRefundDocumentRepository extends DocumentRepository<ReportTkReturnRefundDocument, String> {
    ReportTkReturnRefundDocument findAllByStoreIdAndReturnIdAndOrderId(Integer storeId, String refundId, String orderId);
}