package com.nsy.oms.repository.entity.bd;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * 市场表
 * <AUTHOR>
 * @TableName bd_marketplace
 */
@TableName("bd_marketplace")
public class BdMarketplaceEntity implements Serializable {
    private static final long serialVersionUID = -1326323886825398109L;
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 
     */
    @TableField("marketplace_id")
    private String marketplaceId;

    /**
     * 区域
     */
    @TableField("region")
    private String region;

    /**
     * 区域
     */
    @TableField("region_id")
    private Integer regionId;

    /**
     * 市场名,对应国家
     */
    @TableField("marketplace")
    private String marketplace;

    /**
     * 国家 - 中文名
     */
    @TableField("marketplace_chinese")
    private String marketplaceChinese;

    /**
     * 简称
     */
    @TableField("country_code")
    private String countryCode;

    /**
     * 具体时区id
     */
    @TableField("time_zone_id")
    private String timeZoneId;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;


    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 版本号
     */
    @Version
    @JsonIgnore
    private Integer version;
    /**
     * 站点：多个;分割
     */
    @TableField("marketplace_sites")
    private String marketplaceSites;


    public Integer getRegionId() {
        return regionId;
    }

    public void setRegionId(Integer regionId) {
        this.regionId = regionId;
    }

    /**
     * 
     */
    public Integer getId() {
        return id;
    }

    /**
     * 
     */
    public void setId(Integer id) {
        this.id = id;
    }


    /**
     * 区域
     */
    public String getRegion() {
        return region;
    }

    /**
     * 区域
     */
    public void setRegion(String region) {
        this.region = region;
    }

    /**
     * 市场名,对应国家
     */
    public String getMarketplace() {
        return marketplace;
    }

    /**
     * 市场名,对应国家
     */
    public void setMarketplace(String marketplace) {
        this.marketplace = marketplace;
    }

    public String getMarketplaceId() {
        return marketplaceId;
    }

    public void setMarketplaceId(String marketplaceId) {
        this.marketplaceId = marketplaceId;
    }

    public String getMarketplaceChinese() {
        return marketplaceChinese;
    }

    public void setMarketplaceChinese(String marketplaceChinese) {
        this.marketplaceChinese = marketplaceChinese;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getTimeZoneId() {
        return timeZoneId;
    }

    public void setTimeZoneId(String timeZoneId) {
        this.timeZoneId = timeZoneId;
    }

    public String getMarketplaceSites() {
        return marketplaceSites;
    }

    public void setMarketplaceSites(String marketplaceSites) {
        this.marketplaceSites = marketplaceSites;
    }
}