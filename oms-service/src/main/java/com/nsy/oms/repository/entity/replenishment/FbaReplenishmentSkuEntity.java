package com.nsy.oms.repository.entity.replenishment;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.oms.repository.entity.base.BaseMpEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * fba智能补货sku级别
 * <AUTHOR>
 * @date 2023-03-03
 */
@TableName("fba_replenishment_sku")
public class FbaReplenishmentSkuEntity extends BaseMpEntity {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * skc级别表主键id
     */
    private Integer fbaReplenishmentSkcId;

    /**
     * 商品id
     */
    private Integer productId;

    /**
     * skc
     */
    private String skc;

    /**
     * sku
     */
    private String sku;

    /**
     * 尺码
     */
    private String size;

    /**
     * 店铺id
     */
    private Integer storeId;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 在途库存
     */
    private Integer fbaOnTheWayStock;

    /**
     * 可售库存
     */
    private Integer fbaAvailableStock;
    /**
     * 1. 原FBA可售=fulfilable + reserved fc transfer + reserved fc processing ，现拆分成三个字段：可售、调拨预留、处理预留，问号显示的说明也有调整
     * fulfilable
     */
    private Integer fbaAvailableStockOfFulfilable;
    /**
     * reserved fc transfer
     */
    private Integer fbaAvailableStockOfTransfer;
    /**
     * reserved fc processing
     */
    private Integer fbaAvailableStockOfProcessing;

    /**
     * skc plan量
     */
    private Integer planQty;

    /**
     * 本地仓共享可用库存
     */
    private Integer localShareStock;

    /**
     * 本地仓个人可用库存
     */
    private Integer localPrivateStock;

    /**
     * 7天销量
     */
    @TableField("sales_qty_in_7")
    private Integer salesQtyIn7;

    /**
     * 14天销量
     */
    @TableField("sales_qty_in_14")
    private Integer salesQtyIn14;

    /**
     * 30天销量
     */
    @TableField("sales_qty_in_30")
    private Integer salesQtyIn30;

    /**
     * 退货率
     */
    private BigDecimal returnRate;
    /**
     * 备货时长
     */
    private Integer stockingDays;
    /**
     * 品类系数
     */
    private BigDecimal categoryCoefficient;
    /**
     * 销量预测
     */
    private Integer forecastSales;
    // 尺码比例
    private BigDecimal sizePercentage;
    // 断货时间
    private Date outOfStockDate;
    // 断货天数
    private Integer outOfStockDays;

    // 空运-建议发FBA量
    private Integer airSuggestQty;

    /**
     * 海运-建议发FBA量
     */
    private Integer seaSuggestQty;

    /**
     * 快递-建议发FBA量
     */
    private Integer expressSuggestQty;

    /**
     * 无法补货数量-cannot_shipment_qty
     */
    private Integer cannotShipmentQty;

    /**
     * 补货后库销比
     */
    private BigDecimal stockSalesRate;

    /**
     * 建议发货日
     */
    private Date suggestDeliveryDate;

    /**
     * 申请发空运数量
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer applyForAirQty;

    /**
     * 申请发海运数量
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer applyForSeaQty;

    /**
     * 申请发快递数量
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer applyForExpressQty;

    /**
     * 与建议差异较大时填写原因
     */
    private String applyReason;

    /**
     * seller sku
     */
    private String sellerSku;

    /**
     * fn sku
     */
    private String fnsku;

    /**
     * 品名
     */
    private String itemName;

    /**
     * 市场
     */
    private String marketplaceId;
    /**
     * 市场
     */
    private String marketplaceChinese;

    /**
     * 是否存在多个seller sku
     */
    private Integer hasMultiSellerSku;

    /**
     * 业务员
     */
    private String businessUserAccount;
    private String businessUserName;

    private String pendingSendAirDateArray;
    private String pendingSendAirQtyArray;
    private Date latestSeaSendDate;
    private Date latestAirSendDate;
    private Integer localStock;

    /**
     * skc级别的处理状态
     */
    @TableField(exist = false)
    private Integer status;

    public BigDecimal getSizePercentage() {
        return sizePercentage;
    }

    public void setSizePercentage(BigDecimal sizePercentage) {
        this.sizePercentage = sizePercentage;
    }

    public Integer getApplyForExpressQty() {
        return applyForExpressQty;
    }

    public void setApplyForExpressQty(Integer applyForExpressQty) {
        this.applyForExpressQty = applyForExpressQty;
    }

    public Integer getExpressSuggestQty() {
        return expressSuggestQty;
    }

    public void setExpressSuggestQty(Integer expressSuggestQty) {
        this.expressSuggestQty = expressSuggestQty;
    }

    public Integer getLocalStock() {
        return localStock;
    }

    public void setLocalStock(Integer localStock) {
        this.localStock = localStock;
    }

    public String getPendingSendAirDateArray() {
        return pendingSendAirDateArray;
    }

    public void setPendingSendAirDateArray(String pendingSendAirDateArray) {
        this.pendingSendAirDateArray = pendingSendAirDateArray;
    }

    public String getPendingSendAirQtyArray() {
        return pendingSendAirQtyArray;
    }

    public void setPendingSendAirQtyArray(String pendingSendAirQtyArray) {
        this.pendingSendAirQtyArray = pendingSendAirQtyArray;
    }

    public Date getLatestSeaSendDate() {
        return latestSeaSendDate;
    }

    public void setLatestSeaSendDate(Date latestSeaSendDate) {
        this.latestSeaSendDate = latestSeaSendDate;
    }

    public Date getLatestAirSendDate() {
        return latestAirSendDate;
    }

    public void setLatestAirSendDate(Date latestAirSendDate) {
        this.latestAirSendDate = latestAirSendDate;
    }

    public String getBusinessUserAccount() {
        return businessUserAccount;
    }

    public void setBusinessUserAccount(String businessUserAccount) {
        this.businessUserAccount = businessUserAccount;
    }

    public String getBusinessUserName() {
        return businessUserName;
    }

    public void setBusinessUserName(String businessUserName) {
        this.businessUserName = businessUserName;
    }

    public Integer getFbaAvailableStockOfFulfilable() {
        return fbaAvailableStockOfFulfilable;
    }

    public void setFbaAvailableStockOfFulfilable(Integer fbaAvailableStockOfFulfilable) {
        this.fbaAvailableStockOfFulfilable = fbaAvailableStockOfFulfilable;
    }

    public Integer getFbaAvailableStockOfTransfer() {
        return fbaAvailableStockOfTransfer;
    }

    public void setFbaAvailableStockOfTransfer(Integer fbaAvailableStockOfTransfer) {
        this.fbaAvailableStockOfTransfer = fbaAvailableStockOfTransfer;
    }

    public Integer getFbaAvailableStockOfProcessing() {
        return fbaAvailableStockOfProcessing;
    }

    public void setFbaAvailableStockOfProcessing(Integer fbaAvailableStockOfProcessing) {
        this.fbaAvailableStockOfProcessing = fbaAvailableStockOfProcessing;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getFbaReplenishmentSkcId() {
        return fbaReplenishmentSkcId;
    }

    public void setFbaReplenishmentSkcId(Integer fbaReplenishmentSkcId) {
        this.fbaReplenishmentSkcId = fbaReplenishmentSkcId;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getSkc() {
        return skc;
    }

    public void setSkc(String skc) {
        this.skc = skc;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public Integer getFbaOnTheWayStock() {
        return fbaOnTheWayStock;
    }

    public void setFbaOnTheWayStock(Integer fbaOnTheWayStock) {
        this.fbaOnTheWayStock = fbaOnTheWayStock;
    }

    public Integer getFbaAvailableStock() {
        return fbaAvailableStock;
    }

    public void setFbaAvailableStock(Integer fbaAvailableStock) {
        this.fbaAvailableStock = fbaAvailableStock;
    }

    public Integer getPlanQty() {
        return planQty;
    }

    public void setPlanQty(Integer planQty) {
        this.planQty = planQty;
    }

    public Integer getLocalShareStock() {
        return localShareStock;
    }

    public void setLocalShareStock(Integer localShareStock) {
        this.localShareStock = localShareStock;
    }

    public Integer getLocalPrivateStock() {
        return localPrivateStock;
    }

    public void setLocalPrivateStock(Integer localPrivateStock) {
        this.localPrivateStock = localPrivateStock;
    }

    public Integer getSalesQtyIn7() {
        return salesQtyIn7;
    }

    public void setSalesQtyIn7(Integer salesQtyIn7) {
        this.salesQtyIn7 = salesQtyIn7;
    }

    public Integer getSalesQtyIn14() {
        return salesQtyIn14;
    }

    public void setSalesQtyIn14(Integer salesQtyIn14) {
        this.salesQtyIn14 = salesQtyIn14;
    }

    public Integer getSalesQtyIn30() {
        return salesQtyIn30;
    }

    public void setSalesQtyIn30(Integer salesQtyIn30) {
        this.salesQtyIn30 = salesQtyIn30;
    }

    public BigDecimal getReturnRate() {
        return returnRate;
    }

    public void setReturnRate(BigDecimal returnRate) {
        this.returnRate = returnRate;
    }

    public Integer getStockingDays() {
        return stockingDays;
    }

    public void setStockingDays(Integer stockingDays) {
        this.stockingDays = stockingDays;
    }

    public BigDecimal getCategoryCoefficient() {
        return categoryCoefficient;
    }

    public void setCategoryCoefficient(BigDecimal categoryCoefficient) {
        this.categoryCoefficient = categoryCoefficient;
    }

    public Integer getForecastSales() {
        return forecastSales;
    }

    public void setForecastSales(Integer forecastSales) {
        this.forecastSales = forecastSales;
    }

    public Date getOutOfStockDate() {
        return outOfStockDate;
    }

    public void setOutOfStockDate(Date outOfStockDate) {
        this.outOfStockDate = outOfStockDate;
    }

    public Integer getAirSuggestQty() {
        return airSuggestQty;
    }

    public void setAirSuggestQty(Integer airSuggestQty) {
        this.airSuggestQty = airSuggestQty;
    }

    public Integer getSeaSuggestQty() {
        return seaSuggestQty;
    }

    public void setSeaSuggestQty(Integer seaSuggestQty) {
        this.seaSuggestQty = seaSuggestQty;
    }

    public BigDecimal getStockSalesRate() {
        return stockSalesRate;
    }

    public void setStockSalesRate(BigDecimal stockSalesRate) {
        this.stockSalesRate = stockSalesRate;
    }

    public Date getSuggestDeliveryDate() {
        return suggestDeliveryDate;
    }

    public void setSuggestDeliveryDate(Date suggestDeliveryDate) {
        this.suggestDeliveryDate = suggestDeliveryDate;
    }

    public Integer getApplyForAirQty() {
        return applyForAirQty;
    }

    public void setApplyForAirQty(Integer applyForAirQty) {
        this.applyForAirQty = applyForAirQty;
    }

    public Integer getApplyForSeaQty() {
        return applyForSeaQty;
    }

    public void setApplyForSeaQty(Integer applyForSeaQty) {
        this.applyForSeaQty = applyForSeaQty;
    }

    public String getApplyReason() {
        return applyReason;
    }

    public void setApplyReason(String applyReason) {
        this.applyReason = applyReason;
    }

    public String getSellerSku() {
        return sellerSku;
    }

    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    public String getFnsku() {
        return fnsku;
    }

    public void setFnsku(String fnsku) {
        this.fnsku = fnsku;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getMarketplaceId() {
        return marketplaceId;
    }

    public void setMarketplaceId(String marketplaceId) {
        this.marketplaceId = marketplaceId;
    }

    public String getMarketplaceChinese() {
        return marketplaceChinese;
    }

    public void setMarketplaceChinese(String marketplaceChinese) {
        this.marketplaceChinese = marketplaceChinese;
    }

    public Integer getOutOfStockDays() {
        return outOfStockDays;
    }

    public void setOutOfStockDays(Integer outOfStockDays) {
        this.outOfStockDays = outOfStockDays;
    }

    public Integer getHasMultiSellerSku() {
        return hasMultiSellerSku;
    }

    public void setHasMultiSellerSku(Integer hasMultiSellerSku) {
        this.hasMultiSellerSku = hasMultiSellerSku;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCannotShipmentQty() {
        return cannotShipmentQty;
    }

    public void setCannotShipmentQty(Integer cannotShipmentQty) {
        this.cannotShipmentQty = cannotShipmentQty;
    }

    public String getUpdateDataStr() {
        return "applyForAirQty=" + applyForAirQty
                + ", applyForSeaQty=" + applyForSeaQty
                + ", applyReason='" + applyReason
                + ", sellerSku='" + sellerSku
                + ", marketplaceId='" + marketplaceId
                + ", marketplaceChinese='" + marketplaceChinese
                + ", fnsku='" + fnsku
                + ", itemName='" + itemName;
    }

    public String getUpdateQtyStr() {
        return String.format("id=%s,storeName=%s,sku=%s,applyForAirQty=%s,applyForSeaQty=%s,applyReason=%s",
                id, storeName, sku, applyForAirQty, applyForSeaQty, applyReason);
    }
}
