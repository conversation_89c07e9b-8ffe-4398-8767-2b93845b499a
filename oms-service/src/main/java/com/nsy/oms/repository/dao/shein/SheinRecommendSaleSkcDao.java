package com.nsy.oms.repository.dao.shein;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nsy.oms.repository.entity.shein.SheinRecommendSaleSkcEntity;

import java.util.List;


/**
 *
 */
public interface SheinRecommendSaleSkcDao extends IService<SheinRecommendSaleSkcEntity> {

    List<SheinRecommendSaleSkcEntity> getBySkcInAndPlatform(List<String> skcList, String platform);

    /**
     * @Description: 更新上架中的数据
     * 爬虫数据，把已上架的数据同步回推荐表中，在业务中是（上架中改成已上架）
     * 逻辑上是，把上架中的数据给删除了。
     * @author: linCheng
     * @Date: 2023/9/20 16:57
     * @param: publishStoreId
     * @param: skcs
     * @Return: void
     */
    void delSheinRecommendSaleSkc(Integer publishStoreId, List<String> skcs);


    /**
     * @Description: 根据id获取数据
     * @param: sheinRecommendSaleSkcIdList
     * @Return: List<SheinRecommendSaleSkcEntity>
     */
    List<SheinRecommendSaleSkcEntity> getSheinRecommendSaleSkcListByIds(List<Integer> sheinRecommendSaleSkcIdList);

    SheinRecommendSaleSkcEntity getOneBySkcAndRecommendStore(String skc, Integer recommendStoreId);
    SheinRecommendSaleSkcEntity getOneIncludeDeleteBySkcAndRecommendStore(String skc, Integer recommendStoreId);

    void deleteBeforeToday();

    String getCreateDate();

    List<SheinRecommendSaleSkcEntity> getByProductIdIn(List<Integer> productIds);

    List<SheinRecommendSaleSkcEntity> getByProductIdAndPlatform(Integer productId, String platform);
    List<SheinRecommendSaleSkcEntity> getByProductIdAndPlatformAndInShelf(Integer productId, String platform);

}
