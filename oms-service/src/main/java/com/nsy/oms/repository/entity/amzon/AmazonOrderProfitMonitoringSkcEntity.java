package com.nsy.oms.repository.entity.amzon;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.oms.repository.entity.base.BaseMpEntity;

import java.math.BigDecimal;

/**
 * 亚马逊订单利润率监控skc
 */
@TableName("amazon_order_profit_monitoring_skc")
public class AmazonOrderProfitMonitoringSkcEntity extends BaseMpEntity {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * spu
     */
    @TableField("spu")
    private String spu;

    /**
     * skc
     */
    @TableField("skc")
    private String skc;

    /**
     * 店铺id
     */
    @TableField("store_id")
    private Integer storeId;

    /**
     * 店铺名称
     */
    @TableField("store_name")
    private String storeName;

    @TableField("marketplace_code")
    private String marketplaceCode;

    @TableField("parent_asin")
    private String parentAsin;

    /**
     * 近7天销量
     */
    @TableField("last_7_date_sale_qty")
    private Integer last7DateSaleQty;

    /**
     * 近14天销量
     */
    @TableField("last_14_date_sale_qty")
    private Integer last14DateSaleQty;

    /**
     * 近30天销量
     */
    @TableField("last_30_date_sale_qty")
    private Integer last30DateSaleQty;

    /**
     * 总库存
     */
    @TableField("total_inv")
    private Integer totalInv;

    /**
     * 泉州仓
     */
    @TableField("quanzhou_inv")
    private Integer quanzhouInv;

    /**
     * 海外可售
     */
    @TableField("overseas_inv")
    private Integer overseasInv;

    /**
     * 海外预留
     */
    @TableField("overseas_in_reserved_inv")
    private Integer overseasInReservedInv;

    /**
     * 采购在途
     */
    @TableField("purchase_in_transit_inv")
    private Integer purchaseInTransitInv;

    /**
     * 7天利润率
     */
    @TableField("profit_rate_in_7_days")
    private BigDecimal profitRateIn7Days;

    /**
     * 14天利润率
     */
    @TableField("profit_rate_in_14_days")
    private BigDecimal profitRateIn14Days;

    /**
     * 30天利润率
     */
    @TableField("profit_rate_in_30_days")
    private BigDecimal profitRateIn30Days;

    /**
     * 退货率
     */
    @TableField("return_rate")
    private BigDecimal returnRate;

    /**
     * 广告费率
     */
    @TableField("ad_cost_rate")
    private BigDecimal adCostRate;

    /**
     * 活动价($)
     */
    @TableField("sale_price")
    private BigDecimal salePrice;

    /**
     * 原价($)
     */
    @TableField("your_price")
    private BigDecimal yourPrice;

    /**
     * 近24H最低成交价
     */
    @TableField("min_order_price_in_24_hours")
    private BigDecimal minOrderPriceIn24Hours;

    /**
     * 近24H最高成交价
     */
    @TableField("max_order_price_in_24_hours")
    private BigDecimal maxOrderPriceIn24Hours;

    /**
     * 近24H最低利润率
     */
    @TableField("min_profit_rate_in_24_hours")
    private BigDecimal minProfitRateIn24Hours;

    /**
     * 近24H最高利润率
     */
    @TableField("max_profit_rate_in_24_hours")
    private BigDecimal maxProfitRateIn24Hours;

    /**
     * 当前档佣金率
     */
    @TableField("current_level_commission_rate")
    private BigDecimal currentLevelCommissionRate;

    /**
     * 当前档盈亏平衡价
     */
    @TableField("current_level_balance_price")
    private BigDecimal currentLevelBalancePrice;

    /**
     * 第1档利润率
     */
    @TableField("level_one_profit_rate")
    private BigDecimal levelOneProfitRate;

    /**
     * 第1档降档利润增加额
     */
    @TableField("level_one_downshift_increase_profit")
    private BigDecimal levelOneDownshiftIncreaseProfit;

    /**
     * 第2档利润率
     */
    @TableField("level_two_profit_rate")
    private BigDecimal levelTwoProfitRate;

    /**
     * 第2档降档利润增加额
     */
    @TableField("level_two_downshift_increase_profit")
    private BigDecimal levelTwoDownshiftIncreaseProfit;

    /**
     * 第3档利润率
     */
    @TableField("level_three_profit_rate")
    private BigDecimal levelThreeProfitRate;

    /**
     * 第3档降档利润增加额
     */
    @TableField("level_three_downshift_increase_profit")
    private BigDecimal levelThreeDownshiftIncreaseProfit;

    /**
     * 第4档利润率
     */
    @TableField("level_four_profit_rate")
    private BigDecimal levelFourProfitRate;

    /**
     * 第4档降档利润增加额
     */
    @TableField("level_four_downshift_increase_profit")
    private BigDecimal levelFourDownshiftIncreaseProfit;

    /**
     * FBA配送费
     */
    @TableField("fba_delivery_cost")
    private BigDecimal fbaDeliveryCost;

    /**
     * FBA退货处理费
     */
    @TableField("fba_return_cost")
    private BigDecimal fbaReturnCost;

    /**
     * FBA头程邮费
     */
    @TableField("fba_first_postage_cost")
    private BigDecimal fbaFirstPostageCost;

    /**
     * 仓租费
     */
    @TableField("storage_cost")
    private BigDecimal storageCost;

    /**
     * 废弃成本
     */
    @TableField("disposal_cost")
    private BigDecimal disposalCost;

    /**
     * 平台佣金率及兑换手续费
     */
    @TableField("handling_cost")
    private BigDecimal handlingCost;

    /**
     * 广告费
     */
    @TableField("advertising_cost")
    private BigDecimal advertisingCost;

    /**
     * 测评费用+秒杀COUPON等费用
     */
    @TableField("coupon_cost")
    private BigDecimal couponCost;

    /**
     * 退货平台扣除佣金
     */
    @TableField("return_platform_deducts_cost")
    private BigDecimal returnPlatformDeductsCost;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSpu() {
        return spu;
    }

    public void setSpu(String spu) {
        this.spu = spu;
    }

    public String getSkc() {
        return skc;
    }

    public void setSkc(String skc) {
        this.skc = skc;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getMarketplaceCode() {
        return marketplaceCode;
    }

    public void setMarketplaceCode(String marketplaceCode) {
        this.marketplaceCode = marketplaceCode;
    }

    public String getParentAsin() {
        return parentAsin;
    }

    public void setParentAsin(String parentAsin) {
        this.parentAsin = parentAsin;
    }

    public Integer getLast7DateSaleQty() {
        return last7DateSaleQty;
    }

    public void setLast7DateSaleQty(Integer last7DateSaleQty) {
        this.last7DateSaleQty = last7DateSaleQty;
    }

    public Integer getLast14DateSaleQty() {
        return last14DateSaleQty;
    }

    public void setLast14DateSaleQty(Integer last14DateSaleQty) {
        this.last14DateSaleQty = last14DateSaleQty;
    }

    public Integer getLast30DateSaleQty() {
        return last30DateSaleQty;
    }

    public void setLast30DateSaleQty(Integer last30DateSaleQty) {
        this.last30DateSaleQty = last30DateSaleQty;
    }

    public Integer getTotalInv() {
        return totalInv;
    }

    public void setTotalInv(Integer totalInv) {
        this.totalInv = totalInv;
    }

    public Integer getQuanzhouInv() {
        return quanzhouInv;
    }

    public void setQuanzhouInv(Integer quanzhouInv) {
        this.quanzhouInv = quanzhouInv;
    }

    public Integer getOverseasInv() {
        return overseasInv;
    }

    public void setOverseasInv(Integer overseasInv) {
        this.overseasInv = overseasInv;
    }

    public Integer getOverseasInReservedInv() {
        return overseasInReservedInv;
    }

    public void setOverseasInReservedInv(Integer overseasInReservedInv) {
        this.overseasInReservedInv = overseasInReservedInv;
    }

    public Integer getPurchaseInTransitInv() {
        return purchaseInTransitInv;
    }

    public void setPurchaseInTransitInv(Integer purchaseInTransitInv) {
        this.purchaseInTransitInv = purchaseInTransitInv;
    }

    public BigDecimal getProfitRateIn7Days() {
        return profitRateIn7Days;
    }

    public void setProfitRateIn7Days(BigDecimal profitRateIn7Days) {
        this.profitRateIn7Days = profitRateIn7Days;
    }

    public BigDecimal getProfitRateIn14Days() {
        return profitRateIn14Days;
    }

    public void setProfitRateIn14Days(BigDecimal profitRateIn14Days) {
        this.profitRateIn14Days = profitRateIn14Days;
    }

    public BigDecimal getProfitRateIn30Days() {
        return profitRateIn30Days;
    }

    public void setProfitRateIn30Days(BigDecimal profitRateIn30Days) {
        this.profitRateIn30Days = profitRateIn30Days;
    }

    public BigDecimal getReturnRate() {
        return returnRate;
    }

    public void setReturnRate(BigDecimal returnRate) {
        this.returnRate = returnRate;
    }

    public BigDecimal getAdCostRate() {
        return adCostRate;
    }

    public void setAdCostRate(BigDecimal adCostRate) {
        this.adCostRate = adCostRate;
    }

    public BigDecimal getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(BigDecimal salePrice) {
        this.salePrice = salePrice;
    }

    public BigDecimal getYourPrice() {
        return yourPrice;
    }

    public void setYourPrice(BigDecimal yourPrice) {
        this.yourPrice = yourPrice;
    }

    public BigDecimal getMinOrderPriceIn24Hours() {
        return minOrderPriceIn24Hours;
    }

    public void setMinOrderPriceIn24Hours(BigDecimal minOrderPriceIn24Hours) {
        this.minOrderPriceIn24Hours = minOrderPriceIn24Hours;
    }

    public BigDecimal getMaxOrderPriceIn24Hours() {
        return maxOrderPriceIn24Hours;
    }

    public void setMaxOrderPriceIn24Hours(BigDecimal maxOrderPriceIn24Hours) {
        this.maxOrderPriceIn24Hours = maxOrderPriceIn24Hours;
    }

    public BigDecimal getMinProfitRateIn24Hours() {
        return minProfitRateIn24Hours;
    }

    public void setMinProfitRateIn24Hours(BigDecimal minProfitRateIn24Hours) {
        this.minProfitRateIn24Hours = minProfitRateIn24Hours;
    }

    public BigDecimal getMaxProfitRateIn24Hours() {
        return maxProfitRateIn24Hours;
    }

    public void setMaxProfitRateIn24Hours(BigDecimal maxProfitRateIn24Hours) {
        this.maxProfitRateIn24Hours = maxProfitRateIn24Hours;
    }

    public BigDecimal getCurrentLevelCommissionRate() {
        return currentLevelCommissionRate;
    }

    public void setCurrentLevelCommissionRate(BigDecimal currentLevelCommissionRate) {
        this.currentLevelCommissionRate = currentLevelCommissionRate;
    }

    public BigDecimal getCurrentLevelBalancePrice() {
        return currentLevelBalancePrice;
    }

    public void setCurrentLevelBalancePrice(BigDecimal currentLevelBalancePrice) {
        this.currentLevelBalancePrice = currentLevelBalancePrice;
    }

    public BigDecimal getLevelOneProfitRate() {
        return levelOneProfitRate;
    }

    public void setLevelOneProfitRate(BigDecimal levelOneProfitRate) {
        this.levelOneProfitRate = levelOneProfitRate;
    }

    public BigDecimal getLevelOneDownshiftIncreaseProfit() {
        return levelOneDownshiftIncreaseProfit;
    }

    public void setLevelOneDownshiftIncreaseProfit(BigDecimal levelOneDownshiftIncreaseProfit) {
        this.levelOneDownshiftIncreaseProfit = levelOneDownshiftIncreaseProfit;
    }

    public BigDecimal getLevelTwoProfitRate() {
        return levelTwoProfitRate;
    }

    public void setLevelTwoProfitRate(BigDecimal levelTwoProfitRate) {
        this.levelTwoProfitRate = levelTwoProfitRate;
    }

    public BigDecimal getLevelTwoDownshiftIncreaseProfit() {
        return levelTwoDownshiftIncreaseProfit;
    }

    public void setLevelTwoDownshiftIncreaseProfit(BigDecimal levelTwoDownshiftIncreaseProfit) {
        this.levelTwoDownshiftIncreaseProfit = levelTwoDownshiftIncreaseProfit;
    }

    public BigDecimal getLevelThreeProfitRate() {
        return levelThreeProfitRate;
    }

    public void setLevelThreeProfitRate(BigDecimal levelThreeProfitRate) {
        this.levelThreeProfitRate = levelThreeProfitRate;
    }

    public BigDecimal getLevelThreeDownshiftIncreaseProfit() {
        return levelThreeDownshiftIncreaseProfit;
    }

    public void setLevelThreeDownshiftIncreaseProfit(BigDecimal levelThreeDownshiftIncreaseProfit) {
        this.levelThreeDownshiftIncreaseProfit = levelThreeDownshiftIncreaseProfit;
    }

    public BigDecimal getLevelFourProfitRate() {
        return levelFourProfitRate;
    }

    public void setLevelFourProfitRate(BigDecimal levelFourProfitRate) {
        this.levelFourProfitRate = levelFourProfitRate;
    }

    public BigDecimal getLevelFourDownshiftIncreaseProfit() {
        return levelFourDownshiftIncreaseProfit;
    }

    public void setLevelFourDownshiftIncreaseProfit(BigDecimal levelFourDownshiftIncreaseProfit) {
        this.levelFourDownshiftIncreaseProfit = levelFourDownshiftIncreaseProfit;
    }

    public BigDecimal getFbaDeliveryCost() {
        return fbaDeliveryCost;
    }

    public void setFbaDeliveryCost(BigDecimal fbaDeliveryCost) {
        this.fbaDeliveryCost = fbaDeliveryCost;
    }

    public BigDecimal getFbaReturnCost() {
        return fbaReturnCost;
    }

    public void setFbaReturnCost(BigDecimal fbaReturnCost) {
        this.fbaReturnCost = fbaReturnCost;
    }

    public BigDecimal getFbaFirstPostageCost() {
        return fbaFirstPostageCost;
    }

    public void setFbaFirstPostageCost(BigDecimal fbaFirstPostageCost) {
        this.fbaFirstPostageCost = fbaFirstPostageCost;
    }

    public BigDecimal getStorageCost() {
        return storageCost;
    }

    public void setStorageCost(BigDecimal storageCost) {
        this.storageCost = storageCost;
    }

    public BigDecimal getDisposalCost() {
        return disposalCost;
    }

    public void setDisposalCost(BigDecimal disposalCost) {
        this.disposalCost = disposalCost;
    }

    public BigDecimal getHandlingCost() {
        return handlingCost;
    }

    public void setHandlingCost(BigDecimal handlingCost) {
        this.handlingCost = handlingCost;
    }

    public BigDecimal getAdvertisingCost() {
        return advertisingCost;
    }

    public void setAdvertisingCost(BigDecimal advertisingCost) {
        this.advertisingCost = advertisingCost;
    }

    public BigDecimal getCouponCost() {
        return couponCost;
    }

    public void setCouponCost(BigDecimal couponCost) {
        this.couponCost = couponCost;
    }

    public BigDecimal getReturnPlatformDeductsCost() {
        return returnPlatformDeductsCost;
    }

    public void setReturnPlatformDeductsCost(BigDecimal returnPlatformDeductsCost) {
        this.returnPlatformDeductsCost = returnPlatformDeductsCost;
    }
}