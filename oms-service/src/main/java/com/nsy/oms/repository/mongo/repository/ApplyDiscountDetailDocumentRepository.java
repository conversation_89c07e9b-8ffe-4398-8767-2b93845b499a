package com.nsy.oms.repository.mongo.repository;

import com.nsy.oms.repository.mongo.document.ApplyDiscountDetailDocument;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ApplyDiscountDetailDocumentRepository extends DocumentRepository<ApplyDiscountDetailDocument, String> {
    List<ApplyDiscountDetailDocument> findByStoreNameAndSpu(String storeName, String spu);
}
