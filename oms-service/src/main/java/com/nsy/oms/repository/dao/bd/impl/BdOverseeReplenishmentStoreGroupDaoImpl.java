package com.nsy.oms.repository.dao.bd.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.oms.dto.request.store.OverseaReplenishmentGetGroupStoreRequest;
import com.nsy.oms.repository.dao.bd.BdOverseeReplenishmentStoreGroupDao;
import com.nsy.oms.repository.entity.bd.BdOverseeReplenishmentStoreGroupEntity;
import com.nsy.oms.repository.sql.mapper.bd.BdOverseeReplenishmentStoreGroupMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 海外仓补货店铺分组 Dao 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-19
 */
@Repository
public class BdOverseeReplenishmentStoreGroupDaoImpl 
    extends ServiceImpl<BdOverseeReplenishmentStoreGroupMapper, BdOverseeReplenishmentStoreGroupEntity>
    implements BdOverseeReplenishmentStoreGroupDao {

    @Override
    public List<BdOverseeReplenishmentStoreGroupEntity> getGroupStoreList(OverseaReplenishmentGetGroupStoreRequest request) {
        return list(Wrappers.<BdOverseeReplenishmentStoreGroupEntity>lambdaQuery()
                .in(BdOverseeReplenishmentStoreGroupEntity::getReplenishmentGroupId, request.getReplenishmentGroupIdList())
        );
    }
}