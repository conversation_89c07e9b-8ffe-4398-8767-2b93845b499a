package com.nsy.oms.repository.entity.points;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;

import com.nsy.oms.repository.entity.base.BaseMpEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * B2B积分退款明细
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("b2b_points_refund_item")
public class B2bPointsRefundItemEntity extends BaseMpEntity {

    /**
     * 退款明细ID
     */
    @TableId(value = "b2b_points_refund_item_id", type = IdType.AUTO)
    private Integer b2bPointsRefundItemId;

    /**
     * 退款请求ID
     */
    @TableField("refund_id")
    private Integer refundId;

    /**
     * 订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 流水号
     */
    @TableField("serial_number")
    private String serialNumber;

    /**
     * 链接
     */
    @TableField("url")
    private String url;

    /**
     * 退款平台
     */
    @TableField("refund_platform")
    private String refundPlatform;

    /**
     * 退款金额
     */
    @TableField("refund_amount")
    private BigDecimal refundAmount;

    /**
     * 退款状态
     */
    @TableField("refund_status")
    private String refundStatus;

    /**
     * 币种
     */
    @TableField("currency")
    private String currency;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 退款操作人
     */
    @TableField("refund_operator")
    private String refundOperator;

}
