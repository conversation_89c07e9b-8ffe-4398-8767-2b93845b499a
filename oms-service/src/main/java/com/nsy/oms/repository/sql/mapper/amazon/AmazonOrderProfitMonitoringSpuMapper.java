package com.nsy.oms.repository.sql.mapper.amazon;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.oms.business.domain.request.amazon.AmazonOrderProfitMonitoringPageRequest;
import com.nsy.oms.repository.entity.amzon.AmazonOrderProfitMonitoringSpuEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper
public interface AmazonOrderProfitMonitoringSpuMapper extends BaseMapper<AmazonOrderProfitMonitoringSpuEntity> {

    Page<AmazonOrderProfitMonitoringSpuEntity> getPage(@Param("request") AmazonOrderProfitMonitoringPageRequest request, @Param("pageRequest") IPage<AmazonOrderProfitMonitoringSpuEntity> pageRequest);
}
