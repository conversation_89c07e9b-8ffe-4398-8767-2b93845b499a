package com.nsy.oms.repository.entity.auth;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.oms.annotation.FieldDesc;
import com.nsy.oms.repository.entity.base.BaseMpEntity;

import java.util.Date;

/**
 * amazon授权表
 *
 * <AUTHOR>
 * @TableName sau_amazon_config
 */
@TableName("sau_amazon_config")
public class SauAmazonConfigEntity extends BaseMpEntity {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 店铺id
     */
    @TableField("store_id")
    private Integer storeId;

    /**
     * 店铺名称
     */
    @FieldDesc("店铺名称")
    @TableField("store_name")
    private String storeName;

    /**
     * 销售账号
     */
    @FieldDesc("销售账号")
    @TableField("seller_id")
    private String sellerId;

    /**
     * AWS Acess Key ID
     */
    @FieldDesc("AWS Acess Key ID")
    @TableField("aws_access_key_id")
    private String awsAccessKeyId;

    /**
     * Secret Key
     */
    @TableField("secret_key")
    @FieldDesc("secret_key")
    private String secretKey;

    /**
     * market place id
     */
    @TableField("market_place_id")
    @FieldDesc("market_place_id")
    private String marketPlaceId;

    /**
     * MWS Auth Token
     */
    @TableField("mws_auth_token")
    @FieldDesc("mws_auth_token")
    private String mwsAuthToken;

    /**
     * 接口url
     */
    @TableField("url")
    @FieldDesc("url")
    private String url;

    /**
     * 授权日期
     */
    @TableField("grant_date")
    @FieldDesc("grant_date")
    private Date grantDate;

    /**
     * 有效期
     */
    @TableField("validity_date")
    @FieldDesc("validity_date")
    private Date validityDate;

    /**
     * 开始抓单时间
     */
    @TableField("catch_date")
    @FieldDesc("catch_date")
    private Date catchDate;
    /**
     * 授权状态 0:关闭  1:开启
     */
    @FieldDesc("授权状态")
    @TableField("grant_auth_status")
    private Integer grantAuthStatus;

    /**
     * 状态 0 关闭 1 开启
     */
    @TableField("enable")
    private Integer enable;

    /**
     * 当前抓单时间
     */
    @TableField("current_catch_date")
    private Date currentCatchDate;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getSellerId() {
        return sellerId;
    }

    public void setSellerId(String sellerId) {
        this.sellerId = sellerId;
    }

    public String getAwsAccessKeyId() {
        return awsAccessKeyId;
    }

    public void setAwsAccessKeyId(String awsAccessKeyId) {
        this.awsAccessKeyId = awsAccessKeyId;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getMarketPlaceId() {
        return marketPlaceId;
    }

    public void setMarketPlaceId(String marketPlaceId) {
        this.marketPlaceId = marketPlaceId;
    }

    public String getMwsAuthToken() {
        return mwsAuthToken;
    }

    public void setMwsAuthToken(String mwsAuthToken) {
        this.mwsAuthToken = mwsAuthToken;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Date getGrantDate() {
        return grantDate;
    }

    public void setGrantDate(Date grantDate) {
        this.grantDate = grantDate;
    }

    public Date getValidityDate() {
        return validityDate;
    }

    public void setValidityDate(Date validityDate) {
        this.validityDate = validityDate;
    }

    public Date getCatchDate() {
        return catchDate;
    }

    public void setCatchDate(Date catchDate) {
        this.catchDate = catchDate;
    }

    public Integer getGrantAuthStatus() {
        return grantAuthStatus;
    }

    public void setGrantAuthStatus(Integer grantAuthStatus) {
        this.grantAuthStatus = grantAuthStatus;
    }

    public Integer getEnable() {
        return enable;
    }

    public void setEnable(Integer enable) {
        this.enable = enable;
    }

    public Date getCurrentCatchDate() {
        return currentCatchDate;
    }

    public void setCurrentCatchDate(Date currentCatchDate) {
        this.currentCatchDate = currentCatchDate;
    }
}