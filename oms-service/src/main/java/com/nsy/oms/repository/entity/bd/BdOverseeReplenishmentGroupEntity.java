package com.nsy.oms.repository.entity.bd;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 海外仓补货分组
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-19
 */
@TableName("bd_oversea_replenishment_group")
public class BdOverseeReplenishmentGroupEntity implements Serializable {

    private static final long serialVersionUID = -3470409935081739397L;
    /**
     * 主键id
     */
    @TableId(value = "replenishment_group_id", type = IdType.AUTO)
    private Integer replenishmentGroupId;

    /**
     * 部门
     */
    @TableField("department")
    private String department;

    /**
     * 分组名称
     */
    @TableField("replenishment_group_name")
    private String replenishmentGroupName;

    /**
     * 创建时间
     */
    @TableField(value = "create_date", fill = FieldFill.INSERT)
    private Date createDate;


    /**
     * 更新时间
     */
    @TableField(value = "update_date", fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 版本号
     */
    @Version
    @JsonIgnore
    @TableField("version")
    private Integer version;

    public Integer getReplenishmentGroupId() {
        return replenishmentGroupId;
    }

    public void setReplenishmentGroupId(Integer replenishmentGroupId) {
        this.replenishmentGroupId = replenishmentGroupId;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getReplenishmentGroupName() {
        return replenishmentGroupName;
    }

    public void setReplenishmentGroupName(String replenishmentGroupName) {
        this.replenishmentGroupName = replenishmentGroupName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }
}