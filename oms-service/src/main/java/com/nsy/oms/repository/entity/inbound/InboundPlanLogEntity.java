package com.nsy.oms.repository.entity.inbound;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.oms.repository.entity.base.BaseMpEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * fba补货单操作日志表
 *
 * @TableName inbound_shipment_plan_log
 */
@TableName("inbound_plan_log")
@EqualsAndHashCode(callSuper = true)
@Data
public class InboundPlanLogEntity extends BaseMpEntity {
    /**
     * 日志id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 补货单id
     */
    @TableField("plan_id")
    private Integer planId;

    /**
     * 日志记录
     */
    @TableField("description")
    private String description;

    /**
     * 补货单状态
     */
    @TableField("plan_status")
    private String planStatus;

    /**
     * 类型
     */
    @TableField("log_type")
    private Integer logType;

    /**
     *
     */
    @TableField("operate")
    private String operate;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}