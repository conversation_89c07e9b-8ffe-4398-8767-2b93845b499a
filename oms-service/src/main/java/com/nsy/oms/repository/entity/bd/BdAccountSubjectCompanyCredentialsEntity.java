package com.nsy.oms.repository.entity.bd;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.oms.repository.entity.base.BaseMpEntity;

import java.io.Serializable;

/**
 * 账户主体公司证件表
 * @TableName bd_account_subject_company_credentials
 */
@TableName("bd_account_subject_company_credentials")
public class BdAccountSubjectCompanyCredentialsEntity extends BaseMpEntity implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 主体id
     */
    private Integer subjectId;

    /**
     * 图片地址
     */
    private String url;

    /**
     * 图片名称
     */
    private String name;

    /**
     * 图片原始名称
     */
    private String originName;

    /**
     * 图片后缀
     */
    private String suffix;

    private Integer status;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    public Integer getId() {
        return id;
    }

    /**
     *
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 主体id
     */
    public Integer getSubjectId() {
        return subjectId;
    }

    /**
     * 主体id
     */
    public void setSubjectId(Integer subjectId) {
        this.subjectId = subjectId;
    }

    /**
     * 图片地址
     */
    public String getUrl() {
        return url;
    }

    /**
     * 图片地址
     */
    public void setUrl(String url) {
        this.url = url;
    }

    /**
     * 图片名称
     */
    public String getName() {
        return name;
    }

    /**
     * 图片名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 图片原始名称
     */
    public String getOriginName() {
        return originName;
    }

    /**
     * 图片原始名称
     */
    public void setOriginName(String originName) {
        this.originName = originName;
    }

    /**
     * 图片后缀
     */
    public String getSuffix() {
        return suffix;
    }

    /**
     * 图片后缀
     */
    public void setSuffix(String suffix) {
        this.suffix = suffix;
    }

}