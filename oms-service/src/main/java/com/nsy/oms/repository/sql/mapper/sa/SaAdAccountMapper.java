package com.nsy.oms.repository.sql.mapper.sa;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.oms.business.domain.request.sa.SaAdAccountRequest;
import com.nsy.oms.business.domain.response.sa.SaAdAccountResponse;
import com.nsy.oms.repository.entity.sa.SaAdAccountEntity;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description 针对表【sa_ad_account(社媒账号-广告账号)】的数据库操作Mapper
 * @createDate 2024-10-30 10:48:42
 * @Entity generator.domain.SaAdAccountEntity
 */
public interface SaAdAccountMapper extends BaseMapper<SaAdAccountEntity> {

    Page<SaAdAccountResponse> getList(Page page, @Param("request") SaAdAccountRequest request);
}




