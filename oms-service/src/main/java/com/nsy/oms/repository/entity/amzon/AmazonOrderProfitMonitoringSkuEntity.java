package com.nsy.oms.repository.entity.amzon;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.oms.repository.entity.base.BaseMpEntity;

import java.math.BigDecimal;

/**
 * 亚马逊订单利润率监控sku
 */
@TableName("amazon_order_profit_monitoring_sku")
public class AmazonOrderProfitMonitoringSkuEntity extends BaseMpEntity {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * spu
     */
    @TableField("spu")
    private String spu;

    /**
     * skc
     */
    @TableField("skc")
    private String skc;

    /**
     * sku
     */
    @TableField("sku")
    private String sku;

    /**
     * 店铺id
     */
    @TableField("store_id")
    private Integer storeId;

    /**
     * 店铺名称
     */
    @TableField("store_name")
    private String storeName;

    @TableField("marketplace_code")
    private String marketplaceCode;

    @TableField("parent_asin")
    private String parentAsin;

    /**
     * 最长边/cm
     */
    @TableField("longest_side")
    private BigDecimal longestSide;

    /**
     * 中等边/cm
     */
    @TableField("median_side")
    private BigDecimal medianSide;

    /**
     * 最短边/cm
     */
    @TableField("shortest_side")
    private BigDecimal shortestSide;

    /**
     * 重量/g
     */
    @TableField("weight")
    private BigDecimal weight;

    /**
     * 商品成本/美元
     */
    @TableField("product_cost")
    private BigDecimal productCost;

    /**
     * 退货率
     */
    @TableField("return_rate")
    private BigDecimal returnRate;

    /**
     * 品类退货不可售比例
     */
    @TableField("cate_return_unsallable_rate")
    private BigDecimal cateReturnUnsallableRate;

    /**
     * 广告费率
     */
    @TableField("ad_cost_rate")
    private BigDecimal adCostRate;

    /**
     * 产品尺寸层级(包裹类型)
     */
    @TableField("product_size_tier")
    private String productSizeTier;

    /**
     * 近7天销量
     */
    @TableField("last_7_date_sale_qty")
    private Integer last7DateSaleQty;

    /**
     * 近7天销售收入
     */
    @TableField("last_7_date_sale_income")
    private BigDecimal last7DateSaleIncome;

    /**
     * 近14天销量
     */
    @TableField("last_14_date_sale_qty")
    private Integer last14DateSaleQty;

    /**
     * 近14天销售收入
     */
    @TableField("last_14_date_sale_income")
    private BigDecimal last14DateSaleIncome;

    /**
     * 近30天销量
     */
    @TableField("last_30_date_sale_qty")
    private Integer last30DateSaleQty;

    /**
     * 近30天销售收入
     */
    @TableField("last_30_date_sale_income")
    private BigDecimal last30DateSaleIncome;

    /**
     * 总库存
     */
    @TableField("total_inv")
    private Integer totalInv;

    /**
     * 泉州仓
     */
    @TableField("quanzhou_inv")
    private Integer quanzhouInv;

    /**
     * 海外可售
     */
    @TableField("overseas_inv")
    private Integer overseasInv;

    /**
     * 海外预留
     */
    @TableField("overseas_in_reserved_inv")
    private Integer overseasInReservedInv;

    /**
     * 采购在途
     */
    @TableField("purchase_in_transit_inv")
    private Integer purchaseInTransitInv;

    /**
     * 7日净库销
     */
    @TableField("last_7_date_inv_sales_rate")
    private BigDecimal last7DateInvSalesRate;

    /**
     * 14日净库销
     */
    @TableField("last_14_date_inv_sales_rate")
    private BigDecimal last14DateInvSalesRate;

    /**
     * 30日净库销
     */
    @TableField("last_30_date_inv_sales_rate")
    private BigDecimal last30DateInvSalesRate;

    /**
     * 库龄0-90
     */
    @TableField("inv_age_0_to_90")
    private Integer invAge0To90;

    /**
     * 库龄91-180
     */
    @TableField("inv_age_91_to_180")
    private Integer invAge91To180;

    /**
     * 库龄181-270
     */
    @TableField("inv_age_181_to_270")
    private Integer invAge181To270;

    /**
     * 库龄271-365
     */
    @TableField("inv_age_271_to_365")
    private Integer invAge271To365;

    /**
     * 库龄>365
     */
    @TableField("inv_age_gt_365")
    private Integer invAgeGt365;

    /**
     * 库龄合计
     */
    @TableField("inv_age_total")
    private Integer invAgeTotal;

    /**
     * 活动价($)
     */
    @TableField("sale_price")
    private BigDecimal salePrice;

    /**
     * 原价($)
     */
    @TableField("your_price")
    private BigDecimal yourPrice;

    /**
     * 近24H最低成交价
     */
    @TableField("min_order_price_in_24_hours")
    private BigDecimal minOrderPriceIn24Hours;

    /**
     * 近24H最高成交价
     */
    @TableField("max_order_price_in_24_hours")
    private BigDecimal maxOrderPriceIn24Hours;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSpu() {
        return spu;
    }

    public void setSpu(String spu) {
        this.spu = spu;
    }

    public String getSkc() {
        return skc;
    }

    public void setSkc(String skc) {
        this.skc = skc;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getMarketplaceCode() {
        return marketplaceCode;
    }

    public void setMarketplaceCode(String marketplaceCode) {
        this.marketplaceCode = marketplaceCode;
    }

    public String getParentAsin() {
        return parentAsin;
    }

    public void setParentAsin(String parentAsin) {
        this.parentAsin = parentAsin;
    }

    public BigDecimal getLongestSide() {
        return longestSide;
    }

    public void setLongestSide(BigDecimal longestSide) {
        this.longestSide = longestSide;
    }

    public BigDecimal getMedianSide() {
        return medianSide;
    }

    public void setMedianSide(BigDecimal medianSide) {
        this.medianSide = medianSide;
    }

    public BigDecimal getShortestSide() {
        return shortestSide;
    }

    public void setShortestSide(BigDecimal shortestSide) {
        this.shortestSide = shortestSide;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public BigDecimal getProductCost() {
        return productCost;
    }

    public void setProductCost(BigDecimal productCost) {
        this.productCost = productCost;
    }

    public BigDecimal getReturnRate() {
        return returnRate;
    }

    public void setReturnRate(BigDecimal returnRate) {
        this.returnRate = returnRate;
    }

    public BigDecimal getCateReturnUnsallableRate() {
        return cateReturnUnsallableRate;
    }

    public void setCateReturnUnsallableRate(BigDecimal cateReturnUnsallableRate) {
        this.cateReturnUnsallableRate = cateReturnUnsallableRate;
    }

    public BigDecimal getAdCostRate() {
        return adCostRate;
    }

    public void setAdCostRate(BigDecimal adCostRate) {
        this.adCostRate = adCostRate;
    }

    public String getProductSizeTier() {
        return productSizeTier;
    }

    public void setProductSizeTier(String productSizeTier) {
        this.productSizeTier = productSizeTier;
    }

    public Integer getLast7DateSaleQty() {
        return last7DateSaleQty;
    }

    public void setLast7DateSaleQty(Integer last7DateSaleQty) {
        this.last7DateSaleQty = last7DateSaleQty;
    }

    public BigDecimal getLast7DateSaleIncome() {
        return last7DateSaleIncome;
    }

    public void setLast7DateSaleIncome(BigDecimal last7DateSaleIncome) {
        this.last7DateSaleIncome = last7DateSaleIncome;
    }

    public Integer getLast14DateSaleQty() {
        return last14DateSaleQty;
    }

    public void setLast14DateSaleQty(Integer last14DateSaleQty) {
        this.last14DateSaleQty = last14DateSaleQty;
    }

    public BigDecimal getLast14DateSaleIncome() {
        return last14DateSaleIncome;
    }

    public void setLast14DateSaleIncome(BigDecimal last14DateSaleIncome) {
        this.last14DateSaleIncome = last14DateSaleIncome;
    }

    public Integer getLast30DateSaleQty() {
        return last30DateSaleQty;
    }

    public void setLast30DateSaleQty(Integer last30DateSaleQty) {
        this.last30DateSaleQty = last30DateSaleQty;
    }

    public BigDecimal getLast30DateSaleIncome() {
        return last30DateSaleIncome;
    }

    public void setLast30DateSaleIncome(BigDecimal last30DateSaleIncome) {
        this.last30DateSaleIncome = last30DateSaleIncome;
    }

    public Integer getTotalInv() {
        return totalInv;
    }

    public void setTotalInv(Integer totalInv) {
        this.totalInv = totalInv;
    }

    public Integer getQuanzhouInv() {
        return quanzhouInv;
    }

    public void setQuanzhouInv(Integer quanzhouInv) {
        this.quanzhouInv = quanzhouInv;
    }

    public Integer getOverseasInv() {
        return overseasInv;
    }

    public void setOverseasInv(Integer overseasInv) {
        this.overseasInv = overseasInv;
    }

    public Integer getOverseasInReservedInv() {
        return overseasInReservedInv;
    }

    public void setOverseasInReservedInv(Integer overseasInReservedInv) {
        this.overseasInReservedInv = overseasInReservedInv;
    }

    public Integer getPurchaseInTransitInv() {
        return purchaseInTransitInv;
    }

    public void setPurchaseInTransitInv(Integer purchaseInTransitInv) {
        this.purchaseInTransitInv = purchaseInTransitInv;
    }

    public BigDecimal getLast7DateInvSalesRate() {
        return last7DateInvSalesRate;
    }

    public void setLast7DateInvSalesRate(BigDecimal last7DateInvSalesRate) {
        this.last7DateInvSalesRate = last7DateInvSalesRate;
    }

    public BigDecimal getLast14DateInvSalesRate() {
        return last14DateInvSalesRate;
    }

    public void setLast14DateInvSalesRate(BigDecimal last14DateInvSalesRate) {
        this.last14DateInvSalesRate = last14DateInvSalesRate;
    }

    public BigDecimal getLast30DateInvSalesRate() {
        return last30DateInvSalesRate;
    }

    public void setLast30DateInvSalesRate(BigDecimal last30DateInvSalesRate) {
        this.last30DateInvSalesRate = last30DateInvSalesRate;
    }

    public Integer getInvAge0To90() {
        return invAge0To90;
    }

    public void setInvAge0To90(Integer invAge0To90) {
        this.invAge0To90 = invAge0To90;
    }

    public Integer getInvAge91To180() {
        return invAge91To180;
    }

    public void setInvAge91To180(Integer invAge91To180) {
        this.invAge91To180 = invAge91To180;
    }

    public Integer getInvAge181To270() {
        return invAge181To270;
    }

    public void setInvAge181To270(Integer invAge181To270) {
        this.invAge181To270 = invAge181To270;
    }

    public Integer getInvAge271To365() {
        return invAge271To365;
    }

    public void setInvAge271To365(Integer invAge271To365) {
        this.invAge271To365 = invAge271To365;
    }

    public Integer getInvAgeGt365() {
        return invAgeGt365;
    }

    public void setInvAgeGt365(Integer invAgeGt365) {
        this.invAgeGt365 = invAgeGt365;
    }

    public Integer getInvAgeTotal() {
        return invAgeTotal;
    }

    public void setInvAgeTotal(Integer invAgeTotal) {
        this.invAgeTotal = invAgeTotal;
    }

    public BigDecimal getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(BigDecimal salePrice) {
        this.salePrice = salePrice;
    }

    public BigDecimal getYourPrice() {
        return yourPrice;
    }

    public void setYourPrice(BigDecimal yourPrice) {
        this.yourPrice = yourPrice;
    }

    public BigDecimal getMinOrderPriceIn24Hours() {
        return minOrderPriceIn24Hours;
    }

    public void setMinOrderPriceIn24Hours(BigDecimal minOrderPriceIn24Hours) {
        this.minOrderPriceIn24Hours = minOrderPriceIn24Hours;
    }

    public BigDecimal getMaxOrderPriceIn24Hours() {
        return maxOrderPriceIn24Hours;
    }

    public void setMaxOrderPriceIn24Hours(BigDecimal maxOrderPriceIn24Hours) {
        this.maxOrderPriceIn24Hours = maxOrderPriceIn24Hours;
    }
}