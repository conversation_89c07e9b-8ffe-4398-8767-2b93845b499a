package com.nsy.oms.repository.entity.celebrity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.oms.repository.entity.base.BaseMpEntity;

import java.math.BigDecimal;
import java.util.Date;

@TableName("internet_celebrity_sample_order_item_post_daily")
public class InternetCelebritySampleOrderItemPostDailyEntity extends BaseMpEntity {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    private Integer postId;

    @TableField("video_views")
    private Integer videoViews;

    @TableField("item_sold_num")
    private Integer itemSoldNum;

    @TableField("sku_order_num")
    private Integer skuOrderNum;

    private Date gmvDate;

    private BigDecimal gmv;

    private String currency;

    public Integer getVideoViews() {
        return videoViews;
    }

    public void setVideoViews(Integer videoViews) {
        this.videoViews = videoViews;
    }

    public Integer getItemSoldNum() {
        return itemSoldNum;
    }

    public void setItemSoldNum(Integer itemSoldNum) {
        this.itemSoldNum = itemSoldNum;
    }

    public Integer getSkuOrderNum() {
        return skuOrderNum;
    }

    public void setSkuOrderNum(Integer skuOrderNum) {
        this.skuOrderNum = skuOrderNum;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getPostId() {
        return postId;
    }

    public void setPostId(Integer postId) {
        this.postId = postId;
    }

    public Date getGmvDate() {
        return gmvDate;
    }

    public void setGmvDate(Date gmvDate) {
        this.gmvDate = gmvDate;
    }

    public BigDecimal getGmv() {
        return gmv;
    }

    public void setGmv(BigDecimal gmv) {
        this.gmv = gmv;
    }
}