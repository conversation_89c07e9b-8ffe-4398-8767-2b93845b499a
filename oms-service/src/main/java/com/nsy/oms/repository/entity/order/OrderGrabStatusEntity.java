package com.nsy.oms.repository.entity.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.oms.repository.entity.base.BaseMpEntity;

import java.io.Serializable;

/**
 * 订单抓单状态表
 * <AUTHOR>
 * @TableName order_grab_status
 */
@TableName("order_grab_status")
public class OrderGrabStatusEntity extends BaseMpEntity implements Serializable {
    private static final long serialVersionUID = -501480210823388683L;
    /**
     * 主键
     */
    @TableId(value = "grab_status_id", type = IdType.AUTO)
    private Integer grabStatusId;

    /**
     * 店铺id
     */
    @TableField("store_id")
    private Integer storeId;

    /**
     * 订单ID
     */
    @TableField("order_id")
    private Integer orderId;

    /**
     * 订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 订单类型：1-FBM, 2-FBA
     */
    @TableField("order_type")
    private Integer orderType;

    /**
     * 是否抓取详情：0-否，1-是
     */
    @TableField("is_grab_item")
    private Integer isGrabItem;

    /**
     * 是否抓取地址：0-否，1-是
     */
    @TableField("is_grab_address")
    private Integer isGrabAddress;

    /**
     * 是否推送同步订单mq：0-否，1-是
     */
    @TableField("is_push_sync_order_mq")
    private Integer isPushSyncOrderMq;

    /**
     * 是否删除：0-否，1-是
     */
    @TableField("is_deleted")
    private Integer isDeleted;

    public Integer getGrabStatusId() {
        return grabStatusId;
    }

    public void setGrabStatusId(Integer grabStatusId) {
        this.grabStatusId = grabStatusId;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Integer getIsGrabItem() {
        return isGrabItem;
    }

    public void setIsGrabItem(Integer isGrabItem) {
        this.isGrabItem = isGrabItem;
    }

    public Integer getIsGrabAddress() {
        return isGrabAddress;
    }

    public void setIsGrabAddress(Integer isGrabAddress) {
        this.isGrabAddress = isGrabAddress;
    }

    public Integer getIsPushSyncOrderMq() {
        return isPushSyncOrderMq;
    }

    public void setIsPushSyncOrderMq(Integer isPushSyncOrderMq) {
        this.isPushSyncOrderMq = isPushSyncOrderMq;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }
}