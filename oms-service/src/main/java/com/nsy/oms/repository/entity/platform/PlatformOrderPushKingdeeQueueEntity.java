package com.nsy.oms.repository.entity.platform;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.oms.repository.entity.base.BaseMpEntity;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 平台订单推送金蝶队列表
 *
 * <AUTHOR>
 * @TableName platform_order_push_kingdee_queue
 */
@TableName("platform_order_push_kingdee_queue")
public class PlatformOrderPushKingdeeQueueEntity extends BaseMpEntity implements Serializable {
    private static final long serialVersionUID = 6440565636393763346L;
    /**
     * ID
     */
    @TableId(value = "push_kingdee_queue_id", type = IdType.AUTO)
    private Integer pushKingdeeQueueId;

    /**
     * 平台订单号
     */
    @TableField("platform_order_no")
    private String platformOrderNo;

    /**
     * 订单状态：
     * 10-付款未发货
     * 20-部分发货
     * 21-已发货
     * 30-已取消
     */
    @TableField("order_status")
    private Integer orderStatus;

    /**
     * 订单类型：10-销售订单，20-卖家样品订单，30-平台样品订单
     */
    @TableField("order_type")
    private Integer orderType;

    /**
     * 运费
     */
    @TableField("freight_fee")
    private BigDecimal freightFee;

    /**
     * 平台手续费
     */
    @TableField("commission_fee")
    private BigDecimal commissionFee;

    /**
     * 平台id
     */
    @TableField("platform_id")
    private Integer platformId;

    /**
     * 平台名称
     */
    @TableField("platform_name")
    private String platformName;

    /**
     * 店铺id
     */
    @TableField("store_id")
    private Integer storeId;

    /**
     * 店铺名称
     */
    @TableField("store_name")
    private String storeName;

    /**
     * 推送金蝶状态: 0-未推送，1-推送中，2-已推送，3-忽略
     */
    @TableField("push_status")
    private Integer pushStatus;


    /**
     * 推送金蝶-费用应收单-状态: 0-未推送，1-推送中，2-已推送，3-忽略
     */
    @TableField("push_cost_order_status")
    private Integer pushCostOrderStatus;



    public Integer getPushKingdeeQueueId() {
        return pushKingdeeQueueId;
    }

    public void setPushKingdeeQueueId(Integer pushKingdeeQueueId) {
        this.pushKingdeeQueueId = pushKingdeeQueueId;
    }

    public String getPlatformOrderNo() {
        return platformOrderNo;
    }

    public void setPlatformOrderNo(String platformOrderNo) {
        this.platformOrderNo = platformOrderNo;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public BigDecimal getFreightFee() {
        return freightFee;
    }

    public void setFreightFee(BigDecimal freightFee) {
        this.freightFee = freightFee;
    }

    public BigDecimal getCommissionFee() {
        return commissionFee;
    }

    public void setCommissionFee(BigDecimal commissionFee) {
        this.commissionFee = commissionFee;
    }

    public Integer getPlatformId() {
        return platformId;
    }

    public void setPlatformId(Integer platformId) {
        this.platformId = platformId;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public Integer getPushStatus() {
        return pushStatus;
    }

    public void setPushStatus(Integer pushStatus) {
        this.pushStatus = pushStatus;
    }

    public Integer getPushCostOrderStatus() {
        return pushCostOrderStatus;
    }

    public void setPushCostOrderStatus(Integer pushCostOrderStatus) {
        this.pushCostOrderStatus = pushCostOrderStatus;
    }
}