package com.nsy.oms.repository.entity.inbound;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.oms.repository.entity.base.BaseMpEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * fba补货单
 *
 * @TableName inbound_plan
 */
@TableName("inbound_plan")
@EqualsAndHashCode(callSuper = true)
@Data
public class InboundPlanEntity extends BaseMpEntity {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 店铺id
     */
    @TableField("store_id")
    private Integer storeId;

    /**
     * 店铺名称
     */
    @TableField("store_name")
    private String storeName;

    /**
     * 平台名称
     */
    @TableField("platform")
    private String platform;

    /**
     * 物流公司id
     */
    @TableField("logistics_company_id")
    private Integer logisticsCompanyId;

    /**
     * 物流公司名称
     */
    @TableField("logistics_company_name")
    private String logisticsCompanyName;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 品牌id
     */
    @TableField("brand_id")
    private Integer brandId;

    /**
     * 品牌名称
     */
    @TableField("brand_name")
    private String brandName;

    /**
     * 状态
     */
    @TableField("status")
    private String status;

    /**
     * 审核备注
     */
    @TableField("audit_remarks")
    private String auditRemarks;

    /**
     * 发货仓库(Main, USA)
     */
    @TableField("from_warehouse")
    private String fromWarehouse;

    /**
     * 文件路径(用逗号分割)
     */
    @TableField("urls")
    private String urls;

    /**
     * 仓库id
     */
    @TableField("space_id")
    private Integer spaceId;

    /**
     * 仓库名称
     */
    @TableField("space_name")
    private String spaceName;

    /**
     * 补货计划单号(原erpTid)
     */
    @TableField("erp_tid")
    private String erpTid;

    @TableField("marketplace")
    private String marketplace;
    /**
     * 箱数
     */
    @TableField("box_count")
    private Integer boxCount;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}