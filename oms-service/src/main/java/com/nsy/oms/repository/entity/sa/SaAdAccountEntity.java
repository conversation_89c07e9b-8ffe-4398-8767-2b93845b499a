package com.nsy.oms.repository.entity.sa;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.oms.annotation.FieldDesc;
import com.nsy.oms.repository.entity.base.BaseMpEntity;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 社媒账号-广告账号
 *
 * @TableName sa_ad_account
 */
@TableName("sa_ad_account")
public class SaAdAccountEntity extends BaseMpEntity implements Serializable {
    /**
     *
     */
    @TableId(value = "ad_account_id", type = IdType.AUTO)
    private Integer adAccountId;

    /**
     * 部门
     */
    @TableField("department")
    private String department;
    private String departmentId;

    /**
     * 二级部门
     */
    @TableField("second_department")
    private String secondDepartment;

    /**
     * 账号
     */
    @TableField("account")
    @FieldDesc("账号")
    private String account;

    /**
     * 注册邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 手机号码
     */
    @TableField("phone")
    private String phone;

    /**
     * 分配人员
     */
    @TableField("allocate_user")
    private String allocateUser;

    /**
     * 状态 0 关闭 1 开启
     */
    @TableField("status")
    private Integer status;

    /**
     * 申请单id
     */
    @TableField("applay_id")
    private Integer applayId;

    /**
     * 公司主体
     */
    @TableField("account_subject_id")
    private Integer accountSubjectId;

    @TableField("email_id")
    private Integer emailId;
    @TableField("phone_id")
    private Integer phoneId;

    /**
     * 分配状态:0 待分配 1 已分配
     */
    @TableField("allocate_status")
    private Integer allocateStatus;
    @ApiModelProperty("广告账号ID")
    @FieldDesc("广告账号ID")
    private String accountId;
    @ApiModelProperty("广告账号渠道")
    @FieldDesc("广告账号渠道")
    private String adChannel;

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getAdChannel() {
        return adChannel;
    }

    public void setAdChannel(String adChannel) {
        this.adChannel = adChannel;
    }

    public Integer getAllocateStatus() {
        return allocateStatus;
    }

    public void setAllocateStatus(Integer allocateStatus) {
        this.allocateStatus = allocateStatus;
    }

    public String getSecondDepartment() {
        return secondDepartment;
    }

    public void setSecondDepartment(String secondDepartment) {
        this.secondDepartment = secondDepartment;
    }

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    public Integer getAdAccountId() {
        return adAccountId;
    }

    public void setAdAccountId(Integer adAccountId) {
        this.adAccountId = adAccountId;
    }

    public String getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(String departmentId) {
        this.departmentId = departmentId;
    }

    /**
     * 部门
     */
    public String getDepartment() {
        return department;
    }

    /**
     * 部门
     */
    public void setDepartment(String department) {
        this.department = department;
    }

    /**
     * 账号
     */
    public String getAccount() {
        return account;
    }

    /**
     * 账号
     */
    public void setAccount(String account) {
        this.account = account;
    }

    /**
     * 注册邮箱
     */
    public String getEmail() {
        return email;
    }

    /**
     * 注册邮箱
     */
    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * 手机号码
     */
    public String getPhone() {
        return phone;
    }

    /**
     * 手机号码
     */
    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getAllocateUser() {
        return allocateUser;
    }

    public void setAllocateUser(String allocateUser) {
        this.allocateUser = allocateUser;
    }

    /**
     * 状态 0 关闭 1 开启
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 状态 0 关闭 1 开启
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getApplayId() {
        return applayId;
    }

    public void setApplayId(Integer applayId) {
        this.applayId = applayId;
    }

    public Integer getAccountSubjectId() {
        return accountSubjectId;
    }

    public void setAccountSubjectId(Integer accountSubjectId) {
        this.accountSubjectId = accountSubjectId;
    }

    public Integer getEmailId() {
        return emailId;
    }

    public void setEmailId(Integer emailId) {
        this.emailId = emailId;
    }

    public Integer getPhoneId() {
        return phoneId;
    }

    public void setPhoneId(Integer phoneId) {
        this.phoneId = phoneId;
    }
}