package com.nsy.oms.repository.dao.bd;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nsy.oms.repository.entity.bd.BdFieldRequireConfigEntity;

import java.util.List;


/**
* <AUTHOR>
* @description 针对表【bd_field_require_config(字段必填配置表)】的数据库操作Service
* @createDate 2023-04-18 14:35:23
*/
public interface BdFieldRequireConfigDao extends IService<BdFieldRequireConfigEntity> {

    List<String> getFieldList(String businessType, String moduleType);

    BdFieldRequireConfigEntity getInfo(String businessType);

    void removeInfo(String moduleType, String businessType);
}
