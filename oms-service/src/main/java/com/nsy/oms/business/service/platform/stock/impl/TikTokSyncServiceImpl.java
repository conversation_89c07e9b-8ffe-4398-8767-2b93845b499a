package com.nsy.oms.business.service.platform.stock.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.nsy.api.omspublish.dto.constants.StringConstant;
import com.nsy.oms.business.manage.thirdparty.ThirdPartyApiService;
import com.nsy.oms.business.manage.thirdparty.auth.TiktokAuth;
import com.nsy.oms.business.manage.thirdparty.request.InventorySearchRequest;
import com.nsy.oms.business.manage.thirdparty.response.Goods;
import com.nsy.oms.business.manage.thirdparty.response.Inventory;
import com.nsy.oms.business.manage.thirdparty.response.InventorySearchResponse;
import com.nsy.oms.business.manage.thirdparty.response.OnHandDetail;
import com.nsy.oms.business.service.auth.SauPlatformAuthConfigService;
import com.nsy.oms.business.service.inbound.InboundCreateService;
import com.nsy.oms.business.service.platform.PlatformStockService;
import com.nsy.oms.business.service.platform.stock.TikTokSyncService;
import com.nsy.oms.repository.entity.platform.PlatformStockEntity;
import com.nsy.oms.repository.entity.sa.SaStoreEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: linCheng
 * @create: 2024-04-07 16:29
 **/
@Service
@Slf4j
public class TikTokSyncServiceImpl implements TikTokSyncService {

    @Autowired
    private SauPlatformAuthConfigService sauPlatformAuthConfigService;
    @Autowired
    private PlatformStockService platformStockService;
    @Autowired
    private InboundCreateService inboundCreateService;
    @Autowired
    private ThirdPartyApiService thirdPartyApiService;

    private static final Integer PAGE_SIZE = 100;

    @Override
    public void syncStock(SaStoreEntity storeEntity) {

        TiktokAuth tiktokAuth = sauPlatformAuthConfigService.getTikTokToken(storeEntity.getId());
        InventorySearchRequest request = new InventorySearchRequest();
        request.setTiktokAuth(tiktokAuth);
        request.setPageSize(PAGE_SIZE);
        String nextToken = StringConstant.EMPTY_STRING;
        do {
            request.setNextPageInfo(nextToken);
            InventorySearchResponse inventorySearchResponse = null;
            try {
                inventorySearchResponse = thirdPartyApiService.inventorySearch(request);
            } catch (Exception e) {
                log.error("PlatformStockSyncJob.stockSync.error:{}", e.getMessage(), e);
            }
            if (!Optional.ofNullable(inventorySearchResponse).isPresent()
                    || !Optional.ofNullable(inventorySearchResponse.getInventorySearchDate()).isPresent()) {
                break;
            }
            List<Inventory> inventorys = inventorySearchResponse.getInventorySearchDate().getInventory();
            String nextPageToken = inventorySearchResponse.getInventorySearchDate().getNextPageToken();
            sync(storeEntity, inventorys);
            if (StringUtils.isEmpty(nextPageToken)) {
                break;
            } else {
                nextToken = nextPageToken;
            }
        } while (true);
    }


    private void sync(SaStoreEntity storeEntity, List<Inventory> inventorys) {
        List<String> sellerSkus = inventorys.stream().map(inventory -> inventory.getGoods().getReferenceCode()).collect(Collectors.toList());
        List<String> warehouseIds = inventorys.stream().map(Inventory::getFbtWarehouseId).collect(Collectors.toList());
        List<PlatformStockEntity> platformStockEntities = platformStockService.getByPlatformAndStoreIdAndSellerSkus(storeEntity.getPlatformName(), storeEntity.getId(), sellerSkus, warehouseIds);
        Map<String, PlatformStockEntity> platformStockMap = platformStockEntities.stream().collect(Collectors.toMap(entity -> StrUtil.format("{}_{}", entity.getWarehouseId(), entity.getSellerSku()), Function.identity(), (k1, k2) -> k1));

        Map<String, String> skuMappingMap = inboundCreateService.getSkuMappingMap(sellerSkus, storeEntity.getId());
        List<PlatformStockEntity> platformStockEntityList = new ArrayList<>();
        inventorys.forEach(inventory -> {
            Goods goods = inventory.getGoods();
            OnHandDetail onHandDetail = inventory.getOnHandDetail();
            if (!Optional.ofNullable(goods).isPresent() || !Optional.ofNullable(onHandDetail).isPresent()) {
                return;
            }
            PlatformStockEntity platformStockEntity = platformStockMap.get(StrUtil.format("{}_{}", inventory.getFbtWarehouseId(), goods.getReferenceCode()));
            if (BeanUtil.isEmpty(platformStockEntity)) {
                platformStockEntity = new PlatformStockEntity();
                platformStockEntity.setPlatform(storeEntity.getPlatformName());
                platformStockEntity.setStoreId(storeEntity.getId());
                platformStockEntity.setSellerSku(goods.getReferenceCode());
                platformStockEntity.setErpSku(skuMappingMap.getOrDefault(goods.getReferenceCode(), goods.getReferenceCode()));
                platformStockEntity.setWarehouseId(inventory.getFbtWarehouseId());
                platformStockEntity.setCreateBy(this.getClass().getSimpleName());
                platformStockEntity.setCreateDate(new Date());
            }
            platformStockEntity.setAvailableStock(Optional.ofNullable(onHandDetail.getAvailableQuantity()).isPresent() ? onHandDetail.getAvailableQuantity() : 0);
            platformStockEntity.setReservationStock(Optional.ofNullable(onHandDetail.getReservedQuantity()).isPresent() ? onHandDetail.getReservedQuantity() : 0);
            platformStockEntity.setDeliveryOnwayStock(Optional.ofNullable(inventory.getInTransitQuantity()).isPresent() ? inventory.getInTransitQuantity() : 0);
            platformStockEntity.setUnfulfillableStock(Optional.ofNullable(onHandDetail.getUnfulfillableQuantity()).isPresent() ? onHandDetail.getUnfulfillableQuantity() : 0);
            platformStockEntity.setUpdateBy(this.getClass().getSimpleName());
            platformStockEntity.setLocation(storeEntity.getLocation());
            platformStockEntity.setUpdateDate(new Date());

            platformStockEntityList.add(platformStockEntity);
        });
        platformStockService.saveOrUpdateBatch(platformStockEntityList);
    }
}
