package com.nsy.oms.business.service.celebrity;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nsy.oms.business.domain.response.celebrity.SyncInternetCelebritySampleOrderItem;
import com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderItemEntity;

import java.util.List;

/**
 * <p>
 * 网红样衣订单明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-29
 */
public interface IInternetCelebritySampleOrderItemService extends IService<InternetCelebritySampleOrderItemEntity> {

    List<InternetCelebritySampleOrderItemEntity> getList(Integer internetCelebritySampleOrderId);

    void syncInternetCelebritySampleOrderItem(Integer internetCelebritySampleOrderId, List<SyncInternetCelebritySampleOrderItem> syncInternetCelebritySampleOrderItems);

    void syncInternetCelebritySampleOrderItemByImport(Integer internetCelebritySampleOrderId, String platformOrderNo, List<SyncInternetCelebritySampleOrderItem> syncInternetCelebritySampleOrderItems);

    List<InternetCelebritySampleOrderItemEntity> listByIdList(List<Integer> orderItemIds);

    List<InternetCelebritySampleOrderItemEntity> getListWhenDeliveryDateIsNull();

}
