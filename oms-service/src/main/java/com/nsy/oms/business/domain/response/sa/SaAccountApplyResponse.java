package com.nsy.oms.business.domain.response.sa;

import com.nsy.oms.business.domain.request.sa.FileRequest;
import com.nsy.oms.business.domain.request.sa.SaAccountInfoRequest;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/29 15:03
 */
public class SaAccountApplyResponse {
    @ApiModelProperty("申请id")
    private Integer applyId;

    /**
     * 申请类型 0 创建账号申请 1 关掉申请 2 变更申请
     */
    @ApiModelProperty("申请类型")
    private Integer applyType;
    @ApiModelProperty("主体区域")
    private String mainArea;
    @ApiModelProperty("主营品类名称")
    private String categoryId;

    /**
     * 主营品类名称
     */
    @ApiModelProperty("主营品类id")
    private String categoryName;

    @ApiModelProperty("申请类型-列表显示使用")
    private String applyTypeName;

    /**
     * 账号类型 0 店铺账号 1 社媒号 2 直播号 3 广告账号
     */
    @ApiModelProperty("账号类型 0 店铺账号 1 社媒号 2 直播号 3 广告账号")
    private Integer accountType;
    @ApiModelProperty("账号类型-列表显示使用")
    private String accountTypeName;
    /**
     * 平台id
     */
    @ApiModelProperty("平台id")
    private String platformId;
    @ApiModelProperty("店铺ID")
    private Integer storeId;
    @ApiModelProperty("广告渠道")
    private String adChannel;

    /**
     * 平台名称
     */
    @ApiModelProperty("平台名称")
    private String platformName;

    @ApiModelProperty("申请说明")
    private String applyRemark;

    /**
     * 区域编码
     */
    @ApiModelProperty("区域编码")
    private String areaCode;

    /**
     * 市场
     */
    @ApiModelProperty("市场")
    private List<String> marketplaceList;

    /**
     * 申请说明
     */
    @ApiModelProperty("申请说明")
    private String remark;

    /**
     * 部门
     */
    @ApiModelProperty("部门")
    private String department;
    private String departmentId;
    /**
     * 二级部门
     */
    @ApiModelProperty("二级部门")
    private String secondDepartment;

    @ApiModelProperty("账号")
    private String account;
    @ApiModelProperty("店铺信息")
    private List<SaAccountInfoRequest> storeList;

    /**
     * 社媒账号类型id
     */
    @ApiModelProperty("社媒账号类型id")
    private Integer mediaTypeConfigId;

    /**
     * 直播账号类型id
     */
    @ApiModelProperty("直播账号类型id")
    private Integer liveTypeConfigId;

    /**
     * 电话id
     */
    @ApiModelProperty("电话id")
    private Integer phoneId;
    @ApiModelProperty("电话-列表显示使用")
    private String phone;

    /**
     * 邮箱id
     */
    @ApiModelProperty("邮箱id")
    private Integer emailId;
    @ApiModelProperty("注册邮箱-列表显示")
    private String email;

    /**
     * 附件管理
     */
    @ApiModelProperty("附件管理")
    private List<FileRequest> fileUrlList;


    /**
     * 直营/分销id
     */
    @ApiModelProperty("直营/分销id")
    private Integer retailId;
    @ApiModelProperty("分销商名称")
    private String retailName;

    /**
     * 店铺信息
     */
    @ApiModelProperty("店铺信息-store_base")
    private String storeBase;

    /**
     * 商品通知配置人员
     */
    @ApiModelProperty("商品通知配置人员-变更后")
    private String storeStaffings;
    private String storeStaffingsId;
    @ApiModelProperty("商品通知配置人员-变更前")
    private String oldStoreStaffings;
    private String oldStoreStaffingsId;
    /**
     * 分配人员
     */
    @ApiModelProperty("分配人员")
    private String allocateUser;
    private String allocateUserId;
    /**
     * 分配人员
     */
    @ApiModelProperty("分配人员变更前")
    private String oldAllocateUser;
    private String oldAllocateUserId;

    /**
     * 账号主体id
     */
    @ApiModelProperty("account_subject_id")
    private Integer accountSubjectId;
    @ApiModelProperty("公司主体")
    private String accountSubject;

    /**
     * 工作流id
     */
    @ApiModelProperty("activity_id")
    private Integer activityId;

    /**
     * 账号id:多个逗号隔开
     */
    @ApiModelProperty("account_ids")
    private String accountIds;

    /**
     * 审核状态 0:待发起审批 1:待事业部负责人审批 2:待财务分配主体 3:待分配电话 4:待分配邮箱 5:待填写账号审批完成取消
     */
    @ApiModelProperty("审核状态")
    private String status;
    @ApiModelProperty("审核状态-列表使用")
    private String statusName;
    @ApiModelProperty("申请人")
    private String createBy;
    @ApiModelProperty("创建时间")
    private Date createDate;


    @ApiModelProperty("主营品牌")
    private String brand;
    @ApiModelProperty("主营品类")
    private String category;
    @ApiModelProperty("收款渠道ID")
    private String paymentChannelId;

    @ApiModelProperty("收款渠道名称")
    private String paymentChannelName;

    @ApiModelProperty("收款手续费费率（%）")
    private BigDecimal paymentFee;

    @ApiModelProperty("收款账单下载时间（每月XX日）")
    private Integer paymentInvoiceDownloadDay;

    @ApiModelProperty("结算方式 0 公司收款 1:压货代销 2:月结客户")
    private Integer settlementMethod;
    @ApiModelProperty("结算方式-显示使用")
    private String settlementMethodName;
    @ApiModelProperty("结算周期")
    private String settlementPeriod;

    @ApiModelProperty("交易账单下载时间（每月XX日）")
    private Integer transactionBillDownloadDay;

    @ApiModelProperty("发货渠道")
    private List<String> shippingChannel;

    @ApiModelProperty("发货渠道-显示使用")
    private String shippingChannelName;

    @ApiModelProperty("店铺费用及计算规则说明")
    private String storeFeeCalculateRuleRemark;

    @ApiModelProperty("变更前开户行id")
    private String oldBankId;
    @ApiModelProperty("变更前开户行id-显示使用")
    private String oldBankName;

    @ApiModelProperty("开户行id")
    private String bankId;

    @ApiModelProperty("变更后开户行")
    private String bankName;

    @ApiModelProperty("变更前卡号id")
    private Integer oldCardId;

    @ApiModelProperty("变更前卡号")
    private String oldCardName;

    @ApiModelProperty("卡号id")
    private Integer cardId;

    @ApiModelProperty("变更后卡号")
    private String cardName;

    private String company;

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getAdChannel() {
        return adChannel;
    }

    public void setAdChannel(String adChannel) {
        this.adChannel = adChannel;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getSettlementMethodName() {
        return settlementMethodName;
    }

    public void setSettlementMethodName(String settlementMethodName) {
        this.settlementMethodName = settlementMethodName;
    }

    public String getMainArea() {
        return mainArea;
    }

    public void setMainArea(String mainArea) {
        this.mainArea = mainArea;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getPaymentChannelId() {
        return paymentChannelId;
    }

    public void setPaymentChannelId(String paymentChannelId) {
        this.paymentChannelId = paymentChannelId;
    }

    public String getPaymentChannelName() {
        return paymentChannelName;
    }

    public void setPaymentChannelName(String paymentChannelName) {
        this.paymentChannelName = paymentChannelName;
    }

    public BigDecimal getPaymentFee() {
        return paymentFee;
    }

    public void setPaymentFee(BigDecimal paymentFee) {
        this.paymentFee = paymentFee;
    }

    public Integer getPaymentInvoiceDownloadDay() {
        return paymentInvoiceDownloadDay;
    }

    public void setPaymentInvoiceDownloadDay(Integer paymentInvoiceDownloadDay) {
        this.paymentInvoiceDownloadDay = paymentInvoiceDownloadDay;
    }

    public Integer getSettlementMethod() {
        return settlementMethod;
    }

    public void setSettlementMethod(Integer settlementMethod) {
        this.settlementMethod = settlementMethod;
    }

    public String getSettlementPeriod() {
        return settlementPeriod;
    }

    public void setSettlementPeriod(String settlementPeriod) {
        this.settlementPeriod = settlementPeriod;
    }

    public Integer getTransactionBillDownloadDay() {
        return transactionBillDownloadDay;
    }

    public void setTransactionBillDownloadDay(Integer transactionBillDownloadDay) {
        this.transactionBillDownloadDay = transactionBillDownloadDay;
    }

    public List<String> getShippingChannel() {
        return shippingChannel;
    }

    public void setShippingChannel(List<String> shippingChannel) {
        this.shippingChannel = shippingChannel;
    }

    public String getShippingChannelName() {
        return shippingChannelName;
    }

    public void setShippingChannelName(String shippingChannelName) {
        this.shippingChannelName = shippingChannelName;
    }

    public String getStoreFeeCalculateRuleRemark() {
        return storeFeeCalculateRuleRemark;
    }

    public void setStoreFeeCalculateRuleRemark(String storeFeeCalculateRuleRemark) {
        this.storeFeeCalculateRuleRemark = storeFeeCalculateRuleRemark;
    }

    public String getOldBankId() {
        return oldBankId;
    }

    public void setOldBankId(String oldBankId) {
        this.oldBankId = oldBankId;
    }

    public String getOldBankName() {
        return oldBankName;
    }

    public void setOldBankName(String oldBankName) {
        this.oldBankName = oldBankName;
    }

    public String getBankId() {
        return bankId;
    }

    public void setBankId(String bankId) {
        this.bankId = bankId;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public Integer getOldCardId() {
        return oldCardId;
    }

    public void setOldCardId(Integer oldCardId) {
        this.oldCardId = oldCardId;
    }

    public String getOldCardName() {
        return oldCardName;
    }

    public void setOldCardName(String oldCardName) {
        this.oldCardName = oldCardName;
    }

    public Integer getCardId() {
        return cardId;
    }

    public void setCardId(Integer cardId) {
        this.cardId = cardId;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getStoreStaffingsId() {
        return storeStaffingsId;
    }

    public void setStoreStaffingsId(String storeStaffingsId) {
        this.storeStaffingsId = storeStaffingsId;
    }

    public String getOldStoreStaffingsId() {
        return oldStoreStaffingsId;
    }

    public void setOldStoreStaffingsId(String oldStoreStaffingsId) {
        this.oldStoreStaffingsId = oldStoreStaffingsId;
    }

    public String getAllocateUserId() {
        return allocateUserId;
    }

    public void setAllocateUserId(String allocateUserId) {
        this.allocateUserId = allocateUserId;
    }

    public String getOldAllocateUserId() {
        return oldAllocateUserId;
    }

    public void setOldAllocateUserId(String oldAllocateUserId) {
        this.oldAllocateUserId = oldAllocateUserId;
    }

    public String getApplyRemark() {
        return applyRemark;
    }

    public void setApplyRemark(String applyRemark) {
        this.applyRemark = applyRemark;
    }

    public String getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(String departmentId) {
        this.departmentId = departmentId;
    }

    public String getOldStoreStaffings() {
        return oldStoreStaffings;
    }

    public void setOldStoreStaffings(String oldStoreStaffings) {
        this.oldStoreStaffings = oldStoreStaffings;
    }

    public String getOldAllocateUser() {
        return oldAllocateUser;
    }

    public void setOldAllocateUser(String oldAllocateUser) {
        this.oldAllocateUser = oldAllocateUser;
    }

    public String getRetailName() {
        return retailName;
    }

    public void setRetailName(String retailName) {
        this.retailName = retailName;
    }

    public List<SaAccountInfoRequest> getStoreList() {
        return storeList;
    }

    public void setStoreList(List<SaAccountInfoRequest> storeList) {
        this.storeList = storeList;
    }

    public Integer getApplyId() {
        return applyId;
    }

    public void setApplyId(Integer applyId) {
        this.applyId = applyId;
    }

    public Integer getApplyType() {
        return applyType;
    }

    public void setApplyType(Integer applyType) {
        this.applyType = applyType;
    }

    public String getApplyTypeName() {
        return applyTypeName;
    }

    public void setApplyTypeName(String applyTypeName) {
        this.applyTypeName = applyTypeName;
    }

    public Integer getAccountType() {
        return accountType;
    }

    public void setAccountType(Integer accountType) {
        this.accountType = accountType;
    }

    public String getAccountTypeName() {
        return accountTypeName;
    }

    public void setAccountTypeName(String accountTypeName) {
        this.accountTypeName = accountTypeName;
    }

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public List<String> getMarketplaceList() {
        return marketplaceList;
    }

    public void setMarketplaceList(List<String> marketplaceList) {
        this.marketplaceList = marketplaceList;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getSecondDepartment() {
        return secondDepartment;
    }

    public void setSecondDepartment(String secondDepartment) {
        this.secondDepartment = secondDepartment;
    }

    public Integer getMediaTypeConfigId() {
        return mediaTypeConfigId;
    }

    public void setMediaTypeConfigId(Integer mediaTypeConfigId) {
        this.mediaTypeConfigId = mediaTypeConfigId;
    }

    public Integer getLiveTypeConfigId() {
        return liveTypeConfigId;
    }

    public void setLiveTypeConfigId(Integer liveTypeConfigId) {
        this.liveTypeConfigId = liveTypeConfigId;
    }

    public Integer getPhoneId() {
        return phoneId;
    }

    public void setPhoneId(Integer phoneId) {
        this.phoneId = phoneId;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getEmailId() {
        return emailId;
    }

    public void setEmailId(Integer emailId) {
        this.emailId = emailId;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public List<FileRequest> getFileUrlList() {
        return fileUrlList;
    }

    public void setFileUrlList(List<FileRequest> fileUrlList) {
        this.fileUrlList = fileUrlList;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public Integer getRetailId() {
        return retailId;
    }

    public void setRetailId(Integer retailId) {
        this.retailId = retailId;
    }

    public String getStoreBase() {
        return storeBase;
    }

    public void setStoreBase(String storeBase) {
        this.storeBase = storeBase;
    }

    public String getStoreStaffings() {
        return storeStaffings;
    }

    public void setStoreStaffings(String storeStaffings) {
        this.storeStaffings = storeStaffings;
    }

    public String getAllocateUser() {
        return allocateUser;
    }

    public void setAllocateUser(String allocateUser) {
        this.allocateUser = allocateUser;
    }

    public Integer getAccountSubjectId() {
        return accountSubjectId;
    }

    public void setAccountSubjectId(Integer accountSubjectId) {
        this.accountSubjectId = accountSubjectId;
    }

    public String getAccountSubject() {
        return accountSubject;
    }

    public void setAccountSubject(String accountSubject) {
        this.accountSubject = accountSubject;
    }

    public Integer getActivityId() {
        return activityId;
    }

    public void setActivityId(Integer activityId) {
        this.activityId = activityId;
    }

    public String getAccountIds() {
        return accountIds;
    }

    public void setAccountIds(String accountIds) {
        this.accountIds = accountIds;
    }


    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
}
