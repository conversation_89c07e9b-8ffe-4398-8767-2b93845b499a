package com.nsy.oms.business.manage.erp.response.inbound;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @description:
 * @author: linCheng
 * @create: 2025-05-12 14:20
 **/
@Data
public class TradePaymentInfo {

    @JsonProperty("PaymentType")
    private String paymentType;

    @JsonProperty("Tid")
    private String tid;

    @JsonProperty("PaymentId")
    private String paymentId;

    @JsonProperty("GrossAmount")
    private BigDecimal grossAmount;

    @JsonProperty("GrossCurrency")
    private String grossCurrency;

    @JsonProperty("Url")
    private String url;

}
