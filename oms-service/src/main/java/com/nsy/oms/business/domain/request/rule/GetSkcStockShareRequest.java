package com.nsy.oms.business.domain.request.rule;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description:
 * @author: linCheng
 * @create: 2024-11-01 14:20
 **/
@Data
public class GetSkcStockShareRequest {

    @NotEmpty(message = "【skc】不能为空")
    @ApiModelProperty("skc")
    private List<String> skcs;

    @NotNull(message = "【店铺id】不能为空")
    @ApiModelProperty("店铺id")
    private Integer storeId;

}
