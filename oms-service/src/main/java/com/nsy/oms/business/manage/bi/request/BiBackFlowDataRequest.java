package com.nsy.oms.business.manage.bi.request;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-08-08
 */
public class BiBackFlowDataRequest {
    @NotBlank
    private String type;
    @NotEmpty
    private List<String> tableNames;
    private String operator;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<String> getTableNames() {
        return tableNames;
    }

    public void setTableNames(List<String> tableNames) {
        this.tableNames = tableNames;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
}
