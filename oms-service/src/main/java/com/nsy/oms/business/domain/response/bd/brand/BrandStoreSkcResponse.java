package com.nsy.oms.business.domain.response.bd.brand;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @since  2024-03-27 17:34
 */
@ApiModel(value = "BrandStoreSkcResponse", description = "scm调用-品牌店铺商品信息")
public class BrandStoreSkcResponse {

    @ApiModelProperty(value = "品牌店铺商品集合", name = "bdBrandStoreSkcList")
    private List<BdBrandStoreSkc> bdBrandStoreSkcList;

    public BrandStoreSkcResponse() {
    }

    public BrandStoreSkcResponse(List<BdBrandStoreSkc> bdBrandStoreSkcList) {
        this.bdBrandStoreSkcList = bdBrandStoreSkcList;
    }

    public List<BdBrandStoreSkc> getBdBrandStoreSkcList() {
        return bdBrandStoreSkcList;
    }

    public void setBdBrandStoreSkcList(List<BdBrandStoreSkc> bdBrandStoreSkcList) {
        this.bdBrandStoreSkcList = bdBrandStoreSkcList;
    }
}
