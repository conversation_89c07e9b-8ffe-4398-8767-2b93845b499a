package com.nsy.oms.business.manage.user.request.shared;


import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * User: Emily
 * Date: 2018/4/30
 */
public class SelectModel implements Serializable {

    private static final long serialVersionUID = -6891430851136210911L;
    @ApiModelProperty(value = "value", name = "value")
    private String value;
    @ApiModelProperty(value = "label", name = "label")
    private String label;
    @ApiModelProperty(value = "id", name = "id")
    private String id;
    @ApiModelProperty(value = "text", name = "text")
    private String text;
    @ApiModelProperty(value = "attribute", name = "attribute")
    private String attribute;

    public SelectModel() {
    }

    public SelectModel(String id, String value, String label, String text) {
        this.id = id;
        this.value = value;
        this.label = label;
        this.text = text;
    }

    public SelectModel(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public SelectModel(String id, String value, String label) {
        this.id = id;
        this.value = value;
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getAttribute() {
        return attribute;
    }

    public void setAttribute(String attribute) {
        this.attribute = attribute;
    }
}
