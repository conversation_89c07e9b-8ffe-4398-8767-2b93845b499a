package com.nsy.oms.business.domain.request.points;

import com.nsy.oms.business.domain.request.base.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: linCheng
 * @create: 2025-05-07 17:17
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class B2bPointsRefundPageRequest extends PageRequest {

    @ApiModelProperty("客户名称")
    private String customerName;
    @ApiModelProperty("客户名称")
    private String customerEmail;
    @ApiModelProperty("客户名称")
    private String storeName;
    @ApiModelProperty("账期创建时间-开始")
    private Date accountCreateTimeStart;
    @ApiModelProperty("账期创建时间-结束")
    private Date accountCreateTimeEnd;
    @ApiModelProperty("状态")
    private List<String> stateList;
    @ApiModelProperty("退款请求ID")
    private List<Integer> refundIds;
}
