package com.nsy.oms.business.domain.request.bd.brand;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023年9月19日 0019 下午 04:19:30
 */
public class CheckStoreBrandSkcRequest {

    @NotBlank
    String skc;

    @NotEmpty
    List<String> storeBrandList;

    List<Integer> storeIdList;

    public List<Integer> getStoreIdList() {
        return storeIdList;
    }

    public void setStoreIdList(List<Integer> storeIdList) {
        this.storeIdList = storeIdList;
    }

    public String getSkc() {
        return skc;
    }

    public void setSkc(String skc) {
        this.skc = skc;
    }

    public List<String> getStoreBrandList() {
        return storeBrandList;
    }

    public void setStoreBrandList(List<String> storeBrandList) {
        this.storeBrandList = storeBrandList;
    }
}
