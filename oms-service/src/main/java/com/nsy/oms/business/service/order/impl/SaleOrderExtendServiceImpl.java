package com.nsy.oms.business.service.order.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.transfer.enums.PlatformTypeEnum;
import com.nsy.oms.business.domain.request.order.OrderPackageTrackingRequest;
import com.nsy.oms.business.domain.response.order.OrderTrackingResponse;
import com.nsy.oms.business.manage.thirdparty.ThirdPartyApiService;
import com.nsy.oms.business.manage.thirdparty.auth.AmazonAuth;
import com.nsy.oms.business.manage.thirdparty.request.GetPackagesRequest;
import com.nsy.oms.business.manage.thirdparty.request.GetTrackingRequest;
import com.nsy.oms.business.manage.thirdparty.response.GetPackagesResponse;
import com.nsy.oms.business.manage.thirdparty.response.GetTrackingResponse;
import com.nsy.oms.business.service.auth.SauAmazonConfigService;
import com.nsy.oms.business.service.bd.BdMarketplaceService;
import com.nsy.oms.business.service.order.SaleOrderExtendService;
import com.nsy.oms.business.service.platform.PlatformOrderService;
import com.nsy.oms.repository.entity.bd.BdMarketplaceEntity;
import com.nsy.oms.repository.entity.order.SaleOrderEntity;
import com.nsy.oms.repository.entity.order.SaleOrderExtendEntity;
import com.nsy.oms.repository.entity.platform.PlatformOrderEntity;
import com.nsy.oms.repository.sql.mapper.order.SaleOrderExtendMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description 针对表【order_extend(订单扩展表)】的数据库操作Service实现
 * @createDate 2024-10-17 15:01:11
 */
@Service
public class SaleOrderExtendServiceImpl extends ServiceImpl<SaleOrderExtendMapper, SaleOrderExtendEntity>
        implements SaleOrderExtendService {
    @Autowired
    private SauAmazonConfigService sauAmazonConfigService;
    @Autowired
    private PlatformOrderService platformOrderService;
    @Autowired
    private ThirdPartyApiService thirdPartyApiService;
    @Autowired
    private BdMarketplaceService marketplaceService;

    @Override
    public SaleOrderExtendEntity getByOrderNo(String orderNo) {
        return getOne(Wrappers.<SaleOrderExtendEntity>lambdaQuery().eq(SaleOrderExtendEntity::getOrderNo, orderNo));
    }

    @Override
    public OrderTrackingResponse getOrderPackageTrackingInfo(OrderPackageTrackingRequest request) {
        OrderTrackingResponse response = new OrderTrackingResponse();
        PlatformOrderEntity platformOrderEntity = platformOrderService.getOrderByOrderNo(request.getPlatformOrderNo());
        if (platformOrderEntity == null) {
            return response;
        }
        AmazonAuth amazonAuth = sauAmazonConfigService.getOrderGrabAuthInfo(platformOrderEntity.getStoreId());
        GetPackagesRequest getPackagesRequest = new GetPackagesRequest();
        getPackagesRequest.setAmazonAuth(amazonAuth);
        getPackagesRequest.setOrderNo(platformOrderEntity.getPlatformOriginalOrderNo());
        getPackagesRequest.setPlatform(PlatformTypeEnum.Amazon);
        getPackagesRequest.setStoreId(platformOrderEntity.getStoreId());
        GetPackagesResponse packages = thirdPartyApiService.getPackages(getPackagesRequest);
        if (Objects.isNull(packages) || StrUtil.isBlank(packages.getPackageId())) {
            return response;
        }
        GetTrackingRequest getTrackingRequest = new GetTrackingRequest(platformOrderEntity.getPlatformOriginalOrderNo());
        getTrackingRequest.setAmazonAuth(amazonAuth);
        getTrackingRequest.setPlatform(PlatformTypeEnum.Amazon);
        getTrackingRequest.setPackageNumber(Integer.valueOf(packages.getPackageId()));
        GetTrackingResponse getTrackingResponse = thirdPartyApiService.getTracking(getTrackingRequest);
        if (Objects.isNull(getTrackingResponse)) {
            return response;
        }
        response.setLogisticsChannel(getTrackingResponse.getLogisticsChannel());
        response.setLogisticsNo(getTrackingResponse.getLogisticsNo());
        response.setPackageStatus(getTrackingResponse.getCurrentStatus());
        if (CollectionUtil.isNotEmpty(getTrackingResponse.getTracking())) {
            response.setHistory(getTrackingResponse.getTracking());
        }
        return response;
    }

    /**
     * 保存(本地仓发货)订单表头扩展表
     */
    @Override
    public void saveOrderExtend(SaleOrderEntity saleOrderEntity, String updateBy) {
        if (ObjectUtil.isNull(saleOrderEntity) && StringUtils.isBlank(saleOrderEntity.getMarketCode())) {
            return;
        }
        BdMarketplaceEntity marketplaceEntity = marketplaceService.getByMarketplaceId(saleOrderEntity.getMarketCode());
        if (ObjectUtil.isNull(marketplaceEntity)) {
            return;
        }
        SaleOrderExtendEntity saleOrderExtendEntity = this.getByOrderNo(saleOrderEntity.getOrderNo());
        if (ObjectUtil.isNull(saleOrderExtendEntity)) {
            saleOrderExtendEntity = new SaleOrderExtendEntity();
            saleOrderExtendEntity.setCreateBy(updateBy);
        }
        if (ObjectUtil.isNull(saleOrderExtendEntity.getOrderId())) {
            saleOrderExtendEntity.setOrderId(saleOrderEntity.getOrderId());
            saleOrderExtendEntity.setOrderNo(saleOrderEntity.getOrderNo());
        }
        if (ObjectUtil.isNotNull(saleOrderEntity.getOrderCreateDate())) {
            saleOrderExtendEntity.setOrderCreateDateTimeZone(com.nsy.oms.utils.DateUtils.getTimeByTimeZone(saleOrderEntity.getOrderCreateDate(), marketplaceEntity.getTimeZoneId()));
        }
        if (ObjectUtil.isNotNull(saleOrderEntity.getEarliestShipDate())) {
            saleOrderExtendEntity.setEarliestShipDateTimeZone(com.nsy.oms.utils.DateUtils.getTimeByTimeZone(saleOrderEntity.getEarliestShipDate(), marketplaceEntity.getTimeZoneId()));
        }
        if (ObjectUtil.isNotNull(saleOrderEntity.getLatestShipDate())) {
            saleOrderExtendEntity.setLatestShipDateTimeZone(com.nsy.oms.utils.DateUtils.getTimeByTimeZone(saleOrderEntity.getLatestShipDate(), marketplaceEntity.getTimeZoneId()));
        }
        if (ObjectUtil.isNotNull(saleOrderEntity.getEarliestDeliveryDate())) {
            saleOrderExtendEntity.setEarliestDeliveryDateTimeZone(com.nsy.oms.utils.DateUtils.getTimeByTimeZone(saleOrderEntity.getEarliestDeliveryDate(), marketplaceEntity.getTimeZoneId()));
        }
        if (ObjectUtil.isNotNull(saleOrderEntity.getLatestDeliveryDate())) {
            saleOrderExtendEntity.setLatestDeliveryDateTimeZone(com.nsy.oms.utils.DateUtils.getTimeByTimeZone(saleOrderEntity.getLatestDeliveryDate(), marketplaceEntity.getTimeZoneId()));
        }
        if (ObjectUtil.isNotNull(saleOrderEntity.getOrderPaymentDate())) {
            saleOrderExtendEntity.setOrderPaymentDateTimeZone(com.nsy.oms.utils.DateUtils.getTimeByTimeZone(saleOrderEntity.getOrderPaymentDate(), marketplaceEntity.getTimeZoneId()));
        }
        if (ObjectUtil.isNotNull(saleOrderEntity.getOrderCancelDate())) {
            saleOrderExtendEntity.setOrderCancelDateTimeZone(com.nsy.oms.utils.DateUtils.getTimeByTimeZone(saleOrderEntity.getOrderCancelDate(), marketplaceEntity.getTimeZoneId()));
        }
        saleOrderExtendEntity.setLocation(saleOrderEntity.getLocation());
        saleOrderExtendEntity.setUpdateBy(updateBy);
        this.saveOrUpdate(saleOrderExtendEntity);
    }
}




