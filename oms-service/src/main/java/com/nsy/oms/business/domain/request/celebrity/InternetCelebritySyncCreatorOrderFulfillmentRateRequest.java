package com.nsy.oms.business.domain.request.celebrity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @Author: lhh
 * @Date: 2025-04-18
 * @Description: 网红查询请求类
 */
@NoArgsConstructor
@Data
public class InternetCelebritySyncCreatorOrderFulfillmentRateRequest {

    @NotEmpty
    @JsonProperty("data")
    List<InternetCelebritySyncCreatorOrderFulfillmentRate> data;

    @NoArgsConstructor
    @Data
    public static class InternetCelebritySyncCreatorOrderFulfillmentRate {

        @JsonProperty("store_id")
        private String storeId;
        @JsonProperty("store_name")
        private String storeName;
        @JsonProperty("apply_group")
        private ApplyGroupDTO applyGroup;

        @NoArgsConstructor
        @Data
        public static class ApplyGroupDTO {
            @JsonProperty("creator_info")
            private CreatorInfoDTO creatorInfo;

            @NoArgsConstructor
            @Data
            public static class CreatorInfoDTO {
                @JsonProperty("creator_id")
                private String creatorId;
                @JsonProperty("name")
                private String name;
                @JsonProperty("fulfillment_rate")
                private String fulfillmentRate;
            }
        }
    }
    
}
