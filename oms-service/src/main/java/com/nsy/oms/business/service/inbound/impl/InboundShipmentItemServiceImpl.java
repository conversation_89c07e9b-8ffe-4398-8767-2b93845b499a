package com.nsy.oms.business.service.inbound.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.oms.business.service.inbound.InboundShipmentItemService;
import com.nsy.oms.repository.sql.mapper.inbound.InboundShipmentItemEntityMapper;
import com.nsy.oms.repository.entity.inbound.InboundShipmentItemEntity;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【inbound_shipment_item(入库货件item详情)】的数据库操作Service实现
 * @createDate 2025-03-28 10:34:20
 */
@Service
public class InboundShipmentItemServiceImpl extends ServiceImpl<InboundShipmentItemEntityMapper, InboundShipmentItemEntity> implements InboundShipmentItemService {

    @Override
    public List<InboundShipmentItemEntity> getByShipmentIds(List<String> shipmentIds) {
        if (CollectionUtils.isEmpty(shipmentIds)) {
            return Collections.emptyList();
        }
        return this.list(Wrappers.<InboundShipmentItemEntity>lambdaQuery().in(InboundShipmentItemEntity::getShipmentId, shipmentIds));
    }

    @Override
    public void deleteByShipmentIds(List<String> shipmentIds) {
        LambdaQueryWrapper<InboundShipmentItemEntity> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.in(InboundShipmentItemEntity::getShipmentId, shipmentIds);
        this.remove(lambdaQueryWrapper);
    }
}




