package com.nsy.oms.business.service.celebrity.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.oms.business.service.celebrity.InternetCelebritySampleStoreMappingService;
import com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleStoreMappingEntity;
import com.nsy.oms.repository.sql.mapper.celebrity.InternetCelebritySampleStoreMappingMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【internet_celebrity_sample_store_mapping(达人样衣订单映射)】的数据库操作Service实现
 * @createDate 2025-05-06 11:41:10
 */
@Service
public class InternetCelebritySampleStoreMappingServiceImpl extends ServiceImpl<InternetCelebritySampleStoreMappingMapper, InternetCelebritySampleStoreMappingEntity> implements InternetCelebritySampleStoreMappingService {

    @Override
    public InternetCelebritySampleStoreMappingEntity findTopBySampleStoreId(Integer sampleStoreId) {
        return this.getOne(new LambdaQueryWrapper<InternetCelebritySampleStoreMappingEntity>()
                .eq(InternetCelebritySampleStoreMappingEntity::getSampleStoreId, sampleStoreId)
                .last("limit 1")
        );
    }
}




