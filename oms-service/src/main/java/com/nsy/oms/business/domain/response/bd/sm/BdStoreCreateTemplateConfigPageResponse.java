package com.nsy.oms.business.domain.response.bd.sm;

import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 社媒账号-店铺生成配置表
 *
 * @TableName bd_store_create_template_config
 */
public class BdStoreCreateTemplateConfigPageResponse {
    /**
     *
     */
    @ApiModelProperty("id")
    private Integer id;

    /**
     * 模板名称
     */
    @ApiModelProperty("模板名称")
    private String name;

    /**
     * 模型类型:0 通用模板 1:亚马逊模板 2:沃尔玛平台模板
     */
    @ApiModelProperty("模板类型")
    private String typeName;


    /**
     * 平台
     */
    @ApiModelProperty("平台")
    private String platform;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createDate;


    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateDate;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }
}