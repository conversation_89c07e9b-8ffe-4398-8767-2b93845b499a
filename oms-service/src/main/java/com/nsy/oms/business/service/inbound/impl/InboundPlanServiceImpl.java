package com.nsy.oms.business.service.inbound.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.oms.business.domain.request.inbound.ShipmentPlanPageRequest;
import com.nsy.oms.business.manage.wms.WmsApiService;
import com.nsy.oms.business.manage.wms.request.StaBoxInfoDto;
import com.nsy.oms.business.manage.wms.response.StaBoxItemInfoDto;
import com.nsy.oms.business.service.inbound.InboundPlanLogService;
import com.nsy.oms.business.service.inbound.InboundPlanService;
import com.nsy.oms.elasticjob.store.auth.CheckStoreAuthIsExpireJob;
import com.nsy.oms.enums.inbound.InboundPlanStatusEnum;
import com.nsy.oms.mq.message.StaToBoxMessage;
import com.nsy.oms.repository.entity.inbound.InboundPlanEntity;
import com.nsy.oms.repository.entity.inbound.InboundPlanItemEntity;
import com.nsy.oms.repository.sql.mapper.inbound.InboundPlanEntityMapper;
import com.nsy.oms.utils.mp.LocationContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【inbound_plan(fba补货单)】的数据库操作Service实现
 * @createDate 2025-03-28 10:34:20
 */
@Slf4j
@Service
public class InboundPlanServiceImpl extends ServiceImpl<InboundPlanEntityMapper, InboundPlanEntity> implements InboundPlanService {

    @Resource
    private InboundPlanLogService inboundPlanLogService;
    @Resource
    private WmsApiService wmsApiService;
    @Resource
    private InboundPlanItemServiceImpl planItemEntityService;
    @Resource
    private CheckStoreAuthIsExpireJob checkStoreAuthIsExpireJob;

    @Override
    public IPage<InboundPlanEntity> getByPage(ShipmentPlanPageRequest request, List<Integer> storeIds) {
        return this.baseMapper.getInboundPlanPage(new Page<>(request.getPageIndex(), request.getPageSize()), request, storeIds);
    }

    @Override
    @Transactional
    public void updateStatusToBoxByMQ(StaToBoxMessage messageContent) {
        LocationContext.setLocation(messageContent.getLocation());
        InboundPlanEntity inboundPlanEntity = this.updateStatusByErpTid(messageContent.getOrderNo(), InboundPlanStatusEnum.BOXED.getStatus());
        //修改实际发货数量
        updatePlanItemActualShipmentQuantity(inboundPlanEntity);
        //钉钉通知
        this.sendFBTToBoxNotify(inboundPlanEntity);
        LocationContext.clear();
    }

    private void sendFBTToBoxNotify(InboundPlanEntity inboundPlanEntity) {
        this.sendToBoxNotify(inboundPlanEntity, "FBT");
    }

    private void sendToBoxNotify(InboundPlanEntity inboundPlanEntity, String type) {
        String template = "补货通知:补货单已装箱完成,类型:{},店铺名称:{},补货单号:{}";
        String msg = StrUtil.format(template, type, inboundPlanEntity.getStoreName(), inboundPlanEntity.getErpTid());
        checkStoreAuthIsExpireJob.sendMessageToDingTalk(inboundPlanEntity.getStoreId(), msg);
    }

    @Override
    public void updateStatusToShippedByMQ(StaToBoxMessage messageContent) {
        this.updateStatusByErpTid(messageContent.getOrderNo(), InboundPlanStatusEnum.SHIPPED.getStatus());
    }

    @Override
    public InboundPlanEntity updateStatusByErpTid(String erpTid, String toStatus) {
        LambdaQueryWrapper<InboundPlanEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(InboundPlanEntity::getErpTid, erpTid);
        InboundPlanEntity inboundPlanEntity = this.getOne(lambdaQueryWrapper);
        //设置
        inboundPlanLogService.saveFbaInboundShipmentPlanLog(inboundPlanEntity.getId(), "更新订单状态", StrUtil.format("从{}变更为:{}", inboundPlanEntity.getStatus(), toStatus), toStatus);
        inboundPlanEntity.setStatus(toStatus);
        this.updateById(inboundPlanEntity);
        return inboundPlanEntity;
    }

    private void updatePlanItemActualShipmentQuantity(InboundPlanEntity plan) {
        List<InboundPlanItemEntity> planItemEntities = planItemEntityService.getByPlanId(plan.getId());
        List<StaBoxInfoDto> planAllBoxList = wmsApiService.getStaBoxInfoDetailByErpTid(plan.getErpTid()).getStaBoxInfoDtoList();
        // 获取所有sellerSku的实际发货数量
        Map<String, Integer> skuActualQuantityMap = planAllBoxList.stream()
                .flatMap(box -> box.getStaBoxItemInfoDtoList().stream())
                .collect(Collectors.groupingBy(
                        StaBoxItemInfoDto::getSellerSku,
                        Collectors.summingInt(StaBoxItemInfoDto::getQuantity)
                ));
        // 如果需要更新到planItemEntities中
        planItemEntities.forEach(item -> {
            Integer actualQuantity = skuActualQuantityMap.getOrDefault(item.getSellerSku(), 0);
            item.setActualShipmentQuantity(actualQuantity);
        });
        planItemEntityService.updateBatchById(planItemEntities);
        //计算装箱数据
        plan.setBoxCount(planAllBoxList.size());
        this.updateById(plan);
    }


    @Override
    public List<InboundPlanEntity> getListByErpTid(List<String> erpTids) {
        return this.list(Wrappers.<InboundPlanEntity>lambdaQuery().in(InboundPlanEntity::getErpTid, erpTids));
    }

    @Override
    public List<InboundPlanEntity> getListByStatus(String status) {
        return this.list(Wrappers.<InboundPlanEntity>lambdaQuery().eq(InboundPlanEntity::getStatus, status));
    }
}




