package com.nsy.oms.business.domain.request.points;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @description:
 * @author: linCheng
 * @create: 2025-05-09 14:13
 **/
@Data
public class AffirmWaitConfirmRequest {

    @ApiModelProperty("退款请求ID")
    @NotEmpty(message = "退款请求ID不能为空")
    private List<Integer> refundIds;

}
