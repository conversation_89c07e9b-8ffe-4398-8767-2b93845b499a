package com.nsy.oms.business.service.platform.order;

import com.nsy.api.transfer.enums.PlatformTypeEnum;
import com.nsy.oms.business.service.platform.order.base.BaseCompleteOrderInfoService;
import org.springframework.stereotype.Service;

/**
 * 销售平台抓单重构--TikTok直邮
 * */
@Service
public class TikTokLocalShippingService extends BaseCompleteOrderInfoService {
    /**
     * 平台
     */
    @Override
    public PlatformTypeEnum platform() {
        return PlatformTypeEnum.TikTokLocalShipping;
    }

    /**
     * 按时间抓单 请求参数 每页的条数
     */
    @Override
    protected Integer buildPageSize() {
        return 30;
    }

    /**
     * 按时间抓单：抓单往前时间（分钟）
     */
    @Override
    protected Integer getStartTimeBefore() {
        return -30;
    }

    /**
     * 减去n天，设置当前抓单时间，让店铺重新抓前面的订单，抓漏单
     */
    @Override
    protected Integer getSubtractionOrderCatchDate() {
        return -10;
    }
}
