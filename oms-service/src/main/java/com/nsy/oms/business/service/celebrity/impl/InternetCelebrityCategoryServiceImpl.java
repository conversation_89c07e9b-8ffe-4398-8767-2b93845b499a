package com.nsy.oms.business.service.celebrity.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.oms.business.service.celebrity.InternetCelebrityCategoryService;
import com.nsy.oms.repository.entity.celebrity.InternetCelebrityCategoryEntity;
import com.nsy.oms.repository.sql.mapper.celebrity.InternetCelebrityCategoryMapper;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class InternetCelebrityCategoryServiceImpl 
    extends ServiceImpl<InternetCelebrityCategoryMapper, InternetCelebrityCategoryEntity>
    implements InternetCelebrityCategoryService {

    @Override
    public List<InternetCelebrityCategoryEntity> findByInternetCelebrityId(Integer internetCelebrityId) {
        return this.list(new QueryWrapper<InternetCelebrityCategoryEntity>().lambda()
                .eq(InternetCelebrityCategoryEntity::getInternetCelebrityId, internetCelebrityId));

    }

    @Override
    public List<InternetCelebrityCategoryEntity> listByCelebrityIds(List<Integer> celebrityIds) {
        return this.list(new LambdaQueryWrapper<InternetCelebrityCategoryEntity>()
                .in(InternetCelebrityCategoryEntity::getInternetCelebrityId, celebrityIds));
    }
}