package com.nsy.oms.business.service.points;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nsy.oms.business.domain.request.points.AffirmRefundItemRequest;
import com.nsy.oms.business.domain.request.points.B2bPointsRefundPageRequest;
import com.nsy.oms.business.domain.request.points.CancelRefundItemRequest;
import com.nsy.oms.business.domain.request.points.SyncB2bPointsRefund;
import com.nsy.oms.business.domain.response.base.PageResponse;
import com.nsy.oms.business.domain.response.points.B2bPointsRefundVO;
import com.nsy.oms.repository.entity.points.B2bPointsRefundEntity;

import java.util.List;

/**
 * <p>
 * B2B积分退款 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
public interface IB2bPointsRefundService extends IService<B2bPointsRefundEntity> {

    PageResponse<B2bPointsRefundVO> page(B2bPointsRefundPageRequest request);

    void syncB2bPointsRefund(List<SyncB2bPointsRefund> syncB2bPointsRefunds);

    void cancelWaitConfirm(List<Integer> refundIds);

    void affirmWaitConfirm(List<Integer> refundIds);

    void affirmRefundItem(AffirmRefundItemRequest request);

    void cancelRefundItem(CancelRefundItemRequest request);

    List<B2bPointsRefundEntity> getListByState(String state);

    B2bPointsRefundEntity getByRefundId(Integer refundId);
}
