package com.nsy.oms.business.service.amazon;

import com.nsy.api.oms.dto.request.amazon.CalculateGrossProfitRateRequest;
import com.nsy.api.oms.dto.response.bd.CalculateGrossProfitRateResponse;
import com.nsy.api.oms.dto.response.bd.IncreaseProfitResponse;
import com.nsy.oms.business.manage.bi.response.fact.FactAmazonProfitabilityAnalysisSkc;
import com.nsy.oms.business.manage.bi.response.fact.FactAmazonProfitabilityAnalysisSkcResponse;
import com.nsy.oms.business.manage.bi.response.fact.FactAmazonProfitabilityAnalysisSku;
import com.nsy.oms.business.manage.bi.response.fact.FactAmazonProfitabilityAnalysisSpu;
import com.nsy.oms.business.manage.bi.response.fact.FactAmazonProfitabilityAnalysisSpuResponse;
import com.nsy.oms.business.manage.pms.response.ProductSeasonInfo;
import com.nsy.oms.repository.dao.amazon.AmazonOrderProfitMonitoringLabelDao;
import com.nsy.oms.repository.dao.amazon.AmazonOrderProfitMonitoringSkcDao;
import com.nsy.oms.repository.dao.amazon.AmazonOrderProfitMonitoringSkuDao;
import com.nsy.oms.repository.dao.amazon.AmazonOrderProfitMonitoringSpuDao;
import com.nsy.oms.repository.entity.amzon.AmazonOrderProfitMonitoringLabelEntity;
import com.nsy.oms.repository.entity.amzon.AmazonOrderProfitMonitoringSkcEntity;
import com.nsy.oms.repository.entity.amzon.AmazonOrderProfitMonitoringSkuEntity;
import com.nsy.oms.repository.entity.amzon.AmazonOrderProfitMonitoringSpuEntity;
import com.nsy.oms.utils.BigDecimalUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-05-06
 */
@Service
public class AmazonOrderProfitMonitoringSyncService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AmazonOrderProfitMonitoringSyncService.class);
    @Autowired
    private AmazonOrderProfitMonitoringSpuDao spuDao;
    @Autowired
    private AmazonOrderProfitMonitoringSkcDao skcDao;
    @Autowired
    private AmazonOrderProfitMonitoringSkuDao skuDao;
    @Autowired
    private AmazonProfitCalculateService amazonProfitCalculateService;
    @Autowired
    private AmazonOrderProfitMonitoringLabelDao labelDao;

    public Date getMaxUpdateDate() {
        return spuDao.getMaxUpdateDate();
    }

    public void removeNotCurrentBatchSpu(String batchNo) {
        spuDao.removeNotCurrentBatchSpu(batchNo);
    }

    public void syncSpu(FactAmazonProfitabilityAnalysisSpu spu, Map<String, ProductSeasonInfo> seasonMap, String batchNo) {
        AmazonOrderProfitMonitoringSpuEntity spuEntity = Optional.ofNullable(spuDao
                .getBySpuAndStoreIdAndParentAsin(spu.getSpu(), spu.getStoreId(), spu.getParentAsin())).orElseGet(AmazonOrderProfitMonitoringSpuEntity::new);
        spuEntity.setSpu(spu.getSpu());
        ProductSeasonInfo seasonInfo = seasonMap.get(spu.getSpu());
        if (seasonInfo != null) {
            spuEntity.setSeason(seasonInfo.getSeason());
            spuEntity.setDevelopSeason(seasonInfo.getDevelopSeason());
        }
        spuEntity.setImageUrl(spu.getImageUrl());
        spuEntity.setMarketplaceCode(spu.getMarketplaceCode());
        spuEntity.setParentAsin(spu.getParentAsin());
        spuEntity.setFirstCategoryName(spu.getFirstCategoryName());
        spuEntity.setSecondCategoryName(spu.getSecondCategoryName());
        spuEntity.setThirdCategoryName(spu.getThirdCategoryName());
        spuEntity.setDepartment(spu.getDepartment());
        spuEntity.setStoreId(spu.getStoreId());
        spuEntity.setStoreName(spu.getStoreName());
        spuEntity.setLast7DateSaleQty(spu.getLast7DateSaleQty());
        spuEntity.setLast14DateSaleQty(spu.getLast14DateSaleQty());
        spuEntity.setLast30DateSaleQty(spu.getLast30DateSaleQty());
        spuEntity.setTotalInv(spu.getTotalInv());
        spuEntity.setQuanzhouInv(spu.getQuanzhouInv());
        spuEntity.setOverseasInv(spu.getOverseasInv());
        spuEntity.setOverseasInReservedInv(spu.getOverseasInReservedInv());
        spuEntity.setPurchaseInTransitInv(spu.getPurchaseInTransitInv());
        spuEntity.setReturnRate(spu.getReturnRate());
        spuEntity.setAdCostRate(spu.getAdCostRate());
        spuEntity.setSalePrice(spu.getSalePrice());
        spuEntity.setYourPrice(spu.getYourPrice());
        spuEntity.setMinOrderPriceIn24Hours(spu.getMinOrderPriceIn24Hours());
        spuEntity.setMaxOrderPriceIn24Hours(spu.getMaxOrderPriceIn24Hours());
        setSpuCalculateField(spu, spuEntity);
        spuEntity.setBatchNo(batchNo);
        spuEntity.setLocation(spu.getLocation());
        spuDao.saveOrUpdate(spuEntity);
    }

    private void setSpuCalculateField(FactAmazonProfitabilityAnalysisSpu spu, AmazonOrderProfitMonitoringSpuEntity spuEntity) {
        CalculateGrossProfitRateRequest calculateRequest = new CalculateGrossProfitRateRequest();
        calculateRequest.setCountryCode(spu.getMarketplaceCode());
        calculateRequest.setLength(spu.getLongestSide());
        calculateRequest.setWidth(spu.getMedianSide());
        calculateRequest.setHeight(spu.getShortestSide());
        calculateRequest.setWeight(spu.getWeight());
        calculateRequest.setProductCost(spu.getProductCost());
        calculateRequest.setReturnRate(spu.getReturnRate());
        calculateRequest.setReturnNotForSaleRate(spu.getCateReturnUnsallableRate());
        calculateRequest.setAdvertisingRate(spu.getAdCostRate());
        calculateRequest.setStandard(spu.getProductSizeTier());
        // 计算7天利润率
        calculateRequest.setPriceBeforeDiscount(getAveragePrice(spu.getLast7DateSaleIncome(), spu.getLast7DateSaleQty()));
        calculateRequest.setPriceAfterDiscount(calculateRequest.getPriceBeforeDiscount());
        CalculateGrossProfitRateResponse calculateResponseIn7Days = amazonProfitCalculateService.calculateGrossProfitRate(calculateRequest);
        spuEntity.setProfitRateIn7Days(calculateResponseIn7Days.getGrossProfitRate());
        // 计算14天利润率
        calculateRequest.setPriceBeforeDiscount(getAveragePrice(spu.getLast14DateSaleIncome(), spu.getLast14DateSaleQty()));
        calculateRequest.setPriceAfterDiscount(calculateRequest.getPriceBeforeDiscount());
        CalculateGrossProfitRateResponse calculateResponseIn14Days = amazonProfitCalculateService.calculateGrossProfitRate(calculateRequest);
        spuEntity.setProfitRateIn14Days(calculateResponseIn14Days.getGrossProfitRate());
        // 计算30天利润率
        calculateRequest.setPriceBeforeDiscount(getAveragePrice(spu.getLast30DateSaleIncome(), spu.getLast30DateSaleQty()));
        calculateRequest.setPriceAfterDiscount(calculateRequest.getPriceBeforeDiscount());
        CalculateGrossProfitRateResponse calculateResponseIn30Days = amazonProfitCalculateService.calculateGrossProfitRate(calculateRequest);
        spuEntity.setProfitRateIn30Days(calculateResponseIn30Days.getGrossProfitRate());
        // 计算近24H最低利润率
        BigDecimal minOrderPriceIn24Hours = spu.getMinOrderPriceIn24Hours();
        calculateRequest.setPriceBeforeDiscount(minOrderPriceIn24Hours);
        calculateRequest.setPriceAfterDiscount(minOrderPriceIn24Hours);
        CalculateGrossProfitRateResponse minProfitRateIn24Hours = amazonProfitCalculateService.calculateGrossProfitRate(calculateRequest);
        spuEntity.setMinProfitRateIn24Hours(minProfitRateIn24Hours.getGrossProfitRate());
        // 计算近24H最高利润率
        calculateRequest.setPriceBeforeDiscount(spu.getMaxOrderPriceIn24Hours());
        calculateRequest.setPriceAfterDiscount(spu.getMaxOrderPriceIn24Hours());
        CalculateGrossProfitRateResponse maxProfitRateIn24Hours = amazonProfitCalculateService.calculateGrossProfitRate(calculateRequest);
        spuEntity.setMaxProfitRateIn24Hours(maxProfitRateIn24Hours.getGrossProfitRate());
        // 当前档：取最低成交价算出来的档位
        BigDecimal currentLevelCommissionRate;
        BigDecimal reducedLogisticsCost = BigDecimal.ZERO;
        int level;
        if (spuEntity.getSalePrice().compareTo(new BigDecimal("9.99")) <= 0 || minOrderPriceIn24Hours.compareTo(new BigDecimal("9.99")) <= 0) {
            currentLevelCommissionRate = new BigDecimal("0.05");
            reducedLogisticsCost = new BigDecimal("0.77");
            level = 1;
        } else if (minOrderPriceIn24Hours.compareTo(BigDecimal.TEN) >= 0 && minOrderPriceIn24Hours.compareTo(new BigDecimal("14.99")) <= 0) {
            currentLevelCommissionRate = new BigDecimal("0.05");
            level = 2;
        } else if (minOrderPriceIn24Hours.compareTo(new BigDecimal("15")) >= 0 && minOrderPriceIn24Hours.compareTo(new BigDecimal("19.99")) <= 0) {
            currentLevelCommissionRate = new BigDecimal("0.1");
            level = 3;
        } else {
            currentLevelCommissionRate = new BigDecimal("0.17");
            level = 4;
        }
        spuEntity.setCurrentLevelCommissionRate(currentLevelCommissionRate);
        spuEntity.setCurrentLevelBalancePrice(amazonProfitCalculateService.calculateBalancePrice(calculateRequest, reducedLogisticsCost, currentLevelCommissionRate, level));
    }

    private BigDecimal getAveragePrice(BigDecimal saleIncome, Integer saleQty) {
        try {
            return saleIncome.divide(new BigDecimal(saleQty), 4, RoundingMode.HALF_UP);
        } catch (Exception e) {
            LOGGER.error("error on getAveragePrice: {}", e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    public void syncSkcAndSku(FactAmazonProfitabilityAnalysisSpuResponse response) {
        FactAmazonProfitabilityAnalysisSpu spu = response.getSpu();
        // 该spu+storeId下所有skc
        List<AmazonOrderProfitMonitoringSkcEntity> skcEntities = skcDao.listBySpuAndStoreIdAndParentAsin(spu.getSpu(), spu.getStoreId(), spu.getParentAsin());
        Map<String, AmazonOrderProfitMonitoringSkcEntity> skcEntityMap = skcEntities
                .stream().collect(Collectors.toMap(AmazonOrderProfitMonitoringSkcEntity::getSkc, Function.identity(), (k1, k2) -> k1));
        // 该spu+storeId下所有sku
        List<AmazonOrderProfitMonitoringSkuEntity> skuEntities = skuDao.listBySpuAndStoreIdAndParentAsin(spu.getSpu(), spu.getStoreId(), spu.getParentAsin());
        Map<String, AmazonOrderProfitMonitoringSkuEntity> skuEntityMap = skuEntities
                .stream().collect(Collectors.toMap(AmazonOrderProfitMonitoringSkuEntity::getSku, Function.identity(), (k1, k2) -> k1));
        // 该spu+storeId下所有标签
        labelDao.removeBySpuAndStoreIdAndParentAsin(spu.getSpu(), spu.getStoreId(), spu.getParentAsin());

        List<String> skcList = new ArrayList<>();
        List<String> skuList = new ArrayList<>();
        for (FactAmazonProfitabilityAnalysisSkcResponse skcResponse : response.getSkcList()) {
            FactAmazonProfitabilityAnalysisSkc skc = skcResponse.getSkc();
            skcList.add(skc.getSkc());
            AmazonOrderProfitMonitoringSkcEntity skcEntity = skcEntityMap.getOrDefault(skc.getSkc(), new AmazonOrderProfitMonitoringSkcEntity());
            buildSkcEntity(skc, skcEntity);
            setSkcCalculateField(skc, skcEntity);
            skcDao.saveOrUpdate(skcEntity);
            // 是否利润亏损：近24H利润率<0需要打标：利润亏损
            if (skcEntity.getMinProfitRateIn24Hours().compareTo(BigDecimal.ZERO) < 0 || skcEntity.getMaxProfitRateIn24Hours().compareTo(BigDecimal.ZERO) < 0) {
                saveLabel(skcEntity, "利润亏损");
            }
            syncSkcLabel(skcEntity, skc.getLabel());

            skuDao.saveOrUpdateBatch(skcResponse.getSkuList().stream().map(sku -> {
                skuList.add(sku.getSku());
                AmazonOrderProfitMonitoringSkuEntity skuEntity = skuEntityMap.getOrDefault(sku.getSku(), new AmazonOrderProfitMonitoringSkuEntity());
                buildSkuEntity(sku, skuEntity);
                return skuEntity;
            }).collect(Collectors.toList()));
        }
        // 移除这次拉取的数据不存在的skc和sku
        skcDao.removeByIds(skcEntities.stream().filter(e -> !skcList.contains(e.getSkc())).map(AmazonOrderProfitMonitoringSkcEntity::getId).collect(Collectors.toList()));
        skuDao.removeByIds(skuEntities.stream().filter(e -> !skuList.contains(e.getSku())).map(AmazonOrderProfitMonitoringSkuEntity::getId).collect(Collectors.toList()));
    }

    private void syncSkcLabel(AmazonOrderProfitMonitoringSkcEntity skcEntity, String label) {
        if (StringUtils.isBlank(label)) {
            return;
        }
        labelDao.saveBatch(Arrays.stream(label.split(",")).map(labelName -> {
            AmazonOrderProfitMonitoringLabelEntity entity = new AmazonOrderProfitMonitoringLabelEntity();
            entity.setSpu(skcEntity.getSpu());
            entity.setSkc(skcEntity.getSkc());
            entity.setStoreId(skcEntity.getStoreId());
            entity.setStoreName(skcEntity.getStoreName());
            entity.setMarketplaceCode(skcEntity.getMarketplaceCode());
            entity.setParentAsin(skcEntity.getParentAsin());
            entity.setLabelType(1);
            entity.setLabelName(labelName);
            entity.setLocation(skcEntity.getLocation());
            return entity;
        }).collect(Collectors.toList()));
    }

    private void saveLabel(AmazonOrderProfitMonitoringSkcEntity skcEntity, String labelName) {
        AmazonOrderProfitMonitoringLabelEntity entity = new AmazonOrderProfitMonitoringLabelEntity();
        entity.setSpu(skcEntity.getSpu());
        entity.setSkc(skcEntity.getSkc());
        entity.setStoreId(skcEntity.getStoreId());
        entity.setStoreName(skcEntity.getStoreName());
        entity.setMarketplaceCode(skcEntity.getMarketplaceCode());
        entity.setParentAsin(skcEntity.getParentAsin());
        entity.setLabelType(2);
        entity.setLabelName(labelName);
        entity.setLocation(skcEntity.getLocation());
        labelDao.save(entity);
    }

    private void buildSkcEntity(FactAmazonProfitabilityAnalysisSkc skc, AmazonOrderProfitMonitoringSkcEntity skcEntity) {
        skcEntity.setSpu(skc.getSpu());
        skcEntity.setSkc(skc.getSkc());
        skcEntity.setStoreId(skc.getStoreId());
        skcEntity.setStoreName(skc.getStoreName());
        skcEntity.setMarketplaceCode(skc.getMarketplaceCode());
        skcEntity.setParentAsin(skc.getParentAsin());
        skcEntity.setLast7DateSaleQty(skc.getLast7DateSaleQty());
        skcEntity.setLast14DateSaleQty(skc.getLast14DateSaleQty());
        skcEntity.setLast30DateSaleQty(skc.getLast30DateSaleQty());
        skcEntity.setTotalInv(skc.getTotalInv());
        skcEntity.setQuanzhouInv(skc.getQuanzhouInv());
        skcEntity.setOverseasInv(skc.getOverseasInv());
        skcEntity.setOverseasInReservedInv(skc.getOverseasInReservedInv());
        skcEntity.setPurchaseInTransitInv(skc.getPurchaseInTransitInv());
        skcEntity.setAdCostRate(skc.getAdCostRate());
        skcEntity.setReturnRate(skc.getReturnRate());
        skcEntity.setSalePrice(skc.getSalePrice());
        skcEntity.setYourPrice(skc.getYourPrice());
        skcEntity.setMinOrderPriceIn24Hours(skc.getMinOrderPriceIn24Hours());
        skcEntity.setMaxOrderPriceIn24Hours(skc.getMaxOrderPriceIn24Hours());
        skcEntity.setLocation(skc.getLocation());
    }

    private void buildSkuEntity(FactAmazonProfitabilityAnalysisSku sku, AmazonOrderProfitMonitoringSkuEntity skuEntity) {
        skuEntity.setSpu(sku.getSpu());
        skuEntity.setSkc(sku.getSkc());
        skuEntity.setSku(sku.getSku());
        skuEntity.setStoreId(sku.getStoreId());
        skuEntity.setStoreName(sku.getStoreName());
        skuEntity.setMarketplaceCode(sku.getMarketplaceCode());
        skuEntity.setParentAsin(sku.getParentAsin());
        skuEntity.setLongestSide(sku.getLongestSide());
        skuEntity.setMedianSide(sku.getMedianSide());
        skuEntity.setShortestSide(sku.getShortestSide());
        skuEntity.setWeight(sku.getWeight());
        skuEntity.setProductCost(sku.getProductCost());
        skuEntity.setReturnRate(sku.getReturnRate());
        skuEntity.setCateReturnUnsallableRate(sku.getCateReturnUnsallableRate());
        skuEntity.setAdCostRate(sku.getAdCostRate());
        skuEntity.setProductSizeTier(sku.getProductSizeTier());
        skuEntity.setLast7DateSaleQty(sku.getLast7DateSaleQty());
        skuEntity.setLast7DateSaleIncome(sku.getLast7DateSaleIncome());
        skuEntity.setLast14DateSaleQty(sku.getLast14DateSaleQty());
        skuEntity.setLast14DateSaleIncome(sku.getLast14DateSaleIncome());
        skuEntity.setLast30DateSaleQty(sku.getLast30DateSaleQty());
        skuEntity.setLast30DateSaleIncome(sku.getLast30DateSaleIncome());
        skuEntity.setTotalInv(sku.getTotalInv());
        skuEntity.setQuanzhouInv(sku.getQuanzhouInv());
        skuEntity.setOverseasInv(sku.getOverseasInv());
        skuEntity.setOverseasInReservedInv(sku.getOverseasInReservedInv());
        skuEntity.setPurchaseInTransitInv(sku.getPurchaseInTransitInv());
        skuEntity.setLast7DateInvSalesRate(sku.getLast7DateInvSalesRate());
        skuEntity.setLast14DateInvSalesRate(sku.getLast14DateInvSalesRate());
        skuEntity.setLast30DateInvSalesRate(sku.getLast30DateInvSalesRate());
        skuEntity.setInvAge0To90(sku.getInvAge0To90());
        skuEntity.setInvAge91To180(sku.getInvAge91To180());
        skuEntity.setInvAge181To270(sku.getInvAge181To270());
        skuEntity.setInvAge271To365(sku.getInvAge271To365());
        skuEntity.setInvAgeGt365(sku.getInvAgeGt365());
        skuEntity.setInvAgeTotal(sku.getInvAgeTotal());
        skuEntity.setSalePrice(sku.getSalePrice());
        skuEntity.setYourPrice(sku.getYourPrice());
        skuEntity.setMinOrderPriceIn24Hours(sku.getMinOrderPriceIn24Hours());
        skuEntity.setMaxOrderPriceIn24Hours(sku.getMaxOrderPriceIn24Hours());
        skuEntity.setLocation(sku.getLocation());
    }

    private void setSkcCalculateField(FactAmazonProfitabilityAnalysisSkc skc, AmazonOrderProfitMonitoringSkcEntity skcEntity) {
        CalculateGrossProfitRateRequest calculateRequest = new CalculateGrossProfitRateRequest();
        calculateRequest.setCountryCode(skc.getMarketplaceCode());
        calculateRequest.setLength(skc.getLongestSide());
        calculateRequest.setWidth(skc.getMedianSide());
        calculateRequest.setHeight(skc.getShortestSide());
        calculateRequest.setWeight(skc.getWeight());
        calculateRequest.setProductCost(skc.getProductCost());
        calculateRequest.setReturnRate(skc.getReturnRate());
        calculateRequest.setReturnNotForSaleRate(skc.getCateReturnUnsallableRate());
        calculateRequest.setAdvertisingRate(skc.getAdCostRate());
        calculateRequest.setStandard(skc.getProductSizeTier());
        // 计算7天利润率
        calculateRequest.setPriceBeforeDiscount(getAveragePrice(skc.getLast7DateSaleIncome(), skc.getLast7DateSaleQty()));
        calculateRequest.setPriceAfterDiscount(calculateRequest.getPriceBeforeDiscount());
        CalculateGrossProfitRateResponse calculateResponseIn7Days = amazonProfitCalculateService.calculateGrossProfitRate(calculateRequest);
        skcEntity.setProfitRateIn7Days(calculateResponseIn7Days.getGrossProfitRate());
        // 计算14天利润率
        calculateRequest.setPriceBeforeDiscount(getAveragePrice(skc.getLast14DateSaleIncome(), skc.getLast14DateSaleQty()));
        calculateRequest.setPriceAfterDiscount(calculateRequest.getPriceBeforeDiscount());
        CalculateGrossProfitRateResponse calculateResponseIn14Days = amazonProfitCalculateService.calculateGrossProfitRate(calculateRequest);
        skcEntity.setProfitRateIn14Days(calculateResponseIn14Days.getGrossProfitRate());
        // 计算30天利润率
        calculateRequest.setPriceBeforeDiscount(getAveragePrice(skc.getLast30DateSaleIncome(), skc.getLast30DateSaleQty()));
        calculateRequest.setPriceAfterDiscount(calculateRequest.getPriceBeforeDiscount());
        CalculateGrossProfitRateResponse calculateResponseIn30Days = amazonProfitCalculateService.calculateGrossProfitRate(calculateRequest);
        skcEntity.setProfitRateIn30Days(calculateResponseIn30Days.getGrossProfitRate());
        // 计算近24H最低利润率
        calculateRequest.setPriceBeforeDiscount(skc.getMinOrderPriceIn24Hours());
        calculateRequest.setPriceAfterDiscount(skc.getMinOrderPriceIn24Hours());
        CalculateGrossProfitRateResponse minProfitRateIn24Hours = amazonProfitCalculateService.calculateGrossProfitRate(calculateRequest);
        skcEntity.setMinProfitRateIn24Hours(minProfitRateIn24Hours.getGrossProfitRate());

        skcEntity.setFbaDeliveryCost(minProfitRateIn24Hours.getFbaDeliveryCost());
        skcEntity.setFbaReturnCost(minProfitRateIn24Hours.getFbaReturnCost());
        skcEntity.setFbaFirstPostageCost(minProfitRateIn24Hours.getFbaFirstPostageCost());
        skcEntity.setStorageCost(minProfitRateIn24Hours.getStorageCost());
        skcEntity.setDisposalCost(minProfitRateIn24Hours.getDisposalCost());
        skcEntity.setHandlingCost(minProfitRateIn24Hours.getHandlingCost());
        skcEntity.setAdvertisingCost(minProfitRateIn24Hours.getAdvertisingCost());
        skcEntity.setCouponCost(minProfitRateIn24Hours.getCouponCost());
        skcEntity.setReturnPlatformDeductsCost(minProfitRateIn24Hours.getReturnPlatformDeductsCost());

        // 计算近24H最高利润率
        calculateRequest.setPriceBeforeDiscount(skc.getMaxOrderPriceIn24Hours());
        calculateRequest.setPriceAfterDiscount(skc.getMaxOrderPriceIn24Hours());
        CalculateGrossProfitRateResponse maxProfitRateIn24Hours = amazonProfitCalculateService.calculateGrossProfitRate(calculateRequest);
        skcEntity.setMaxProfitRateIn24Hours(maxProfitRateIn24Hours.getGrossProfitRate());
        // 当前档
        calculateRequest.setPriceBeforeDiscount(skc.getMinOrderPriceIn24Hours());
        calculateRequest.setPriceAfterDiscount(skc.getMinOrderPriceIn24Hours());
        calculateRequest.setDiscountRate(BigDecimal.ZERO);
        // 当前档的利润率
        setSkcCurrentLevel(skcEntity, calculateRequest);
    }

    /**
     * 如果活动价小于9.99，当前档就是第一档
     * 否则就用24h最低的成交价去命中各个档次区间
     */
    private void setSkcCurrentLevel(AmazonOrderProfitMonitoringSkcEntity skcEntity, CalculateGrossProfitRateRequest calculateRequest) {
        // 当前档：取最低成交价算出来的档位
        BigDecimal minOrderPriceIn24Hours = skcEntity.getMinOrderPriceIn24Hours();
        BigDecimal currentLevelCommissionRate;
        BigDecimal reducedLogisticsCost = BigDecimal.ZERO;
        int level;
        if (skcEntity.getSalePrice().compareTo(new BigDecimal("9.99")) <= 0 || minOrderPriceIn24Hours.compareTo(new BigDecimal("9.99")) <= 0) {
            currentLevelCommissionRate = new BigDecimal("0.05");
            reducedLogisticsCost = new BigDecimal("0.77");
            level = 1;
        } else if (minOrderPriceIn24Hours.compareTo(BigDecimal.TEN) >= 0 && minOrderPriceIn24Hours.compareTo(new BigDecimal("14.99")) <= 0) {
            currentLevelCommissionRate = new BigDecimal("0.05");
            level = 2;
        } else if (minOrderPriceIn24Hours.compareTo(new BigDecimal("15")) >= 0 && minOrderPriceIn24Hours.compareTo(new BigDecimal("19.99")) <= 0) {
            currentLevelCommissionRate = new BigDecimal("0.1");
            level = 3;
        } else {
            currentLevelCommissionRate = new BigDecimal("0.17");
            level = 4;
        }

        skcEntity.setCurrentLevelCommissionRate(currentLevelCommissionRate);
        skcEntity.setCurrentLevelBalancePrice(amazonProfitCalculateService.calculateBalancePrice(calculateRequest, reducedLogisticsCost, currentLevelCommissionRate, level));
        // 当前档的利润率
        CalculateGrossProfitRateResponse currentLevelGrossProfitRate = amazonProfitCalculateService.calculateGrossProfitRate(calculateRequest, reducedLogisticsCost, currentLevelCommissionRate);
        // 降档利润增加额：取最高的那一档
        // 变体列表的降档利润增加额需排除同档和更高档的增加额，另外负利润也不用展示
        List<IncreaseProfitResponse> increaseProfitResponseList = amazonProfitCalculateService.calculateIncreaseProfit(calculateRequest, currentLevelGrossProfitRate);

        List<IncreaseProfitResponse> list = increaseProfitResponseList.stream()
                .filter(e -> e.getLevel() < level && BigDecimalUtil.isValid(e.getIncreaseProfit())).collect(Collectors.toList());
        if (!list.isEmpty()) {
            // 建议降档：存在利润率比近24H利润率高且售价比当前售价低
            saveLabel(skcEntity, "建议降档");
            list.forEach(e -> {
                switch (e.getLevel()) {
                    case 1:
                        skcEntity.setLevelOneProfitRate(e.getProfitRate());
                        skcEntity.setLevelOneDownshiftIncreaseProfit(e.getIncreaseProfit());
                        break;
                    case 2:
                        skcEntity.setLevelTwoProfitRate(e.getProfitRate());
                        skcEntity.setLevelTwoDownshiftIncreaseProfit(e.getIncreaseProfit());
                        break;
                    case 3:
                        skcEntity.setLevelThreeProfitRate(e.getProfitRate());
                        skcEntity.setLevelThreeDownshiftIncreaseProfit(e.getIncreaseProfit());
                        break;
                    case 4:
                        skcEntity.setLevelFourProfitRate(e.getProfitRate());
                        skcEntity.setLevelFourDownshiftIncreaseProfit(e.getIncreaseProfit());
                        break;
                    default:
                        break;
                }
            });
        }
    }
}
