package com.nsy.oms.business.manage.activity;

import com.nsy.api.core.apicore.util.JSONUtils;
import com.nsy.oms.business.domain.request.activiti.ActiveProcessInstancesRequest;
import com.nsy.oms.business.domain.request.activiti.AssignTaskRequest;
import com.nsy.oms.business.domain.request.activiti.BatchCompleteRequest;
import com.nsy.oms.business.domain.request.activiti.BatchJumpRequest;
import com.nsy.oms.business.domain.request.activiti.BatchStartProcessRequest;
import com.nsy.oms.business.domain.request.activiti.DeleteTaskRequest;
import com.nsy.oms.business.domain.request.activiti.MyTaskQueryRequest;
import com.nsy.oms.business.domain.request.activiti.UpdateTaskAssigneeRequest;
import com.nsy.oms.business.domain.request.activiti.UpdateTaskInfo;
import com.nsy.oms.business.domain.request.activiti.UpdateTaskRequest;
import com.nsy.oms.business.domain.response.activit.TaskQueryResponse;
import com.nsy.oms.business.domain.response.activit.TaskResponse;
import com.nsy.oms.business.manage.user.response.SysUserInfo;
import com.nsy.oms.utils.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@Service
public class ActivitiApiService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ActivitiApiService.class);

    @Value("${nsy.service.url.activiti}")
    private String activitiServiceUrl;
    @Inject
    private RestTemplate restTemplate;


    public void startProcessInstanceByKey(BatchStartProcessRequest startProcessRequest) {
        String uri = String.format("%s/batch-start-process-instance-by-key", activitiServiceUrl);
        LOGGER.info("start-process-instance-by-key:{}", JSONUtils.toJSON(startProcessRequest));
        this.restTemplate.postForLocation(uri, startProcessRequest);
    }


    public void completeTask(BatchCompleteRequest req) {
        String uri = String.format("%s/batch-complete", activitiServiceUrl);
        req.setAdmin(true);
        LOGGER.info("batch-complete:{}", JSONUtils.toJSON(req));
        this.restTemplate.postForLocation(uri, req);
    }

    public void activeProcessInstances(Set<String> businessKeys) {
        String uri = String.format("%s/process-instances/active-by-businessKey", activitiServiceUrl);
        ActiveProcessInstancesRequest request = new ActiveProcessInstancesRequest();
        request.setBusinessKeys(businessKeys);
        LOGGER.info("active-by-businessKey:{}", JSONUtils.toJSON(request));
        this.restTemplate.postForLocation(uri, request);
    }

    public List<TaskResponse> queryTasksByBusinessKeys(MyTaskQueryRequest request) {
        if (CollectionUtils.isEmpty(request.getBusinessKeys())) {
            return new ArrayList<>();
        }
        String uri = String.format("%s/query-tasks-by-businessKeys", activitiServiceUrl);
        LOGGER.info("query-tasks-by-businessKeys request:{}", JsonMapper.toJson(request));
        ResponseEntity<TaskQueryResponse> responseEntity = this.restTemplate.postForEntity(uri, request, TaskQueryResponse.class);
        return responseEntity.getBody().getTasks();
    }


    public void updateTasks(List<UpdateTaskInfo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        UpdateTaskRequest request = new UpdateTaskRequest();
        request.setList(list);
        String uri = String.format("%s/update-tasks", activitiServiceUrl);
        LOGGER.info("update-tasks request:{}", JsonMapper.toJson(request));
        this.restTemplate.postForLocation(uri, request);
    }

    /**
     * 查询工作流目前所处节点信息
     *
     * @param bussinessKey
     * @return
     */
    public List<TaskResponse> tasksByBusinessKey(String bussinessKey) {
        String uri = String.format("%s/tasks-by-business-key/%s", activitiServiceUrl, bussinessKey);
        LOGGER.info("tasks-by-business-key request:{}", JsonMapper.toJson(bussinessKey));
        ResponseEntity<TaskQueryResponse> responseEntity = this.restTemplate.getForEntity(uri, TaskQueryResponse.class);
        LOGGER.info("tasks-by-business-key responseEntity:{}", JsonMapper.toJson(responseEntity));
        return responseEntity.getBody().getTasks();
    }

    /**
     * 结束工作流
     *
     * @param request
     */
    public void deleteTask(DeleteTaskRequest request) {
        String uri = String.format("%s/delete-task", activitiServiceUrl);
        LOGGER.info("delete-task request:{}", JsonMapper.toJson(request));
        this.restTemplate.postForLocation(uri, request);
    }

    /**
     * 跳转到工作流指定节点
     */
    public void jumpToTargetNode(BatchJumpRequest request) {
        String uri = String.format("%s/jump-to-target-node", activitiServiceUrl);
        LOGGER.info("jump-to-target-node request:{}", JsonMapper.toJson(request));
        this.restTemplate.postForLocation(uri, request);
    }

    /**
     * 获取工作的指派人员
     *
     * @return
     */
    public List<SysUserInfo> getConfigCandidateUser(String taskId) {
        String uri = String.format("%s/task/candidate?taskId=%s", activitiServiceUrl, taskId);
        try {
            ResponseEntity<String> responseEntity = this.restTemplate.getForEntity(uri, String.class);
            LOGGER.debug(JsonMapper.toJson(responseEntity));
            if (Objects.nonNull(responseEntity.getBody())) {
                return JSONUtils.fromJSONArray(responseEntity.getBody(), SysUserInfo.class);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        LOGGER.info("获取[{}]流程候选人失败，无法获取流程实例候选人", taskId);
        return Collections.emptyList();
    }


    /**
     * 任务指派
     *
     * @param request
     */
    public void assignTask(AssignTaskRequest request) {
        String uri = String.format("%s/assign-task", activitiServiceUrl);
        LOGGER.info("assign-task request:{}", JsonMapper.toJson(request));
        this.restTemplate.put(uri, request);
    }

    public void updateTaskAssign(UpdateTaskAssigneeRequest request) {
        String uri = String.format("%s/update-task-assign", activitiServiceUrl);
        LOGGER.info("assign-task request:{}", JsonMapper.toJson(request));
        this.restTemplate.postForLocation(uri, request);
    }
}
