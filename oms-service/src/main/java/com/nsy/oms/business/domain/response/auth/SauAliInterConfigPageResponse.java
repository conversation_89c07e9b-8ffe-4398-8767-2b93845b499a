package com.nsy.oms.business.domain.response.auth;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

@ApiModel(value = "SauAliInterConfigPageResponse", description = "阿里国际授权分页列表")
public class SauAliInterConfigPageResponse implements Serializable {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID", name = "id")
    private Integer id;

    /**
     * 店铺名称
     */
    @ApiModelProperty(value = "店铺名称", name = "storeName")
    private String storeName;

    /**
     * 店铺ID
     */
    @ApiModelProperty(value = "店铺ID", name = "storeId")
    private Integer storeId;

    /**
     * 市场
     */
    @ApiModelProperty(value = "市场", name = "market")
    private String market;

    /**
     * 授权状态 0:关闭  1:开启
     */
    @ApiModelProperty(value = "授权状态 0:关闭  1:开启", name = "grantAuthStatus")
    private Integer grantAuthStatus;

    /**
     * 授权日期
     */
    @ApiModelProperty(value = "授权日期", name = "grantDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date grantDate;


    /**
     * 有效期
     */
    @ApiModelProperty(value = "有效期", name = "validityDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date validityDate;


    /**
     * 开始抓单时间
     */
    @ApiModelProperty(value = "开始抓单时间", name = "catchDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date catchDate;


    /**
     * 当前抓单时间
     */
    @ApiModelProperty(value = "当前抓单时间", name = "currentCatchDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date currentCatchDate;


    /**
     * token重新刷新时间
     */
    @ApiModelProperty(value = "token重新刷新时间", name = "refreshTokenTimeout")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date refreshTokenTimeout;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", name = "createDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", name = "createBy")
    private String createBy;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public Integer getGrantAuthStatus() {
        return grantAuthStatus;
    }

    public void setGrantAuthStatus(Integer grantAuthStatus) {
        this.grantAuthStatus = grantAuthStatus;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getGrantDate() {
        return grantDate;
    }

    public void setGrantDate(Date grantDate) {
        this.grantDate = grantDate;
    }

    public Date getValidityDate() {
        return validityDate;
    }

    public void setValidityDate(Date validityDate) {
        this.validityDate = validityDate;
    }

    public Date getCatchDate() {
        return catchDate;
    }

    public void setCatchDate(Date catchDate) {
        this.catchDate = catchDate;
    }

    public Date getCurrentCatchDate() {
        return currentCatchDate;
    }

    public void setCurrentCatchDate(Date currentCatchDate) {
        this.currentCatchDate = currentCatchDate;
    }

    public Date getRefreshTokenTimeout() {
        return refreshTokenTimeout;
    }

    public void setRefreshTokenTimeout(Date refreshTokenTimeout) {
        this.refreshTokenTimeout = refreshTokenTimeout;
    }
}