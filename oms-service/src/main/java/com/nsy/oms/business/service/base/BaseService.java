package com.nsy.oms.business.service.base;

import com.nsy.oms.enums.sa.PlatformEnum;
import com.nsy.oms.repository.entity.auth.SauAmazonConfigEntity;

/**
 * <AUTHOR>
 * @date 2022/12/20 14:58
 */
public interface BaseService {
    /**
     * 发送信息
     *
     * @param storeId
     * @param type
     */
    void sendQueue(Integer storeId, String type);

    /**
     * 发送信息
     *
     * @param storeId
     * @param status  状态 0 关闭 1 启用
     */
    void sendAuthQueue(Integer storeId, Integer status, String type);

    /**
     * 判断是否同个平台
     *
     * @param platformId
     * @param platformEnums
     * @return
     */
    void validateIsSamePlatform(Integer platformId, PlatformEnum[] platformEnums);

    /**
     * 校验店铺是否新增授权
     *
     * @param storeId
     */
    void validateStoreIsAddAuth(Integer storeId);

    /**
     * 判断是否有查看销售账号数据权限
     *
     * @return
     */
    boolean isCanViewDataOfSaleAccount();

    /**
     * 判断是否有查看账号主体数据权限
     *
     * @return
     */
    boolean isCanViewDataOfAccountSubject();

    void sendAuthToAmazon(SauAmazonConfigEntity sauAmazonConfigEntity);
}
