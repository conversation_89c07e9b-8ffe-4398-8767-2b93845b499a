package com.nsy.oms.business.domain.request.rule;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description:
 * @author: linCheng
 * @create: 2024-11-01 14:41
 **/
@Data
public class ConfigSkcStockShareRequest {
    @ApiModelProperty("仓库id")
    private Integer spaceId;

    @ApiModelProperty("skc")
    private String skc;

    @ApiModelProperty("skcs")
    private List<String> skcs;

    @ApiModelProperty("部门名称")
    private String departmentName;

    @ApiModelProperty("部门名称")
    private List<String> departmentNames;

    @NotNull(message = "【店铺id】不能为空")
    @ApiModelProperty("店铺id")
    private Integer storeId;

    @ApiModelProperty("店铺ids")
    private List<Integer> storeIds;

    @ApiModelProperty("店铺ids")
    private List<Integer> checkStoreIds;

    @ApiModelProperty("是否查询部门级别配置")
    private Integer queryDepartmentFlag;

    public ConfigSkcStockShareRequest() {
    }

    public ConfigSkcStockShareRequest(List<String> skcs, Integer storeId) {
        this.skcs = skcs;
        this.storeId = storeId;
    }

    public ConfigSkcStockShareRequest(List<String> skcs, String departmentName, Integer queryDepartmentFlag) {
        this.skcs = skcs;
        this.departmentName = departmentName;
        this.queryDepartmentFlag = queryDepartmentFlag;
    }

    public ConfigSkcStockShareRequest(Integer spaceId, String skc, String departmentName, List<Integer> checkStoreIds) {
        this.spaceId = spaceId;
        this.skc = skc;
        this.departmentName = departmentName;
        this.checkStoreIds = checkStoreIds;
    }
}
