package com.nsy.oms.business.manage.search;

import com.alibaba.fastjson.JSON;
import com.nsy.api.core.apicore.base.BaseListResponse;
import com.nsy.api.core.apicore.util.JSONUtils;
import com.nsy.oms.business.manage.search.domain.ProductIndex;
import com.nsy.oms.business.manage.search.request.OmsProductSearchRequest;
import com.nsy.oms.business.manage.search.request.PublishProductStoreListRequest;
import com.nsy.oms.business.manage.search.request.SyncSheinRecommendSaleSkcRequest;
import com.nsy.oms.business.manage.search.response.GetPublishProductStoreResponse;
import com.nsy.oms.business.service.sa.SaStoreWebsiteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.inject.Inject;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class SearchApiService {

    @Inject
    private RestTemplate restTemplate;

    @Value("${nsy.service.url.search}")
    private String searchServiceUrl;
    @Inject
    private SaStoreWebsiteService saStoreWebsiteService;


    public void syncSheinRecommendByIds(List<Integer> sheinRecommendSaleSkcIds) {
        if (CollectionUtils.isEmpty(sheinRecommendSaleSkcIds)) return;
        SyncSheinRecommendSaleSkcRequest request = new SyncSheinRecommendSaleSkcRequest();
        request.setSheinRecommendSaleSkcIds(sheinRecommendSaleSkcIds);
        try {
            String uri = String.format("%s/oms-publish-shein-recommend-skc/syncByIds", searchServiceUrl);
            this.restTemplate.postForEntity(uri, request, String.class);
        } catch (Exception e) {
            log.error("SearchApiService.syncSheinRecommendByIds.error", e);
        }
    }


    public void deleteSheinRecommendByIds(List<Integer> sheinRecommendSaleSkcIds) {
        SyncSheinRecommendSaleSkcRequest request = new SyncSheinRecommendSaleSkcRequest();
        request.setSheinRecommendSaleSkcIds(sheinRecommendSaleSkcIds);
        try {
            String uri = String.format("%s/oms-publish-shein-recommend-skc/del", searchServiceUrl);
            HttpEntity<SyncSheinRecommendSaleSkcRequest> requestEntity = new HttpEntity<>(request);
            this.restTemplate.exchange(uri, HttpMethod.DELETE, requestEntity, Void.class);
        } catch (Exception e) {
            log.error("SearchApiService.deleteSheinRecommendByIds.error", e);
        }

    }

    public ProductIndex getProductIndex(String spu) {
        OmsProductSearchRequest request = new OmsProductSearchRequest();
        request.setProductSku(spu);
        request.setIsPrecise(1);
        log.info("spu {}", spu);
        List<ProductIndex> productIndexList = this.productSpecList(request);
        log.info("productIndexList {}", JSON.toJSONString(productIndexList));
        if (CollectionUtils.isEmpty(productIndexList)) {
            return null;
        }
        return productIndexList.get(0);
    }

    public ProductIndex getProductIndexBySku(String sku) {
        OmsProductSearchRequest request = new OmsProductSearchRequest();
        request.setSku(sku);
        request.setIsPrecise(1);
        log.info("sku {}", sku);
        List<ProductIndex> productIndexList = this.productSpecList(request);
        log.info("productIndexList {}", JSON.toJSONString(productIndexList));
        if (CollectionUtils.isEmpty(productIndexList)) {
            return new ProductIndex();
        }
        return productIndexList.get(0);
    }

    public ProductIndex getProductIndexBySkc(String skc) {
        OmsProductSearchRequest request = new OmsProductSearchRequest();
        request.setSkc(skc);
        request.setIsPrecise(1);
        log.info("skc {}", skc);
        List<ProductIndex> productIndexList = this.productSpecList(request);
        log.info("productIndexList {}", JSON.toJSONString(productIndexList));
        if (CollectionUtils.isEmpty(productIndexList)) {
            return null;
        }
        return productIndexList.get(0);
    }

    public List<ProductIndex> productSpecList(OmsProductSearchRequest getProductRequest) {
        String uri = String.format("%s/oms-product/search-for-client", searchServiceUrl);
        try {
            ResponseEntity<BaseListResponse> responseEntity = this.restTemplate.postForEntity(uri, getProductRequest, BaseListResponse.class);
            BaseListResponse<ProductIndex> baseListResponse = responseEntity.getBody();
            if (baseListResponse != null && !CollectionUtils.isEmpty(baseListResponse.getContent())) {
                return JSON.parseArray(JSONUtils.toJSON(baseListResponse.getContent()), ProductIndex.class);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public GetPublishProductStoreResponse queryPublishProductStore(Integer storeId, String parentCode) {
        PublishProductStoreListRequest request = new PublishProductStoreListRequest();
        request.setWebsiteParentCode(parentCode);
        request.setWebsiteIds(saStoreWebsiteService.getWebsiteIdList(storeId));
        log.info("[rpc请求] SearchApiService productList request: {} timestamp: {}", JSON.toJSONString(request), LocalDateTime.now());
        String uri = String.format("%s/oms-publish-product-store/search", searchServiceUrl);
        ResponseEntity<GetPublishProductStoreResponse> responseEntity = this.restTemplate.postForEntity(uri, request, GetPublishProductStoreResponse.class);
        log.info("[rpc响应] SearchApiService productList responseEntity: {} timestamp: {}", JSON.toJSONString(responseEntity), LocalDateTime.now());
        return responseEntity.getBody();
    }


}
