package com.nsy.oms.business.manage.omspublish;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.nsy.oms.business.domain.request.base.IdsRequest;
import com.nsy.oms.business.domain.response.sa.SaStoreWebsiteResponse;
import com.nsy.oms.business.manage.omspublish.request.PublishProductSpecRequest;
import com.nsy.oms.business.manage.omspublish.request.QueryAmazonImageRequest;
import com.nsy.oms.business.manage.omspublish.request.QueryAmazonParentCodeRequest;
import com.nsy.oms.business.manage.omspublish.request.QueryAmazonSkcByParentCodeStoreRequest;
import com.nsy.oms.business.manage.omspublish.request.SyncPublishProductSpecRequest;
import com.nsy.oms.business.manage.omspublish.response.PublishProductSpec;
import com.nsy.oms.business.manage.omspublish.response.PublishProductSpecInfo;
import com.nsy.oms.business.manage.omspublish.response.SelectWebsiteModel;
import com.nsy.oms.business.manage.user.request.shared.SelectModel;
import com.nsy.oms.business.service.sa.SaStoreWebsiteService;
import com.nsy.oms.constants.StringConstant;
import com.nsy.oms.utils.JsonMapper;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import javax.inject.Inject;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OmsPublishApiService {

    @Inject
    private RestTemplate restTemplate;

    @Value("${nsy.service.url.oms-publish}")
    private String searchServiceUrl;

    @Inject
    SaStoreWebsiteService saStoreWebsiteService;

    public List<String> getByPlatformAndColorSkuList(List<String> colorSkuList, List<Integer> websiteIds, Boolean isForcedPush, String platform) {
        SyncPublishProductSpecRequest request = new SyncPublishProductSpecRequest();
        request.setColorSkuList(colorSkuList);
        request.setWebsiteIds(websiteIds);
        request.setIsForcedPush(isForcedPush);
        request.setPlatform(platform);
        try {
            String uri = String.format("%s/oms-publish-product-skc", searchServiceUrl);
            return JsonMapper.jsonStringToObjectArray(this.restTemplate.postForEntity(uri, request, String.class).getBody(), String.class);
        } catch (Exception e) {
            log.error("OmsPublishApiService.getByPlatformAndColorSkuList.error", e);
        }
        return Lists.newArrayList();
    }

    public List<String> getByPlatformAndColorSkuListWithNoSupply(List<String> colorSkuList, List<Integer> websiteIds, String platform) {
        SyncPublishProductSpecRequest request = new SyncPublishProductSpecRequest();
        request.setColorSkuList(colorSkuList);
        request.setWebsiteIds(websiteIds);
        request.setPlatform(platform);
        try {
            String uri = String.format("%s/oms-publish-product-skc/no-supply", searchServiceUrl);
            return JsonMapper.jsonStringToObjectArray(this.restTemplate.postForEntity(uri, request, String.class).getBody(), String.class);
        } catch (Exception e) {
            log.error("OmsPublishApiService.getByPlatformAndColorSkuList.error", e);
        }
        return Lists.newArrayList();
    }

    public List<SelectModel> getSpuPublishData(Integer productId, String platform) {
        try {
            String uri = String.format("%s/oms-publish-product-skc/%s/%s", searchServiceUrl, productId, platform);
            return JsonMapper.jsonStringToObjectArray(this.restTemplate.getForObject(uri, String.class), SelectModel.class);
        } catch (Exception e) {
            log.error("OmsPublishApiService.getByPlatformAndColorSkuList.error", e);
        }
        return Lists.newArrayList();
    }


    public List<SelectWebsiteModel> getErpWebsiteConfigByConfigWebsiteIds(List<Integer> websiteIds) {
        IdsRequest request = new IdsRequest();
        request.setIds(websiteIds);
        try {
            String uri = String.format("%s/erp-website-by-ids", searchServiceUrl);
            return JsonMapper.jsonStringToObjectArray(this.restTemplate.postForEntity(uri, request, String.class).getBody(), SelectWebsiteModel.class);
        } catch (Exception e) {
            log.error("OmsPublishApiService.getErpWebsiteConfigByConfigWebsiteIds.error", e);
        }
        return Lists.newArrayList();
    }

    public List<String> queryAmazonParentCodeByLikeSkcAndStore(String skc, List<Integer> storeIdList) {
        if (CollectionUtils.isEmpty(storeIdList)) return Lists.newArrayList();
        List<Integer> websiteIdList = getWebsiteIdList(storeIdList);
        if (CollectionUtils.isEmpty(websiteIdList)) return Lists.newArrayList();
        QueryAmazonParentCodeRequest request = QueryAmazonParentCodeRequest.builder().skc(skc).websiteIdList(websiteIdList).build();
        log.info("[rpc请求] OmsPublishApiService queryAmazonParentCodeByLikeSkcAndStore request: {} timestamp: {}", JSON.toJSONString(request), LocalDateTime.now());
        String uri = String.format("%s/publish-product-store-amazon/query-parent-code", searchServiceUrl);
        try {
            ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);
            log.info("[rpc响应] OmsPublishApiService queryAmazonParentCodeByLikeSkcAndStore response: {} timestamp: {}", JSON.toJSONString(response), LocalDateTime.now());
            return JsonMapper.jsonStringToObjectArray(response.getBody(), String.class);
        } catch (Exception e) {
            log.error("查询刊登parent code 失败", e);
        }
        return Lists.newArrayList();
    }

    @NotNull
    private List<Integer> getWebsiteIdList(List<Integer> storeIdList) {
        List<Integer> websiteIdList = new ArrayList<>();
        storeIdList.forEach(storeId -> {
            List<Integer> ids = saStoreWebsiteService.getWebsiteList(storeId).stream().map(SaStoreWebsiteResponse::getWebsiteId).map(Integer::valueOf).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(ids)) {
                websiteIdList.addAll(ids);
            }
        });
        return websiteIdList;
    }

    public List<String> querySkcByParentCodeAndStore(String parentCode, Integer storeId) {
        List<Integer> websiteIdList = getWebsiteIdList(Collections.singletonList(storeId));
        if (CollectionUtils.isEmpty(websiteIdList)) return Lists.newArrayList();
        QueryAmazonSkcByParentCodeStoreRequest request = QueryAmazonSkcByParentCodeStoreRequest.builder().parentCode(parentCode).websiteId(websiteIdList.get(0)).build();
        log.info("[rpc请求] OmsPublishApiService queryAmazonParentCodeByLikeSkcAndStore request: {} timestamp: {}", JSON.toJSONString(request), LocalDateTime.now());
        String uri = String.format("%s/publish-product-store-amazon/query-skc-by-parent-code", searchServiceUrl);
        try {
            ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);
            log.info("[rpc响应] OmsPublishApiService queryAmazonParentCodeByLikeSkcAndStore response: {} timestamp: {}", JSON.toJSONString(response), LocalDateTime.now());
            return JsonMapper.jsonStringToObjectArray(response.getBody(), String.class);
        } catch (Exception e) {
            log.error("查询刊登parent code 失败", e);
        }
        return Lists.newArrayList();
    }

    public String queryImageBySkcAndWebsiteIds(String skc, List<Integer> websiteIds) {
        if (CollectionUtils.isEmpty(websiteIds)) return StringConstant.EMPTY;
        log.info("[rpc请求] OmsPublishApiService queryImageBySkcAndWebsiteIds skc: {} websiteIds: {} timestamp: {}", skc, websiteIds, LocalDateTime.now());
        String uri = String.format("%s/publish-product-store-amazon/query-image", searchServiceUrl);
        try {
            ResponseEntity<String> response = this.restTemplate.postForEntity(uri, QueryAmazonImageRequest.builder().websiteIds(websiteIds).skc(skc).build(), String.class);
            log.info("[rpc响应] OmsPublishApiService queryImageBySkcAndWebsiteIds response: {} timestamp: {}", JSON.toJSONString(response), LocalDateTime.now());
            return response.getBody();
        } catch (Exception e) {
            log.error("查询刊登parent code 失败", e);
        }
        return StringConstant.EMPTY;
    }

    public List<PublishProductSpec> getPublishProducts(String platform, Integer websiteId, List<String> websiteItemSkus) {
        PublishProductSpecRequest request = new PublishProductSpecRequest(websiteId, platform, websiteItemSkus);
        try {
            String uri = String.format("%s/publish-product-spec/list", searchServiceUrl);
            return JsonMapper.jsonStringToObjectArray(this.restTemplate.postForEntity(uri, request, String.class).getBody(), PublishProductSpec.class);
        } catch (Exception e) {
            log.error("OmsPublishApiService.getPublishProducts.error", e);
        }
        return Lists.newArrayList();
    }

    public List<PublishProductSpecInfo> getPublishSpecInfos(List<PublishProductSpecInfo> specInfos) {
        if (CollUtil.isEmpty(specInfos)) return Lists.newArrayList();
        try {
            String uri = String.format("%s/publish-product-spec/info-list", searchServiceUrl);
            return JsonMapper.jsonStringToObjectArray(this.restTemplate.postForEntity(uri, specInfos, String.class).getBody(), PublishProductSpecInfo.class);
        } catch (Exception e) {
            log.error("OmsPublishApiService.getPublishSpecInfos.error", e);
        }
        return Lists.newArrayList();
    }

    public PublishProductSpecInfo getPublishSpecInfoBySellerProductId(PublishProductSpecInfo specInfo) {
        try {
            String uri = String.format("%s/publish-product-spec/info-by-seller-product-id", searchServiceUrl);
            return this.restTemplate.postForEntity(uri, specInfo, PublishProductSpecInfo.class).getBody();
        } catch (Exception e) {
            log.error("OmsPublishApiService.getPublishSpecInfo.error", e);
        }
        return null;
    }

}
