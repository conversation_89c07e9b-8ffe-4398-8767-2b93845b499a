package com.nsy.oms.business.service.bd;


import com.baomidou.mybatisplus.extension.service.IService;
import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.oms.business.domain.request.bd.brand.BdBrandCommonPageRequest;
import com.nsy.oms.business.domain.request.bd.brand.BdBrandQueryRequest;
import com.nsy.oms.business.domain.request.bd.brand.BdBrandSaveRequest;
import com.nsy.oms.business.domain.response.bd.brand.BdBrand;
import com.nsy.oms.business.manage.user.request.shared.SelectModel;
import com.nsy.oms.repository.entity.bd.BdBrandEntity;

import java.util.Collection;
import java.util.List;

/**
 * 品牌配置表
 *
 * <AUTHOR>
 * @date 2023-09-21
 */
public interface BdBrandService extends IService<BdBrandEntity> {
    /**
     * 获取品牌下拉框
     *
     * @return List<SelectModel>
     */
    List<SelectModel> getBrandSelect();

    List<SelectModel> getBrandNameSelect();

    List<SelectModel> getBrandSelectIgnoreTenant();

    /**
     * 根据前端传的location过滤品牌配置下拉框数据
     */
    List<SelectModel> getBrandSelectByLocation(String location);

    /**
     * 获取仓库下拉框
     *
     * @return List<SelectModel>
     */
    List<SelectModel> getSpaceSelect();

    List<SelectModel> commonSpaceSelect();

    /**
     * 获取品牌分页数据
     */
    PageResponse<BdBrand> page(BdBrandCommonPageRequest request);

    /**
     * 获取品牌分页数据(忽略区域)
     */
    PageResponse<BdBrand> pageIgnoreTenant(BdBrandCommonPageRequest request);

    /**
     * 新增、编辑
     */
    Integer saveOrUpdate(BdBrandSaveRequest request);

    /**
     * 根据品牌名称查询
     */
    List<BdBrandEntity> listByBrandNames(Collection<String> brandNames, String location);

    BdBrandEntity getByName(String name, String location);

    BdBrandEntity getByStoreId(String name);

    List<BdBrandEntity> listByRequest(BdBrandQueryRequest request);

    /**
     * 新增或删除品牌店铺的时候，更新品牌的店铺数
     */
    void updateAllocateStoreNum(Integer brandId);

    void updateAllocateProductNum(Integer brandId);

    BdBrand getBrandByBrandCompanyName(String brandCompanyName);

    BdBrand getBrandByBrandName(String brandName);

    List<SelectModel> getBrand();

    List<SelectModel> getAllBrandSelect();

    List<SelectModel> getAllBrandSelectIgnoreTenant();

    List<BdBrand> getBrandSpaceByDepartment(String department);
}
