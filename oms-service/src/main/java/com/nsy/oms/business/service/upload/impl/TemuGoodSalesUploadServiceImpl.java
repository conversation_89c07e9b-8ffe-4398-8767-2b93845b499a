package com.nsy.oms.business.service.upload.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.nsy.oms.business.domain.request.upload.UploadRequest;
import com.nsy.oms.business.domain.response.upload.UploadResponse;
import com.nsy.oms.business.domain.upload.TemuGoodSalesImport;
import com.nsy.oms.business.service.sa.SaStoreService;
import com.nsy.oms.business.service.temu.TemuSaleDataService;
import com.nsy.oms.business.service.upload.IUploadService;
import com.nsy.oms.constants.upload.QuartzUploadQueueTypeEnum;
import com.nsy.oms.repository.entity.sa.SaStoreEntity;
import com.nsy.oms.utils.JsonMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TemuGoodSalesUploadServiceImpl implements IUploadService {
    @Autowired
    private Validator validator;
    @Autowired
    private TemuSaleDataService service;
    @Autowired
    private SaStoreService storeService;

    @Override
    public QuartzUploadQueueTypeEnum uploadType() {
        return QuartzUploadQueueTypeEnum.TEMU_GOOD_SALES_IMPORT;
    }

    @Override
    public UploadResponse processUploadData(UploadRequest request) {
        log.info("TemuGoodSalesUploadServiceImpl上传数据{}", request);
        UploadResponse uploadResponse = new UploadResponse();
        if (!StringUtils.hasText(request.getDataJsonStr())) {
            return uploadResponse;
        }
        List<TemuGoodSalesImport> importList = JsonMapper.jsonStringToObjectArray(request.getDataJsonStr(), TemuGoodSalesImport.class);
        if (CollectionUtils.isEmpty(importList)) {
            return uploadResponse;
        }
        List<TemuGoodSalesImport> errorList = new ArrayList<>();
        List<TemuGoodSalesImport> list = new ArrayList<>();
        validData(request, importList, errorList, list);
        if (!list.isEmpty()) {
            service.importDataByList(list, errorList);
        }
        if (!errorList.isEmpty()) {
            uploadResponse.setDataJsonStr(JsonMapper.toJson(errorList));
            return uploadResponse;
        }
        return new UploadResponse();
    }

    private void validData(UploadRequest request, List<TemuGoodSalesImport> importList, List<TemuGoodSalesImport> errorList, List<TemuGoodSalesImport> list) {
        importList.forEach(importItem -> {
            //校验规则
            Set<ConstraintViolation<TemuGoodSalesImport>> validateErrorList = validator.validate(importItem);
            if (!validateErrorList.isEmpty()) {
                importItem.setErrorMsg(validateErrorList.stream().map(ConstraintViolation::getMessage).collect(Collectors.toList()).toString());
                errorList.add(importItem);
                return;
            }
            //判断店铺是否数据库里
            Integer storeId = importItem.getStoreId();
            SaStoreEntity storeEntity = storeService.getById(storeId);
            if (ObjectUtil.isEmpty(storeEntity)) {
                importItem.setErrorMsg(StrUtil.format("店铺Id[{}]不存在", storeId));
                errorList.add(importItem);
                return;
            }
            importItem.setStoreName(storeEntity.getErpStoreName());
            //判断商品是否存在,并且将商品skuId存入
//            String specSku = importItem.getTemuSkuExtCode();
//            ProductSpecResponse specSkuEntity = pmsApiService.getProductSpecBySpecSku(specSku);
//            if (specSkuEntity == null) {
//                importItem.setErrorMsg(StrUtil.format("sku[{}]不存在", specSku));
//                errorList.add(importItem);
//                return;
//            }
            importItem.setCreateBy(request.getCreateBy());
            list.add(importItem);
        });
    }
}
