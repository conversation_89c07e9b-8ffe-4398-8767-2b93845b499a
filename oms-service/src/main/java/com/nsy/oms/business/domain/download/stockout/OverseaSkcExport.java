package com.nsy.oms.business.domain.download.stockout;

import com.nsy.api.core.apicore.annotation.NsyExcelProperty;
import java.math.BigDecimal;


/**
 * <AUTHOR>
 */
public class OverseaSkcExport {
    @NsyExcelProperty("skc")
    private String skc;

    @NsyExcelProperty("标签")
    private String label;

    @NsyExcelProperty("图片地址")
    private String imageUrl;

    @NsyExcelProperty("商品标题")
    private String title;

    @NsyExcelProperty("sku")
    private String sku;

    @NsyExcelProperty("尺码")
    private String sizeCode;

    /**
     * 海外仓可用库存
     */
    @NsyExcelProperty("总可用库存")
    private Integer totalAvailableStock;

    /**
     * 海外仓在途库存
     */
    @NsyExcelProperty("总在途库存")
    private Integer totalOnTheWayStock;

    /**
     * 7日出库量
     */
    @NsyExcelProperty("总7日出库量")
    private Integer totalStockOutQtyIn7;

    /**
     * 14日出库量
     */
    @NsyExcelProperty("总14日出库量")
    private Integer totalStockOutQtyIn14;

    /**
     * 30日出库量
     */
    @NsyExcelProperty("总30日出库量")
    private Integer totalStockOutQtyIn30;

    /**
     * 7日均出库量
     */
    @NsyExcelProperty("总7日均出库量")
    private BigDecimal totalDailyStockOutQtyIn7;

    /**
     * 14日均出库量
     */
    @NsyExcelProperty("总14日均出库量")
    private BigDecimal totalDailyStockOutQtyIn14;

    /**
     * 30日均出库量
     */
    @NsyExcelProperty("总30日均出库量")
    private BigDecimal totalDailyStockOutQtyIn30;

    /**
     * 可售天数
     */
    @NsyExcelProperty("总可售天数")
    private Integer totalAvailableDays;

    /**
     * 仓库名称
     */
    @NsyExcelProperty("仓库名称")
    private String spaceName;

    /**
     * 海外仓可用库存
     */
    @NsyExcelProperty("可用库存")
    private Integer availableStock;

    /**
     * 海外仓在途库存
     */
    @NsyExcelProperty("在途库存")
    private Integer onTheWayStock;

    /**
     * 7日出库量
     */
    @NsyExcelProperty("7日出库量")
    private Integer stockOutQtyIn7;

    /**
     * 14日出库量
     */
    @NsyExcelProperty("14日出库量")
    private Integer stockOutQtyIn14;

    /**
     * 30日出库量
     */
    @NsyExcelProperty("30日出库量")
    private Integer stockOutQtyIn30;

    /**
     * 7日均出库量
     */
    @NsyExcelProperty("7日均出库量")
    private BigDecimal dailyStockOutQtyIn7;

    /**
     * 14日均出库量
     */
    @NsyExcelProperty("14日均出库量")
    private BigDecimal dailyStockOutQtyIn14;

    /**
     * 30日均出库量
     */
    @NsyExcelProperty("30日均出库量")
    private BigDecimal dailyStockOutQtyIn30;

    /**
     * 可售天数
     */
    @NsyExcelProperty("可售天数")
    private Integer availableDays;


    public String getSkc() {
        return skc;
    }

    public void setSkc(String skc) {
        this.skc = skc;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getSizeCode() {
        return sizeCode;
    }

    public void setSizeCode(String sizeCode) {
        this.sizeCode = sizeCode;
    }

    public Integer getTotalAvailableStock() {
        return totalAvailableStock;
    }

    public void setTotalAvailableStock(Integer totalAvailableStock) {
        this.totalAvailableStock = totalAvailableStock;
    }

    public Integer getTotalOnTheWayStock() {
        return totalOnTheWayStock;
    }

    public void setTotalOnTheWayStock(Integer totalOnTheWayStock) {
        this.totalOnTheWayStock = totalOnTheWayStock;
    }

    public Integer getTotalStockOutQtyIn7() {
        return totalStockOutQtyIn7;
    }

    public void setTotalStockOutQtyIn7(Integer totalStockOutQtyIn7) {
        this.totalStockOutQtyIn7 = totalStockOutQtyIn7;
    }

    public Integer getTotalStockOutQtyIn14() {
        return totalStockOutQtyIn14;
    }

    public void setTotalStockOutQtyIn14(Integer totalStockOutQtyIn14) {
        this.totalStockOutQtyIn14 = totalStockOutQtyIn14;
    }

    public Integer getTotalStockOutQtyIn30() {
        return totalStockOutQtyIn30;
    }

    public void setTotalStockOutQtyIn30(Integer totalStockOutQtyIn30) {
        this.totalStockOutQtyIn30 = totalStockOutQtyIn30;
    }

    public BigDecimal getTotalDailyStockOutQtyIn7() {
        return totalDailyStockOutQtyIn7;
    }

    public void setTotalDailyStockOutQtyIn7(BigDecimal totalDailyStockOutQtyIn7) {
        this.totalDailyStockOutQtyIn7 = totalDailyStockOutQtyIn7;
    }

    public BigDecimal getTotalDailyStockOutQtyIn14() {
        return totalDailyStockOutQtyIn14;
    }

    public void setTotalDailyStockOutQtyIn14(BigDecimal totalDailyStockOutQtyIn14) {
        this.totalDailyStockOutQtyIn14 = totalDailyStockOutQtyIn14;
    }

    public BigDecimal getTotalDailyStockOutQtyIn30() {
        return totalDailyStockOutQtyIn30;
    }

    public void setTotalDailyStockOutQtyIn30(BigDecimal totalDailyStockOutQtyIn30) {
        this.totalDailyStockOutQtyIn30 = totalDailyStockOutQtyIn30;
    }

    public Integer getTotalAvailableDays() {
        return totalAvailableDays;
    }

    public void setTotalAvailableDays(Integer totalAvailableDays) {
        this.totalAvailableDays = totalAvailableDays;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public Integer getAvailableStock() {
        return availableStock;
    }

    public void setAvailableStock(Integer availableStock) {
        this.availableStock = availableStock;
    }

    public Integer getOnTheWayStock() {
        return onTheWayStock;
    }

    public void setOnTheWayStock(Integer onTheWayStock) {
        this.onTheWayStock = onTheWayStock;
    }

    public Integer getStockOutQtyIn7() {
        return stockOutQtyIn7;
    }

    public void setStockOutQtyIn7(Integer stockOutQtyIn7) {
        this.stockOutQtyIn7 = stockOutQtyIn7;
    }

    public Integer getStockOutQtyIn14() {
        return stockOutQtyIn14;
    }

    public void setStockOutQtyIn14(Integer stockOutQtyIn14) {
        this.stockOutQtyIn14 = stockOutQtyIn14;
    }

    public Integer getStockOutQtyIn30() {
        return stockOutQtyIn30;
    }

    public void setStockOutQtyIn30(Integer stockOutQtyIn30) {
        this.stockOutQtyIn30 = stockOutQtyIn30;
    }

    public BigDecimal getDailyStockOutQtyIn7() {
        return dailyStockOutQtyIn7;
    }

    public void setDailyStockOutQtyIn7(BigDecimal dailyStockOutQtyIn7) {
        this.dailyStockOutQtyIn7 = dailyStockOutQtyIn7;
    }

    public BigDecimal getDailyStockOutQtyIn14() {
        return dailyStockOutQtyIn14;
    }

    public void setDailyStockOutQtyIn14(BigDecimal dailyStockOutQtyIn14) {
        this.dailyStockOutQtyIn14 = dailyStockOutQtyIn14;
    }

    public BigDecimal getDailyStockOutQtyIn30() {
        return dailyStockOutQtyIn30;
    }

    public void setDailyStockOutQtyIn30(BigDecimal dailyStockOutQtyIn30) {
        this.dailyStockOutQtyIn30 = dailyStockOutQtyIn30;
    }

    public Integer getAvailableDays() {
        return availableDays;
    }

    public void setAvailableDays(Integer availableDays) {
        this.availableDays = availableDays;
    }
}




