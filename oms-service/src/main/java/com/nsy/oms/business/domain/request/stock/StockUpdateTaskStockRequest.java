package com.nsy.oms.business.domain.request.stock;

public class StockUpdateTaskStockRequest {

    // 1  变动模式    2 覆盖模式
    private Integer inventoryType = 2;

    //仓库Id
    private Integer spaceId;

    // 是否融合标库存
    private Integer isPreLabel = 0;
    /**
     * Sku
     */
    private String sku;

    /**
     * 盘点数量
     */
    private Integer qty;

    /**
     * 盘点原因
     */
    private String remark;

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public Integer getInventoryType() {
        return inventoryType;
    }

    public void setInventoryType(Integer inventoryType) {
        this.inventoryType = inventoryType;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getQty() {
        return qty;
    }

    public void setQty(Integer qty) {
        this.qty = qty;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getIsPreLabel() {
        return isPreLabel;
    }

    public void setIsPreLabel(Integer isPreLabel) {
        this.isPreLabel = isPreLabel;
    }
}
