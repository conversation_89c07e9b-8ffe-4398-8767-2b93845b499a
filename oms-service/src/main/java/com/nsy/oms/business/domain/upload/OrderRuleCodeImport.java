package com.nsy.oms.business.domain.upload;

import com.alibaba.excel.annotation.ExcelProperty;


public class OrderRuleCodeImport extends BaseErrorTemplate {
    @ExcelProperty("编码")
    private String code;
    @ExcelProperty("店铺名称")
    private String storeName;
    @ExcelProperty("指定海外仓")
    private String spaceName;
    @ExcelProperty("是否允许发货")
    private Integer isDelivery;
    @ExcelProperty("保留库存类型")
    private Integer reserveInventoryType;
    @ExcelProperty("库存")
    private Integer reserveInventory;

    private Integer storeId;
    private Integer spaceId;

    public Integer getIsDelivery() {
        return isDelivery;
    }

    public void setIsDelivery(Integer isDelivery) {
        this.isDelivery = isDelivery;
    }

    public Integer getReserveInventoryType() {
        return reserveInventoryType;
    }

    public void setReserveInventoryType(Integer reserveInventoryType) {
        this.reserveInventoryType = reserveInventoryType;
    }

    public Integer getReserveInventory() {
        return reserveInventory;
    }

    public void setReserveInventory(Integer reserveInventory) {
        this.reserveInventory = reserveInventory;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }
}
