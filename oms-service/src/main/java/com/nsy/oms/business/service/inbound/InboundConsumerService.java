package com.nsy.oms.business.service.inbound;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.nsy.oms.business.manage.tms.TmsApiService;
import com.nsy.oms.business.manage.tms.response.TmsLogisticsChannelsConfig;
import com.nsy.oms.enums.inbound.InboundPlanStatusEnum;
import com.nsy.oms.enums.inbound.ShipmentStatusEnum;
import com.nsy.oms.mq.message.FbtOrderShipShipmentInfoDto;
import com.nsy.oms.mq.message.OrderShippedMessage;
import com.nsy.oms.repository.entity.inbound.InboundPlanEntity;
import com.nsy.oms.repository.entity.inbound.InboundShipmentEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: linCheng
 * @create: 2025-04-07 17:57
 **/
@Service
@Slf4j
public class InboundConsumerService {
    @Autowired
    private InboundPlanService inboundPlanService;

    @Autowired
    private InboundShipmentService inboundShipmentService;
    @Resource
    private TmsApiService tmsApiService;

    public void orderShip(OrderShippedMessage messageContent) {

        List<InboundPlanEntity> inboundPlanEntities = inboundPlanService.getListByErpTid(messageContent.getOrderNoList());
        if (CollectionUtils.isEmpty(inboundPlanEntities)) {
            log.info("InboundConsumerService.orderShip.inboundPlanEntities.isEmpty,orderNoList:{}", JSON.toJSONString(messageContent.getOrderNoList()));
            return;
        }
        inboundPlanEntities.forEach(inboundPlanEntity -> {
            inboundPlanEntity.setStatus(InboundPlanStatusEnum.SHIPPED.status());
            inboundPlanEntity.setUpdateDate(new Date());
            inboundPlanEntity.setUpdateBy("orderShip");
        });
        inboundPlanService.updateBatchById(inboundPlanEntities);


        List<Integer> planIds = inboundPlanEntities.stream().map(InboundPlanEntity::getId).collect(Collectors.toList());
        List<InboundShipmentEntity> inboundShipmentEntities = inboundShipmentService.getByPlanIds(planIds);
        if (CollectionUtils.isEmpty(inboundShipmentEntities)) {
            log.info("InboundConsumerService.orderShip.inboundShipmentEntities.isEmpty,planIds:{}", JSON.toJSONString(planIds));
            return;
        }
        //根据shipmentId转为map
        Map<String, FbtOrderShipShipmentInfoDto> shipShipmentInfoDtoMap = messageContent.getFbtOrderShipShipmentInfoDtoList().stream().collect(Collectors.toMap(FbtOrderShipShipmentInfoDto::getFbtShipmentId, Function.identity()));
        inboundShipmentEntities.forEach(inboundShipmentEntity -> {
            inboundShipmentEntity.setStatus(ShipmentStatusEnum.SHIPPED.status());
            FbtOrderShipShipmentInfoDto shipmentInfoDto = shipShipmentInfoDtoMap.get(inboundShipmentEntity.getShipmentId());
            Date deliveryTime = shipmentInfoDto.getDeliveryTime();
            inboundShipmentEntity.setDeliveryTime(deliveryTime);
            inboundShipmentEntity.setLogisticsName(CollUtil.isNotEmpty(shipmentInfoDto.getLogisticsCompanyList()) ? shipmentInfoDto.getLogisticsCompanyList().get(0) : "");
            inboundShipmentEntity.setLogisticsNumber(CollUtil.isNotEmpty(shipmentInfoDto.getLogisticsNoList()) ? shipmentInfoDto.getLogisticsNoList().get(0) : "");
            inboundShipmentEntity.setUpdateDate(new Date());
            inboundShipmentEntity.setUpdateBy("orderShip");
            //计算预计签收时间
            try {
                TmsLogisticsChannelsConfig channelConfigResponse = tmsApiService.getByLogisticsChannelNames(inboundShipmentEntity.getLocation(), inboundShipmentEntity.getLogisticsName());
                int timeline = channelConfigResponse.getChannelTimeLineList().stream().findAny().get().getTimeline();
                inboundShipmentEntity.setEstimateReceiveDate(DateUtil.offsetDay(deliveryTime, timeline));
            } catch (Exception e) {
                log.error("获取预计签收时间失败", e);
            }
        });
        inboundShipmentService.updateBatchById(inboundShipmentEntities);
    }

}
