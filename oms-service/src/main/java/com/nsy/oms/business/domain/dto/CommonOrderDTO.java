package com.nsy.oms.business.domain.dto;


import java.math.BigDecimal;

public class CommonOrderDTO {
    /**
     * 店铺ID
     */
    private Integer storeId;

    /**
     * 订单ID
     */
    private Integer orderId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 运费
     */
    private BigDecimal postFee;
    /**
     * 商品总金额
     */
    private BigDecimal totalFee;
    /**
     * 商品折扣(优惠金额，是该商品单件优惠*数量。如果有平台折扣，需要按金额比例折算进来)
     */
    private BigDecimal discountFee;
    /**
     * 订单折扣（订单优惠 ：满减，打折，抵用券等）
     */
    private BigDecimal discountMoney;

    /**
     * iossNumer
     */
    private String iossNumer;

    /**
     * 地区
     */
    private String location;

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public BigDecimal getPostFee() {
        return postFee;
    }

    public void setPostFee(BigDecimal postFee) {
        this.postFee = postFee;
    }

    public BigDecimal getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(BigDecimal totalFee) {
        this.totalFee = totalFee;
    }

    public BigDecimal getDiscountFee() {
        return discountFee;
    }

    public void setDiscountFee(BigDecimal discountFee) {
        this.discountFee = discountFee;
    }

    public BigDecimal getDiscountMoney() {
        return discountMoney;
    }

    public void setDiscountMoney(BigDecimal discountMoney) {
        this.discountMoney = discountMoney;
    }

    public String getIossNumer() {
        return iossNumer;
    }

    public void setIossNumer(String iossNumer) {
        this.iossNumer = iossNumer;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }
}
