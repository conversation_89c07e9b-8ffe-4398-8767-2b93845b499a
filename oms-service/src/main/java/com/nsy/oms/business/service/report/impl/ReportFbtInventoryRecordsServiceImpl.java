package com.nsy.oms.business.service.report.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.transfer.domain.common.TiktokAuthResponse;
import com.nsy.api.transfer.domain.request.platform.tiktok.TiktokFbtInventRecordsRequest;
import com.nsy.api.transfer.domain.response.platform.tiktok.invent.TikTokInventPageResponse;
import com.nsy.api.transfer.domain.response.platform.tiktok.invent.TikTokInventRecordsResponse;
import com.nsy.api.transfer.enums.PlatformTypeEnum;
import com.nsy.oms.business.domain.dto.FbtInventRecordsParameterDTO;
import com.nsy.oms.business.manage.transfer.TransferApiService;
import com.nsy.oms.business.service.report.ReportFbtInventoryRecordsService;
import com.nsy.oms.repository.entity.report.ReportFbtInventoryRecordsEntity;
import com.nsy.oms.repository.sql.mapper.report.ReportFbtInventoryRecordsMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * FBT库存变动报表Service实现类
 */
@Slf4j
@Service
public class ReportFbtInventoryRecordsServiceImpl extends ServiceImpl<ReportFbtInventoryRecordsMapper, ReportFbtInventoryRecordsEntity> implements ReportFbtInventoryRecordsService {

    @Autowired
    private TransferApiService transferApiService;

    @Override
    public void saveRecords(List<TiktokAuthResponse> authList, FbtInventRecordsParameterDTO parameterDTO) {
        if (!CollectionUtils.isEmpty(authList)) {
            authList.forEach(item -> {
                // 如果不为空就只执行一个店铺
                if (Objects.nonNull(parameterDTO.getStoreId()) && !item.getStoreId().equals(parameterDTO.getStoreId())) {
                    return;
                }
                doGetRecords(parameterDTO, item);
            });
        }

    }

    private void doGetRecords(FbtInventRecordsParameterDTO parameterDTO, TiktokAuthResponse item) {
        if (StringUtils.isNotBlank(item.getAccessToken())) {
            PlatformTypeEnum platformTypeEnum = PlatformTypeEnum.getPlatform(item.getPlatformId());
            if (platformTypeEnum == null) {
                log.error("根据ID获取平台失败！");
                return;
            }
            TiktokFbtInventRecordsRequest request = new TiktokFbtInventRecordsRequest();
            request.setLocation(parameterDTO.getLocation());
            request.setStoreId(item.getStoreId());
            request.setPageSize(parameterDTO.getFetchCount());
            request.setPlatform(platformTypeEnum);

            if (StringUtils.isNotBlank(parameterDTO.getStartDate()) || StringUtils.isNotBlank(parameterDTO.getEndDate())) {
                setDate(parameterDTO, request);
            } else {
                // 获取两天前的日期，并设置为当天的零点
                Date twoDaysAgo = DateUtil.offsetDay(new Date(), -2);
                String twoDaysAgoStr = DateUtil.format(DateUtil.beginOfDay(twoDaysAgo), "yyyy-MM-dd HH:mm:ss");
                request.setCreateTimeGe(twoDaysAgoStr);
                String twoDaysAgoEndStr = DateUtil.format(DateUtil.endOfDay(twoDaysAgo), "yyyy-MM-dd HH:mm:ss");
                request.setCreateTimeLe(twoDaysAgoEndStr);
            }
            try {
                getTiktokFbtInventList(platformTypeEnum, request, item);
            } catch (InterruptedException e) {
                log.error(String.format("%s获取库存变动列表失败:%s", item.getStoreId(), e.getMessage()));
            }
        }
    }

    private static void setDate(FbtInventRecordsParameterDTO parameterDTO, TiktokFbtInventRecordsRequest request) {
        if (StringUtils.isNotBlank(parameterDTO.getStartDate())) {
            request.setCreateTimeGe(parameterDTO.getStartDate());
        }
        if (StringUtils.isNotBlank(parameterDTO.getEndDate())) {
            request.setCreateTimeLe(parameterDTO.getEndDate());
        }
    }

    @Transactional
    public void getTiktokFbtInventList(PlatformTypeEnum platformTypeEnum, TiktokFbtInventRecordsRequest request, TiktokAuthResponse tiktokAuth) throws InterruptedException {
        TikTokInventPageResponse tikTokResponse = null;
        int maxRetries = 5;
        int currentRetry = 0;
        boolean success = false;

        while (!success && currentRetry < maxRetries) {
            try {
                tikTokResponse = transferApiService.getFbtInventPage(platformTypeEnum.getUrl(), request, tiktokAuth);
                log.info("获取tk库存变动列表：{}", tikTokResponse);
                success = true;
                currentRetry = 0; // 请求成功后重置重试次数
            } catch (Exception e) {
                currentRetry++;
                log.error("获取tk库存变动列表失败，当前重试次数：{}，错误信息：{}", currentRetry, e.getMessage());
                if (currentRetry < maxRetries) {
                    Thread.sleep(5000); // 等待10秒后重试
                } else {
                    log.error("获取tk库存变动列表失败，已达到最大重试次数：{}", maxRetries);
                    throw new BusinessServiceException("获取tk库存变动列表失败，已达到最大重试次数");
                }
            }
        }

        if (Objects.isNull(tikTokResponse) || Objects.isNull(tikTokResponse.getData()) || tikTokResponse.getCode() != 0) {
            return;
        }
        List<TikTokInventRecordsResponse> reportFbtInventRecords = tikTokResponse.getData().getInventoryRecords();
        if (!CollectionUtils.isEmpty(reportFbtInventRecords)) {
            List<ReportFbtInventoryRecordsEntity> list = new ArrayList<>();
            reportFbtInventRecords.forEach(item -> {
                ReportFbtInventoryRecordsEntity entity = new ReportFbtInventoryRecordsEntity();
                // 删除重复数据
                baseMapper.delete(new LambdaQueryWrapper<ReportFbtInventoryRecordsEntity>()
                        .eq(ReportFbtInventoryRecordsEntity::getOrderId, item.getOrder().getId())
                        .eq(ReportFbtInventoryRecordsEntity::getStoreId, tiktokAuth.getStoreId())
                        .eq(ReportFbtInventoryRecordsEntity::getGoodsId, item.getGoods().getId())
                        .eq(ReportFbtInventoryRecordsEntity::getFbtWarehouseId, item.getFbtWarehouseId()));
                BeanUtils.copyProperties(item, entity);
                entity.setStoreId(tiktokAuth.getStoreId());
                entity.setStoreName(tiktokAuth.getStoreName());
                entity.setOrderId(item.getOrder().getId());
                entity.setOrderType(item.getOrder().getType());
                entity.setGoodsId(item.getGoods().getId());
                entity.setGoodsName(item.getGoods().getName());
                entity.setFbtWarehouseId(item.getFbtWarehouseId());
                entity.setGoodsReferenceCode(item.getGoods().getReferenceCode());
                // 时间戳转日期
                entity.setOrderCreateTime(new Date(item.getCreateTime() * 1000));
                entity.setLocation(request.getLocation());
                entity.setCreateDate(new Date());
                entity.setUpdateDate(new Date());
                list.add(entity);
            });
            this.saveBatch(list);
        }
        if (StringUtils.isNotBlank(tikTokResponse.getData().getNextPageToken())) {
            if (checkToken(request, tikTokResponse)) return;
            Thread.sleep(10000);
            request.setNextPageToken(tikTokResponse.getData().getNextPageToken());
            getTiktokFbtInventList(platformTypeEnum, request, tiktokAuth);
        }

    }

    private static boolean checkToken(TiktokFbtInventRecordsRequest request, TikTokInventPageResponse tikTokResponse) {
        if (StringUtils.isNotBlank(request.getNextPageToken()) && request.getNextPageToken().equals(tikTokResponse.getData().getNextPageToken())) {
            return true;
        }
        return false;
    }

}
