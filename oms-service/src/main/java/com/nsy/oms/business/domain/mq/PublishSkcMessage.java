package com.nsy.oms.business.domain.mq;

public class PublishSkcMessage {
    public static final String DELETE = "delete";
    public static final String UPDATE = "update";

    private String psWebsiteIdSkc;
    private String updateType;

    public String getPsWebsiteIdSkc() {
        return psWebsiteIdSkc;
    }

    public void setPsWebsiteIdSkc(String psWebsiteIdSkc) {
        this.psWebsiteIdSkc = psWebsiteIdSkc;
    }

    public String getUpdateType() {
        return updateType;
    }

    public void setUpdateType(String updateType) {
        this.updateType = updateType;
    }

    public static PublishSkcMessage of(String psWebsiteIdSkc, String updateType) {
        PublishSkcMessage message = new PublishSkcMessage();
        message.setPsWebsiteIdSkc(psWebsiteIdSkc);
        message.setUpdateType(updateType);
        return message;
    }
}
