package com.nsy.oms.business.service.upload;


import com.nsy.oms.business.domain.request.upload.UploadRequest;
import com.nsy.oms.business.domain.response.upload.UploadResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UploadService {
    @Autowired
    private UploadFactory factory;

    public UploadResponse distribution(UploadRequest request) {
        return factory.distribution(request);
    }
}
