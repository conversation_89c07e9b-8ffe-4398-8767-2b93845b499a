package com.nsy.oms.business.domain.response.external;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api("CRM订单分页列表类")
public class CrmOrderPageListResponse implements Serializable {

    private static final long serialVersionUID = -5389170541534645717L;

    @ApiModelProperty("店铺id")
    private Integer storeId;

    @ApiModelProperty("市场编码")
    private String marketCode;

    @ApiModelProperty("订单ID")
    private Integer orderId;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("订单状态：10-付款未发货 20-部分发货 21-已发货 30-已取消")
    private Integer orderStatus;

    @ApiModelProperty("订单状态：10-付款未发货 20-部分发货 21-已发货 30-已取消")
    private String orderStatusName;

    @ApiModelProperty("实付金额")
    private BigDecimal paymentAmount;

    @ApiModelProperty("币种")
    private String currency;

    @ApiModelProperty("物流方式")
    private String shippingType;

    @ApiModelProperty("订单创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderCreateDate;

    @ApiModelProperty("订单发货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderDeliverDate;

    @ApiModelProperty("物流渠道")
    private String logisticsChannel;

    @ApiModelProperty("物流单号")
    private String logisticsNo;

    @ApiModelProperty("订单详情")
    private List<CrmOrderPageItemListResponse> itemList;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getOrderStatusName() {
        return orderStatusName;
    }

    public void setOrderStatusName(String orderStatusName) {
        this.orderStatusName = orderStatusName;
    }

    public BigDecimal getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(BigDecimal paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getShippingType() {
        return shippingType;
    }

    public void setShippingType(String shippingType) {
        this.shippingType = shippingType;
    }

    public Date getOrderCreateDate() {
        return orderCreateDate;
    }

    public void setOrderCreateDate(Date orderCreateDate) {
        this.orderCreateDate = orderCreateDate;
    }

    public Date getOrderDeliverDate() {
        return orderDeliverDate;
    }

    public void setOrderDeliverDate(Date orderDeliverDate) {
        this.orderDeliverDate = orderDeliverDate;
    }

    public List<CrmOrderPageItemListResponse> getItemList() {
        return itemList;
    }

    public void setItemList(List<CrmOrderPageItemListResponse> itemList) {
        this.itemList = itemList;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getMarketCode() {
        return marketCode;
    }

    public void setMarketCode(String marketCode) {
        this.marketCode = marketCode;
    }

    public String getLogisticsChannel() {
        return logisticsChannel;
    }

    public void setLogisticsChannel(String logisticsChannel) {
        this.logisticsChannel = logisticsChannel;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }
}
