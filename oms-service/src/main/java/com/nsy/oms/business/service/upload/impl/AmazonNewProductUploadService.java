package com.nsy.oms.business.service.upload.impl;

import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.oms.business.domain.request.upload.UploadRequest;
import com.nsy.oms.business.domain.response.upload.UploadResponse;
import com.nsy.oms.business.domain.upload.AmazonProductImport;
import com.nsy.oms.business.service.upload.IUploadService;
import com.nsy.oms.constants.upload.QuartzUploadQueueTypeEnum;
import com.nsy.oms.repository.mongo.document.AmazonNewProductDocument;
import com.nsy.oms.repository.mongo.repository.AmazonNewProductDocumentRepository;
import com.nsy.oms.utils.JsonMapper;
import com.nsy.oms.utils.ListUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 亚马逊排位赛商品导入
 * <AUTHOR>
 * @date 2023-02-24
 */
@Service
public class AmazonNewProductUploadService implements IUploadService {
    @Autowired
    private AmazonNewProductDocumentRepository amazonNewProductDocumentRepository;

    @Override
    public QuartzUploadQueueTypeEnum uploadType() {
        return QuartzUploadQueueTypeEnum.AMAZON_NEW_PRODUCT_IMPORT;
    }

    @Override
    public UploadResponse processUploadData(UploadRequest request) {
        UploadResponse uploadResponse = new UploadResponse();
        if (!StringUtils.hasText(request.getDataJsonStr()) || !StringUtils.hasText(request.getUploadParams())) {
            return uploadResponse;
        }
        List<AmazonProductImport> importList = JsonMapper.jsonStringToObjectArray(request.getDataJsonStr(), AmazonProductImport.class);
        if (CollectionUtils.isEmpty(importList)) {
            return uploadResponse;
        }
        List<AmazonNewProductDocument> list = convertData(importList, request.getCreateBy());
        List<AmazonProductImport> errorList = new ArrayList<>();
        // 不重复数据不写入
        list.forEach(document -> {
            if (amazonNewProductDocumentRepository.findBySeasonAndStoreNameAndSpu(document.getSeason(), document.getStoreName(), document.getSpu()).isEmpty()) {
                amazonNewProductDocumentRepository.save(document);
            } else {
                errorList.add(buildDuplicateErrorImport(document));
            }
        });
        return uploadResponse;
    }

    private AmazonProductImport buildDuplicateErrorImport(AmazonNewProductDocument document) {
        AmazonProductImport amazonProductImport = new AmazonProductImport();
        amazonProductImport.setSeason(document.getSeason());
        amazonProductImport.setStoreName(document.getStoreName());
        amazonProductImport.setSpu(document.getSpu());
        amazonProductImport.setErrorMsg("系统已存在该数据");
        return amazonProductImport;
    }

    private List<AmazonNewProductDocument> convertData(List<AmazonProductImport> list, String createBy) {
        Date date = new Date();
        return list.stream().map(e -> {
            AmazonNewProductDocument document = new AmazonNewProductDocument();
            BeanUtilsEx.copyProperties(e, document);
            document.setCreateDate(date);
            document.setCreateBy(createBy);
            return document;
        }).filter(ListUtil.distinctByKey(e -> String.format("%s-%s-%s", e.getSeason(), e.getStoreName(), e.getSpu()))).collect(Collectors.toList());
    }
}
