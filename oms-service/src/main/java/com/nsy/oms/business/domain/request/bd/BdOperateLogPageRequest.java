package com.nsy.oms.business.domain.request.bd;


import com.nsy.oms.business.domain.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


@ApiModel("日志")
public class BdOperateLogPageRequest extends PageRequest {
    @ApiModelProperty(value = "业务主键ID", name = "id")
    @NotNull(message = "业务主键ID不能为空")
    private Integer id;

    @ApiModelProperty(value = "业务类型", name = "type")
    @NotBlank(message = "业务类型不能为空")
    private String type;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
