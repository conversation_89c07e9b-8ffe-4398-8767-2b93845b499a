package com.nsy.oms.business.domain.response.auth;

import com.nsy.base.secure.DesensitizationProp;
import com.nsy.base.secure.enums.SensitiveTypeEnum;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/3/21 14:37
 */
public class PlatformAuthConfigPageResponse {
    @ApiModelProperty("id")
    private Integer id;
    @ApiModelProperty("平台名称")
    private String platformName;
    @ApiModelProperty("店铺账号")
    private String storeName;
    @ApiModelProperty("开启/关闭 0 关闭 1开启")
    private Integer status;
    @ApiModelProperty("创建人")
    private String createBy;
    @ApiModelProperty("创建时间")
    private Date createDate;
    @ApiModelProperty("店铺id")
    private Integer storeId;
    @ApiModelProperty("扩展属性1")
    @DesensitizationProp(SensitiveTypeEnum.BANK_CARD)
    private String accountProperties1;
    @ApiModelProperty("扩展属性2")
    @DesensitizationProp(SensitiveTypeEnum.BANK_CARD)
    private String accountProperties2;
    @ApiModelProperty("扩展属性3")
    @DesensitizationProp(SensitiveTypeEnum.BANK_CARD)
    private String accountProperties3;
    @ApiModelProperty("扩展属性4")
    @DesensitizationProp(SensitiveTypeEnum.BANK_CARD)
    private String accountProperties4;
    @ApiModelProperty("扩展属性5")
    @DesensitizationProp(SensitiveTypeEnum.BANK_CARD)
    private String accountProperties5;
    private String accountProperties6;

    @ApiModelProperty("serviceId")
    private String serviceId;
    /**
     * 授权日期
     */
    @ApiModelProperty("授权日期")
    private Date grantDate;

    /**
     * 有效期
     */
    @ApiModelProperty("有效期")
    private Date validityDate;

    public String getAccountProperties6() {
        return accountProperties6;
    }

    public void setAccountProperties6(String accountProperties6) {
        this.accountProperties6 = accountProperties6;
    }

    public Date getGrantDate() {
        return grantDate;
    }

    public void setGrantDate(Date grantDate) {
        this.grantDate = grantDate;
    }

    public Date getValidityDate() {
        return validityDate;
    }

    public void setValidityDate(Date validityDate) {
        this.validityDate = validityDate;
    }

    public String getAccountProperties1() {
        return accountProperties1;
    }

    public void setAccountProperties1(String accountProperties1) {
        this.accountProperties1 = accountProperties1;
    }

    public String getAccountProperties2() {
        return accountProperties2;
    }

    public void setAccountProperties2(String accountProperties2) {
        this.accountProperties2 = accountProperties2;
    }

    public String getAccountProperties3() {
        return accountProperties3;
    }

    public void setAccountProperties3(String accountProperties3) {
        this.accountProperties3 = accountProperties3;
    }

    public String getAccountProperties4() {
        return accountProperties4;
    }

    public void setAccountProperties4(String accountProperties4) {
        this.accountProperties4 = accountProperties4;
    }

    public String getAccountProperties5() {
        return accountProperties5;
    }

    public void setAccountProperties5(String accountProperties5) {
        this.accountProperties5 = accountProperties5;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
}
