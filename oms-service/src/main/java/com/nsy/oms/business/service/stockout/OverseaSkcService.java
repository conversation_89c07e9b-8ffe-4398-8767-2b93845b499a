package com.nsy.oms.business.service.stockout;

import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.oms.business.domain.request.stockout.OverseaSkcPageRequest;
import com.nsy.oms.business.domain.response.stockout.OverseaOnTheWayStockResponse;
import com.nsy.oms.business.domain.response.stockout.OverseaSkcPageResponse;
import com.nsy.oms.business.domain.response.stockout.OverseaSkuStoreSpaceStockoutResponse;

import java.util.List;

public interface OverseaSkcService {
    PageResponse<OverseaSkcPageResponse> page(OverseaSkcPageRequest request);
    
    List<OverseaOnTheWayStockResponse> onTheWayStockList(String department, Integer replenishmentGroupId, List<String> skcList);

    List<OverseaSkuStoreSpaceStockoutResponse> skuStoreSpaceStockout(String department, Integer replenishmentGroupId, String skc);
}
