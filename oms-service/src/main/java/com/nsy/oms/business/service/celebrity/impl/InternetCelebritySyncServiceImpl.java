package com.nsy.oms.business.service.celebrity.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.omspublish.dto.enumeration.LocationEnum;
import com.nsy.api.pms.dto.request.BaseListRequest;
import com.nsy.oms.business.domain.request.celebrity.InternetCelebritySyncAdRequest;
import com.nsy.oms.business.domain.request.celebrity.InternetCelebritySyncCreatorRequest;
import com.nsy.oms.business.domain.request.celebrity.InternetCelebritySyncMaxAdRequest;
import com.nsy.oms.business.domain.request.celebrity.InternetCelebritySyncVideoRequest;
import com.nsy.oms.business.domain.response.celebrity.InternetCelebrityCreatorNoInfoResponse;
import com.nsy.oms.business.domain.upload.InternetCelebrityVideoImport;
import com.nsy.oms.business.manage.user.UserApiService;
import com.nsy.oms.business.manage.user.request.OssRequest;
import com.nsy.oms.business.service.celebrity.CelebrityBusinessDeveloperService;
import com.nsy.oms.business.service.celebrity.IInternetCelebritySampleOrderItemPostService;
import com.nsy.oms.business.service.celebrity.InternetCelebrityAdDailyService;
import com.nsy.oms.business.service.celebrity.InternetCelebrityCategoryService;
import com.nsy.oms.business.service.celebrity.InternetCelebrityConsumerMessageService;
import com.nsy.oms.business.service.celebrity.InternetCelebrityNameRecordService;
import com.nsy.oms.business.service.celebrity.InternetCelebritySampleOrderItemPostDailyService;
import com.nsy.oms.business.service.celebrity.InternetCelebrityService;
import com.nsy.oms.business.service.celebrity.InternetCelebrityStoreRelationService;
import com.nsy.oms.business.service.celebrity.InternetCelebritySyncService;
import com.nsy.oms.business.service.sa.SaStoreService;
import com.nsy.oms.business.service.system.AliYunOssService;
import com.nsy.oms.enums.tkcreator.InternetCelebrityLevelType;
import com.nsy.oms.enums.tkcreator.InternetCelebrityMessageType;
import com.nsy.oms.enums.tkcreator.InternetCelebrityRelationStatus;
import com.nsy.oms.mq.producer.InternetCelebrityMessageSendService;
import com.nsy.oms.repository.entity.celebrity.CelebrityBusinessDeveloperEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebrityAdDailyEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebrityCategoryEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebrityEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebrityNameRecordEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderItemPostDailyEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderItemPostEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebrityStoreRelationEntity;
import com.nsy.oms.repository.entity.sa.SaStoreEntity;
import com.nsy.oms.repository.sql.mapper.celebrity.InternetCelebrityMapper;
import com.nsy.oms.repository.sql.mapper.celebrity.InternetCelebritySampleOrderItemPostMapper;
import com.nsy.oms.utils.IntranetContext;
import com.nsy.oms.utils.JsonMapper;
import com.nsy.oms.utils.TiktokCreatorUtil;
import com.nsy.oms.utils.mp.LocationContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.inject.Inject;
import java.io.ByteArrayInputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

@Slf4j
@Service
public class InternetCelebritySyncServiceImpl implements InternetCelebritySyncService {

    @Inject
    IInternetCelebritySampleOrderItemPostService videoService;
    @Inject
    InternetCelebritySampleOrderItemPostMapper videoMapper;
    @Inject
    InternetCelebritySampleOrderItemPostDailyService videoDailyService;
    @Inject
    InternetCelebrityAdDailyService adDailyService;
    @Inject
    InternetCelebrityMapper internetCelebrityMapper;
    @Inject
    InternetCelebrityService internetCelebrityService;
    @Inject
    InternetCelebrityNameRecordService nameRecordService;
    @Inject
    SaStoreService saStoreService;
    @Inject
    ApplicationContext applicationContext;
    @Inject
    InternetCelebrityCategoryService categoryService;
    @Inject
    AliYunOssService aliYunOssService;
    @Inject
    UserApiService userApiService;
    @Inject
    InternetCelebrityMessageSendService internetCelebrityMessageSendService;
    @Inject
    InternetCelebrityConsumerMessageService internetCelebrityConsumerMessageService;
    @Inject
    InternetCelebrityStoreRelationService relationService;
    @Autowired
    private CelebrityBusinessDeveloperService celebrityBusinessDeveloperService;

    @Override
    public void syncCreator(InternetCelebritySyncCreatorRequest request) {
        IntranetContext.setIntranet(1);
        log.info("[syncCreator] InternetCelebritySyncServiceImpl syncCreator request: {} ", JsonMapper.toJson(request));

        List<String> creatorNoList = request.getData().stream().map(InternetCelebritySyncCreatorRequest.InternetCelebritySyncCreator::getCid).filter(StrUtil::isNotEmpty).collect(Collectors.toList());
        List<String> creatorNameList = request.getData().stream().map(InternetCelebritySyncCreatorRequest.InternetCelebritySyncCreator::getAccountHandle).filter(StrUtil::isNotEmpty).collect(Collectors.toList());

        if (CollUtil.isEmpty(creatorNoList) || CollUtil.isEmpty(creatorNameList)) {
            log.info("[同步达人数据错误 - 没有达人id或名字信息] InternetCelebritySyncServiceImpl syncCreator : {} ", JsonMapper.toJson(request));
            throw new BusinessServiceException("同步达人数据错误 - 没有达人id或名字信息");
        } else if (StrUtil.isEmpty(request.getData().get(0).getStoreId())) {
            log.info("[同步视频数据错误 - 没有店铺id] InternetCelebritySyncServiceImpl syncCreator : {} ", JsonMapper.toJson(request));
            throw new BusinessServiceException("同步视频数据错误 - 没有店铺id");
        }

        SaStoreEntity storeEntity = saStoreService.getById(Integer.valueOf(request.getData().get(0).getStoreId()));
        String location = Objects.isNull(storeEntity) ? LocationEnum.QUANZHOU.name() : storeEntity.getLocation();
        LocationContext.setLocation(location);

        Map<String, InternetCelebrityEntity> creatorNoMap = internetCelebrityMapper.findAllByInternetCelebrityNoIn(creatorNoList).stream().collect(Collectors.toMap(InternetCelebrityEntity::getInternetCelebrityNo, Function.identity(), (a, b) -> a));
        Map<String, InternetCelebrityEntity> creatorNameMap = internetCelebrityMapper.findAllByInternetCelebrityNameIn(creatorNameList).stream().collect(Collectors.toMap(InternetCelebrityEntity::getInternetCelebrityName, Function.identity(), (a, b) -> a));

        request.getData().forEach(creator -> {
            try {
                applicationContext.getBean(InternetCelebritySyncServiceImpl.class).doSyncCreator(creator, creatorNoMap, creatorNameMap, location);
            } catch (Exception e) {
                log.error("同步达人数据异常", e);
                log.info("[同步达人数据异常] InternetCelebritySyncServiceImpl syncCreator : {} ", JsonMapper.toJson(creator));
            }
        });
    }

    @Transactional
    public void doSyncCreator(InternetCelebritySyncCreatorRequest.InternetCelebritySyncCreator creator, Map<String, InternetCelebrityEntity> creatorNoMap, Map<String, InternetCelebrityEntity> creatorNameMap, String location) {
        InternetCelebrityEntity entity = creatorNoMap.get(creator.getCid());

        if (Objects.isNull(entity)) {
            entity = creatorNameMap.get(creator.getAccountHandle());
        }

        if (Objects.isNull(entity)) {
            entity = new InternetCelebrityEntity();
            entity.setCreateBy("syncInternetCelebrity");
            entity.setLocation(location);
        }

        boolean nameNotEqual = !Objects.equals(entity.getInternetCelebrityName(), creator.getAccountHandle());

        entity.setInternetCelebrityNo(creator.getCid());
        entity.setInternetCelebrityName(creator.getAccountHandle());
        entity.setImageUrl(processImagesData(creator.getImagesData()));
        entity.setEmail(creator.getEmail());
        entity.setGmv(TiktokCreatorUtil.stringToBigDecimal(creator.getGmvLast1Months()));
        entity.setGpm(TiktokCreatorUtil.stringToBigDecimal(creator.getGpmLast1Months()));
        entity.setAvgOrderAmount(TiktokCreatorUtil.stringToBigDecimal(creator.getGmvPerBuyerLast1Months()));
        entity.setSaleQty(TiktokCreatorUtil.stringToInt(creator.getItemsSoldLast1Months()));
        entity.setFollowers(TiktokCreatorUtil.stringToInt(creator.getFollowerRatio()));
        entity.setFollowersIn30Age18ToAge24(TiktokCreatorUtil.divide(creator.getFollowerAge18To24Last1Months()));
        entity.setFollowersIn30Age25ToAge34(TiktokCreatorUtil.divide(creator.getFollowerAge25To34Last1Months()));
        entity.setFollowersIn30Age35ToAge44(TiktokCreatorUtil.divide(creator.getFollowerAge35To44Last1Months()));
        entity.setFollowersIn30Age45ToAge54(TiktokCreatorUtil.divide(creator.getFollowerAge45To54Last1Months()));
        entity.setFollowersIn30Age55AndMore(TiktokCreatorUtil.divide(creator.getFollowerAge55UpLast1Months()));
        entity.setFulfillmentRateInTotal(TiktokCreatorUtil.divide(TiktokCreatorUtil.divide(TiktokCreatorUtil.stringToBigDecimal(creator.getSampleFulfillmentRate()))));
        entity.setUpdateBy("syncInternetCelebrity");
        if (StrUtil.isEmpty(entity.getLevel())) entity.setLevel(InternetCelebrityLevelType.C.name());
        internetCelebrityService.saveOrUpdate(entity);

        saveCategory(creator, location, entity);

        saveNameRecordIfNotEqual(creator, nameNotEqual, entity);

        saveRelationWhenProcessCreator(creator, entity);

        internetCelebrityMessageSendService.sendMessage(entity.getId(), InternetCelebrityMessageType.CREATOR);
    }

    private void saveRelationWhenProcessCreator(InternetCelebritySyncCreatorRequest.InternetCelebritySyncCreator creator, InternetCelebrityEntity entity) {
        InternetCelebrityStoreRelationEntity relation = internetCelebrityConsumerMessageService.findRelation(Integer.valueOf(creator.getStoreId()), null, entity.getId(), entity.getInternetCelebrityNo(), entity.getInternetCelebrityName());
        if (Objects.isNull(relation)) {
            relation = new InternetCelebrityStoreRelationEntity();
            relation.setStoreId(Integer.valueOf(creator.getStoreId()));
            relation.setStoreName(creator.getStoreName());
            relation.setRelationStatus(InternetCelebrityRelationStatus.NOT_RELATION.getValue());
            relation.setCommissionRate(BigDecimal.ZERO);
            relation.setRemark("");
            relation.setUpdateBy("syncCreator");
            relation.setCreateBy("syncCreator");
            relation.setCommissionRate(new BigDecimal("0.12"));
        }
        if (StrUtil.isNotEmpty(creator.getBdName())) {
            CelebrityBusinessDeveloperEntity bd = celebrityBusinessDeveloperService.findByEmail(creator.getBdName());
            if (Objects.nonNull(bd)) {
                relation.setBdId(bd.getId());
                relation.setBdName(bd.getBdName());
                relation.setBdAccount(bd.getBdAccount());
                relation.setBdEmail(bd.getBdEmail());
            }
        }
        relation.setInternetCelebrityName(entity.getInternetCelebrityName());
        relation.setInternetCelebrityNo(entity.getInternetCelebrityNo());
        relation.setInternetCelebrityId(entity.getId());
        relationService.saveOrUpdate(relation);
    }

    private void saveNameRecordIfNotEqual(InternetCelebritySyncCreatorRequest.InternetCelebritySyncCreator creator, boolean nameNotEqual, InternetCelebrityEntity entity) {
        if (nameNotEqual) {
            InternetCelebrityNameRecordEntity nameRecordEntity = new InternetCelebrityNameRecordEntity();
            nameRecordEntity.setInternetCelebrityId(entity.getId());
            nameRecordEntity.setInternetCelebrityNo(creator.getCid());
            nameRecordEntity.setInternetCelebrityName(creator.getAccountHandle());
            nameRecordEntity.setUpdateBy("syncInternetCelebrity");
            nameRecordEntity.setCreateBy("syncInternetCelebrity");
            nameRecordEntity.setLocation(entity.getLocation());
            nameRecordService.save(nameRecordEntity);
        }
    }

    private void saveCategory(InternetCelebritySyncCreatorRequest.InternetCelebritySyncCreator creator, String location, InternetCelebrityEntity entity) {
        List<InternetCelebrityCategoryEntity> categoryEntities = categoryService.findByInternetCelebrityId(entity.getId());
        if (CollUtil.isNotEmpty(categoryEntities))
            categoryService.removeByIds(categoryEntities.stream().map(InternetCelebrityCategoryEntity::getId).collect(Collectors.toList()));

        if (CollUtil.isNotEmpty(creator.getIndustryGroups())) {
            List<InternetCelebrityCategoryEntity> newCategoryEntities = new ArrayList<>(creator.getIndustryGroups().size());
            creator.getIndustryGroups().forEach(industryGroupsDTO -> {
                InternetCelebrityCategoryEntity categoryEntity = new InternetCelebrityCategoryEntity();
                categoryEntity.setInternetCelebrityId(entity.getId());
                categoryEntity.setCategoryNo(industryGroupsDTO.getKey());
                categoryEntity.setCategoryName("-1".equals(industryGroupsDTO.getKey()) ? "其他" : industryGroupsDTO.getName());
                categoryEntity.setRate(new BigDecimal(industryGroupsDTO.getValue()));
                categoryEntity.setUpdateBy("syncInternetCelebrity");
                categoryEntity.setCreateBy("syncInternetCelebrity");
                categoryEntity.setLocation(location);
                newCategoryEntities.add(categoryEntity);
            });

            categoryService.saveBatch(newCategoryEntities);
        }
    }

    @Override
    public void syncVideo(InternetCelebritySyncVideoRequest request) {
        IntranetContext.setIntranet(1);
        log.info("[syncVideo] InternetCelebritySyncServiceImpl syncVideo request: {} ", JsonMapper.toJson(request));

        List<String> videoIds = request.getData().stream().map(InternetCelebritySyncVideoRequest.DataDTO::getVideoMeta).map(InternetCelebritySyncVideoRequest.DataDTO.VideoMetaDTO::getId).filter(StrUtil::isNotEmpty).distinct().collect(Collectors.toList());

        if (CollUtil.isEmpty(videoIds)) {
            log.info("[同步视频数据错误 - 没有视频id] InternetCelebritySyncServiceImpl syncVideo : {} ", JsonMapper.toJson(request));
            throw new BusinessServiceException("同步视频数据错误 - 没有视频id");
        } else if (StrUtil.isEmpty(request.getData().get(0).getStoreId())) {
            log.info("[同步视频数据错误 - 没有店铺id] InternetCelebritySyncServiceImpl syncVideo : {} ", JsonMapper.toJson(request));
            throw new BusinessServiceException("同步视频数据错误 - 没有店铺id");
        }

        SaStoreEntity storeEntity = saStoreService.getById(Integer.valueOf(request.getData().get(0).getStoreId()));
        String location = Objects.isNull(storeEntity) ? LocationEnum.QUANZHOU.name() : storeEntity.getLocation();
        LocationContext.setLocation(location);

        String storeName = Objects.isNull(storeEntity) ? request.getData().get(0).getStoreName() : storeEntity.getErpStoreName();

        Map<String, List<InternetCelebritySampleOrderItemPostEntity>> videoGroup = videoMapper.findAllByStoreIdAndVideoCodeIn(Integer.valueOf(request.getData().get(0).getStoreId()), videoIds).stream().collect(Collectors.groupingBy(InternetCelebritySampleOrderItemPostEntity::getVideoCode));

        request.getData().forEach(dataDTO -> {
            if (Objects.isNull(dataDTO.getVideoMeta()) || StrUtil.isEmpty(dataDTO.getVideoMeta().getId())) {
                log.info("[同步视频错误-视频id为空] InternetCelebritySyncServiceImpl syncVideo : {} ", JsonMapper.toJson(dataDTO));
                return;
            } else if (CollUtil.isEmpty(dataDTO.getProductList())) {
                log.info("[同步视频错误-商品id为空] InternetCelebritySyncServiceImpl syncVideo : {} ", JsonMapper.toJson(dataDTO));
                return;
            } else if (Objects.isNull(dataDTO.getCreatorMeta()) || StrUtil.isEmpty(dataDTO.getCreatorMeta().getId()) || StrUtil.isEmpty(dataDTO.getCreatorMeta().getHandle())) {
                log.info("[同步视频错误-达人数据为空] InternetCelebritySyncServiceImpl syncVideo : {} ", JsonMapper.toJson(dataDTO));
                return;
            }

            Map<String, InternetCelebritySampleOrderItemPostEntity> productIdVideoMap = videoGroup.getOrDefault(dataDTO.getVideoMeta().getId(), new ArrayList<>()).stream().collect(Collectors.toMap(InternetCelebritySampleOrderItemPostEntity::getSellerProductId, Function.identity(), (a, b) -> a));
            dataDTO.getProductList().forEach(productListDTO -> {
                try {
                    applicationContext.getBean(InternetCelebritySyncServiceImpl.class).doSyncVideo(productListDTO, dataDTO, productIdVideoMap, storeName, location);
                } catch (Exception e) {
                    log.info("[同步视频异常] InternetCelebritySyncServiceImpl syncVideo : {} ", JsonMapper.toJson(dataDTO));
                    log.error("同步视频异常", e);
                }
            });
        });
    }

    @Transactional
    public void doSyncVideo(InternetCelebritySyncVideoRequest.DataDTO.ProductListDTO productListDTO, InternetCelebritySyncVideoRequest.DataDTO dataDTO, Map<String, InternetCelebritySampleOrderItemPostEntity> productIdVideoMap, String storeName, String location) {
        InternetCelebritySampleOrderItemPostEntity videoEntity = productIdVideoMap.get(productListDTO.getProductId());

        if (Objects.isNull(videoEntity)) {
            videoEntity = new InternetCelebritySampleOrderItemPostEntity();
            videoEntity.setStoreId(Integer.valueOf(dataDTO.getStoreId()));
            videoEntity.setStoreName(storeName);
            videoEntity.setCreateBy("syncVideo");
            videoEntity.setLocation(location);
            videoEntity.setVideoCode(dataDTO.getVideoMeta().getId());
            videoEntity.setSellerProductId(productListDTO.getProductId());
        }
        videoEntity.setGmv(Optional.ofNullable(videoEntity.getGmv()).orElse(BigDecimal.ZERO).add(TiktokCreatorUtil.stringToBigDecimal(dataDTO.getRevenue().getAmount())));
        videoEntity.setInternetCelebrityNo(dataDTO.getCreatorMeta().getId());
        videoEntity.setInternetCelebrityName(dataDTO.getCreatorMeta().getHandle());
        if (Objects.nonNull(dataDTO.getVideoMeta()) && Objects.nonNull(dataDTO.getVideoMeta().getVideo())) {
            videoEntity.setVideoUrl(dataDTO.getVideoMeta().getVideo().getPostUrl());
        }
        videoEntity.setPostDate(TiktokCreatorUtil.convertIntegerToDate(dataDTO.getPublishTime()));
        videoEntity.setUpdateBy("syncVideo");
        videoService.saveOrUpdate(videoEntity);

        saveVideoDaily(dataDTO, location, videoEntity);

        internetCelebrityMessageSendService.sendMessage(videoEntity.getInternetCelebritySampleOrderItemPostId(), InternetCelebrityMessageType.VIDEO);

    }

    private void saveVideoDaily(InternetCelebritySyncVideoRequest.DataDTO dataDTO, String location, InternetCelebritySampleOrderItemPostEntity videoEntity) {
        DateTime startDate = DateUtil.parseDate(dataDTO.getAnalysisDate());
        DateTime endDate = StrUtil.isNotEmpty(dataDTO.getAnalysisDateEnd()) ? DateUtil.parseDate(dataDTO.getAnalysisDateEnd()) : startDate;

        BigDecimal gvmOfAll = TiktokCreatorUtil.stringToBigDecimal(dataDTO.getRevenue().getAmount());
        long daysBetween = DateUtil.between(startDate, endDate, DateUnit.DAY) + 1;
        BigDecimal gvmOfAvg = gvmOfAll.divide(new BigDecimal(daysBetween), 4, RoundingMode.HALF_UP);

        // 计算平均分配的值，处理空值
        int totalViews = dataDTO.getViewCnt() == null ? 0 : dataDTO.getViewCnt();
        int totalSkuOrders = dataDTO.getSkuOrderCnt() == null ? 0 : dataDTO.getSkuOrderCnt();
        int totalSubOrders = dataDTO.getSubOrderCnt() == null ? 0 : dataDTO.getSubOrderCnt();

        int avgViews = (int) Math.ceil((double) totalViews / daysBetween);
        int avgSkuOrders = (int) Math.ceil((double) totalSkuOrders / daysBetween);
        int avgSubOrders = (int) Math.ceil((double) totalSubOrders / daysBetween);

        if (avgViews == 0 && avgSkuOrders == 0 && avgSubOrders == 0 && BigDecimal.ZERO.compareTo(gvmOfAvg) == 0) {
            for (Date tempDate = startDate; tempDate.compareTo(endDate) <= 0; tempDate = DateUtils.addDays(tempDate, 1)) {
                InternetCelebritySampleOrderItemPostDailyEntity videoDailyEntity = videoDailyService.findTopByPostIdAndDate(videoEntity.getInternetCelebritySampleOrderItemPostId(), tempDate);
                if (Objects.isNull(videoDailyEntity)) {
                    continue;
                }
                videoDailyService.removeById(videoDailyEntity.getId());
            }
        } else {
            int remainingViews = totalViews;
            int remainingSkuOrders = totalSkuOrders;
            int remainingSubOrders = totalSubOrders;

            for (Date tempDate = startDate; tempDate.compareTo(endDate) <= 0; tempDate = DateUtils.addDays(tempDate, 1)) {
                InternetCelebritySampleOrderItemPostDailyEntity videoDailyEntity = videoDailyService.findTopByPostIdAndDate(videoEntity.getInternetCelebritySampleOrderItemPostId(), tempDate);

                if (Objects.isNull(videoDailyEntity)) {
                    videoDailyEntity = new InternetCelebritySampleOrderItemPostDailyEntity();
                    videoDailyEntity.setPostId(videoEntity.getInternetCelebritySampleOrderItemPostId());
                    videoDailyEntity.setCreateBy("syncVideo");
                    videoDailyEntity.setGmvDate(tempDate);
                }

                // 分配视图数
                int currentViews = Math.min(avgViews, remainingViews);
                videoDailyEntity.setVideoViews(currentViews);
                remainingViews -= currentViews;

                // 分配SKU订单数
                int currentSkuOrders = Math.min(avgSkuOrders, remainingSkuOrders);
                videoDailyEntity.setSkuOrderNum(currentSkuOrders);
                remainingSkuOrders -= currentSkuOrders;

                // 分配子订单数
                int currentSubOrders = Math.min(avgSubOrders, remainingSubOrders);
                videoDailyEntity.setItemSoldNum(currentSubOrders);
                remainingSubOrders -= currentSubOrders;

                videoDailyEntity.setCurrency(dataDTO.getRevenue().getCurrencyCode());
                videoDailyEntity.setGmv(gvmOfAvg);
                videoDailyEntity.setUpdateBy("syncVideo");
                videoDailyEntity.setLocation(location);
                videoDailyService.saveOrUpdate(videoDailyEntity);
            }
        }
    }


    @Override
    @Transactional
    public void syncVideoByImport(InternetCelebrityVideoImport videoImport, String creator) {
        InternetCelebritySampleOrderItemPostEntity videoEntity = videoMapper.findByStoreIdAndVideoCodeAndSellerProductId(videoImport.getStoreId(), videoImport.getVideoID(), videoImport.getProductID());

        if (Objects.isNull(videoEntity)) {
            videoEntity = new InternetCelebritySampleOrderItemPostEntity();
            videoEntity.setStoreId(videoImport.getStoreId());
            videoEntity.setStoreName(videoImport.getStoreName());
            videoEntity.setCreateBy(creator);
            videoEntity.setLocation(videoImport.getLocation());
            videoEntity.setVideoCode(videoImport.getVideoID());
            videoEntity.setSellerProductId(videoImport.getProductID());
        }
        videoEntity.setGmv(Optional.ofNullable(videoEntity.getGmv()).orElse(BigDecimal.ZERO).add(TiktokCreatorUtil.stringToBigDecimal(videoImport.getAffiliateVideoGMV())));
        videoEntity.setInternetCelebrityName(videoImport.getCreatorName());
        videoEntity.setPostDate(DateUtil.parse(videoImport.getPostTime(), NORM_DATETIME_PATTERN));
        videoEntity.setUpdateBy(creator);
        videoService.saveOrUpdate(videoEntity);

        saveVideoDailyByImport(videoImport, videoEntity);

        internetCelebrityMessageSendService.sendMessage(videoEntity.getInternetCelebritySampleOrderItemPostId(), InternetCelebrityMessageType.VIDEO);
    }

    private void saveVideoDailyByImport(InternetCelebrityVideoImport videoImport, InternetCelebritySampleOrderItemPostEntity videoEntity) {
        String[] split = videoImport.getDate().split("-");
        DateTime startDate = DateUtil.parseDate(String.format("%s-%s-%s", split[0], split[1], split[2]));
        DateTime endDate = DateUtil.parseDate(String.format("%s-%s-%s", split[3], split[4], split[5]));

        List<InternetCelebritySampleOrderItemPostDailyEntity> dailyEntities = videoDailyService.findAllByPostIdAndDateRange(videoEntity.getInternetCelebritySampleOrderItemPostId(), startDate, endDate);

        if (CollUtil.isNotEmpty(dailyEntities)) {
            List<Date> dataList = dailyEntities.stream().map(InternetCelebritySampleOrderItemPostDailyEntity::getGmvDate).collect(Collectors.toList());
            log.info("[无法导入,存在日期内的视频gmv数据] InternetCelebritySyncServiceImpl saveVideoDailyByImport dataList: {}", JsonMapper.toJson(dataList));
            throw new BusinessServiceException(String.format("无法导入,存在[%s]日期的gmv数据", JsonMapper.toJson(dataList)));
        }

        BigDecimal gvmOfAll = TiktokCreatorUtil.stringToBigDecimal(videoImport.getAffiliateVideoGMV());
        long daysBetween = DateUtil.between(startDate, endDate, DateUnit.DAY) + 1;
        BigDecimal gvmOfAvg = gvmOfAll.divide(new BigDecimal(daysBetween), 4, RoundingMode.HALF_UP);

        // 计算平均分配的值，处理空值
        int totalViews = StrUtil.isEmpty(videoImport.getVideoViews()) ? 0 : Integer.parseInt(videoImport.getVideoViews());
        int totalSkuOrders = StrUtil.isEmpty(videoImport.getVideoOrders()) ? 0 : Integer.parseInt(videoImport.getVideoOrders());
        int totalItemSoldNum = StrUtil.isEmpty(videoImport.getItemsSold()) ? 0 : Integer.parseInt(videoImport.getItemsSold());

        int avgViews = (int) Math.ceil((double) totalViews / daysBetween);
        int avgSkuOrders = (int) Math.ceil((double) totalSkuOrders / daysBetween);
        int avgItemSoldNum = (int) Math.ceil((double) totalItemSoldNum / daysBetween);

        if (!(avgViews == 0 && avgSkuOrders == 0 && avgItemSoldNum == 0 && BigDecimal.ZERO.compareTo(gvmOfAvg) == 0)) {
            int remainingViews = totalViews;
            int remainingSkuOrders = totalSkuOrders;
            int remainingItemSoldNum = totalItemSoldNum;
            for (int i = 0; i < daysBetween; i++) {
                Date tempDate = DateUtils.addDays(startDate, i);
                InternetCelebritySampleOrderItemPostDailyEntity videoDailyEntity = videoDailyService.findTopByPostIdAndDate(videoEntity.getInternetCelebritySampleOrderItemPostId(), tempDate);

                if (Objects.isNull(videoDailyEntity)) {
                    videoDailyEntity = new InternetCelebritySampleOrderItemPostDailyEntity();
                    videoDailyEntity.setPostId(videoEntity.getInternetCelebritySampleOrderItemPostId());
                    videoDailyEntity.setCreateBy(videoEntity.getUpdateBy());
                    videoDailyEntity.setGmvDate(tempDate);
                }
                // 分配视图数
                int currentViews = Math.min(avgViews, remainingViews);
                videoDailyEntity.setVideoViews(currentViews);
                remainingViews -= currentViews;

                // 分配SKU订单数
                int currentSkuOrders = Math.min(avgSkuOrders, remainingSkuOrders);
                videoDailyEntity.setSkuOrderNum(currentSkuOrders);
                remainingSkuOrders -= currentSkuOrders;

                // 分配子订单数
                int currentItemSoldNum = Math.min(avgItemSoldNum, remainingItemSoldNum);
                videoDailyEntity.setItemSoldNum(currentItemSoldNum);
                remainingItemSoldNum -= currentItemSoldNum;

                videoDailyEntity.setCurrency("USD");
                videoDailyEntity.setGmv(gvmOfAvg);
                videoDailyEntity.setUpdateBy(videoEntity.getUpdateBy());
                videoDailyEntity.setLocation(videoEntity.getLocation());
                videoDailyService.saveOrUpdate(videoDailyEntity);
            }
        }
    }

    @Override
    public void syncAd(InternetCelebritySyncAdRequest request) {
        IntranetContext.setIntranet(1);
        log.info("[syncAd] InternetCelebritySyncServiceImpl syncAd request: {} ", JsonMapper.toJson(request));

        SaStoreEntity storeEntity = saStoreService.getById(Integer.valueOf(request.getData().get(0).getStoreId()));
        String location = Objects.isNull(storeEntity) ? LocationEnum.QUANZHOU.name() : storeEntity.getLocation();
        LocationContext.setLocation(location);

        String storeName = Objects.isNull(storeEntity) ? request.getData().get(0).getStoreName() : storeEntity.getErpStoreName();

        request.getData().forEach(dataDTO -> {
            try {
                applicationContext.getBean(InternetCelebritySyncServiceImpl.class).doSyncAd(dataDTO, storeName, location);
            } catch (Exception e) {
                log.info("[同步广告异常] InternetCelebritySyncServiceImpl syncVideo : {} ", JsonMapper.toJson(dataDTO));
                log.error("同步广告异常", e);
            }
        });
    }

    @Transactional
    public void doSyncAd(InternetCelebritySyncAdRequest.DataDTO dataDTO, String storeName, String location) {
        log.info("[doSyncAd] InternetCelebritySyncServiceImpl doSyncAd dataDTO: {} ", JsonMapper.toJson(dataDTO));
        if (Objects.isNull(dataDTO.getStoreId())) {
            log.info("[doSyncAd error] InternetCelebritySyncServiceImpl doSyncAd : 店铺id为空");
            return;
        } else if (StrUtil.isEmpty(dataDTO.getStatData().getCreativeId())) {
            log.info("[doSyncAd error] InternetCelebritySyncServiceImpl doSyncAd : ad id为空");
            return;
        } else if (StrUtil.isEmpty(dataDTO.getVideoId())) {
            log.info("[doSyncAd error] InternetCelebritySyncServiceImpl doSyncAd : Video id为空");
            return;
        }

        DateTime startDate = DateUtil.parseDate(dataDTO.getAnalysisDate());
        DateTime endDate = StrUtil.isNotEmpty(dataDTO.getAnalysisDateEnd()) ? DateUtil.parseDate(dataDTO.getAnalysisDateEnd()) : startDate;

        long daysBetween = DateUtil.between(startDate, endDate, DateUnit.DAY) + 1;
        BigDecimal costOfAll = TiktokCreatorUtil.stringToBigDecimal(dataDTO.getRowData().getStatCost());
        BigDecimal costOfAvg = costOfAll.divide(new BigDecimal(daysBetween), 4, RoundingMode.HALF_UP);
        BigDecimal saleOfAll = TiktokCreatorUtil.stringToBigDecimal(dataDTO.getRowData().getTimeAttrTotalOnsiteShoppingValue());
        BigDecimal saleOfAvg = saleOfAll.divide(new BigDecimal(daysBetween), 4, RoundingMode.HALF_UP);

        List<InternetCelebrityAdDailyEntity> dailyEntities = new ArrayList<>((int) daysBetween);
        for (Date tempDate = startDate; tempDate.compareTo(endDate) <= 0; tempDate = DateUtils.addDays(tempDate, 1)) {
            InternetCelebrityAdDailyEntity adDailyEntity = adDailyService.findTopByStoreIdAndAdNoAndVideoNoAndAdDate(Integer.valueOf(dataDTO.getStoreId()), dataDTO.getStatData().getCreativeId(), dataDTO.getVideoId(), tempDate);

            if (Objects.isNull(adDailyEntity)) {
                adDailyEntity = new InternetCelebrityAdDailyEntity();
                adDailyEntity.setStoreId(Integer.valueOf(dataDTO.getStoreId()));
                adDailyEntity.setStoreName(storeName);
                adDailyEntity.setCurrency("");
                adDailyEntity.setAdDate(tempDate);
                adDailyEntity.setAdNo(dataDTO.getStatData().getCreativeId());
                adDailyEntity.setVideoNo(dataDTO.getVideoId());
                adDailyEntity.setCreateBy("doSyncAd");
                adDailyEntity.setLocation(location);
            }
            adDailyEntity.setAdAmount(costOfAvg);
            adDailyEntity.setSaleAmount(saleOfAvg);
            adDailyEntity.setCurrency("");
            adDailyEntity.setUpdateBy("doSyncAd");
            adDailyEntity.setLocation(location);
            adDailyService.saveOrUpdate(adDailyEntity);
            dailyEntities.add(adDailyEntity);
        }
    }

    @Override
    public void syncMaxAd(InternetCelebritySyncMaxAdRequest request) {
        IntranetContext.setIntranet(1);
        log.info("[syncMaxAd] InternetCelebritySyncServiceImpl syncMaxAd request: {} ", JsonMapper.toJson(request));

        SaStoreEntity storeEntity = saStoreService.getById(Integer.valueOf(request.getData().get(0).getStoreId()));
        String location = Objects.isNull(storeEntity) ? LocationEnum.QUANZHOU.name() : storeEntity.getLocation();
        LocationContext.setLocation(location);
        String storeName = Objects.isNull(storeEntity) ? request.getData().get(0).getStoreName() : storeEntity.getErpStoreName();

        request.getData().forEach(dataDTO -> {
            try {
                applicationContext.getBean(InternetCelebritySyncServiceImpl.class).doSyncMaxAd(dataDTO, storeName, location);
            } catch (Exception e) {
                log.info("[同步max广告异常] InternetCelebritySyncServiceImpl syncMaxAd : {} ", JsonMapper.toJson(dataDTO));
                log.error("同步max广告异常", e);
            }
        });
    }

    @Transactional
    public void doSyncMaxAd(InternetCelebritySyncMaxAdRequest.DataDTO dataDTO, String storeName, String location) {
        log.info("[doSyncMaxAd] InternetCelebritySyncServiceImpl doSyncMaxAd dataDTO: {} ", JsonMapper.toJson(dataDTO));
        if (Objects.isNull(dataDTO.getStoreId())) {
            log.info("[doSyncMaxAd error] InternetCelebritySyncServiceImpl doSyncMaxAd : 店铺id为空");
            return;
        } else if (CollUtil.isEmpty(dataDTO.getProducts())) {
            log.info("[doSyncMaxAd error] InternetCelebritySyncServiceImpl doSyncMaxAd : products为空");
            return;
        } else if (StrUtil.isEmpty(dataDTO.getCampaignId())) {
            log.info("[doSyncMaxAd error] InternetCelebritySyncServiceImpl doSyncMaxAd : CampaignId为空");
            return;
        }
        DateTime startDate = DateUtil.parseDate(dataDTO.getAnalysisDate());
        DateTime endDate = StrUtil.isNotEmpty(dataDTO.getAnalysisDateEnd()) ? DateUtil.parseDate(dataDTO.getAnalysisDateEnd()) : startDate;

        long daysBetween = DateUtil.between(startDate, endDate, DateUnit.DAY) + 1;
        BigDecimal costOfAll = TiktokCreatorUtil.stringToBigDecimal(dataDTO.getOverview().getCost());
        BigDecimal costOfAvg = costOfAll.divide(new BigDecimal(daysBetween), 4, RoundingMode.HALF_UP);
        BigDecimal roi = TiktokCreatorUtil.stringToBigDecimal(dataDTO.getOverview().getOnsiteRoi2Shopping());

        BigDecimal salesOfAll = dataDTO.getProducts().stream().map(InternetCelebritySyncMaxAdRequest.DataDTO.ProductsDTO::getCreatives)
                .flatMap(Collection::stream)
                .map(InternetCelebritySyncMaxAdRequest.DataDTO.ProductsDTO.CreativesDTO::getOnsiteRoi2ShoppingValue)
                .filter(StrUtil::isNotEmpty)
                .map(TiktokCreatorUtil::stringToBigDecimal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        boolean saleIsBiggerThanZero = salesOfAll.compareTo(BigDecimal.ZERO) > 0;

        BigDecimal allShowCnt = dataDTO.getProducts().stream().map(InternetCelebritySyncMaxAdRequest.DataDTO.ProductsDTO::getCreatives)
                .flatMap(Collection::stream)
                .map(InternetCelebritySyncMaxAdRequest.DataDTO.ProductsDTO.CreativesDTO::getRoi2ShowCnt)
                .filter(StrUtil::isNotEmpty)
                .map(TiktokCreatorUtil::stringToBigDecimal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);


        List<InternetCelebrityAdDailyEntity> dailyEntities = new ArrayList<>();
        for (Date tempDate = startDate; tempDate.compareTo(endDate) <= 0; tempDate = DateUtils.addDays(tempDate, 1)) {

            Date finalTempDate = tempDate;
            dataDTO.getProducts().forEach(productsDTO -> {
                if (CollUtil.isEmpty(productsDTO.getCreatives())) {
                    log.info("[doSyncMaxAd error] InternetCelebritySyncServiceImpl doSyncMaxAd : creatives为空");
                    return;
                }
                productsDTO.getCreatives().forEach(creativeDTO -> {
                    if ("-1".equals(creativeDTO.getItemId())) {
                        log.info("[doSyncMaxAd error] InternetCelebritySyncServiceImpl doSyncMaxAd : video id is -1");
                        return;
                    }

                    InternetCelebrityAdDailyEntity adDailyEntity = adDailyService.findTopByStoreIdAndAdNoAndVideoNoAndAdDate(Integer.valueOf(dataDTO.getStoreId()), dataDTO.getCampaignId(), creativeDTO.getItemId(), finalTempDate);
                    if (Objects.isNull(adDailyEntity)) {
                        adDailyEntity = new InternetCelebrityAdDailyEntity();
                        adDailyEntity.setStoreId(Integer.valueOf(dataDTO.getStoreId()));
                        adDailyEntity.setStoreName(storeName);
                        adDailyEntity.setAdDate(finalTempDate);
                        adDailyEntity.setAdNo(dataDTO.getCampaignId());
                        adDailyEntity.setVideoNo(creativeDTO.getItemId());
                        adDailyEntity.setCreateBy("doSyncMaxAd");
                        adDailyEntity.setLocation(location);
                    }
                    BigDecimal saleOfAll = StrUtil.isEmpty(creativeDTO.getOnsiteRoi2ShoppingValue()) ? BigDecimal.ZERO : TiktokCreatorUtil.stringToBigDecimal(creativeDTO.getOnsiteRoi2ShoppingValue());
                    BigDecimal sales = saleOfAll.divide(new BigDecimal(daysBetween), 4, RoundingMode.HALF_UP);
                    // 存在收入不为0, 则 收入 / roi
                    // 否则 总消耗 / 所有曝光 * 当前曝光
                    setAdData(creativeDTO, saleIsBiggerThanZero, adDailyEntity, sales, roi, allShowCnt, costOfAvg);
                    adDailyEntity.setCurrency("USD");
                    adDailyEntity.setUpdateBy("doSyncMaxAd");
                    adDailyEntity.setLocation(location);
                    adDailyService.saveOrUpdate(adDailyEntity);
                    dailyEntities.add(adDailyEntity);
                });
            });
        }
        dailyEntities.forEach(item -> internetCelebrityMessageSendService.sendMessage(item.getId(), InternetCelebrityMessageType.AD));
    }

    private static void setAdData(InternetCelebritySyncMaxAdRequest.DataDTO.ProductsDTO.CreativesDTO creativeDTO, boolean saleIsBiggerThanZero, InternetCelebrityAdDailyEntity adDailyEntity, BigDecimal sales, BigDecimal roi, BigDecimal allShowCnt, BigDecimal costOfAvg) {
        if (saleIsBiggerThanZero) {
            adDailyEntity.setAdAmount(sales.divide(roi, 4, RoundingMode.HALF_UP));
        } else {
            if (allShowCnt.compareTo(BigDecimal.ZERO) == 0) {
                adDailyEntity.setAdAmount(BigDecimal.ZERO);
            } else {
                BigDecimal cost = costOfAvg.divide(allShowCnt, 4, RoundingMode.HALF_UP).multiply(TiktokCreatorUtil.stringToBigDecimal(creativeDTO.getRoi2ShowCnt()));
                adDailyEntity.setAdAmount(cost);
            }
        }
        adDailyEntity.setSaleAmount(sales);
        log.info("[max param] InternetCelebritySyncServiceImpl adDailyEntity : {} saleIsBiggerThanZero: {} sales: {} roi: {} allShowCnt: {} costOfAvg: {} Roi2ShowCnt: {}", JsonMapper.toJson(adDailyEntity), saleIsBiggerThanZero, sales, roi, allShowCnt, costOfAvg, creativeDTO.getRoi2ShowCnt());
    }

    @Override
    public InternetCelebrityCreatorNoInfoResponse creatorNoInfoPage(BaseListRequest request) {
        IntranetContext.setIntranet(1);
        log.info("[creatorNoInfoPage] InternetCelebritySyncServiceImpl syncMaxAd request: {} ", JsonMapper.toJson(request));
        Page<InternetCelebrityStoreRelationEntity> relationEntityPage = relationService.pageNoCelebrityId(request);
        InternetCelebrityCreatorNoInfoResponse response = new InternetCelebrityCreatorNoInfoResponse();

        if (CollUtil.isEmpty(relationEntityPage.getRecords())) {
            return response;
        }
        response.setData(relationEntityPage.getRecords().stream().map(relation -> {
            InternetCelebrityCreatorNoInfoResponse.DataDTO dataDTO = new InternetCelebrityCreatorNoInfoResponse.DataDTO();
            dataDTO.setStoreId(relation.getStoreId());
            dataDTO.setStoreName(relation.getStoreName());
            dataDTO.setCreatorNo(relation.getInternetCelebrityNo());
            dataDTO.setCreatorName(relation.getInternetCelebrityName());
            return dataDTO;
        }).collect(Collectors.toList()));
        return response;
    }

    /**
     * 处理网红图片流数据
     *
     * @param imagesData Base64编码的图片流数据
     * @return 处理后的图片URL
     */
    private String processImagesData(String imagesData) {
        if (StringUtils.isNotBlank(imagesData)) {
            try {
                // 获取OSS配置
                OssRequest ossRequest = new OssRequest();
                ossRequest.setBucket("nsy-erp-public");
                ossRequest.setFromService("api-oms");
                ossRequest.setNum(1);
                List<String> url = userApiService.getOss(ossRequest).getUrl();

                // 解码Base64编码的图片字符串，将字节数组转换为输入流
                ByteArrayInputStream imageStream = new ByteArrayInputStream(Base64.getDecoder().decode(imagesData));

                // 上传到OSS并返回URL
                return aliYunOssService.putObjectAndGetUrl(imageStream, aliYunOssService.generateRandomFileName(".jpg", CollectionUtils.isEmpty(url) ? "" : url.get(0)), "oss-cn-hangzhou.aliyuncs.com");
            } catch (Exception e) {
                log.error("处理网红图片流数据失败", e);
            }
        }
        return "";
    }


}