package com.nsy.oms.business.service.stock.impl;

import com.nsy.oms.business.domain.stock.StockInfo;
import com.nsy.oms.enums.stock.StockBrandTypeEnum;
import com.nsy.oms.enums.stock.StockOperateTypeEnum;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/*
 * 获取库存，由于预配
 * */
@Service
public class StockPrematchService {

    public List<StockInfo> getStockList(Integer storeId, Integer spaceId, List<Integer> specIdList, StockOperateTypeEnum stockOperateTypeEnum, StockBrandTypeEnum stockBrandTypeEnum) {
        List<StockInfo> stockInfoList = new ArrayList<>();

        switch（stockOperateTypeEnum）{
            case StockOperateTypeEnum.OVERSEAS_RESTOCK:

                break;
            default:
        }

        return stockInfoList;
    }

    private List<StockInfo> getSkuStockInfo
}
