package com.nsy.oms.business.service.download.points;

import cn.hutool.core.date.DateUtil;
import com.nsy.api.core.apicore.response.CustomExcelResponse;
import com.nsy.api.core.apicore.util.NsyExcelUtil;
import com.nsy.api.oms.enums.QuartzDownloadQueueTypeEnum;
import com.nsy.oms.business.domain.download.points.B2bPointsRefundExport;
import com.nsy.oms.business.domain.request.download.DownloadRequest;
import com.nsy.oms.business.domain.request.points.B2bPointsRefundPageRequest;
import com.nsy.oms.business.domain.response.base.DownloadResponse;
import com.nsy.oms.business.domain.response.base.PageResponse;
import com.nsy.oms.business.domain.response.points.B2bPointsRefundItemVO;
import com.nsy.oms.business.domain.response.points.B2bPointsRefundVO;
import com.nsy.oms.business.service.download.IDownloadService;
import com.nsy.oms.business.service.points.IB2bPointsRefundService;
import com.nsy.oms.utils.JsonMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * @description:
 * @author: linCheng
 * @create: 2025-05-08 09:32
 **/
@Slf4j
@Service
public class B2bPointsRefundDownloadServiceImpl implements IDownloadService {

    @Resource
    private IB2bPointsRefundService b2bPointsRefundService;

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.OMS_B2B_POINTS_REFUND;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        log.info("B2bPointsRefundDownloadServiceImpl-DownloadRequest:{}", JsonMapper.toJson(request));
        DownloadResponse response = new DownloadResponse();

        B2bPointsRefundPageRequest b2bPointsRefundPageRequest = JsonMapper.fromJson(request.getRequestContent(), B2bPointsRefundPageRequest.class);
        b2bPointsRefundPageRequest.setPageIndex(request.getPageIndex());
        b2bPointsRefundPageRequest.setPageSize(request.getPageSize());
        PageResponse<B2bPointsRefundVO> b2bPointsRefundPageResponse = b2bPointsRefundService.page(b2bPointsRefundPageRequest);
        Collection<B2bPointsRefundVO> b2bPointsRefundVOList = b2bPointsRefundPageResponse.getContent();
        if (CollectionUtils.isEmpty(b2bPointsRefundVOList)) {
            return DownloadResponse.of("[]", 0L);
        }

        List<List<Object>> data = new ArrayList<>();
        b2bPointsRefundVOList.forEach(b2bPointsRefundVO -> {
            List<B2bPointsRefundItemVO> b2bPointsRefundItemVOList = b2bPointsRefundVO.getB2bPointsRefundItemVOList();
            if (CollectionUtils.isEmpty(b2bPointsRefundItemVOList)) {
                data.add(NsyExcelUtil.getData(B2bPointsRefundExport.class, getB2bPointsRefundExport(b2bPointsRefundVO, null)));
            } else {
                b2bPointsRefundItemVOList.forEach(b2bPointsRefundItemVO -> {
                    data.add(NsyExcelUtil.getData(B2bPointsRefundExport.class, getB2bPointsRefundExport(b2bPointsRefundVO, b2bPointsRefundItemVO)));
                });
            }
        });

        CustomExcelResponse excelResponse = new CustomExcelResponse();
        excelResponse.setHeaders(NsyExcelUtil.getCommonHeads(B2bPointsRefundExport.class));
        excelResponse.setData(data);
        response.setTotalCount(b2bPointsRefundPageResponse.getTotalCount());
        response.setDataJsonStr(JsonMapper.toJson(excelResponse));
        log.info("B2bPointsRefundDownloadServiceImpl-DownloadResponse:{}", JsonMapper.toJson(response));
        return response;
    }

    private B2bPointsRefundExport getB2bPointsRefundExport(B2bPointsRefundVO b2bPointsRefundVO, B2bPointsRefundItemVO b2bPointsRefundItemVO) {
        B2bPointsRefundExport b2bPointsRefundExport = new B2bPointsRefundExport();
        Optional.ofNullable(b2bPointsRefundVO).ifPresent(refund -> {
            b2bPointsRefundExport.setRefundId(Optional.ofNullable(refund.getRefundId()).isPresent() ? refund.getRefundId() : 0);
            b2bPointsRefundExport.setAccountCreateTime(Optional.ofNullable(refund.getAccountCreateTime()).isPresent() ? DateUtil.format(refund.getAccountCreateTime(), "yyyy-MM-dd") : null);
            b2bPointsRefundExport.setCustomerName(StringUtils.isNotEmpty(refund.getCustomerName()) ? refund.getCustomerName() : "");
            b2bPointsRefundExport.setCustomerEmail(StringUtils.isNotEmpty(refund.getCustomerEmail()) ? refund.getCustomerEmail() : "");
            b2bPointsRefundExport.setStoreName(StringUtils.isNotEmpty(refund.getStoreName()) ? refund.getStoreName() : "");
            b2bPointsRefundExport.setSalesman(StringUtils.isNotEmpty(refund.getSalesman()) ? refund.getSalesman() : "");
            b2bPointsRefundExport.setLastOrderTime(Optional.ofNullable(refund.getAccountCreateTime()).isPresent() ? DateUtil.format(refund.getAccountCreateTime(), "yyyy-MM-dd") : null);
            b2bPointsRefundExport.setPointsRefunded(Optional.ofNullable(refund.getPointsRefunded()).isPresent() ? refund.getPointsRefunded() : 0);
            b2bPointsRefundExport.setAmountRefunded(Optional.ofNullable(refund.getAmountRefunded()).isPresent() ? refund.getAmountRefunded() : BigDecimal.ZERO);
            b2bPointsRefundExport.setState(StringUtils.isNotEmpty(refund.getState()) ? refund.getState() : "");
        });
        Optional.ofNullable(b2bPointsRefundItemVO).ifPresent(item -> {
            b2bPointsRefundExport.setOrderNo(StringUtils.isNotEmpty(item.getOrderNo()) ? item.getOrderNo() : "");
            b2bPointsRefundExport.setSerialNumber(StringUtils.isNotEmpty(item.getSerialNumber()) ? item.getSerialNumber() : "");
            b2bPointsRefundExport.setRefundPlatform(StringUtils.isNotEmpty(item.getRefundPlatform()) ? item.getRefundPlatform() : "");
            b2bPointsRefundExport.setRefundAmount(Optional.ofNullable(item.getRefundAmount()).isPresent() ? item.getRefundAmount() : BigDecimal.ZERO);
            b2bPointsRefundExport.setRefundStatus(StringUtils.isNotEmpty(item.getRefundStatus()) ? item.getRefundStatus() : "");
            b2bPointsRefundExport.setRefundOperator(StringUtils.isNotEmpty(item.getRefundOperator()) ? item.getRefundOperator() : "");
            b2bPointsRefundExport.setUpdateDate(Optional.ofNullable(item.getUpdateDate()).isPresent() ? DateUtil.format(item.getUpdateDate(), "yyyy-MM-dd") : null);
            b2bPointsRefundExport.setRemark(StringUtils.isNotEmpty(item.getRemark()) ? item.getRemark() : "");
        });
        return b2bPointsRefundExport;
    }
}
