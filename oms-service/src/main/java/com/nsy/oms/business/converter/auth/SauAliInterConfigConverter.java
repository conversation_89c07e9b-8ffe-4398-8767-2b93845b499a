package com.nsy.oms.business.converter.auth;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.oms.business.domain.request.auth.SauAliInterConfigRequest;
import com.nsy.oms.business.domain.response.auth.SauAliInterConfigPageResponse;
import com.nsy.oms.business.domain.response.auth.SauAliInterConfigResponse;
import com.nsy.oms.repository.entity.auth.SauAliInterConfigEntity;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SauAliInterConfigConverter {

    SauAliInterConfigConverter INSTANCE = Mappers.getMapper(SauAliInterConfigConverter.class);

    SauAliInterConfigPageResponse toSauAliInterConfigPageResponse(SauAliInterConfigEntity bo);

    SauAliInterConfigEntity toSauAliInterConfigEntity(SauAliInterConfigEntity bo);

    SauAliInterConfigEntity toSauAliInterConfigEntity(SauAliInterConfigRequest request);

    SauAliInterConfigResponse toSauAliInterConfigResponse(SauAliInterConfigEntity bo);

    @InheritConfiguration(name = "toSauAliInterConfigPageResponse")
    Page<SauAliInterConfigPageResponse> toSauAliInterConfigPageResponsePage(IPage<SauAliInterConfigEntity> page);

}
