package com.nsy.oms.business.domain.request.celebrity;

import com.nsy.oms.business.domain.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author: lhh
 * @Date: 2025-04-18
 * @Description: 网红查询请求类
 */
@ApiModel("达人建联列表查询req")
public class InternetCelebrityRelationPageRequest extends PageRequest {
    @ApiModelProperty("ids")
    private List<Integer> ids;

    @ApiModelProperty("达人账号")
    private String internetCelebrityName;

    @ApiModelProperty("达人账号")
    private List<String> internetCelebrityNameList;

    @ApiModelProperty("视频id")
    private String videoCode;

    @ApiModelProperty("视频id")
    private List<String> videoCodeList;

    @ApiModelProperty("订单id")
    private String platformOrderNo;

    @ApiModelProperty("订单id")
    private List<String> platformOrderNoList;

    @ApiModelProperty("skc")
    private String skc;

    @ApiModelProperty("广告意图")
    private List<String> adIntention;

    @ApiModelProperty("视频授权")
    private List<String> videoAuthorization;

    @ApiModelProperty("建联结果")
    private List<String> relationStatus;

    @ApiModelProperty("客服邮箱")
    private List<String> bdEmailList;

    @ApiModelProperty("店铺")
    private List<Integer> relateStoreIdList;

    @ApiModelProperty("卖家备注")
    private String remark;

    @ApiModelProperty("达人画像")
    private String internetCelebrityInfo;

    @ApiModelProperty("达人Email")
    private String email;


    @ApiModelProperty("最小视频gmv")
    private BigDecimal minGmv;

    @ApiModelProperty("最大视频gmv")
    private BigDecimal maxGmv;

    @ApiModelProperty("建联开始日期")
    private Date relationStartDate;

    @ApiModelProperty("建联结束日期")
    private Date relationEndDate;

    @ApiModelProperty("下单日期")
    private Date orderCreateStartDate;

    @ApiModelProperty("下单日期")
    private Date orderCreateEndDate;

    @ApiModelProperty("妥投时间")
    private Date orderCompromiseStartDate;

    @ApiModelProperty("妥投时间")
    private Date orderCompromiseEndDate;

    @ApiModelProperty("公海未归属")
    private Integer bdNotCare;

    @ApiModelProperty("bdId")
    private Integer bdId;

    @ApiModelProperty("hasAdCode")
    private Integer hasAdCode;

    @ApiModelProperty("快速筛选")
    private List<String> quickSearchTypeList;

    @ApiModelProperty("客服")
    private List<Integer> bdIdList;

    public Integer getHasAdCode() {
        return hasAdCode;
    }

    public void setHasAdCode(Integer hasAdCode) {
        this.hasAdCode = hasAdCode;
    }

    public List<Integer> getBdIdList() {
        return bdIdList;
    }

    public void setBdIdList(List<Integer> bdIdList) {
        this.bdIdList = bdIdList;
    }

    public Integer getBdNotCare() {
        return bdNotCare;
    }

    public void setBdNotCare(Integer bdNotCare) {
        this.bdNotCare = bdNotCare;
    }

    public Integer getBdId() {
        return bdId;
    }

    public void setBdId(Integer bdId) {
        this.bdId = bdId;
    }

    public Date getOrderCreateStartDate() {
        return orderCreateStartDate;
    }

    public void setOrderCreateStartDate(Date orderCreateStartDate) {
        this.orderCreateStartDate = orderCreateStartDate;
    }

    public Date getOrderCreateEndDate() {
        return orderCreateEndDate;
    }

    public void setOrderCreateEndDate(Date orderCreateEndDate) {
        this.orderCreateEndDate = orderCreateEndDate;
    }

    public Date getOrderCompromiseStartDate() {
        return orderCompromiseStartDate;
    }

    public void setOrderCompromiseStartDate(Date orderCompromiseStartDate) {
        this.orderCompromiseStartDate = orderCompromiseStartDate;
    }

    public Date getOrderCompromiseEndDate() {
        return orderCompromiseEndDate;
    }

    public void setOrderCompromiseEndDate(Date orderCompromiseEndDate) {
        this.orderCompromiseEndDate = orderCompromiseEndDate;
    }

    public List<String> getQuickSearchTypeList() {
        return quickSearchTypeList;
    }

    public void setQuickSearchTypeList(List<String> quickSearchTypeList) {
        this.quickSearchTypeList = quickSearchTypeList;
    }

    public List<Integer> getIds() {
        return ids;
    }

    public void setIds(List<Integer> ids) {
        this.ids = ids;
    }

    public String getInternetCelebrityName() {
        return internetCelebrityName;
    }

    public void setInternetCelebrityName(String internetCelebrityName) {
        this.internetCelebrityName = internetCelebrityName;
    }

    public List<String> getInternetCelebrityNameList() {
        return internetCelebrityNameList;
    }

    public void setInternetCelebrityNameList(List<String> internetCelebrityNameList) {
        this.internetCelebrityNameList = internetCelebrityNameList;
    }

    public String getVideoCode() {
        return videoCode;
    }

    public void setVideoCode(String videoCode) {
        this.videoCode = videoCode;
    }

    public List<String> getVideoCodeList() {
        return videoCodeList;
    }

    public void setVideoCodeList(List<String> videoCodeList) {
        this.videoCodeList = videoCodeList;
    }

    public String getPlatformOrderNo() {
        return platformOrderNo;
    }

    public void setPlatformOrderNo(String platformOrderNo) {
        this.platformOrderNo = platformOrderNo;
    }

    public List<String> getPlatformOrderNoList() {
        return platformOrderNoList;
    }

    public void setPlatformOrderNoList(List<String> platformOrderNoList) {
        this.platformOrderNoList = platformOrderNoList;
    }

    public String getSkc() {
        return skc;
    }

    public void setSkc(String skc) {
        this.skc = skc;
    }

    public List<String> getAdIntention() {
        return adIntention;
    }

    public void setAdIntention(List<String> adIntention) {
        this.adIntention = adIntention;
    }

    public List<String> getVideoAuthorization() {
        return videoAuthorization;
    }

    public void setVideoAuthorization(List<String> videoAuthorization) {
        this.videoAuthorization = videoAuthorization;
    }

    public List<String> getRelationStatus() {
        return relationStatus;
    }

    public void setRelationStatus(List<String> relationStatus) {
        this.relationStatus = relationStatus;
    }

    public List<String> getBdEmailList() {
        return bdEmailList;
    }

    public void setBdEmailList(List<String> bdEmailList) {
        this.bdEmailList = bdEmailList;
    }

    public List<Integer> getRelateStoreIdList() {
        return relateStoreIdList;
    }

    public void setRelateStoreIdList(List<Integer> relateStoreIdList) {
        this.relateStoreIdList = relateStoreIdList;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getInternetCelebrityInfo() {
        return internetCelebrityInfo;
    }

    public void setInternetCelebrityInfo(String internetCelebrityInfo) {
        this.internetCelebrityInfo = internetCelebrityInfo;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public BigDecimal getMinGmv() {
        return minGmv;
    }

    public void setMinGmv(BigDecimal minGmv) {
        this.minGmv = minGmv;
    }

    public BigDecimal getMaxGmv() {
        return maxGmv;
    }

    public void setMaxGmv(BigDecimal maxGmv) {
        this.maxGmv = maxGmv;
    }

    public Date getRelationStartDate() {
        return relationStartDate;
    }

    public void setRelationStartDate(Date relationStartDate) {
        this.relationStartDate = relationStartDate;
    }

    public Date getRelationEndDate() {
        return relationEndDate;
    }

    public void setRelationEndDate(Date relationEndDate) {
        this.relationEndDate = relationEndDate;
    }
}
