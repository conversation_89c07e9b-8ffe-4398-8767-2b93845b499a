package com.nsy.oms.business.service.celebrity.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.oms.business.domain.request.celebrity.SetDateRequest;
import com.nsy.oms.business.service.celebrity.InternetCelebrityOrderItemPostMappingService;
import com.nsy.oms.repository.entity.celebrity.InternetCelebrityOrderItemPostMappingEntity;
import com.nsy.oms.repository.sql.mapper.celebrity.InternetCelebrityOrderItemPostMappingMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
public class InternetCelebrityOrderItemPostMappingServiceImpl
        extends ServiceImpl<InternetCelebrityOrderItemPostMappingMapper, InternetCelebrityOrderItemPostMappingEntity>
        implements InternetCelebrityOrderItemPostMappingService {

    @Override
    public InternetCelebrityOrderItemPostMappingEntity findTopByPostId(Integer postId) {
        LambdaQueryWrapper<InternetCelebrityOrderItemPostMappingEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(InternetCelebrityOrderItemPostMappingEntity::getInternetCelebritySampleOrderItemPostId, postId).last("limit 1");
        return this.getOne(lambdaQueryWrapper);
    }

    @Override
    public InternetCelebrityOrderItemPostMappingEntity findTopByOrderItemId(Integer orderItemId) {
        LambdaQueryWrapper<InternetCelebrityOrderItemPostMappingEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(InternetCelebrityOrderItemPostMappingEntity::getInternetCelebritySampleOrderItemId, orderItemId).last("limit 1");
        return this.getOne(lambdaQueryWrapper);
    }

    @Override
    public List<InternetCelebrityOrderItemPostMappingEntity> findAllByRelationId(Integer relationId) {
        LambdaQueryWrapper<InternetCelebrityOrderItemPostMappingEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(InternetCelebrityOrderItemPostMappingEntity::getStoreRelationId, relationId);
        return this.list(lambdaQueryWrapper);
    }

    @Override
    public List<InternetCelebrityOrderItemPostMappingEntity> findAllByRelationIdIn(List<Integer> relationIds) {
        LambdaQueryWrapper<InternetCelebrityOrderItemPostMappingEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(InternetCelebrityOrderItemPostMappingEntity::getStoreRelationId, relationIds);
        return this.list(lambdaQueryWrapper);
    }

    @Override
    public List<InternetCelebrityOrderItemPostMappingEntity> findAllByIdGT(Integer id, Integer size) {
        LambdaQueryWrapper<InternetCelebrityOrderItemPostMappingEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.gt(InternetCelebrityOrderItemPostMappingEntity::getId, id)
                .last("limit " + size);
        return this.list(lambdaQueryWrapper);
    }

    @Override
    public List<InternetCelebrityOrderItemPostMappingEntity> findAllByOrderItemIdIn(List<Integer> orderItemIdList) {
        LambdaQueryWrapper<InternetCelebrityOrderItemPostMappingEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(InternetCelebrityOrderItemPostMappingEntity::getInternetCelebritySampleOrderItemId, orderItemIdList);
        return this.list(lambdaQueryWrapper);
    }

    @Transactional
    @Override
    public void setRelationDate(SetDateRequest request) {
        if (CollectionUtils.isEmpty(request.getMappingIds())) {
            throw new BusinessServiceException("无建联id");
        }
        List<InternetCelebrityOrderItemPostMappingEntity> mappingEntityList = listByIds(request.getMappingIds());
        if (CollectionUtils.isEmpty(mappingEntityList)) {
            throw new BusinessServiceException("无建联关系");
        }
        mappingEntityList.forEach(s -> s.setRelationDate(request.getDate()));
        this.updateBatchById(mappingEntityList);
    }
}