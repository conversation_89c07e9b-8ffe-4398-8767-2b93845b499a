package com.nsy.oms.business.service.upload.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.pms.dto.product.ProductSpecDTO;
import com.nsy.oms.business.domain.request.upload.UploadRequest;
import com.nsy.oms.business.domain.response.upload.UploadResponse;
import com.nsy.oms.business.domain.upload.OrderRuleCodeImport;
import com.nsy.oms.business.manage.erp.ErpApiService;
import com.nsy.oms.business.manage.erp.domain.SpaceInfo;
import com.nsy.oms.business.manage.erp.response.SpaceInfoResponse;
import com.nsy.oms.business.manage.pms.PmsApiService;
import com.nsy.oms.business.service.bd.BdOrderRuleCodeService;
import com.nsy.oms.business.service.upload.IUploadService;
import com.nsy.oms.constants.upload.QuartzUploadQueueTypeEnum;
import com.nsy.oms.enumstable.OrderRuleCodeRuleTypeEnum;
import com.nsy.oms.repository.entity.bd.BdOrderRuleCodeEntity;
import com.nsy.oms.utils.JsonMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: linCheng
 * @create: 2023-10-23 15:53
 **/
@Slf4j
@Service
public class OrderRuleCodeBySpaceRemoveUploadService implements IUploadService {

    @Autowired
    private ErpApiService erpApiService;
    @Autowired
    private PmsApiService pmsApiService;
    @Autowired
    private BdOrderRuleCodeService bdOrderRuleService;

    @Override
    public QuartzUploadQueueTypeEnum uploadType() {
        return QuartzUploadQueueTypeEnum.ORDER_RULE_CODE_BY_SPACE_REMOVE;
    }


    @Override
    public UploadResponse processUploadData(UploadRequest request) {
        log.error("OrderRuleCodeBySpaceRemoveUploadService.processUploadData.request:{}", JSON.toJSONString(request));
        UploadResponse uploadResponse = new UploadResponse();
        if (!StringUtils.hasText(request.getDataJsonStr()) || !StringUtils.hasText(request.getUploadParams())) {
            return uploadResponse;
        }

        List<OrderRuleCodeImport> importList = JsonMapper.jsonStringToObjectArray(request.getDataJsonStr(), OrderRuleCodeImport.class).stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(importList)) {
            return uploadResponse;
        }

        Map paramMap = JsonMapper.fromJson(request.getUploadParams(), Map.class);
        String orderRuleId = (String) paramMap.get("orderRuleId");
        String codeType = (String) paramMap.get("codeType");
        if (!StringUtils.hasText(orderRuleId)) {
            throw new BusinessServiceException("订单规则不能为空");
        }
        if (!StringUtils.hasText(codeType)) {
            throw new BusinessServiceException("订单规则编码类型不能为空");
        }

        //校验
        SpaceInfoResponse spaceInfoResponse = erpApiService.querySpaceInfoList(Collections.singletonList(request.getLocation().toUpperCase(Locale.ROOT)), "HWDF");
        Map<String, Integer> spaceMap = spaceInfoResponse.getSpaceInfoList().stream().collect(Collectors.toMap(SpaceInfo::getSpaceName, SpaceInfo::getSpaceId, (value1, value2) -> value2));
        List<OrderRuleCodeImport> errorList = new ArrayList<>();
        List<BdOrderRuleCodeEntity> bdOrderRuleCodeEntities = check(orderRuleId, spaceMap, codeType, importList, errorList);
        if (CollectionUtils.isNotEmpty(errorList)) {
            uploadResponse.setDataJsonStr(JsonMapper.toJson(errorList));
            return uploadResponse;
        }

        //存数据
        process(bdOrderRuleCodeEntities);

        return uploadResponse;
    }


    private List<BdOrderRuleCodeEntity> check(String orderRuleId,
                                              Map<String, Integer> spaceMap,
                                              String codeType,
                                              List<OrderRuleCodeImport> importList,
                                              List<OrderRuleCodeImport> errorList) {
        List<String> codeList = new ArrayList<>();
        if ("skc".equalsIgnoreCase(codeType)) {
            codeList = pmsApiService.getSkcInfo(importList.stream().map(OrderRuleCodeImport::getCode).collect(Collectors.toList())).stream().map(ProductSpecDTO::getColorSku).distinct().collect(Collectors.toList());
        } else if ("sku".equalsIgnoreCase(codeType)) {
            codeList = pmsApiService.specInfo(importList.stream().map(OrderRuleCodeImport::getCode).collect(Collectors.toList())).stream().map(ProductSpecDTO::getSpecSku).distinct().collect(Collectors.toList());
        }

        List<BdOrderRuleCodeEntity> bdOrderRuleCodeEntityList = bdOrderRuleService.getByOrderRuleIdAndCodeTypeAndRuleType(Integer.valueOf(orderRuleId), codeType, OrderRuleCodeRuleTypeEnum.SPACE.getCode());
        Map<String, BdOrderRuleCodeEntity> bdOrderRuleMap = bdOrderRuleCodeEntityList.stream().collect(Collectors.toMap(item -> StrUtil.format("{}_{}", item.getSpaceName(), item.getCode()), Function.identity(), (k1, k2) -> k1));

        List<BdOrderRuleCodeEntity> bdOrderRuleCodeEntities = new ArrayList<>();
        for (OrderRuleCodeImport orderRuleCodeImport : importList) {
            BdOrderRuleCodeEntity bdOrderRuleCodeEntity = bdOrderRuleMap.get(StrUtil.format("{}_{}", orderRuleCodeImport.getSpaceName(), orderRuleCodeImport.getCode()));
            if (!Optional.ofNullable(bdOrderRuleCodeEntity).isPresent()) {
                orderRuleCodeImport.setErrorMsg(String.format("%s/海外仓不存在", codeType));
                errorList.add(orderRuleCodeImport);
                continue;
            }
            if (!Optional.ofNullable(spaceMap.get(orderRuleCodeImport.getSpaceName())).isPresent()) {
                orderRuleCodeImport.setErrorMsg("海外仓不存在");
                errorList.add(orderRuleCodeImport);
                continue;
            }
            if (!codeList.contains(orderRuleCodeImport.getCode())) {
                orderRuleCodeImport.setErrorMsg(String.format("%s不存在", codeType));
                errorList.add(orderRuleCodeImport);
                continue;
            }
            bdOrderRuleCodeEntities.add(bdOrderRuleCodeEntity);
        }
        return bdOrderRuleCodeEntities;
    }


    private void process(List<BdOrderRuleCodeEntity> bdOrderRuleSkcEntities) {
        bdOrderRuleService.removeByIds(bdOrderRuleSkcEntities.stream().map(BdOrderRuleCodeEntity::getBdOrderRuleCodeId).collect(Collectors.toList()));
    }

}
