package com.nsy.oms.business.manage.bi;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.nsy.api.core.apicore.base.BaseListResponse;
import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.api.core.apicore.util.NsyJacksonUtils;
import com.nsy.oms.business.domain.request.stockout.GetBySkuBySkcRequest;
import com.nsy.oms.business.domain.request.stockout.OverseaSkcPageRequest;
import com.nsy.oms.business.domain.response.stockout.OverseaSkcPageResponse;
import com.nsy.oms.business.domain.response.stockout.OverseaSkuStoreSpaceStockoutResponse;
import com.nsy.oms.business.manage.bi.domain.BiDataBackFlowRecord;
import com.nsy.oms.business.manage.bi.domain.ReportStoreSkcSale;
import com.nsy.oms.business.manage.bi.request.AdsSkuStoreSalesForecastRequest;
import com.nsy.oms.business.manage.bi.request.BiBackFlowDataRequest;
import com.nsy.oms.business.manage.bi.request.ReportSkcSaleBetweenDaysRequest;
import com.nsy.oms.business.manage.bi.request.ReportSkcSaleRequest;
import com.nsy.oms.business.manage.bi.response.BiFbaOnTheWayModel;
import com.nsy.oms.business.manage.bi.response.BiForecastSalesModel;
import com.nsy.oms.business.manage.bi.response.BiHistorySalesModel;
import com.nsy.oms.business.manage.bi.response.BiSkuStoreStockModel;
import com.nsy.oms.business.manage.bi.response.fact.FactAmazonProfitabilityAnalysisSpuResponse;
import com.nsy.oms.business.manage.search.request.BaseListRequest;
import com.nsy.oms.constants.StringConstant;
import com.nsy.oms.utils.Json;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.inject.Inject;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class BiApiService {
    @Inject
    private RestTemplate restTemplate;
    @Value("${nsy.service.url.bi}")
    private String biServiceUrl;

    public List<BiForecastSalesModel> getForecastSales(Integer storeId, String skc, Date startDate, Date endDate) {
        AdsSkuStoreSalesForecastRequest request = buildAdsSkuStoreSalesForecastRequest(storeId, skc, startDate, endDate);
        String uri = String.format("%s/sales-forecast/list", biServiceUrl);
        try {
            ResponseEntity<String> respEntity = this.restTemplate.postForEntity(uri, request, String.class);
            if (respEntity.getBody() != null) {
                List<BiForecastSalesModel> list = NsyJacksonUtils.jsonToList(respEntity.getBody(), BiForecastSalesModel.class);
                list.forEach(f -> f.setBusinessDateStr(DateUtil.format(f.getBusinessDate(), StringConstant.DATE_FORMAT_DATE)));
                return list;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public List<BiHistorySalesModel> getHistorySales(Integer storeId, String skc, Date startDate, Date endDate) {
        AdsSkuStoreSalesForecastRequest request = buildAdsSkuStoreSalesForecastRequest(storeId, skc, startDate, endDate);
        String uri = String.format("%s/sales-history/list", biServiceUrl);
        try {
            ResponseEntity<String> respEntity = this.restTemplate.postForEntity(uri, request, String.class);
            if (respEntity.getBody() != null) {
                List<BiHistorySalesModel> list = NsyJacksonUtils.jsonToList(respEntity.getBody(), BiHistorySalesModel.class);
                list.forEach(f -> f.setSaleDateStr(DateUtil.format(f.getSaleDate(), StringConstant.DATE_FORMAT_DATE)));
                return list;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    private AdsSkuStoreSalesForecastRequest buildAdsSkuStoreSalesForecastRequest(Integer storeId, String skc, Date startDate, Date endDate) {
        AdsSkuStoreSalesForecastRequest request = new AdsSkuStoreSalesForecastRequest();
        request.setSkc(skc);
        request.setStoreId(storeId);
        request.setStartDate(startDate);
        request.setEndDate(endDate);
        return request;
    }

    public String getFirstOrderDate(Integer storeId, String skc) {
        String uri = String.format("%s/first-order-date?storeId=%s&skc=%s", biServiceUrl, storeId, skc);
        try {
            ResponseEntity<String> respEntity = this.restTemplate.getForEntity(uri, String.class);
            return respEntity.getBody();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    public BiSkuStoreStockModel getSkuStoreStock(Integer storeId, String sku) {
        String uri = String.format("%s/fba-sku-stock?storeId=%s&sku=%s", biServiceUrl, storeId, sku);
        try {
            ResponseEntity<BiSkuStoreStockModel> respEntity = this.restTemplate.getForEntity(uri, BiSkuStoreStockModel.class);
            return respEntity.getBody();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    public List<BiFbaOnTheWayModel> getFbaOnTheWay(Integer storeId, String sku) {
        String uri = String.format("%s/fba-on-the-way?storeId=%s&sku=%s", biServiceUrl, storeId, sku);
        try {
            ResponseEntity<String> respEntity = this.restTemplate.getForEntity(uri, String.class);
            if (respEntity.getBody() != null) {
                List<BiFbaOnTheWayModel> list = NsyJacksonUtils.jsonToList(respEntity.getBody(), BiFbaOnTheWayModel.class);
                list.forEach(f -> f.setArrivalDateStr(DateUtil.format(f.getArrivalDate(), StringConstant.DATE_FORMAT_DATE)));
                return list;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public List<String> queryNewProductByStore(Integer storeId) {
        String uri = String.format("%s/report/query-new-product?storeId=%s", biServiceUrl, storeId);
        log.info("[rpc请求] BiApiService queryNewProductByStore storeId: {} timestamp: {}", JSON.toJSONString(storeId), LocalDateTime.now());
        try {
            ResponseEntity<BaseListResponse> respEntity = this.restTemplate.getForEntity(uri, BaseListResponse.class);
            if (Objects.nonNull(respEntity.getBody())) {
                log.info("[rpc响应] BiApiService queryNewProductByStore respEntity: {} timestamp: {}", JSON.toJSONString(respEntity), LocalDateTime.now());
                return respEntity.getBody().getContent();
            }
        } catch (Exception e) {
            log.error(String.format("查询店铺新品异常 [%s]", storeId), e);
        }
        return Collections.emptyList();
    }

    public List<ReportStoreSkcSale> querySaleBetweenDays(Integer storeId, List<String> skcList, Date startDate, Date endDate) {
        if (CollectionUtils.isEmpty(skcList)) return Collections.emptyList();
        ReportSkcSaleBetweenDaysRequest request = ReportSkcSaleBetweenDaysRequest.builder()
                .storeId(storeId)
                .skcList(skcList)
                .startDate(startDate)
                .endDate(endDate)
                .build();
        String uri = String.format("%s/report/query-sale-between-days", biServiceUrl);
        log.info("[rpc请求] BiApiService querySaleBetweenDays request: {} timestamp: {}", JSON.toJSONString(request), LocalDateTime.now());
        try {
            ResponseEntity<BaseListResponse> respEntity = this.restTemplate.postForEntity(uri, request, BaseListResponse.class);
            if (Objects.nonNull(respEntity.getBody())) {
                log.info("[rpc响应] BiApiService querySaleBetweenDays respEntity: {} timestamp: {}", JSON.toJSONString(respEntity), LocalDateTime.now());
                return Json.listToObject(respEntity.getBody().getContent(), ReportStoreSkcSale.class);
            }
        } catch (Exception e) {
            log.error(String.format("查询店铺 销量异常 [%s]", storeId), e);
        }
        return Collections.emptyList();
    }

    public List<ReportStoreSkcSale> queryNewProductSale(Integer storeId, List<String> skcList) {
        if (CollectionUtils.isEmpty(skcList)) return Collections.emptyList();
        ReportSkcSaleRequest request = ReportSkcSaleRequest.builder()
                .storeId(storeId)
                .skcList(skcList)
                .build();
        String uri = String.format("%s/report/query-sale", biServiceUrl);
        log.info("[rpc请求] BiApiService queryNewProductSale request: {} timestamp: {}", JSON.toJSONString(request), LocalDateTime.now());
        try {
            ResponseEntity<BaseListResponse> respEntity = this.restTemplate.postForEntity(uri, request, BaseListResponse.class);
            if (Objects.nonNull(respEntity.getBody())) {
                log.info("[rpc响应] BiApiService queryNewProductSale respEntity: {} timestamp: {}", JSON.toJSONString(respEntity), LocalDateTime.now());
                return Json.listToObject(respEntity.getBody().getContent(), ReportStoreSkcSale.class);
            }
        } catch (Exception e) {
            log.error(String.format("查询店铺 新品销量异常 [%s]", storeId), e);
        }
        return Collections.emptyList();
    }

    public PageResponse<OverseaSkcPageResponse> getOverseaSkcPage(OverseaSkcPageRequest request) {
        String uri = String.format("%s/oversea-stockout/page", biServiceUrl);
        try {
            log.info(String.format("getOverseaSkcPage: %s, request: %s", uri, NsyJacksonUtils.toJson(request)));
            ResponseEntity<String> respEntity = this.restTemplate.postForEntity(uri, request, String.class);
            log.info(String.format("getOverseaSkcPage: %s, response: %s", uri, NsyJacksonUtils.toJson(respEntity)));
            if (StringUtils.isNotBlank(respEntity.getBody())) {
                return NsyJacksonUtils.toObj(respEntity.getBody(), new TypeReference<PageResponse<OverseaSkcPageResponse>>() { });
            }
        } catch (Exception e) {
            log.error(String.format("getOverseaSkcPage: %s, msg: %s", uri, e.getMessage()), e);
        }
        return PageResponse.of(new ArrayList<>(), 0L);
    }

    public List<String> getSkuBySkc(GetBySkuBySkcRequest getBySkuBySkcRequest) {
        String uri = String.format("%s/oversea-stockout/getSkuBySkc", biServiceUrl);
        try {
            log.info(String.format("getSkuBySkc: %s, request: %s", uri, NsyJacksonUtils.toJson(getBySkuBySkcRequest)));
            ResponseEntity<String> respEntity = this.restTemplate.postForEntity(uri, getBySkuBySkcRequest, String.class);
            log.info("getSkuBySkc response: {}", JSON.toJSONString(respEntity));
            if (StringUtils.isNotBlank(respEntity.getBody())) {
                return NsyJacksonUtils.jsonToList(respEntity.getBody(), String.class);
            }
        } catch (Exception e) {
            log.error(String.format("getSkuBySkc error %s", e.getMessage()), e);
        }
        return Collections.emptyList();
    }

    public List<OverseaSkuStoreSpaceStockoutResponse> skuStoreSpaceStockout(String department, Integer replenishmentGroupId, String skc) {
        String uri = String.format("%s/oversea-stockout/sku-store-space-stockout-list?department=%s&replenishmentGroupId=%s&skc=%s", biServiceUrl, department, replenishmentGroupId, skc);
        log.info("skuStoreSpaceStockout request uri: {}", uri);
        try {
            ResponseEntity<String> respEntity = this.restTemplate.getForEntity(uri, String.class);
            log.info("skuStoreSpaceStockout response: {}", JSON.toJSONString(respEntity));
            if (StringUtils.isNotBlank(respEntity.getBody())) {
                return NsyJacksonUtils.toObj(respEntity.getBody(), new TypeReference<List<OverseaSkuStoreSpaceStockoutResponse>>() { });
            }
        } catch (Exception e) {
            log.error(String.format("skuStoreSpaceStockout error %s", e.getMessage()), e);
        }
        return Collections.emptyList();
    }

    public List<FactAmazonProfitabilityAnalysisSpuResponse> getFactAmazonProfitabilityAnalysisSpuList(BaseListRequest request) {
        String uri = String.format("%s/fact-amazon-profitability-analysis-spu-list", biServiceUrl);
        ResponseEntity<String> respEntity = this.restTemplate.postForEntity(uri, request, String.class);
        return JSONUtil.toList(respEntity.getBody(), FactAmazonProfitabilityAnalysisSpuResponse.class);
    }

    public List<BiDataBackFlowRecord> getBiDataBackFlowRecordList(BiBackFlowDataRequest request) {
        String uri = String.format("%s/bi-data-backflow-record-list", biServiceUrl);
        ResponseEntity<String> respEntity = this.restTemplate.postForEntity(uri, request, String.class);
        return JSONUtil.toList(respEntity.getBody(), BiDataBackFlowRecord.class);
    }
}
