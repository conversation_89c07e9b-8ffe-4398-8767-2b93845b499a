package com.nsy.oms.business.service.amazon;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.base.BaseListResponse;
import com.nsy.api.core.apicore.privilege.LoginInfoService;
import com.nsy.api.oms.dto.request.amazon.CalculateGrossProfitRateRequest;
import com.nsy.api.oms.dto.response.bd.CalculateGrossProfitRateResponse;
import com.nsy.api.oms.dto.response.bd.IncreaseProfitResponse;
import com.nsy.oms.business.domain.request.amazon.AmazonOrderProfitMonitoringPageRequest;
import com.nsy.oms.business.domain.request.amazon.AmazonOrderProfitMonitoringSelectRequest;
import com.nsy.oms.business.domain.response.amazon.AmazonOrderProfitMonitoringSkcResponse;
import com.nsy.oms.business.domain.response.amazon.AmazonOrderProfitMonitoringSkuDto;
import com.nsy.oms.business.domain.response.amazon.AmazonOrderProfitMonitoringSpuResponse;
import com.nsy.oms.business.domain.response.amazon.ProfitData;
import com.nsy.oms.business.manage.pms.PmsApiService;
import com.nsy.oms.business.manage.user.UserApiService;
import com.nsy.oms.business.manage.user.request.shared.SelectModel;
import com.nsy.oms.business.service.privilege.AccessControlService;
import com.nsy.oms.business.service.replenishment.FbaReplenishmentSkcService;
import com.nsy.oms.repository.dao.amazon.AmazonOrderProfitMonitoringLabelDao;
import com.nsy.oms.repository.dao.amazon.AmazonOrderProfitMonitoringSkcDao;
import com.nsy.oms.repository.dao.amazon.AmazonOrderProfitMonitoringSkuDao;
import com.nsy.oms.repository.dao.amazon.AmazonOrderProfitMonitoringSpuDao;
import com.nsy.oms.repository.entity.amzon.AmazonOrderProfitMonitoringLabelEntity;
import com.nsy.oms.repository.entity.amzon.AmazonOrderProfitMonitoringSkcEntity;
import com.nsy.oms.repository.entity.amzon.AmazonOrderProfitMonitoringSkuEntity;
import com.nsy.oms.repository.entity.amzon.AmazonOrderProfitMonitoringSpuEntity;
import com.nsy.oms.utils.BigDecimalUtil;
import com.nsy.oms.utils.ListUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-05-06
 */
@Service
public class AmazonOrderProfitMonitoringSearchService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AmazonOrderProfitMonitoringSearchService.class);
    @Autowired
    private AmazonOrderProfitMonitoringSpuDao spuDao;
    @Autowired
    private AmazonOrderProfitMonitoringSkcDao skcDao;
    @Autowired
    private AmazonOrderProfitMonitoringSkuDao skuDao;
    @Autowired
    private AmazonOrderProfitMonitoringLabelDao labelDao;
    @Autowired
    private AccessControlService accessControlService;
    @Autowired
    private UserApiService userApiService;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private AmazonProfitCalculateService amazonProfitCalculateService;
    @Autowired
    private PmsApiService pmsApiService;

    public BaseListResponse<AmazonOrderProfitMonitoringSpuResponse> page(AmazonOrderProfitMonitoringPageRequest request) {
        initPageRequest(request);
        Page<AmazonOrderProfitMonitoringSpuEntity> page = spuDao.getPage(request);
        BaseListResponse<AmazonOrderProfitMonitoringSpuResponse> baseListResponse = new BaseListResponse<>();
        baseListResponse.setTotalCount(page.getTotal());
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return baseListResponse;
        }
        baseListResponse.setContent(page.getRecords().stream().map(spuEntity -> {
            AmazonOrderProfitMonitoringSpuResponse response = new AmazonOrderProfitMonitoringSpuResponse();
            response.setId(spuEntity.getId());
            response.setSpu(spuEntity.getSpu());
            response.setSeason(spuEntity.getSeason());
            response.setDevelopSeason(spuEntity.getDevelopSeason());
            response.setImageUrl(spuEntity.getImageUrl());
            response.setMarketplaceCode(spuEntity.getMarketplaceCode());
            response.setParentAsin(spuEntity.getParentAsin());
            String categoryName = String.format("%s-%s", spuEntity.getFirstCategoryName(), spuEntity.getSecondCategoryName());
            if (StringUtils.hasText(spuEntity.getThirdCategoryName())) {
                categoryName = categoryName + "-" + spuEntity.getThirdCategoryName();
            }
            response.setCategoryName(categoryName);
            response.setDepartment(spuEntity.getDepartment());
            response.setStoreId(spuEntity.getStoreId());
            response.setStoreName(spuEntity.getStoreName());
            response.setLast7DateSaleQty(spuEntity.getLast7DateSaleQty());
            response.setLast14DateSaleQty(spuEntity.getLast14DateSaleQty());
            response.setLast30DateSaleQty(spuEntity.getLast30DateSaleQty());
            response.setTotalInv(spuEntity.getTotalInv());
            response.setQuanzhouInv(spuEntity.getQuanzhouInv());
            response.setOverseasInv(spuEntity.getOverseasInv());
            response.setOverseasInReservedInv(spuEntity.getOverseasInReservedInv());
            response.setPurchaseInTransitInv(spuEntity.getPurchaseInTransitInv());
            response.setProfitRateIn7Days(toPercent(spuEntity.getProfitRateIn7Days()));
            response.setProfitRateIn14Days(toPercent(spuEntity.getProfitRateIn14Days()));
            response.setProfitRateIn30Days(toPercent(spuEntity.getProfitRateIn30Days()));
            response.setReturnRate(toPercent(spuEntity.getReturnRate()));
            response.setAdCostRate(toPercent(spuEntity.getAdCostRate()));
            response.setSalePrice(spuEntity.getSalePrice());
            response.setYourPrice(spuEntity.getYourPrice());
            response.setMinOrderPriceIn24Hours(spuEntity.getMinOrderPriceIn24Hours());
            response.setMaxOrderPriceIn24Hours(spuEntity.getMaxOrderPriceIn24Hours());
            response.setMinProfitRateIn24Hours(toPercent(spuEntity.getMinProfitRateIn24Hours()));
            response.setMaxProfitRateIn24Hours(toPercent(spuEntity.getMaxProfitRateIn24Hours()));
            response.setCurrentLevelCommissionRate(toPercent(spuEntity.getCurrentLevelCommissionRate()));
            response.setCurrentLevelBalancePrice(spuEntity.getCurrentLevelBalancePrice());
            response.setBatchNo(spuEntity.getBatchNo());
            response.setSkcList(buildSkcResponseList(spuEntity));
            return response;
        }).collect(Collectors.toList()));
        return baseListResponse;
    }

    private List<AmazonOrderProfitMonitoringSkcResponse> buildSkcResponseList(AmazonOrderProfitMonitoringSpuEntity spuEntity) {
        List<AmazonOrderProfitMonitoringSkcEntity> skcEntities = skcDao.listBySpuAndStoreIdAndParentAsin(spuEntity.getSpu(), spuEntity.getStoreId(), spuEntity.getParentAsin());
        if (skcEntities.isEmpty()) {
            return Collections.emptyList();
        }
        Map<String, List<AmazonOrderProfitMonitoringLabelEntity>> labelMap = labelDao.listBySkcInAndStoreIdAndParentAsin(skcEntities.stream()
                        .map(AmazonOrderProfitMonitoringSkcEntity::getSkc).collect(Collectors.toList()), spuEntity.getStoreId(), spuEntity.getParentAsin())
                .stream().collect(Collectors.groupingBy(AmazonOrderProfitMonitoringLabelEntity::getSkc));

        return skcEntities.stream().map(skcEntity -> {
            AmazonOrderProfitMonitoringSkcResponse skcResponse = buildSkcResponse(skcEntity);
            List<AmazonOrderProfitMonitoringLabelEntity> labelEntities = labelMap.get(skcEntity.getSkc());
            if (!CollectionUtils.isEmpty(labelEntities)) {
                skcResponse.setLabelList(labelEntities.stream().filter(label -> label.getLabelType() == 1)
                        .map(AmazonOrderProfitMonitoringLabelEntity::getLabelName).distinct().collect(Collectors.toList()));
                skcResponse.setSuggestLabelList(labelEntities.stream().filter(label -> label.getLabelType() == 2)
                        .map(AmazonOrderProfitMonitoringLabelEntity::getLabelName).distinct().collect(Collectors.toList()));
            }
            return skcResponse;
        }).collect(Collectors.toList());
    }

    private void initPageRequest(AmazonOrderProfitMonitoringPageRequest request) {
        List<Integer> permissionStoreIds = accessControlService.isAdmin() ? Collections.emptyList()
                : accessControlService.doPrivileged(new FbaReplenishmentSkcService.FbaReplenishmentSkcPrivilegeAction(userApiService, loginInfoService.getUserName()));
        request.setPermissionStoreIds(permissionStoreIds);
        if (StringUtils.hasText(request.getSku())) {
            request.setSku("%" + request.getSku() + "%");
        }
        if (request.getMinProfitRate() != null) {
            request.setMinProfitRate(toRate(request.getMinProfitRate()));
        }
        if (request.getMaxProfitRate() != null) {
            request.setMaxProfitRate(toRate(request.getMaxProfitRate()));
        }
        if (!CollectionUtils.isEmpty(request.getLabelNames()) && request.getLabelNames().stream().anyMatch("无标签"::equals)) {
            request.setLabelNames(Collections.emptyList());
            request.setIsNoLabel(1);
        }
    }

    private AmazonOrderProfitMonitoringSkcResponse buildSkcResponse(AmazonOrderProfitMonitoringSkcEntity skcEntity) {
        AmazonOrderProfitMonitoringSkcResponse response = new AmazonOrderProfitMonitoringSkcResponse();
        response.setSpu(skcEntity.getSpu());
        response.setSkc(skcEntity.getSkc());
        response.setStoreId(skcEntity.getStoreId());
        response.setStoreName(skcEntity.getStoreName());
        response.setMarketplaceCode(skcEntity.getMarketplaceCode());
        response.setParentAsin(skcEntity.getParentAsin());
        response.setLast7DateSaleQty(skcEntity.getLast7DateSaleQty());
        response.setLast14DateSaleQty(skcEntity.getLast14DateSaleQty());
        response.setLast30DateSaleQty(skcEntity.getLast30DateSaleQty());
        response.setTotalInv(skcEntity.getTotalInv());
        response.setQuanzhouInv(skcEntity.getQuanzhouInv());
        response.setOverseasInv(skcEntity.getOverseasInv());
        response.setOverseasInReservedInv(skcEntity.getOverseasInReservedInv());
        response.setPurchaseInTransitInv(skcEntity.getPurchaseInTransitInv());
        response.setProfitRateIn7Days(toPercent(skcEntity.getProfitRateIn7Days()));
        response.setProfitRateIn14Days(toPercent(skcEntity.getProfitRateIn14Days()));
        response.setProfitRateIn30Days(toPercent(skcEntity.getProfitRateIn30Days()));
        response.setReturnRate(toPercent(skcEntity.getReturnRate()));
        response.setAdCostRate(toPercent(skcEntity.getAdCostRate()));
        response.setSalePrice(skcEntity.getSalePrice());
        response.setYourPrice(skcEntity.getYourPrice());
        response.setMinOrderPriceIn24Hours(skcEntity.getMinOrderPriceIn24Hours());
        response.setMaxOrderPriceIn24Hours(skcEntity.getMaxOrderPriceIn24Hours());
        response.setMinProfitRateIn24Hours(toPercent(skcEntity.getMinProfitRateIn24Hours()));
        response.setMaxProfitRateIn24Hours(toPercent(skcEntity.getMaxProfitRateIn24Hours()));
        response.setCurrentLevelCommissionRate(toPercent(skcEntity.getCurrentLevelCommissionRate()));
        response.setCurrentLevelBalancePrice(skcEntity.getCurrentLevelBalancePrice());
        response.setLevelOneProfitRate(toPercent(skcEntity.getLevelOneProfitRate()));
        response.setLevelOneDownshiftIncreaseProfit(skcEntity.getLevelOneDownshiftIncreaseProfit());
        response.setLevelTwoProfitRate(toPercent(skcEntity.getLevelTwoProfitRate()));
        response.setLevelTwoDownshiftIncreaseProfit(skcEntity.getLevelTwoDownshiftIncreaseProfit());
        response.setLevelThreeProfitRate(toPercent(skcEntity.getLevelThreeProfitRate()));
        response.setLevelThreeDownshiftIncreaseProfit(skcEntity.getLevelThreeDownshiftIncreaseProfit());
        response.setLevelFourProfitRate(toPercent(skcEntity.getLevelFourProfitRate()));
        response.setLevelFourDownshiftIncreaseProfit(skcEntity.getLevelFourDownshiftIncreaseProfit());
        return response;
    }

    public List<SelectModel> getStoreList(AmazonOrderProfitMonitoringSelectRequest request) {
        List<AmazonOrderProfitMonitoringSpuEntity> entities = spuDao.listBySpu(request.getSpu(), null);
        return entities.stream()
                .filter(ListUtil.distinctByKey(AmazonOrderProfitMonitoringSpuEntity::getStoreId))
                .map(e -> new SelectModel(e.getStoreId().toString(), e.getStoreName()))
                .collect(Collectors.toList());
    }

    public List<SelectModel> getParentAsinList(AmazonOrderProfitMonitoringSelectRequest request) {
        List<AmazonOrderProfitMonitoringSpuEntity> entities = spuDao.listBySpu(request.getSpu(), request.getStoreId());
        return entities.stream()
                .map(AmazonOrderProfitMonitoringSpuEntity::getParentAsin)
                .map(parentAsin -> new SelectModel(parentAsin, parentAsin))
                .collect(Collectors.toList());
    }

    public List<SelectModel> getSkcList(AmazonOrderProfitMonitoringSelectRequest request) {
        List<AmazonOrderProfitMonitoringSkcEntity> entities = skcDao.listBySpuAndStoreIdAndParentAsin(request.getSpu(), request.getStoreId(), request.getParentAsin());
        return entities.stream()
                .sorted(Comparator.comparing(AmazonOrderProfitMonitoringSkcEntity::getLast7DateSaleQty).reversed())
                .map(AmazonOrderProfitMonitoringSkcEntity::getSkc)
                .distinct().map(skc -> new SelectModel(skc, skc))
                .collect(Collectors.toList());
    }

    public List<SelectModel> getSkuList(AmazonOrderProfitMonitoringSelectRequest request) {
        List<AmazonOrderProfitMonitoringSkuEntity> entities = skuDao.listBySkcAndStoreIdAndParentAsin(request.getSkc(), request.getStoreId(), request.getParentAsin());
        return entities.stream()
                .sorted(Comparator.comparing(AmazonOrderProfitMonitoringSkuEntity::getLast7DateSaleQty).reversed())
                .map(AmazonOrderProfitMonitoringSkuEntity::getSku)
                .map(sku -> new SelectModel(sku, sku))
                .collect(Collectors.toList());
    }

    public AmazonOrderProfitMonitoringSkuDto getSkuDetail(AmazonOrderProfitMonitoringSelectRequest request) {
        AmazonOrderProfitMonitoringSkuEntity entity = skuDao.getBySkuAndStoreIdAndParentAsin(request.getSku(), request.getStoreId(), request.getParentAsin());
        AmazonOrderProfitMonitoringSkuDto response = buildAmazonOrderProfitMonitoringSkuDto(entity);
        // 设置表格
        CalculateGrossProfitRateRequest calculateRequest = buildSkuCalculateGrossProfitRateRequest(entity);
        LOGGER.info("{} getSkuDetail calculateGrossProfitRateRequest:{}", entity.getSku(), JSONUtil.toJsonStr(calculateRequest));
        CalculateGrossProfitRateResponse calculateResponse = amazonProfitCalculateService.calculateGrossProfitRate(calculateRequest);
        response.setList(amazonProfitCalculateService.calculateIncreaseProfit(calculateRequest, calculateResponse));
        response.getList().forEach(e -> {
            e.setCommissionRate(toPercent(e.getCommissionRate()));
            e.setProfitRate(toPercent(e.getProfitRate()));
        });
        // 设置图表
        response.setData(buildProfitData(entity, response.getList(), calculateRequest, calculateResponse));
        return response;
    }

    /**
     * 近24H成交价
     * 折后售价($）
     * 跟23.99比较，如果大于，就直接用，否则使用23.99
     * <p>
     * 利润曲线：每隔1美元，算一次利润率，原价格算一次，新价格算一次
     * 盈亏平衡价：原价格的表格的盈亏平衡价，大于0的取出来，类似[12.32,0]
     * 当前售价：近24H成交价、折后售价($）。类似[16.99,利润率]
     */
    private ProfitData buildProfitData(AmazonOrderProfitMonitoringSkuEntity entity, List<IncreaseProfitResponse> increaseProfitList,
                                       CalculateGrossProfitRateRequest calculateRequest, CalculateGrossProfitRateResponse calculateResponse) {
        BigDecimal oldActivityPrice = entity.getMinOrderPriceIn24Hours();
        BigDecimal maxPrice = new BigDecimal("23.99");
        if (oldActivityPrice.compareTo(maxPrice) > 0) {
            maxPrice = oldActivityPrice;
        }
        List<List<BigDecimal>> priceRangeList = new ArrayList<>();
        List<BigDecimal> oldBalancePriceList = increaseProfitList.stream().map(IncreaseProfitResponse::getBalancePrice).filter(BigDecimalUtil::isValid).collect(Collectors.toList());
        List<List<List<BigDecimal>>> oldProfitList = new ArrayList<>(4);
        for (int i = 0; i < 4; i++) {
            oldProfitList.add(new ArrayList<>());
        }
        // 1美元1档
        for (BigDecimal i = new BigDecimal("0.99"); i.compareTo(maxPrice) <= 0; i = i.add(BigDecimal.ONE)) {
            calculateRequest.setPriceBeforeDiscount(i);
            calculateRequest.setPriceAfterDiscount(i);
            BigDecimal grossProfit = amazonProfitCalculateService.calculateGrossProfitRate(calculateRequest).getGrossProfitRate();

            priceRangeList.add(Lists.newArrayList(i, toPercent(grossProfit)));

            BigDecimal start = i;
            BigDecimal end = i.add(BigDecimal.ONE);
            // 找出落在 (start, end) 区间的balancePrice
            List<List<BigDecimal>> inRange = oldBalancePriceList.stream()
                    .filter(price -> price.compareTo(start) > 0 && price.compareTo(end) < 0)
                    .map(price -> Lists.newArrayList(price, BigDecimal.ZERO))
                    .collect(Collectors.toList());
            // 找出落在 (start, end) 区间的activityPrice
            if (oldActivityPrice.compareTo(start) > 0 && oldActivityPrice.compareTo(end) < 0) {
                inRange.add(Lists.newArrayList(oldActivityPrice, toPercent(calculateResponse.getGrossProfitRate())));
            }
            inRange.sort(Comparator.comparing(e -> e.get(0)));
            priceRangeList.addAll(inRange);
        }
        ProfitData data = new ProfitData();
        data.setPriceRange(priceRangeList.stream().map(e -> e.get(0)).collect(Collectors.toList()));
        priceRangeList.forEach(p -> {
            if (p.get(1).compareTo(BigDecimal.ZERO) >= 0) {
                oldProfitList.get(getLevel(p.get(0)) - 1).add(p); // 减1，因为getLevel返回1-4，而索引是0-3
            }
        });
        oldProfitList.removeIf(List::isEmpty);
        data.setOldProfit(oldProfitList);
        data.setOldBalancePrice(oldBalancePriceList.stream().map(price -> Lists.newArrayList(price, BigDecimal.ZERO)).collect(Collectors.toList()));
        data.setOldActivityPrice(Lists.newArrayList(oldActivityPrice, toPercent(calculateResponse.getGrossProfitRate())));
        return data;
    }

    private int getLevel(BigDecimal price) {
        if (price.compareTo(new BigDecimal("9.99")) <= 0) {
            return 1;
        } else if (price.compareTo(new BigDecimal("14.99")) <= 0) {
            return 2;
        } else if (price.compareTo(new BigDecimal("19.99")) <= 0) {
            return 3;
        } else {
            return 4;
        }
    }

    private AmazonOrderProfitMonitoringSkuDto buildAmazonOrderProfitMonitoringSkuDto(AmazonOrderProfitMonitoringSkuEntity entity) {
        AmazonOrderProfitMonitoringSkuDto response = new AmazonOrderProfitMonitoringSkuDto();
        response.setSku(entity.getSku());
        response.setStoreId(entity.getStoreId());
        response.setParentAsin(entity.getParentAsin());
        response.setSalePrice(entity.getSalePrice());
        response.setYourPrice(entity.getYourPrice());
        response.setMinOrderPriceIn24Hours(entity.getMinOrderPriceIn24Hours());
        response.setDiscountRate(toPercent(BigDecimal.ZERO));
        response.setReturnRate(toPercent(entity.getReturnRate()));
        response.setAdCostRate(toPercent(entity.getAdCostRate()));
        response.setCateReturnUnsallableRate(toPercent(entity.getCateReturnUnsallableRate()));
        response.setCouponRate(new BigDecimal("2"));
        response.setFirstDistanceUnitPrice(new BigDecimal("4"));
        response.setProductCost(entity.getProductCost());
        // 销量库存信息
        response.setLast7DateSaleQty(entity.getLast7DateSaleQty());
        response.setLast14DateSaleQty(entity.getLast14DateSaleQty());
        response.setLast30DateSaleQty(entity.getLast30DateSaleQty());
        response.setTotalInv(entity.getTotalInv());
        response.setQuanzhouInv(entity.getQuanzhouInv());
        response.setOverseasInv(entity.getOverseasInv());
        response.setOverseasInReservedInv(entity.getOverseasInReservedInv());
        response.setPurchaseInTransitInv(entity.getPurchaseInTransitInv());
        response.setLast7DateInvSalesRate(entity.getLast7DateInvSalesRate());
        response.setLast14DateInvSalesRate(entity.getLast14DateInvSalesRate());
        response.setLast30DateInvSalesRate(entity.getLast30DateInvSalesRate());
        response.setInvAge0To90(entity.getInvAge0To90());
        response.setInvAge91To180(entity.getInvAge91To180());
        response.setInvAge181To270(entity.getInvAge181To270());
        response.setInvAge271To365(entity.getInvAge271To365());
        response.setInvAgeGt365(entity.getInvAgeGt365());
        response.setInvAgeTotal(entity.getInvAgeTotal());
        return response;
    }

    private CalculateGrossProfitRateRequest buildSkuCalculateGrossProfitRateRequest(AmazonOrderProfitMonitoringSkuEntity entity) {
        CalculateGrossProfitRateRequest calculateRequest = new CalculateGrossProfitRateRequest();
        calculateRequest.setCountryCode(entity.getMarketplaceCode());
        calculateRequest.setPriceBeforeDiscount(entity.getMinOrderPriceIn24Hours());
        calculateRequest.setPriceAfterDiscount(entity.getMinOrderPriceIn24Hours());
        calculateRequest.setLength(entity.getLongestSide());
        calculateRequest.setWidth(entity.getMedianSide());
        calculateRequest.setHeight(entity.getShortestSide());
        calculateRequest.setWeight(entity.getWeight());
        calculateRequest.setProductCost(entity.getProductCost());
        calculateRequest.setReturnRate(entity.getReturnRate());
        calculateRequest.setReturnNotForSaleRate(entity.getCateReturnUnsallableRate());
        calculateRequest.setAdvertisingRate(entity.getAdCostRate());
        calculateRequest.setStandard(entity.getProductSizeTier());
        calculateRequest.setDiscountRate(BigDecimal.ZERO);
        calculateRequest.setCouponRate(new BigDecimal("0.02"));
        return calculateRequest;
    }

    /**
     * 比例转为百分比
     */
    private BigDecimal toPercent(BigDecimal val) {
        return val != null ? val.multiply(new BigDecimal("100")) : BigDecimal.ZERO;
    }

    /**
     * 百分比转为比例
     */
    private BigDecimal toRate(BigDecimal val) {
        return val != null ? val.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP) : BigDecimal.ZERO;
    }

    public AmazonOrderProfitMonitoringSkuDto calculateSku(AmazonOrderProfitMonitoringSkuDto request) {
        AmazonOrderProfitMonitoringSkuEntity entity = skuDao.getBySkuAndStoreIdAndParentAsin(request.getSku(), request.getStoreId(), request.getParentAsin());
        AmazonOrderProfitMonitoringSkuDto response = new AmazonOrderProfitMonitoringSkuDto();

        BigDecimal priceAfterDiscount = BigDecimal.ONE.subtract(toRate(request.getDiscountRate())).multiply(request.getSalePrice()).setScale(2, RoundingMode.HALF_UP);
        CalculateGrossProfitRateRequest calculateRequest = new CalculateGrossProfitRateRequest();
        calculateRequest.setCountryCode(entity.getMarketplaceCode());
        calculateRequest.setPriceBeforeDiscount(request.getSalePrice());
        calculateRequest.setPriceAfterDiscount(priceAfterDiscount);
        calculateRequest.setLength(entity.getLongestSide());
        calculateRequest.setWidth(entity.getMedianSide());
        calculateRequest.setHeight(entity.getShortestSide());
        calculateRequest.setWeight(entity.getWeight());
        calculateRequest.setProductCost(request.getProductCost());
        calculateRequest.setReturnRate(toRate(request.getReturnRate()));
        calculateRequest.setReturnNotForSaleRate(toRate(request.getCateReturnUnsallableRate()));
        calculateRequest.setAdvertisingRate(toRate(request.getAdCostRate()));
        calculateRequest.setStandard(entity.getProductSizeTier());
        calculateRequest.setDiscountRate(toRate(request.getDiscountRate()));
        calculateRequest.setCouponRate(toRate(request.getCouponRate()));
        LOGGER.info("{} calculateSku calculateGrossProfitRateRequest:{}", entity.getSku(), JSONUtil.toJsonStr(calculateRequest));
        CalculateGrossProfitRateResponse calculateResponse = amazonProfitCalculateService.calculateGrossProfitRate(calculateRequest);

        response.setPriceAfterDiscount(priceAfterDiscount);
        response.setProfitRate(toPercent(calculateResponse.getGrossProfitRate()));
        response.setList(amazonProfitCalculateService.calculateIncreaseProfit(calculateRequest, calculateResponse));
        response.getList().forEach(e -> {
            e.setCommissionRate(toPercent(e.getCommissionRate()));
            e.setProfitRate(toPercent(e.getProfitRate()));
        });
        AmazonOrderProfitMonitoringSelectRequest selectRequest = new AmazonOrderProfitMonitoringSelectRequest();
        selectRequest.setStoreId(entity.getStoreId());
        selectRequest.setParentAsin(entity.getParentAsin());
        selectRequest.setSku(entity.getSku());
        // sku的原图表信息
        ProfitData profitData = getSkuDetail(selectRequest).getData();
        // 设置图表
        List<BigDecimal> newBalancePriceList = response.getList().stream().map(IncreaseProfitResponse::getBalancePrice).filter(BigDecimalUtil::isValid).collect(Collectors.toList());
        List<List<List<BigDecimal>>> newProfitList = new ArrayList<>(4);
        for (int i = 0; i < 4; i++) {
            newProfitList.add(new ArrayList<>());
        }
        List<PriceRange> priceRangeList = buildNewPriceRangeList(profitData.getPriceRange(), calculateRequest, newBalancePriceList, response.getPriceAfterDiscount(), calculateResponse);
        priceRangeList.forEach(p -> {
            if (p.getProfitRate().compareTo(BigDecimal.ZERO) >= 0 && !p.isOldBalanceOrActivityPrice()) {
                newProfitList.get(getLevel(p.getPrice()) - 1).add(Lists.newArrayList(p.getPrice(), p.getProfitRate())); // 减1，因为getLevel返回1-4，而索引是0-3
            }
        });
        newProfitList.removeIf(List::isEmpty);
        profitData.setPriceRange(priceRangeList.stream().map(PriceRange::getPrice).collect(Collectors.toList()));
        profitData.setNewProfit(newProfitList);
        profitData.setNewBalancePrice(newBalancePriceList.stream().map(price -> Lists.newArrayList(price, BigDecimal.ZERO)).collect(Collectors.toList()));
        profitData.setNewActivityPrice(Lists.newArrayList(response.getPriceAfterDiscount(), toPercent(calculateResponse.getGrossProfitRate())));
        response.setData(profitData);
        return response;
    }

    private List<PriceRange> buildNewPriceRangeList(List<BigDecimal> oldPriceRangeList, CalculateGrossProfitRateRequest calculateRequest,
                                                    List<BigDecimal> newBalancePriceList, BigDecimal newActivityPrice, CalculateGrossProfitRateResponse calculateResponse) {
        // 原来的价格区间，如果不是0.99结尾，则认为是原来的盈亏平衡价或活动售价
        List<BigDecimal> oldBalanceOrActivityPriceList = oldPriceRangeList
                .stream().filter(price -> price.remainder(BigDecimal.ONE).compareTo(new BigDecimal("0.99")) != 0).collect(Collectors.toList());
        // 取原图表的最大区间值，如果新的价格比他大，就用新的
        BigDecimal maxPrice = oldPriceRangeList.get(oldPriceRangeList.size() - 1);
        if (newActivityPrice.compareTo(maxPrice) > 0) {
            maxPrice = newActivityPrice;
        }
        // 获取整数部分和小数部分
        // 如果小数部分不是0.99，则向上取整到下一个0.99
        if (maxPrice.remainder(BigDecimal.ONE).compareTo(new BigDecimal("0.99")) != 0) {
            maxPrice = maxPrice.setScale(0, RoundingMode.DOWN).add(new BigDecimal("0.99"));
        }

        List<PriceRange> priceRangeList = new ArrayList<>();
        // 1美元1档
        for (BigDecimal i = new BigDecimal("0.99"); i.compareTo(maxPrice) <= 0; i = i.add(BigDecimal.ONE)) {
            calculateRequest.setPriceBeforeDiscount(i);
            calculateRequest.setPriceAfterDiscount(i);
            BigDecimal grossProfit = amazonProfitCalculateService.calculateGrossProfitRate(calculateRequest).getGrossProfitRate();

            priceRangeList.add(new PriceRange(i, toPercent(grossProfit), false));

            BigDecimal start = i;
            BigDecimal end = i.add(BigDecimal.ONE);
            // 找出落在 (start, end) 区间的balancePrice
            List<PriceRange> inRange = newBalancePriceList.stream()
                    .filter(price -> price.compareTo(start) > 0 && price.compareTo(end) < 0)
                    .map(price -> new PriceRange(price, BigDecimal.ZERO, false))
                    .collect(Collectors.toList());
            // 找出落在 (start, end) 区间的activityPrice
            if (newActivityPrice.compareTo(start) > 0 && newActivityPrice.compareTo(end) < 0) {
                inRange.add(new PriceRange(newActivityPrice, toPercent(calculateResponse.getGrossProfitRate()), false));
            }
            // 旧的平衡价和活动价
            inRange.addAll(oldBalanceOrActivityPriceList.stream().filter(price -> price.compareTo(start) > 0 && price.compareTo(end) < 0)
                    .map(price -> new PriceRange(price, BigDecimal.ZERO, true)).collect(Collectors.toList()));
            if (!inRange.isEmpty()) {
                inRange.sort(Comparator.comparing(PriceRange::getPrice));
                priceRangeList.addAll(inRange);
            }
        }
        return priceRangeList;
    }

    public List<SelectModel> getAmazonDevelopSeasonSelect() {
        return pmsApiService.getDevelopSeasonSelect().stream().filter(s -> s.getLabel().contains("亚马逊"))
                .map(s -> new SelectModel(s.getLabel(), s.getLabel())).collect(Collectors.toList());
    }


    private static class PriceRange {
        private BigDecimal price;
        private BigDecimal profitRate;
        private boolean oldBalanceOrActivityPrice;

        PriceRange() {
        }

        PriceRange(BigDecimal price, BigDecimal profitRate, boolean oldBalanceOrActivityPrice) {
            this.price = price;
            this.profitRate = profitRate;
            this.oldBalanceOrActivityPrice = oldBalanceOrActivityPrice;
        }

        public BigDecimal getPrice() {
            return price;
        }

        public void setPrice(BigDecimal price) {
            this.price = price;
        }

        public BigDecimal getProfitRate() {
            return profitRate;
        }

        public void setProfitRate(BigDecimal profitRate) {
            this.profitRate = profitRate;
        }

        public boolean isOldBalanceOrActivityPrice() {
            return oldBalanceOrActivityPrice;
        }

        public void setOldBalanceOrActivityPrice(boolean oldBalanceOrActivityPrice) {
            this.oldBalanceOrActivityPrice = oldBalanceOrActivityPrice;
        }
    }
}
