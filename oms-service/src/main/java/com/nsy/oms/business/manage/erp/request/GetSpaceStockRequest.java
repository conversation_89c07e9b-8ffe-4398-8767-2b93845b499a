package com.nsy.oms.business.manage.erp.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class GetSpaceStockRequest {

    @JsonProperty("ErpSkus")
    public List<String> erpSku;

    @JsonProperty("DisAInfoId")
    public int disAInfoId;

    @JsonProperty("IsFbaStore")
    public Boolean fbaStore;

    @JsonProperty("SpaceId")
    public Integer spaceId;

    @JsonProperty("SpaceIdList")
    public List<Integer> spaceIdList;

    public List<String> getErpSku() {
        return erpSku;
    }

    public void setErpSku(List<String> erpSku) {
        this.erpSku = erpSku;
    }

    public int getDisAInfoId() {
        return disAInfoId;
    }

    public void setDisAInfoId(int disAInfoId) {
        this.disAInfoId = disAInfoId;
    }

    public Boolean getFbaStore() {
        return fbaStore;
    }

    public void setFbaStore(Boolean fbaStore) {
        this.fbaStore = fbaStore;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public List<Integer> getSpaceIdList() {
        return spaceIdList;
    }

    public void setSpaceIdList(List<Integer> spaceIdList) {
        this.spaceIdList = spaceIdList;
    }
}
