package com.nsy.oms.business.service.points.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.privilege.LoginInfoService;
import com.nsy.api.oms.dto.request.auth.SaAuthRequest;
import com.nsy.api.oms.dto.response.auth.StoreAuthInfoDetailResponse;
import com.nsy.api.oms.dto.response.auth.StoreAuthInfoResponse;
import com.nsy.oms.business.manage.erp.ErpApiService;
import com.nsy.oms.business.manage.erp.request.inbound.SetRefundOrderInfoRequest;
import com.nsy.oms.business.manage.erp.response.inbound.TradePaymentInfo;
import com.nsy.oms.business.manage.thirdparty.ThirdPartyApiService;
import com.nsy.oms.business.manage.thirdparty.auth.ShopyyAuth;
import com.nsy.oms.business.manage.thirdparty.request.CancelPushMallRequest;
import com.nsy.oms.business.service.feign.StoreFeignService;
import com.nsy.oms.business.service.points.IB2bPointsRefundItemService;
import com.nsy.oms.enums.points.B2bPointsRefundItemStateEnum;
import com.nsy.oms.repository.dao.sa.SaStoreWebsiteDao;
import com.nsy.oms.repository.entity.points.B2bPointsRefundItemEntity;
import com.nsy.oms.repository.entity.sa.SaStoreWebsiteEntity;
import com.nsy.oms.repository.sql.mapper.points.B2bPointsRefundItemMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * B2B积分退款明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Slf4j
@Service
public class B2bPointsRefundItemServiceImpl extends ServiceImpl<B2bPointsRefundItemMapper, B2bPointsRefundItemEntity> implements IB2bPointsRefundItemService {
    @Autowired
    private StoreFeignService storeFeignService;
    @Resource
    private SaStoreWebsiteDao saStoreWebsiteDao;
    @Autowired
    private ThirdPartyApiService thirdPartyApiService;
    @Resource
    private LoginInfoService loginInfoService;
    @Resource
    private ErpApiService erpApiService;

    @Override
    public List<B2bPointsRefundItemEntity> getListByRefundIds(List<Integer> refundIds) {
        return ListUtils.partition(refundIds, 200)
                .stream()
                .map(refundIdList -> this.list(new LambdaQueryWrapper<B2bPointsRefundItemEntity>().in(B2bPointsRefundItemEntity::getRefundId, refundIdList)))
                .flatMap(item -> item.stream().filter(Objects::nonNull))
                .collect(Collectors.toList());
    }

    @Override
    public void syncItems(Integer refundId, List<TradePaymentInfo> tradePaymentInfoList) {
        if (CollectionUtils.isEmpty(tradePaymentInfoList)) {
            return;
        }
        List<B2bPointsRefundItemEntity> b2bPointsRefundItemEntities = new ArrayList<>();
        tradePaymentInfoList.forEach(item -> {
            B2bPointsRefundItemEntity b2bPointsRefundItemEntity = new B2bPointsRefundItemEntity();
            b2bPointsRefundItemEntity.setOrderNo(StringUtils.isNotEmpty(item.getTid()) ? item.getTid() : "");
            b2bPointsRefundItemEntity.setSerialNumber(StringUtils.isNotEmpty(item.getPaymentId()) ? item.getPaymentId() : "");
            b2bPointsRefundItemEntity.setRefundAmount(Optional.ofNullable(item.getGrossAmount()).isPresent() ? item.getGrossAmount() : BigDecimal.ZERO);
            b2bPointsRefundItemEntity.setCurrency(StringUtils.isNotEmpty(item.getGrossCurrency()) ? item.getGrossCurrency() : "");
            b2bPointsRefundItemEntity.setUrl(StringUtils.isNotEmpty(item.getUrl()) ? item.getUrl() : "");
            b2bPointsRefundItemEntity.setRefundPlatform(StringUtils.isNotEmpty(item.getPaymentType()) ? item.getPaymentType() : "");

            b2bPointsRefundItemEntity.setRefundStatus(B2bPointsRefundItemStateEnum.WAIT_REFUND.getStatus());
            b2bPointsRefundItemEntity.setCreateBy("syncItems");
            b2bPointsRefundItemEntity.setCreateDate(new Date());
            b2bPointsRefundItemEntity.setRefundId(refundId);
            b2bPointsRefundItemEntities.add(b2bPointsRefundItemEntity);
        });
        this.saveBatch(b2bPointsRefundItemEntities);
    }

    @Override
    public void affirmOrCancelRefundItem(Integer b2bPointsRefundItemId, String remark, String refundStatus, Integer storeId) {
        B2bPointsRefundItemEntity b2bPointsRefundItemEntity = this.getById(b2bPointsRefundItemId);
        if (!Optional.ofNullable(b2bPointsRefundItemEntity).isPresent() || !B2bPointsRefundItemStateEnum.WAIT_REFUND.getStatus().equals(b2bPointsRefundItemEntity.getRefundStatus())) {
            throw new BusinessServiceException(String.format("订单明细不存在,itemId:%s", b2bPointsRefundItemId));
        }
        if (B2bPointsRefundItemStateEnum.ALREADY_REFUND.getStatus().equals(refundStatus)) {
            //推送商通
            setRefundOrderInfo(b2bPointsRefundItemEntity, storeId);
        } else if (B2bPointsRefundItemStateEnum.CANCEL.getStatus().equals(refundStatus)) {
            cancelPushMall(storeId, b2bPointsRefundItemEntity.getRefundId(), b2bPointsRefundItemEntity.getRefundAmount());
        }

        b2bPointsRefundItemEntity.setRefundStatus(refundStatus);
        b2bPointsRefundItemEntity.setRemark(StringUtils.isNotEmpty(remark) ? remark : "");
        String name = loginInfoService.getName();
        b2bPointsRefundItemEntity.setUpdateBy(name);
        b2bPointsRefundItemEntity.setUpdateDate(new Date());
        b2bPointsRefundItemEntity.setRefundOperator(name);
        this.updateById(b2bPointsRefundItemEntity);
    }

    private void setRefundOrderInfo(B2bPointsRefundItemEntity b2bPointsRefundItemEntity, Integer storeId) {
        SetRefundOrderInfoRequest setRefundOrderInfoRequest = new SetRefundOrderInfoRequest();
        setRefundOrderInfoRequest.setRefundRequestId(b2bPointsRefundItemEntity.getRefundId());
        setRefundOrderInfoRequest.setStoreId(storeId);
        setRefundOrderInfoRequest.setPaymentType(b2bPointsRefundItemEntity.getRefundPlatform());
        setRefundOrderInfoRequest.setTid(b2bPointsRefundItemEntity.getOrderNo());
        setRefundOrderInfoRequest.setPaymentId(b2bPointsRefundItemEntity.getSerialNumber());
        setRefundOrderInfoRequest.setGrossAmount(b2bPointsRefundItemEntity.getRefundAmount());
        setRefundOrderInfoRequest.setGrossCurrency(b2bPointsRefundItemEntity.getCurrency());
        setRefundOrderInfoRequest.setOperator(StringUtils.isEmpty(loginInfoService.getName()) ? "admin" : loginInfoService.getName());

        erpApiService.setRefundOrderInfo(setRefundOrderInfoRequest);
    }

    @Override
    public void cancelPushMall(Integer storeId, Integer refundId, BigDecimal refundAmount) {

        CancelPushMallRequest syncB2bPointsRefundRequest = new CancelPushMallRequest();
        StoreAuthInfoDetailResponse auth = getPublishProduct(storeId);
        ShopyyAuth shopxsyAuth = new ShopyyAuth();
        shopxsyAuth.setStoreUrl(auth.getUrl());
        shopxsyAuth.setToken(auth.getWebsiteKey());
        syncB2bPointsRefundRequest.setShopyyAuth(shopxsyAuth);
        syncB2bPointsRefundRequest.setRefundId(refundId);
        syncB2bPointsRefundRequest.setAmount(refundAmount);

        thirdPartyApiService.cancelPushMall(syncB2bPointsRefundRequest);
    }

    private StoreAuthInfoDetailResponse getPublishProduct(Integer storeId) {
        List<SaStoreWebsiteEntity> saStoreWebsiteEntities = saStoreWebsiteDao.getList(storeId);
        if (CollectionUtils.isEmpty(saStoreWebsiteEntities)) {
            throw new BusinessServiceException("平台与店铺的配置数据不存在");
        }
        SaStoreWebsiteEntity saStoreWebsiteEntity = saStoreWebsiteEntities.stream().findFirst().get();
        Integer websiteId = saStoreWebsiteEntity.getWebsiteId();

        SaAuthRequest saAuthRequest = new SaAuthRequest();
        saAuthRequest.setWebsiteIds(Collections.singletonList(websiteId));
        StoreAuthInfoResponse storeAuthInfoResponse = storeFeignService.getStoreAuthInfo(saAuthRequest);

        if (!Optional.ofNullable(storeAuthInfoResponse).isPresent() || CollectionUtils.isEmpty(storeAuthInfoResponse.getWebsiteAuthList())) {
            throw new BusinessServiceException("店铺授权数据不存在");
        }
        return storeAuthInfoResponse.getWebsiteAuthList().get(0);
    }


}
