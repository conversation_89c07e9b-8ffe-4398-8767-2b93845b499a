package com.nsy.oms.business.domain.download.inbound;

import com.nsy.api.core.apicore.annotation.NsyExcelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: linCheng
 * @create: 2025-04-03 13:57
 **/
@Data
public class InboundPlanExport {

    @NsyExcelProperty("补货单号")
    private String erpTid;

    @NsyExcelProperty("状态")
    private String status;

    @NsyExcelProperty("店铺")
    private String storeName;

    @NsyExcelProperty("品牌")
    private String brandName;

    @NsyExcelProperty("物流方式")
    private String logisticsCompanyName;

    @NsyExcelProperty("备注")
    private String remark;

    @NsyExcelProperty("创建时间")
    private Date createDate;

    @NsyExcelProperty("SELLER SKU")
    private String sellerSku;

    @NsyExcelProperty("ERP SKU")
    private String erpSku;

    @NsyExcelProperty("条码")
    private String barcode;

    @NsyExcelProperty("预计发货数量")
    private Integer estimatedShipmentQuantity;

    @NsyExcelProperty("实际发货数量")
    private Integer actualShipmentQuantity;

}
