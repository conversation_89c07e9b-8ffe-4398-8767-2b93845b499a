package com.nsy.oms.business.service.platform.order.base;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.nsy.api.core.apicore.constant.enums.IsEnum;
import com.nsy.api.core.apicore.constant.enums.QueueStatusEnum;
import com.nsy.api.core.apicore.util.MD5Util;
import com.nsy.api.core.apicore.util.NsyCollUtil;
import com.nsy.api.core.apicore.util.NsyJacksonUtils;
import com.nsy.api.transfer.domain.request.platform.PlatformOrderRequest;
import com.nsy.api.transfer.domain.response.platform.PlatformOrderInfo;
import com.nsy.api.transfer.domain.response.platform.PlatformOrderInfoResponse;
import com.nsy.oms.business.manage.thirdparty.ThirdPartyApiService;
import com.nsy.oms.business.service.order.OrderGrabStatusService;
import com.nsy.oms.business.service.order.OrderIdempotentService;
import com.nsy.oms.business.service.order.OrderItemGrabQueueService;
import com.nsy.oms.business.service.order.OrderMissedQueueService;
import com.nsy.oms.business.service.order.SaleOrderService;
import com.nsy.oms.business.service.platform.impl.PlatformOrderServiceImpl;
import com.nsy.oms.enums.order.OrderTypeEnum;
import com.nsy.oms.enums.platform.PlatformOrderStatusEnum;
import com.nsy.oms.enums.platform.PlatformShippingTypeEnum;
import com.nsy.oms.repository.dao.sa.SaStoreConfigDao;
import com.nsy.oms.repository.entity.order.SaleOrderEntity;
import com.nsy.oms.repository.entity.order.OrderIdempotentEntity;
import com.nsy.oms.repository.entity.order.OrderItemGrabQueueEntity;
import com.nsy.oms.repository.entity.order.OrderMissedQueueEntity;
import com.nsy.oms.repository.entity.platform.PlatformOrderEntity;
import com.nsy.oms.repository.entity.sa.SaStoreEntity;
import com.nsy.oms.utils.SpringContextUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 平台抓单--返回只有订单表头信息和订单收件人信息（没有订单明细）
 */
public abstract class BaseOrderInfoService extends BasePlatformOrderService {
    private static final Logger LOGGER = LoggerFactory.getLogger(BaseOrderInfoService.class);
    @Autowired
    private ThirdPartyApiService thirdPartyApiService;
    @Autowired
    private SaStoreConfigDao saStoreConfigDao;
    @Autowired
    private SaleOrderService saleOrderService;
    @Autowired
    private PlatformOrderServiceImpl platformOrderService;
    @Autowired
    private OrderGrabStatusService orderGrabStatusService;
    @Autowired
    private OrderIdempotentService orderIdempotentService;
    @Autowired
    private OrderItemGrabQueueService orderItemGrabQueueService;
    @Autowired
    private OrderMissedQueueService orderMissedQueueService;
    @Value("${spring.profiles.active}")
    private String env;
    /**
     * 按时间抓单
     */
    @Override
    protected void getPlatformOrderByDate(SaStoreEntity storeEntity, PlatformOrderRequest orderRequest) {
        PlatformOrderInfoResponse response = thirdPartyApiService.getPlatformOrderListByDateForOrderInfo(orderRequest);
        if (ObjectUtil.isNull(response) || NsyCollUtil.isEmpty(response.getPlatformOrderInfoList())) {
            saStoreConfigDao.saveCurrentOrderCatchDate(storeEntity, orderRequest.getEndOrderTime(), getSubtractionOrderCatchDate(), "自动抓单"); //更新店铺最晚抓单时间
            return;
        }
        saStoreConfigDao.saveCurrentOrderCatchDate(storeEntity, calculateLatestOrderGrabDate(response.getPlatformOrderInfoList(), orderRequest.getEndOrderTime(), response.getHasNextPage()), getSubtractionOrderCatchDate(), "自动抓单"); //更新店铺最晚抓单时间
        doSaveOrderInfoByDate(storeEntity, response); //更新订单信息
        boolean hasNextPage = ObjectUtil.isNotNull(response.getHasNextPage()) && response.getHasNextPage();
        if (hasNextPage) {
            orderRequest.setPageIndex(orderRequest.getPageIndex() + 1);
            orderRequest.setNextPageInfo(response.getNextPageInfo());
            getPlatformOrderByDate(storeEntity, orderRequest);
        }
    }

    /**
     * 按订单号抓单
     */
    @Override
    protected void getPlatformOrderListByIds(SaStoreEntity storeEntity, PlatformOrderRequest orderRequest, List<OrderMissedQueueEntity> orderMissedQueueEntityList) {
        PlatformOrderInfoResponse response = thirdPartyApiService.getPlatformOrderListByIdsForOrderInfo(orderRequest);
        if (ObjectUtil.isNull(response) || NsyCollUtil.isEmpty(response.getPlatformOrderInfoList())) {
            orderMissedQueueService.updateOrderMissedQueue(orderMissedQueueEntityList, QueueStatusEnum.EXECUTE_FAIL, "订单为空", "自动抓单");
            return;
        }
        doSaveOrderInfoByOrderNo(storeEntity, response); //更新订单信息
        orderMissedQueueService.updateOrderMissedQueue(orderMissedQueueEntityList, QueueStatusEnum.EXECUTE_SUCCESS, "", "自动抓单");
    }

    /**
     * 按订单号抓订单明细
     */
    @Override
    protected void getPlatformOrderItemListByIds(SaStoreEntity storeEntity, PlatformOrderRequest orderRequest, List<OrderItemGrabQueueEntity> orderItemGrabQueueEntityList) {
        PlatformOrderInfoResponse response = thirdPartyApiService.getPlatformOrderItemListByIdsForOrderInfo(orderRequest);
        if (ObjectUtil.isNull(response) || NsyCollUtil.isEmpty(response.getPlatformOrderInfoList())) {
            orderItemGrabQueueService.updateOrderItemGrabQueue(orderItemGrabQueueEntityList, QueueStatusEnum.EXECUTE_FAIL, "订单为空", "自动抓单");
            return;
        }
        doSaveOrderItemInfo(storeEntity, response); //更新订单明细信息
        orderItemGrabQueueService.updateOrderItemGrabQueue(orderItemGrabQueueEntityList, QueueStatusEnum.EXECUTE_SUCCESS, "", "自动抓单");
    }

    /**
     * 处理根据时间区间抓单返回信息--只有订单表头信息和订单收件人信息（没有订单明细）
     */
    protected void doSaveOrderInfoByDate(SaStoreEntity storeEntity, PlatformOrderInfoResponse response) {
        response.getPlatformOrderInfoList().forEach(platformOrderInfo -> {
            SpringContextUtils.getBean(this.getClass()).handleOrderInfo(platformOrderInfo, storeEntity); //处理抓单返回订单信息
        });
    }

    /**
     * 处理根据订单号抓单返回信息--只有订单表头信息和订单收件人信息（没有订单明细）
     */
    protected void doSaveOrderInfoByOrderNo(SaStoreEntity storeEntity, PlatformOrderInfoResponse response) {
        response.getPlatformOrderInfoList().forEach(platformOrderInfo -> {
            SpringContextUtils.getBean(this.getClass()).handleOrderInfo(platformOrderInfo, storeEntity); //处理抓单返回订单信息
        });
    }

    /**
     * 处理根据订单号抓取订单明细信息，没有订单表头
     */
    protected void doSaveOrderItemInfo(SaStoreEntity storeEntity, PlatformOrderInfoResponse response) {
        response.getPlatformOrderInfoList().forEach(platformOrderInfo -> {
            SpringContextUtils.getBean(this.getClass()).handleOrderItemInfo(platformOrderInfo, storeEntity);
        });
    }

    /**
     * 处理抓单返回订单信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleOrderInfo(PlatformOrderInfo platformOrderInfo, SaStoreEntity storeEntity) {
        if ("production".equalsIgnoreCase(env) && ObjectUtil.isNotNull(platformOrderInfo.getOrderPaymentDate()) && platformOrderInfo.getOrderPaymentDate().before(DateUtil.offset(DateUtil.beginOfDay(new Date()), DateField.YEAR, -1))) {
            return; //付款时间一年前不处理
        }
        OrderIdempotentEntity orderIdempotentEntity = orderIdempotentService.getByStoreIdAndOrderNo(storeEntity.getId(), platformOrderInfo.getPlatformOrderNo()); //检验订单幂等
        String orderCrypt = MD5Util.crypt(NsyJacksonUtils.toJson(platformOrderInfo));
        if (ObjectUtil.isNotNull(orderIdempotentEntity)) {
            LOGGER.info("店铺ID_订单号_幂等表加密_单据加密:{}", String.format("%s_%s_%s_%s", storeEntity.getId(), platformOrderInfo.getPlatformOrderNo(), orderIdempotentEntity.getContent(), orderCrypt));
            if (orderIdempotentEntity.getContent().equals(orderCrypt)) {
                return; //返回信息和上一次没有变化，无须处理
            }
        }
        Integer platformId;
        Integer orderId;
        String platformOrderNo;
        OrderTypeEnum orderType;
        if (PlatformShippingTypeEnum.isPlatformOrder(platformOrderInfo.getShippingType())) {
            if (PlatformOrderStatusEnum.PENDING.getOutCode().equalsIgnoreCase(platformOrderInfo.getOrderStatus())) {
                LOGGER.info("订单号:{},平台时未付款状态，不允许抓到系统中", platformOrderInfo.getPlatformOrderNo());
                return;
            }
            PlatformOrderEntity oldPlatformOrderEntity = platformOrderService.getOrderByStoreIdAndOrderNo(storeEntity.getId(), platformOrderInfo.getPlatformOrderNo());
            if (ObjectUtil.isNotNull(oldPlatformOrderEntity) && checkOrderStatusIsDelivery(oldPlatformOrderEntity.getPlatformOriginalOrderNo(), oldPlatformOrderEntity.getOrderStatus())) {
                return; //订单状态已发货，无须处理
            }
            PlatformOrderEntity platformOrderEntity = savePlatformOrder(platformOrderInfo, oldPlatformOrderEntity, storeEntity); //保存(平台仓发货)订单表头
            savePlatformOrderReceiver(platformOrderInfo, platformOrderEntity); //保存(平台仓发货)订单收件人信息
            platformId = platformOrderEntity.getPlatformId();
            orderId = platformOrderEntity.getPlatformOrderId();
            platformOrderNo = platformOrderEntity.getPlatformOriginalOrderNo();
            orderType = OrderTypeEnum.FBA;
        } else {
            SaleOrderEntity oldSaleOrderEntity = saleOrderService.getByPlatformOrderNo(storeEntity.getId(), platformOrderInfo.getPlatformOrderNo());
            if (ObjectUtil.isNotNull(oldSaleOrderEntity) && checkOrderStatusIsDelivery(oldSaleOrderEntity.getPlatformOrderNo(), oldSaleOrderEntity.getOrderStatus())) {
                return; //订单状态已发货，无须处理
            }
            SaleOrderEntity saleOrderEntity = saveOrder(platformOrderInfo, oldSaleOrderEntity, storeEntity); //保存(本地仓发货)订单表头
            saveOrderReceiver(platformOrderInfo, saleOrderEntity); //保存(本地仓发货)订单收件人信息
            platformId = saleOrderEntity.getPlatformId();
            orderId = saleOrderEntity.getOrderId();
            platformOrderNo = saleOrderEntity.getPlatformOrderNo();
            orderType = OrderTypeEnum.FBM;
        }
        orderIdempotentService.saveOrUpdateOrderIdempotent(orderCrypt, storeEntity, platformOrderNo, "自动抓单"); //保存或更新 订单幂等
        orderGrabStatusService.saveOrUpdateOrderGrabStatus(storeEntity, orderId, platformOrderNo, orderType, "自动抓单", IsEnum.IS_NOT.getCode(), IsEnum.IS.getCode()); //保存或更新 订单状态表
        orderItemGrabQueueService.saveOrderItemGrabQueue(storeEntity, orderId, platformOrderNo, orderType, platformId, "自动抓单"); //插入 订单号明细队列
    }

    /**
     * 处理根据订单号抓取订单明细信息，没有订单表头
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleOrderItemInfo(PlatformOrderInfo platformOrderInfo, SaStoreEntity storeEntity) {
        PlatformOrderEntity platformOrderEntity = platformOrderService.getOrderByStoreIdAndOrderNo(storeEntity.getId(), platformOrderInfo.getPlatformOrderNo());
        if (ObjectUtil.isNotNull(platformOrderEntity) && !checkOrderStatusIsDelivery(platformOrderEntity.getPlatformOriginalOrderNo(), platformOrderEntity.getOrderStatus())) {
            savePlatformOrderItem(platformOrderInfo, platformOrderEntity); //保存(平台仓发货)订单明细信息
            orderGrabStatusService.updateOrderGrabStatusForIsGrabItem(storeEntity.getId(), platformOrderEntity.getPlatformOrderId(), platformOrderEntity.getPlatformOriginalOrderNo(), IsEnum.IS.getCode(), "自动抓单"); //更新 订单状态表是否抓取详情
            return;
        }

        SaleOrderEntity saleOrderEntity = saleOrderService.getByPlatformOrderNo(storeEntity.getId(), platformOrderInfo.getPlatformOrderNo());
        if (ObjectUtil.isNotNull(saleOrderEntity) && !checkOrderStatusIsDelivery(saleOrderEntity.getPlatformOrderNo(), saleOrderEntity.getOrderStatus())) {
            saveOrderItem(platformOrderInfo, saleOrderEntity); //保存(本地仓发货)订单明细信息
            orderGrabStatusService.updateOrderGrabStatusForIsGrabItem(storeEntity.getId(), saleOrderEntity.getOrderId(), saleOrderEntity.getPlatformOrderNo(), IsEnum.IS.getCode(), "自动抓单"); //更新 订单状态表是否抓取详情
        }
    }

    /**
     * 计算订单最新抓单时间
     */
    protected Date calculateLatestOrderGrabDate(List<PlatformOrderInfo> list, Date endOrderTime, boolean hasNextPage) {
        if (hasNextPage) {
            return list.stream()
                    .map(PlatformOrderInfo::getUpdateDate)
                    .filter(updateDate -> Optional.ofNullable(updateDate).isPresent())
                    .max(Date::compareTo)
                    .orElse(endOrderTime);
        }
        return endOrderTime;
    }
}
