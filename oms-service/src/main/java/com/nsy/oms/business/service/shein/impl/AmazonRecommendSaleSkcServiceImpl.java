package com.nsy.oms.business.service.shein.impl;

import com.alibaba.fastjson.JSON;
import com.nsy.api.core.apicore.privilege.LoginInfoService;
import com.nsy.oms.business.domain.request.shein.SheinRecommendSaleSkcPushRequest;
import com.nsy.oms.business.domain.request.shein.SheinRecommendSaleSkcRequest;
import com.nsy.oms.business.manage.omspublish.OmsPublishApiService;
import com.nsy.oms.business.manage.omspublish.response.SelectWebsiteModel;
import com.nsy.oms.business.manage.product.ProductApiService;
import com.nsy.oms.business.manage.search.SearchApiService;
import com.nsy.oms.business.manage.user.request.shared.SelectModel;
import com.nsy.oms.business.manage.user.response.SysUserInfo;
import com.nsy.oms.business.service.shein.RecommendSaleSkcAdapter;
import com.nsy.oms.business.service.shein.RecommendSaleSkcPlatformService;
import com.nsy.oms.business.service.shein.SheinRecommendOperationLogService;
import com.nsy.oms.constants.StringConstant;
import com.nsy.oms.enums.shein.SheinRecommendPlatformEnum;
import com.nsy.oms.enums.shein.SheinRecommendSaleSkcStatusEnum;
import com.nsy.oms.enums.shein.SheinRecommendTypeEnum;
import com.nsy.oms.repository.dao.sa.SaStoreWebsiteDao;
import com.nsy.oms.repository.dao.shein.SheinRecommendSaleSkcDao;
import com.nsy.oms.repository.entity.sa.SaStoreEntity;
import com.nsy.oms.repository.entity.sa.SaStoreWebsiteEntity;
import com.nsy.oms.repository.entity.shein.SheinRecommendOperationLogEntity;
import com.nsy.oms.repository.entity.shein.SheinRecommendSaleSkcEntity;
import com.nsy.oms.utils.Validator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 *
 */
@Slf4j
@Service("amazonRecommendSaleSkcServiceImpl")
public class AmazonRecommendSaleSkcServiceImpl implements RecommendSaleSkcPlatformService {

    @Autowired
    private OmsPublishApiService omsPublishApiService;
    @Autowired
    private SheinRecommendSaleSkcDao sheinRecommendSaleSkcDao;
    @Autowired
    private SaStoreWebsiteDao saStoreWebsiteDao;
    @Autowired
    private SheinRecommendOperationLogService sheinRecommendOperationLogService;
    @Autowired
    private SearchApiService searchApiService;
    @Autowired
    private ProductApiService productApiService;
    @Autowired
    private LoginInfoService loginInfoService;

    @Override
    public String platform() {
        return SheinRecommendPlatformEnum.AMAZON.getOmsPublishPlatform();
    }

    @Override
    public void validPush(SheinRecommendSaleSkcPushRequest request, List<Integer> storeIds) {
        List<SheinRecommendSaleSkcEntity> sheinRecommendSaleSkcEntities = sheinRecommendSaleSkcDao.getBySkcInAndPlatform(request.getColorList(), platform());
        sheinRecommendSaleSkcEntities = sheinRecommendSaleSkcEntities.stream().filter(item -> Objects.nonNull(item.getRecommendStoreId()) ? storeIds.contains(item.getRecommendStoreId()) : storeIds.contains(item.getPublishStoreId())).collect(Collectors.toList());
        Validator.valid(sheinRecommendSaleSkcEntities, CollectionUtils::isNotEmpty, String.format("已经在同店铺有建议款式[%s]不允许重复推送", sheinRecommendSaleSkcEntities.stream().map(SheinRecommendSaleSkcEntity::getSkc).distinct().collect(Collectors.joining(","))));
        List<String> existSkcList = omsPublishApiService.getByPlatformAndColorSkuList(request.getColorList(), request.getWebsiteIds(), request.getIsForcedPush(), platform());
        Validator.valid(existSkcList, CollectionUtils::isNotEmpty, String.format("已经在同店铺上架的款式[%s]不允许重复推送", existSkcList.stream().distinct().collect(Collectors.joining(","))));
    }

    @Override
    public void validModifyRecommendStore(List<SheinRecommendSaleSkcEntity> sourceEntities, SaStoreEntity targetStore) {
        List<SheinRecommendSaleSkcEntity> skcPlatformEntities = sheinRecommendSaleSkcDao.getBySkcInAndPlatform(sourceEntities.stream().map(SheinRecommendSaleSkcEntity::getSkc).collect(Collectors.toList()), SheinRecommendPlatformEnum.of(targetStore.allPlatform()).getOmsPublishPlatform());
        List<String> dumplicatedSkcList = skcPlatformEntities.stream().filter(item -> Objects.equals(targetStore.getId(), item.getRecommendStoreId())).map(SheinRecommendSaleSkcEntity::getSkc).distinct().collect(Collectors.toList());
        Validator.valid(dumplicatedSkcList, CollectionUtils::isNotEmpty, String.format("目标推荐店铺已经存在相同skc推荐,请确认[%s]", dumplicatedSkcList.stream().distinct().collect(Collectors.joining(","))));
    }

    /**
     * 1. 其他spu占用
     * 2. skc忽略 + 存在?
     * 4. 新品? 复色?
     * @param request
     * @param saStoreEntity
     * @return
     */
    @Override
    public Optional<SheinRecommendSaleSkcEntity> filterAndBuildSystemAddSaleSkc(SheinRecommendSaleSkcRequest request, SaStoreEntity saStoreEntity) {
        // 推荐列表
        List<SheinRecommendSaleSkcEntity> recommendSaleSkcEntities = sheinRecommendSaleSkcDao.getByProductIdAndPlatform(request.getProductId(), platform()).stream().filter(item -> Objects.equals(saStoreEntity.getId(), item.getPublishStoreId()) || Objects.equals(saStoreEntity.getId(), item.getRecommendStoreId())).collect(Collectors.toList());
        Optional<SheinRecommendSaleSkcEntity> ignore = recommendSaleSkcEntities.stream()
                .filter(item -> item.getSkc().equals(request.getSkc()))
                .filter(item -> Objects.equals(item.getStatus(), SheinRecommendSaleSkcStatusEnum.IGNORE.getCode()) || Objects.equals(item.getStatus(), SheinRecommendSaleSkcStatusEnum.UN_SHELF.getCode()) || Objects.equals(item.getStatus(), SheinRecommendSaleSkcStatusEnum.SHELFING.getCode())).findAny();
        if (ignore.isPresent()) {
            ignore.get().setUpdateDate(new Date());
            return ignore;
        }

        // 刊登
        String websiteId;
        List<SaStoreWebsiteEntity> saStoreWebsiteEntities = saStoreWebsiteDao.getListByStoreIds(Collections.singletonList(saStoreEntity.getId()));
        if (CollectionUtils.isNotEmpty(saStoreWebsiteEntities)) {
            websiteId = saStoreWebsiteEntities.get(0).getWebsiteId().toString();
        } else {
            websiteId = StringConstant.EMPTY;
        }
        boolean newProduct = true;
        List<SelectModel> spuPublishStores = omsPublishApiService.getSpuPublishData(request.getProductId(), platform());
        if (CollectionUtils.isNotEmpty(spuPublishStores)) {
            Optional<SelectModel> omsIgnore = spuPublishStores.stream().filter(item -> item.getId().equals(request.getSkc()) && item.getLabel().equals(websiteId)).findAny();
            if (omsIgnore.isPresent()) return Optional.empty();
            Optional<SelectModel> spuPublished = spuPublishStores.stream().filter(item -> item.getLabel().equals(websiteId)).findAny();
            if (spuPublished.isPresent()) {
                newProduct = false;
            }
        }

        Optional<SheinRecommendSaleSkcEntity> hasRecommend = recommendSaleSkcEntities.stream().filter(item -> Objects.equals(saStoreEntity.getId(), item.getRecommendStoreId()))
                .filter(item -> Objects.equals(item.getStatus(), SheinRecommendSaleSkcStatusEnum.SHELFING.getCode())).findAny();
        if (hasRecommend.isPresent()) {
            newProduct = false;
        }
        SheinRecommendSaleSkcEntity entity = sheinRecommendSaleSkcDao.getOneIncludeDeleteBySkcAndRecommendStore(request.getSkc(), saStoreEntity.getId());
        if (Objects.isNull(entity)) {
            entity = new SheinRecommendSaleSkcEntity();
            entity.setCreateDate(new Date());
            entity.setCreateBy(RecommendSaleSkcAdapter.SYSTEM_OPERATOR);
            entity.setLocation(saStoreEntity.getLocation());
        }
        entity.setProductId(request.getProductId());
        entity.setPlatform(platform());
        entity.setReason(0);
        entity.setStatus(SheinRecommendSaleSkcStatusEnum.UN_SHELF.getCode());
        entity.setRecommendStoreId(saStoreEntity.getId());
        entity.setRecommendStoreName(saStoreEntity.getErpStoreName());
        entity.setSkc(request.getSkc());
        entity.setRecommendType(newProduct ? SheinRecommendTypeEnum.NEW_PRODUCT.name() : SheinRecommendTypeEnum.ADD_COLOR.name());
        entity.setUpdateDate(new Date());
        entity.setUpdateBy(RecommendSaleSkcAdapter.SYSTEM_OPERATOR);
        return Optional.of(entity);
    }

    /**
     * 标识上架同spu,skc操作
     * @param sourceEntity
     */
    @Override
    public void modifySameSpuOrSkc(SheinRecommendSaleSkcEntity sourceEntity, SysUserInfo userInfo) {
        log.info("[打印参数] TiktokShopRecommendSaleSkcServiceImpl modifySameSpuOrSkc sourceEntity: {} timestamp: {}", JSON.toJSONString(sourceEntity), LocalDateTime.now());
        // 同spu未上架数据
        List<SheinRecommendSaleSkcEntity> otherEntities = sheinRecommendSaleSkcDao.getByProductIdAndPlatformAndInShelf(sourceEntity.getProductId(), sourceEntity.getPlatform())
                .stream().filter(item -> !Objects.equals(sourceEntity.getSheinRecommendSaleSkcId(), item.getSheinRecommendSaleSkcId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(otherEntities)) {
            List<SheinRecommendSaleSkcEntity> updateList = new ArrayList<>();
            List<SheinRecommendOperationLogEntity> logEntities = new ArrayList<>();
            log.info("[打印参数2] TiktokShopRecommendSaleSkcServiceImpl modifySameSpuOrSkc otherEntities: {} timestamp: {}", JSON.toJSONString(otherEntities), LocalDateTime.now());
            // 同spu其他更新为复色
            otherEntities.stream().filter(item -> Objects.equals(item.getRecommendStoreId(), sourceEntity.getRecommendStoreId()) && Objects.equals(item.getRecommendType(), SheinRecommendTypeEnum.NEW_PRODUCT.name())).forEach(item -> {
                String content = String.format("skc:%s;推荐类型:%s->%s;推荐店铺:%s", item.getSkc(), SheinRecommendTypeEnum.valueOf(item.getRecommendType()).getDesc(), SheinRecommendTypeEnum.ADD_COLOR.getDesc(), item.getRecommendStoreName());
                SheinRecommendOperationLogEntity sheinRecommendOperationLogEntity = sheinRecommendOperationLogService.buildSheinRecommendOperationLog(item.getSheinRecommendSaleSkcId(), "更改复色", content, userInfo.getUserId().toString(), userInfo.getUserAccount(), userInfo.getUserName(), userInfo.getLocation());
                logEntities.add(sheinRecommendOperationLogEntity);
                item.setRecommendType(SheinRecommendTypeEnum.ADD_COLOR.name());
                item.setUpdateBy(userInfo.getUserAccount());
                updateList.add(item);
            });
            sheinRecommendSaleSkcDao.saveOrUpdateBatch(updateList);
            sheinRecommendOperationLogService.saveBatch(logEntities);
            searchApiService.syncSheinRecommendByIds(updateList.stream().map(SheinRecommendSaleSkcEntity::getSheinRecommendSaleSkcId).collect(Collectors.toList()));
            log.info("[打印参数3] TiktokShopRecommendSaleSkcServiceImpl modifySameSpuOrSkc updateList: {} timestamp: {}", JSON.toJSONString(updateList), LocalDateTime.now());
        }
        List<SaStoreWebsiteEntity> saStoreWebsiteEntities = saStoreWebsiteDao.getListByStoreIds(Collections.singletonList(sourceEntity.getPublishStoreId()));
        if (CollectionUtils.isEmpty(saStoreWebsiteEntities)) return;
        List<SelectWebsiteModel> websiteModels = omsPublishApiService.getErpWebsiteConfigByConfigWebsiteIds(saStoreWebsiteEntities.stream().map(SaStoreWebsiteEntity::getWebsiteId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(websiteModels)) return;
        // source需要到待刊登
        productApiService.addQueueForProductToPublish(sourceEntity.getProductId(), Collections.singletonList(sourceEntity.getSkc()), websiteModels, loginInfoService.getUserName());
    }

    @Override
    public void pending(SheinRecommendSaleSkcEntity sheinRecommendSaleSkcEntity) {

        List<SaStoreWebsiteEntity> saStoreWebsiteEntities = saStoreWebsiteDao.getListByStoreIds(Collections.singletonList(sheinRecommendSaleSkcEntity.getPublishStoreId()));
        if (CollectionUtils.isEmpty(saStoreWebsiteEntities)) return;
        List<SelectWebsiteModel> websiteModels = omsPublishApiService.getErpWebsiteConfigByConfigWebsiteIds(saStoreWebsiteEntities.stream().map(SaStoreWebsiteEntity::getWebsiteId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(websiteModels)) return;

        productApiService.removeQueueForProductToPublish(sheinRecommendSaleSkcEntity.getProductId(), sheinRecommendSaleSkcEntity.getSkc(), websiteModels, loginInfoService.getUserName());

        sheinRecommendSaleSkcEntity.setStatus(SheinRecommendSaleSkcStatusEnum.UN_SHELF.getCode());
        sheinRecommendSaleSkcEntity.setMarkDate(null);
    }

}
