package com.nsy.oms.business.manage.bi.response.fact;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 亚马逊利润测算基础信息表
 */
public class FactAmazonProfitabilityAnalysisSku {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * spu
     */

    private String spu;

    /**
     * skc
     */

    private String skc;

    private String sku;

    /**
     * 店铺id
     */

    private Integer storeId;

    /**
     * 店铺名称
     */

    private String storeName;

    /**
     * 市场
     */

    private String marketplaceCode;

    /**
     * asin
     */

    private String parentAsin;

    /**
     * 最长边/cm
     */

    private BigDecimal longestSide;

    /**
     * 中等边/cm
     */

    private BigDecimal medianSide;

    /**
     * 最短边/cm
     */

    private BigDecimal shortestSide;

    /**
     * 重量/g
     */

    private BigDecimal weight;

    /**
     * 商品成本/美元
     */

    private BigDecimal productCost;

    /**
     * 退货率
     */

    private BigDecimal returnRate;

    /**
     * 品类退货不可售比例
     */

    private BigDecimal cateReturnUnsallableRate;

    /**
     * 广告费率
     */

    private BigDecimal adCostRate;

    /**
     * 产品尺寸层级(包裹类型)
     */

    private String productSizeTier;

    /**
     * 近7天销量
     */

    private Integer last7DateSaleQty;

    /**
     * 近7天销售收入
     */

    private BigDecimal last7DateSaleIncome;

    /**
     * 近14天销量
     */

    private Integer last14DateSaleQty;

    /**
     * 近14天销售收入
     */

    private BigDecimal last14DateSaleIncome;

    /**
     * 近30天销量
     */

    private Integer last30DateSaleQty;

    /**
     * 近30天销售收入
     */

    private BigDecimal last30DateSaleIncome;

    /**
     * 总库存
     */

    private Integer totalInv;

    /**
     * 泉州仓
     */

    private Integer quanzhouInv;

    /**
     * 海外仓
     */

    private Integer overseasInv;

    /**
     * 海外预留
     */
    private Integer overseasInReservedInv;

    /**
     * 采购在途
     */

    private Integer purchaseInTransitInv;

    /**
     * 7日净库销
     */

    private BigDecimal last7DateInvSalesRate;

    /**
     * 14日净库销
     */

    private BigDecimal last14DateInvSalesRate;

    /**
     * 30日净库销
     */

    private BigDecimal last30DateInvSalesRate;

    /**
     * 库龄0-90
     */

    private Integer invAge0To90;

    /**
     * 库龄91-180
     */

    private Integer invAge91To180;

    /**
     * 库龄181-270
     */

    private Integer invAge181To270;

    /**
     * 库龄271-365
     */

    private Integer invAge271To365;

    /**
     * 库龄>365
     */

    private Integer invAgeGt365;

    /**
     * 库龄合计
     */

    private Integer invAgeTotal;

    /**
     * 活动价($)
     */

    private BigDecimal salePrice;

    /**
     * 原价($)
     */

    private BigDecimal yourPrice;

    /**
     * 近24H最低成交价
     */
    private BigDecimal minOrderPriceIn24Hours;

    /**
     * 近24H最高成交价
     */
    private BigDecimal maxOrderPriceIn24Hours;

    /**
     * 区域
     */

    private String location;

    /**
     * 创建时间
     */

    private Date createDate;

    /**
     * 更新时间
     */

    private Date updateDate;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSpu() {
        return spu;
    }

    public void setSpu(String spu) {
        this.spu = spu;
    }

    public String getSkc() {
        return skc;
    }

    public void setSkc(String skc) {
        this.skc = skc;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getMarketplaceCode() {
        return marketplaceCode;
    }

    public void setMarketplaceCode(String marketplaceCode) {
        this.marketplaceCode = marketplaceCode;
    }

    public String getParentAsin() {
        return parentAsin;
    }

    public void setParentAsin(String parentAsin) {
        this.parentAsin = parentAsin;
    }

    public BigDecimal getLongestSide() {
        return longestSide;
    }

    public void setLongestSide(BigDecimal longestSide) {
        this.longestSide = longestSide;
    }

    public BigDecimal getMedianSide() {
        return medianSide;
    }

    public void setMedianSide(BigDecimal medianSide) {
        this.medianSide = medianSide;
    }

    public BigDecimal getShortestSide() {
        return shortestSide;
    }

    public void setShortestSide(BigDecimal shortestSide) {
        this.shortestSide = shortestSide;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public BigDecimal getProductCost() {
        return productCost;
    }

    public void setProductCost(BigDecimal productCost) {
        this.productCost = productCost;
    }

    public BigDecimal getReturnRate() {
        return returnRate;
    }

    public void setReturnRate(BigDecimal returnRate) {
        this.returnRate = returnRate;
    }

    public BigDecimal getCateReturnUnsallableRate() {
        return cateReturnUnsallableRate;
    }

    public void setCateReturnUnsallableRate(BigDecimal cateReturnUnsallableRate) {
        this.cateReturnUnsallableRate = cateReturnUnsallableRate;
    }

    public BigDecimal getAdCostRate() {
        return adCostRate;
    }

    public void setAdCostRate(BigDecimal adCostRate) {
        this.adCostRate = adCostRate;
    }

    public String getProductSizeTier() {
        return productSizeTier;
    }

    public void setProductSizeTier(String productSizeTier) {
        this.productSizeTier = productSizeTier;
    }

    public Integer getLast7DateSaleQty() {
        return last7DateSaleQty;
    }

    public void setLast7DateSaleQty(Integer last7DateSaleQty) {
        this.last7DateSaleQty = last7DateSaleQty;
    }

    public BigDecimal getLast7DateSaleIncome() {
        return last7DateSaleIncome;
    }

    public void setLast7DateSaleIncome(BigDecimal last7DateSaleIncome) {
        this.last7DateSaleIncome = last7DateSaleIncome;
    }

    public Integer getLast14DateSaleQty() {
        return last14DateSaleQty;
    }

    public void setLast14DateSaleQty(Integer last14DateSaleQty) {
        this.last14DateSaleQty = last14DateSaleQty;
    }

    public BigDecimal getLast14DateSaleIncome() {
        return last14DateSaleIncome;
    }

    public void setLast14DateSaleIncome(BigDecimal last14DateSaleIncome) {
        this.last14DateSaleIncome = last14DateSaleIncome;
    }

    public Integer getLast30DateSaleQty() {
        return last30DateSaleQty;
    }

    public void setLast30DateSaleQty(Integer last30DateSaleQty) {
        this.last30DateSaleQty = last30DateSaleQty;
    }

    public BigDecimal getLast30DateSaleIncome() {
        return last30DateSaleIncome;
    }

    public void setLast30DateSaleIncome(BigDecimal last30DateSaleIncome) {
        this.last30DateSaleIncome = last30DateSaleIncome;
    }

    public Integer getTotalInv() {
        return totalInv;
    }

    public void setTotalInv(Integer totalInv) {
        this.totalInv = totalInv;
    }

    public Integer getQuanzhouInv() {
        return quanzhouInv;
    }

    public void setQuanzhouInv(Integer quanzhouInv) {
        this.quanzhouInv = quanzhouInv;
    }

    public Integer getOverseasInv() {
        return overseasInv;
    }

    public void setOverseasInv(Integer overseasInv) {
        this.overseasInv = overseasInv;
    }

    public Integer getOverseasInReservedInv() {
        return overseasInReservedInv;
    }

    public void setOverseasInReservedInv(Integer overseasInReservedInv) {
        this.overseasInReservedInv = overseasInReservedInv;
    }

    public Integer getPurchaseInTransitInv() {
        return purchaseInTransitInv;
    }

    public void setPurchaseInTransitInv(Integer purchaseInTransitInv) {
        this.purchaseInTransitInv = purchaseInTransitInv;
    }

    public BigDecimal getLast7DateInvSalesRate() {
        return last7DateInvSalesRate;
    }

    public void setLast7DateInvSalesRate(BigDecimal last7DateInvSalesRate) {
        this.last7DateInvSalesRate = last7DateInvSalesRate;
    }

    public BigDecimal getLast14DateInvSalesRate() {
        return last14DateInvSalesRate;
    }

    public void setLast14DateInvSalesRate(BigDecimal last14DateInvSalesRate) {
        this.last14DateInvSalesRate = last14DateInvSalesRate;
    }

    public BigDecimal getLast30DateInvSalesRate() {
        return last30DateInvSalesRate;
    }

    public void setLast30DateInvSalesRate(BigDecimal last30DateInvSalesRate) {
        this.last30DateInvSalesRate = last30DateInvSalesRate;
    }

    public Integer getInvAge0To90() {
        return invAge0To90;
    }

    public void setInvAge0To90(Integer invAge0To90) {
        this.invAge0To90 = invAge0To90;
    }

    public Integer getInvAge91To180() {
        return invAge91To180;
    }

    public void setInvAge91To180(Integer invAge91To180) {
        this.invAge91To180 = invAge91To180;
    }

    public Integer getInvAge181To270() {
        return invAge181To270;
    }

    public void setInvAge181To270(Integer invAge181To270) {
        this.invAge181To270 = invAge181To270;
    }

    public Integer getInvAge271To365() {
        return invAge271To365;
    }

    public void setInvAge271To365(Integer invAge271To365) {
        this.invAge271To365 = invAge271To365;
    }

    public Integer getInvAgeGt365() {
        return invAgeGt365;
    }

    public void setInvAgeGt365(Integer invAgeGt365) {
        this.invAgeGt365 = invAgeGt365;
    }

    public Integer getInvAgeTotal() {
        return invAgeTotal;
    }

    public void setInvAgeTotal(Integer invAgeTotal) {
        this.invAgeTotal = invAgeTotal;
    }

    public BigDecimal getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(BigDecimal salePrice) {
        this.salePrice = salePrice;
    }

    public BigDecimal getYourPrice() {
        return yourPrice;
    }

    public void setYourPrice(BigDecimal yourPrice) {
        this.yourPrice = yourPrice;
    }

    public BigDecimal getMinOrderPriceIn24Hours() {
        return minOrderPriceIn24Hours;
    }

    public void setMinOrderPriceIn24Hours(BigDecimal minOrderPriceIn24Hours) {
        this.minOrderPriceIn24Hours = minOrderPriceIn24Hours;
    }

    public BigDecimal getMaxOrderPriceIn24Hours() {
        return maxOrderPriceIn24Hours;
    }

    public void setMaxOrderPriceIn24Hours(BigDecimal maxOrderPriceIn24Hours) {
        this.maxOrderPriceIn24Hours = maxOrderPriceIn24Hours;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }
} 