package com.nsy.oms.business.service.auth.impl;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.privilege.LoginInfoService;
import com.nsy.oms.business.converter.auth.SauAliInterConfigConverter;
import com.nsy.oms.business.domain.CommonAuthResponse;
import com.nsy.oms.business.domain.request.auth.SauAliInterConfigPageRequest;
import com.nsy.oms.business.domain.request.auth.SauAliInterConfigRequest;
import com.nsy.oms.business.domain.request.external.ErpUpdateStoreAuthInfoRequest;
import com.nsy.oms.business.domain.request.sa.DistributorsStoreRequest;
import com.nsy.oms.business.domain.response.auth.SauAliInterConfigPageResponse;
import com.nsy.oms.business.domain.response.auth.SauAliInterConfigResponse;
import com.nsy.oms.business.domain.response.base.PageResponse;
import com.nsy.oms.business.domain.response.sa.DistributorsStoreResponse;
import com.nsy.oms.business.external.ErpPlatformService;
import com.nsy.oms.business.service.auth.SauAliInterConfigService;
import com.nsy.oms.business.service.bd.BdOperateLogService;
import com.nsy.oms.business.service.base.BaseService;
import com.nsy.oms.business.service.sa.SaStoreService;
import com.nsy.oms.enums.bd.BusinessTypeEnum;
import com.nsy.oms.enums.sa.PlatformEnum;
import com.nsy.oms.enums.sau.GrantAuthStatusEnum;
import com.nsy.oms.repository.dao.auth.SauAliInterConfigDao;
import com.nsy.oms.repository.entity.auth.SauAliInterConfigEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/11/22 10:33
 * sau 是 店铺授权缩写(store_auth)
 */
@Service
@Slf4j
public class SauAliInterConfigServiceImpl implements SauAliInterConfigService, ErpPlatformService<SauAliInterConfigResponse> {
    @Autowired
    private SauAliInterConfigDao sauAliInterConfigDao;
    @Autowired
    private BdOperateLogService bdOperateLogService;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private BaseService baseService;
    @Autowired
    private SaStoreService saStoreService;

    @Override
    public PageResponse<SauAliInterConfigPageResponse> pageList(SauAliInterConfigPageRequest pageRequest) {
        pageRequest.setLocation(loginInfoService.getLocation());
        Page<SauAliInterConfigPageResponse> page = SauAliInterConfigConverter.INSTANCE.toSauAliInterConfigPageResponsePage(sauAliInterConfigDao.pageList(pageRequest));
        List<SauAliInterConfigPageResponse> records = page.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            return PageResponse.of(records, page.getTotal());
        }
        return PageResponse.empty();
    }

    @Override
    public SauAliInterConfigResponse getAppointInfoById(Integer id) {
        return SauAliInterConfigConverter.INSTANCE.toSauAliInterConfigResponse(sauAliInterConfigDao.getDetailInfoById(id));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveInfo(SauAliInterConfigRequest request) {
        SauAliInterConfigEntity sauAliInterConfigEntity;
        baseService.validateStoreIsAddAuth(request.getStoreId());
        try {
            //授权信息
            sauAliInterConfigEntity = SauAliInterConfigConverter.INSTANCE.toSauAliInterConfigEntity(request);
            sauAliInterConfigEntity.setMarketId(0);
            sauAliInterConfigEntity.setCreateBy(loginInfoService.getName());
            sauAliInterConfigDao.saveInfo(sauAliInterConfigEntity);
            saStoreService.updateStoreIsAuthStatus(request.getStoreId());
        } catch (DuplicateKeyException e) {
            throw new BusinessServiceException(String.format("店铺【%s】已存在", request.getStoreName()));
        }

        //操作日志
        bdOperateLogService.addLog(BusinessTypeEnum.ALI_INTER.name(), sauAliInterConfigEntity.getId());
        baseService.sendQueue(sauAliInterConfigEntity.getStoreId(), BusinessTypeEnum.ALI_INTER.name());
        baseService.sendAuthQueue(sauAliInterConfigEntity.getStoreId(), 1, BusinessTypeEnum.ALI_INTER.name());

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateInfo(SauAliInterConfigRequest request) {
        //授权信息
        Integer id = request.getId();
        SauAliInterConfigEntity oldConfig = isExist(request.getId());
        if (!oldConfig.getStoreId().equals(request.getStoreId())) {
            throw new BusinessServiceException("店铺不允许重新修改");
        }
        SauAliInterConfigEntity newConfig = SauAliInterConfigConverter.INSTANCE.toSauAliInterConfigEntity(request);
        newConfig.setUpdateBy(loginInfoService.getName());
        sauAliInterConfigDao.updateById(newConfig);

        //操作日志
        String description = bdOperateLogService.getLogDescription(oldConfig, newConfig, SauAliInterConfigEntity.class, false);
        bdOperateLogService.updateLog(BusinessTypeEnum.ALI_INTER.name(), id, description);

        baseService.sendQueue(oldConfig.getStoreId(), BusinessTypeEnum.ALI_INTER.name());

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateGrantAuthStatus(Integer id, GrantAuthStatusEnum grantAuthStatusEnum) {
        SauAliInterConfigEntity config = isExist(id);
        //幂等操作
        if (GrantAuthStatusEnum.isDuplicated(grantAuthStatusEnum.getCode(), config.getGrantAuthStatus())) {
            return;
        }

        //更新
        String updateBy = loginInfoService.getName();
        sauAliInterConfigDao.updateGrantAuthStatus(id, grantAuthStatusEnum.getCode(), updateBy);
        SauAliInterConfigEntity newConfig = SauAliInterConfigConverter.INSTANCE.toSauAliInterConfigEntity(config);
        newConfig.setUpdateBy(updateBy);
        newConfig.setGrantAuthStatus(grantAuthStatusEnum.getCode());

        //操作日志
        String description = bdOperateLogService.getLogDescription(config, newConfig, SauAliInterConfigEntity.class);
        bdOperateLogService.updateLog(BusinessTypeEnum.ALI_INTER.name(), id, description);

        //发送消息
        baseService.sendQueue(config.getStoreId(), BusinessTypeEnum.ALI_INTER.name());
        baseService.sendAuthQueue(config.getStoreId(), grantAuthStatusEnum.getCode(), BusinessTypeEnum.ALI_INTER.name());
        baseService.sendAuthQueue(config.getStoreId(), grantAuthStatusEnum.getCode(), BusinessTypeEnum.ALI_INTER.name());
    }

    /**
     * 校验是否存在该授权信息
     *
     * @param id
     * @return
     */
    private SauAliInterConfigEntity isExist(Integer id) {
        return Optional.ofNullable(sauAliInterConfigDao.getById(id)).orElseThrow(() -> new BusinessServiceException("找不到该授权信息"));
    }

    @Override
    public SauAliInterConfigResponse getInfoResponse(Integer storeId) {
        return SauAliInterConfigConverter.INSTANCE.toSauAliInterConfigResponse(sauAliInterConfigDao.getDetailInfoByStoreId(storeId));
    }

    @Override
    public PlatformEnum[] platformType() {
        return new PlatformEnum[]{PlatformEnum.ALIBABA_INTERNATION};
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateStoreAuthInfo(ErpUpdateStoreAuthInfoRequest request) {
        SauAliInterConfigEntity sauAliInterConfigEntity = sauAliInterConfigDao.getDetailInfoByStoreId(request.getStoreId());

        if (Objects.isNull(sauAliInterConfigEntity)) {
            throw new BusinessServiceException(String.format("该授权店铺不存在, 店铺ID: %s", request.getStoreId()));
        }
        sauAliInterConfigEntity.setAccessToken(request.getAccountProperties3());
        sauAliInterConfigEntity.setUpdateBy(request.getUpdateBy());
        sauAliInterConfigEntity.setRefreshTokenTimeout(request.getRefreshTokenTimeout());
        sauAliInterConfigEntity.setRefreshToken(request.getRefreshToken());
        sauAliInterConfigDao.updateById(sauAliInterConfigEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveData(DistributorsStoreRequest saStoreRequest, Integer storeId) {
        SauAliInterConfigEntity oldSauAliInterConfigEntity = sauAliInterConfigDao.getDetailInfoByStoreId(storeId);
        SauAliInterConfigEntity sauAliInterConfigEntity = new SauAliInterConfigEntity();
        sauAliInterConfigEntity.setStoreId(storeId);
        sauAliInterConfigEntity.setStoreName(saStoreRequest.getStoreName());
        sauAliInterConfigEntity.setAppkey(saStoreRequest.getAccountKey());
        sauAliInterConfigEntity.setAppsecret(saStoreRequest.getAccountSecret());
        sauAliInterConfigEntity.setAuthorizationCode(saStoreRequest.getAccountProperties2());
        sauAliInterConfigEntity.setAccessToken(saStoreRequest.getAccountProperties3());
        sauAliInterConfigEntity.setMarketId(0);
        if (ObjectUtils.isEmpty(oldSauAliInterConfigEntity)) {
            sauAliInterConfigDao.save(sauAliInterConfigEntity);
        } else {
            sauAliInterConfigEntity.setId(oldSauAliInterConfigEntity.getId());
            sauAliInterConfigDao.updateById(sauAliInterConfigEntity);
        }
    }

    @Override
    public DistributorsStoreResponse getStoreResponse(Integer storeId) {
        DistributorsStoreResponse storeResponse = new DistributorsStoreResponse();
        SauAliInterConfigResponse sauAliInterConfigResponse = getInfoResponse(storeId);
        storeResponse.setAccountKey(!ObjectUtils.isEmpty(sauAliInterConfigResponse) ? sauAliInterConfigResponse.getAppkey() : StringUtil.EMPTY);
        storeResponse.setAccountSecret(!ObjectUtils.isEmpty(sauAliInterConfigResponse) ? sauAliInterConfigResponse.getAppsecret() : StringUtil.EMPTY);
        storeResponse.setAccountProperties2(!ObjectUtils.isEmpty(sauAliInterConfigResponse) ? sauAliInterConfigResponse.getAuthorizationCode() : StringUtil.EMPTY);
        return storeResponse;
    }

    @Override
    public List<SauAliInterConfigEntity> getExpireStores(Integer notifyDay) {
        return sauAliInterConfigDao.getExpireStores(notifyDay);
    }

    @Override
    public Integer getAuthStatus(Integer storeId) {
        return sauAliInterConfigDao.getAuthStatus(storeId);
    }

    @Override
    public CommonAuthResponse getAuthInfo(Integer storeId) {
        SauAliInterConfigEntity entity = sauAliInterConfigDao.getDetailInfoByStoreId(storeId);
        if (Objects.nonNull(entity)) {
            CommonAuthResponse response = new CommonAuthResponse();
            response.setExtendedAttributesTwo(entity.getAuthorizationCode());
            response.setExtendedAttributesThree(entity.getAccessToken());
            response.setAccountKey(entity.getAppkey());
            response.setAccountSecret(entity.getAppsecret());
            return response;
        }
        return null;
    }

    @Override
    public SauAliInterConfigResponse getAuthInfoByWebsiteId(Integer websiteId) {
        return sauAliInterConfigDao.getAuthInfoByWebsiteId(websiteId);
    }
}
