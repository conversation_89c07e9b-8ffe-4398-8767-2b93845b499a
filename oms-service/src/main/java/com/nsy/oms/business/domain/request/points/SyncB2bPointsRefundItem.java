package com.nsy.oms.business.domain.request.points;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SyncB2bPointsRefundItem {

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("流水号")
    private String serialNumber;

    @ApiModelProperty("退款平台")
    private String refundPlatform;

    @ApiModelProperty("退款金额")
    private BigDecimal refundAmount;

    @ApiModelProperty("链接")
    private String url;

}