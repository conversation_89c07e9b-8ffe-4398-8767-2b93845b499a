package com.nsy.oms.business.domain.request.celebrity;

import com.nsy.oms.business.domain.request.base.IdsRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Author: lhh
 * @Date: 2025-04-24
 * @Description:
 */
@ApiModel("修改达人账号")
public class UpdateInternetCelebrityNameRequest extends IdsRequest {
    @ApiModelProperty("达人账号")
    private String internetCelebrityName;

    public String getInternetCelebrityName() {
        return internetCelebrityName;
    }

    public void setInternetCelebrityName(String internetCelebrityName) {
        this.internetCelebrityName = internetCelebrityName;
    }
}
