package com.nsy.oms.business.service.inbound.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.privilege.LoginInfoService;
import com.nsy.oms.business.service.inbound.InboundPlanLogService;
import com.nsy.oms.repository.entity.inbound.InboundPlanLogEntity;
import com.nsy.oms.repository.sql.mapper.inbound.InboundPlanLogEntityMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【inbound_shipment_plan_log(fba补货单操作日志表)】的数据库操作Service实现
* @createDate 2025-03-28 10:34:20
*/
@Service
@Slf4j
public class InboundPlanLogServiceImpl extends ServiceImpl<InboundPlanLogEntityMapper, InboundPlanLogEntity> implements InboundPlanLogService {

    @Resource
    private LoginInfoService loginInfoService;
    @Override
    public List<InboundPlanLogEntity> getByPlanIds(List<Integer> planIds) {
        return this.list(Wrappers.<InboundPlanLogEntity>lambdaQuery().in(InboundPlanLogEntity::getPlanId, planIds));
    }

    @Override
    public void saveFbaInboundShipmentPlanLog(Integer planId, String operate, String description, String planStatus) {
        try {
            InboundPlanLogEntity fbaInboundShipmentPlanLogMpEntity = new InboundPlanLogEntity();
            fbaInboundShipmentPlanLogMpEntity.setPlanId(planId);
            fbaInboundShipmentPlanLogMpEntity.setOperate(StringUtils.isNotEmpty(operate) ? operate : "");
            fbaInboundShipmentPlanLogMpEntity.setDescription(StringUtils.isNotEmpty(description) ? StringUtils.substring(description, 0, 1024) : "");
            fbaInboundShipmentPlanLogMpEntity.setPlanStatus(planStatus);
            fbaInboundShipmentPlanLogMpEntity.setCreateBy(loginInfoService.getName());
            this.save(fbaInboundShipmentPlanLogMpEntity);
        } catch (Exception e) {
            log.error("补货单操作日志保存,planid:{},异常：{}", planId, e.getMessage());
        }
    }
}




