package com.nsy.oms.business.domain.request.celebrity;

import com.nsy.oms.business.domain.request.base.IdsRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

/**
 * @Author: lhh
 * @Date: 2025-05-05
 * @Description:
 */
@ApiModel("更新bd状态")
public class BusinessDeveloperUpdateStatusRequest extends IdsRequest {
    @ApiModelProperty("状态(1启用0体用)")
    @NotNull(message = "状态不能为空")
    private Integer status;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
