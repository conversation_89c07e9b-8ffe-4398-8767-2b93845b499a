package com.nsy.oms.business.domain.request.activiti;

import java.util.Map;

public class JumpTaskRequest {
    private String taskId;
    private String targetTaskKey;
    private String processDefinitionKey;
    private String taskName;
    private String searchCode;
    private String createBy;
    private String createName;
    private String businessKey;
    private Map variables;
    private String description;
    /**
     * 是否为任务参数，默认为否
     */
    private boolean localVariable = false;

    /*
     *  公司id
     */
    private String companyId;
    private String operator;
    private String imageUrl;

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public boolean isLocalVariable() {
        return localVariable;
    }

    public void setLocalVariable(boolean localVariable) {
        this.localVariable = localVariable;
    }

    public Map getVariables() {
        return variables;
    }

    public void setVariables(Map variables) {
        this.variables = variables;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    public String getProcessDefinitionKey() {
        return processDefinitionKey;
    }

    public void setProcessDefinitionKey(String processDefinitionKey) {
        this.processDefinitionKey = processDefinitionKey;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getSearchCode() {
        return searchCode;
    }

    public void setSearchCode(String searchCode) {
        this.searchCode = searchCode;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTargetTaskKey() {
        return targetTaskKey;
    }

    public void setTargetTaskKey(String targetTaskKey) {
        this.targetTaskKey = targetTaskKey;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }
}
