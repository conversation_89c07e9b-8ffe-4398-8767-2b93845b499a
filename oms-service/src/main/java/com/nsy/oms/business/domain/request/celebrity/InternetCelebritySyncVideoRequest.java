package com.nsy.oms.business.domain.request.celebrity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @Author: lhh
 * @Date: 2025-04-18
 * @Description: 网红查询请求类
 */
@NoArgsConstructor
@Data
public class InternetCelebritySyncVideoRequest {


    @NotEmpty
    @JsonProperty("data")
    List<DataDTO> data;

    @NoArgsConstructor
    @Data
    public static class DataDTO {
        @JsonProperty("creator_meta")
        CreatorMetaDTO creatorMeta;
        @JsonProperty("video_meta")
        VideoMetaDTO videoMeta;
        @JsonProperty("product_list")
        List<ProductListDTO> productList;
        @JsonProperty("publish_time")
        Integer publishTime;
        @JsonProperty("view_cnt")
        Integer viewCnt;
        @JsonProperty("sku_order_cnt")
        Integer skuOrderCnt;
        @JsonProperty("sub_order_cnt")
        Integer subOrderCnt;
        @JsonProperty("revenue")
        RevenueDTO revenue;
        @JsonProperty("analysis_date")
        String analysisDate;
        @JsonProperty("analysis_date_end")
        String analysisDateEnd;
        @JsonProperty("store_id")
        String storeId;
        @JsonProperty("store_name")
        String storeName;

        @NoArgsConstructor
        @Data
        public static class CreatorMetaDTO {
            @JsonProperty("id")
            String id;
            @JsonProperty("handle")
            String handle;
            @JsonProperty("alias")
            String alias;
            @JsonProperty("type")
            Integer type;
        }

        @NoArgsConstructor
        @Data
        public static class VideoMetaDTO {
            @JsonProperty("id")
            String id;
            @JsonProperty("name")
            String name;
            @JsonProperty("publish_time")
            Integer publishTime;
            @JsonProperty("video")
            VideoDTO video;

            @NoArgsConstructor
            @Data
            public static class VideoDTO {
                @JsonProperty("id")
                String id;
                @JsonProperty("duration")
                Double duration;
                @JsonProperty("post_url")
                String postUrl;
                @JsonProperty("media_type")
                String mediaType;

            }
        }

        @NoArgsConstructor
        @Data
        public static class RevenueDTO {
            @JsonProperty("amount_formatted")
            String amountFormatted;
            @JsonProperty("amount_delimited")
            String amountDelimited;
            @JsonProperty("amount")
            String amount;
            @JsonProperty("currency_code")
            String currencyCode;
            @JsonProperty("currency_symbol")
            String currencySymbol;
        }

        @NoArgsConstructor
        @Data
        public static class ProductListDTO {
            @JsonProperty("product_id")
            String productId;
            @JsonProperty("product_name")
            String productName;
        }
    }
}
