package com.nsy.oms.business.manage.amazon.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class SkuPrefixDto {
    @ApiModelProperty("主键id")
    private Integer id;

    @ApiModelProperty("店铺id")
    private Integer amazonStoreId;

    @ApiModelProperty("店铺名称")
    private String storeName;

    @ApiModelProperty("商品前缀")
    private String skuPrefix;

    @ApiModelProperty("业务员账号")
    private String userAccount;

    @ApiModelProperty("业务员名称")
    private String userName;

    @ApiModelProperty("状态: 0=禁用, 1=启用")
    private Integer status;

    @ApiModelProperty("状态描述: 禁用, 启用")
    private String statusDesc;

    @ApiModelProperty("创建者")
    private String createBy;

    @ApiModelProperty("创建时间")
    private Date createDate;

    @ApiModelProperty("更新者")
    private String updateBy;

    @ApiModelProperty("更新时间")
    private Date updateDate;

}
