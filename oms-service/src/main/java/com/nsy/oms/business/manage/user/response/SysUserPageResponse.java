package com.nsy.oms.business.manage.user.response;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @date 2022/12/1 11:30
 */
public class SysUserPageResponse {
    @ApiModelProperty("用户id")
    private Integer userId;

    @ApiModelProperty("用户名")
    private String userAccount;

    @ApiModelProperty("姓名")
    private String userName;

    @ApiModelProperty("用户名")
    private String userCode;

    @ApiModelProperty("部门id")
    private Integer departmentId;

    @ApiModelProperty("部门")
    private String departmentName;

    @ApiModelProperty("职位id")
    private Integer dutyId;

    @ApiModelProperty("职位")
    private String dutyName;

    @ApiModelProperty("主职角色")
    private String mainRole;

    @ApiModelProperty("兼职角色")
    private String secondRole;

    @ApiModelProperty("手机号")
    private String tel;

    @ApiModelProperty("状态：1离职, 0在职")
    private Integer status;

    @ApiModelProperty("是否绑定钉钉")
    private Integer isJoinDing;

    @ApiModelProperty("公司id")
    private Integer companyId;

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getUserAccount() {
        return userAccount;
    }

    public void setUserAccount(String userAccount) {
        this.userAccount = userAccount;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public Integer getDutyId() {
        return dutyId;
    }

    public void setDutyId(Integer dutyId) {
        this.dutyId = dutyId;
    }

    public String getDutyName() {
        return dutyName;
    }

    public void setDutyName(String dutyName) {
        this.dutyName = dutyName;
    }

    public String getMainRole() {
        return mainRole;
    }

    public void setMainRole(String mainRole) {
        this.mainRole = mainRole;
    }

    public String getSecondRole() {
        return secondRole;
    }

    public void setSecondRole(String secondRole) {
        this.secondRole = secondRole;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getIsJoinDing() {
        return isJoinDing;
    }

    public void setIsJoinDing(Integer isJoinDing) {
        this.isJoinDing = isJoinDing;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }
}
