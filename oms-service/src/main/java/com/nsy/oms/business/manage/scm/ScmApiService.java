package com.nsy.oms.business.manage.scm;

import com.alibaba.fastjson.JSON;
import com.nsy.api.core.apicore.base.BaseListResponse;
import com.nsy.api.core.apicore.util.NsyJacksonUtils;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.oms.business.manage.scm.domain.CountStoreApplyNumDto;
import com.nsy.oms.business.manage.scm.request.BdReplenishmentConfigRequest;
import com.nsy.oms.business.manage.scm.request.CountStoreApplyNumRequest;
import com.nsy.oms.business.manage.scm.request.GetEstimatedArrivalDateRequest;
import com.nsy.oms.business.manage.scm.response.BdReplenishmentConfigPageResponse;
import com.nsy.oms.business.manage.scm.response.BdReplenishmentLogisticsConfigPageResponse;
import com.nsy.oms.business.manage.scm.response.BdReplenishmentSeasonConfigResponse;
import com.nsy.oms.business.manage.scm.response.GetEstimatedArrivalDateResponse;
import com.nsy.oms.repository.dao.sa.SaStoreDao;
import com.nsy.oms.repository.entity.sa.SaStoreEntity;
import com.nsy.oms.utils.Json;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.inject.Inject;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2023-03-03
 */
@Service
public class ScmApiService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ScmApiService.class);
    @Inject
    private RestTemplate restTemplate;
    @Value("${nsy.service.url.scm}")
    private String scmServiceUrl;
    @Inject
    private SaStoreDao saStoreDao;

    public BdReplenishmentLogisticsConfigPageResponse getReplenishmentLogisticsConfig(String fromWarehouse, String marketplaceId, String location) {
        if (!StringUtils.hasText(fromWarehouse) || !StringUtils.hasText(marketplaceId)) {
            return new BdReplenishmentLogisticsConfigPageResponse();
        }
        String uri = String.format("%s/bd-replenishment-logistics-config?fromWarehouse=%s&marketplaceId=%s&location=%s", scmServiceUrl, fromWarehouse, marketplaceId, location);
        try {
            ResponseEntity<BdReplenishmentLogisticsConfigPageResponse> respEntity
                    = this.restTemplate.getForEntity(uri, BdReplenishmentLogisticsConfigPageResponse.class);
            return Optional.ofNullable(respEntity.getBody()).orElseGet(BdReplenishmentLogisticsConfigPageResponse::new);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return new BdReplenishmentLogisticsConfigPageResponse();
    }

    public BdReplenishmentConfigPageResponse getBdReplenishmentStoreConfig(Integer productCategoryId, Integer storeId, Integer season, String businessType) {
        if (productCategoryId == null || storeId == null || season == null) {
            return null;
        }
        BdReplenishmentConfigRequest request = new BdReplenishmentConfigRequest();
        request.setProductCategoryId(productCategoryId);
        request.setStoreId(storeId);
        request.setSeason(season);
        request.setBusinessType(businessType);
        SaStoreEntity saStoreEntity = saStoreDao.getById(storeId);
        if (saStoreEntity == null) {
            LOGGER.warn("店铺配置不存在,storeId:{}", storeId);
            return null;
        }
        request.setPlatformId(saStoreEntity.getPlatformId());
        request.setDepartment(saStoreEntity.getDepartment());

        String uri = String.format("%s/bd-replenishment-store-config", scmServiceUrl);
        try {
            ResponseEntity<BdReplenishmentConfigPageResponse> respEntity = this.restTemplate.postForEntity(uri, request, BdReplenishmentConfigPageResponse.class);
            return respEntity.getBody();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return null;
    }

    public List<CountStoreApplyNumDto> countStoreApplyNumBySpecs(List<String> skuList, String location) {
        if (CollectionUtils.isEmpty(skuList)) return new ArrayList<>();
        CountStoreApplyNumRequest request = new CountStoreApplyNumRequest();
        request.setLocation(location);
        request.setSkuList(skuList);
        try {
            String uri = String.format("%s/purchase-apply/apply/count-store-apply-num", scmServiceUrl);
            CountStoreApplyNumDto[] res = this.restTemplate.postForObject(uri, request, CountStoreApplyNumDto[].class);
            if (null == res) {
                return Collections.emptyList();
            }
            return Arrays.stream(res).collect(Collectors.toList());
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public List<BdReplenishmentSeasonConfigResponse> getSeasonConfigsByLocation(String location) {
        if (!StringUtils.hasText(location)) {
            return Collections.emptyList();
        }
        String uri = String.format("%s/bd-replenishment-season-config/location?location=%s", scmServiceUrl, location);
        LOGGER.info("[rpc请求] ScmApiService getSeasonConfigsByLocation location: {} timestamp: {}", JSON.toJSONString(location), LocalDateTime.now());
        try {
            ResponseEntity<BaseListResponse> respEntity = this.restTemplate.getForEntity(uri, BaseListResponse.class);
            BaseListResponse body = respEntity.getBody();
            if (Objects.nonNull(body) && !CollectionUtils.isEmpty(body.getContent())) {
                LOGGER.info("[rpc响应] ScmApiService getSeasonConfigsByLocation body: {} timestamp: {}", JSON.toJSONString(body), LocalDateTime.now());
                return Json.listToObject(body.getContent(), BdReplenishmentSeasonConfigResponse.class);
            }
        } catch (Exception e) {
            LOGGER.error("获取季节配置错误", e);
            throw e;
        }
        return Collections.emptyList();
    }

    public List<GetEstimatedArrivalDateResponse> getEstimatedArrivalDate(GetEstimatedArrivalDateRequest request) {
        String uri = String.format("%s/replenishment-logistics-config/get-estimated_arrival-date", scmServiceUrl);
        try {
            LOGGER.info("getEstimatedArrivalDate request:{}", NsyJacksonUtils.toJson(request));
            ResponseEntity<String> respEntity = this.restTemplate.postForEntity(uri, request, String.class);
            LOGGER.info("getEstimatedArrivalDate response:{}", NsyJacksonUtils.toJson(respEntity));
            if (Objects.nonNull(respEntity.getBody())) {
                return NsyJacksonUtils.jsonToList(respEntity.getBody(), GetEstimatedArrivalDateResponse.class);
            }
        } catch (Exception e) {
            LOGGER.error("getEstimatedArrivalDate error: requestData: {}, msg: {}", request.getSpaceInfoRequestList(), e.getMessage());
        }
        return new ArrayList<>();
    }

}
