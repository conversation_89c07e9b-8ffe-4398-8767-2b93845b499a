package com.nsy.oms.business.manage.pms.response;

import java.math.BigDecimal;
import java.util.Date;

public class FetchProductSpecBaseInfosResponse {
    private Integer specId;
    private Integer productId;
    /**
     * 规格编码
     */
    private String specSku;
    /**
     * 商品编码
     */
    private String spu;
    /**
     * 条形码
     */
    private String barcode;
    /**
     * 尺码
     */
    private String size;
    /**
     * 尺码编码
     */
    private String sizeCode;
    /**
     * 关联到字典表id，方便尺码排序
     */
    private Integer sizeDictionaryId;
    /**
     * 美码
     */
    private String usSize;
    /**
     * 颜色
     */
    private String color;
    /**
     * sku编码对应的颜色编号，以区分同色系的颜色
     */
    private String colorCode;
    /**
     * colorSku=product_sku+'-'+color_code
     */
    private String colorSku;
    /**
     * ERP对应的spec_id
     */
    private Integer erpSpecId;
    /**
     * 采购价
     */
    private BigDecimal purchasePrice;
    /**
     * 网上售价
     */
    private BigDecimal userPrice;
    /**
     * 吊牌价
     */
    private BigDecimal price;
    /**
     * 内部结算价
     */
    private BigDecimal settlementPrice;
    /**
     * 预估重量
     */
    private BigDecimal estimateWeight;
    /**
     * 重量
     */
    private BigDecimal weight;
    /**
     * 体积重
     */
    private BigDecimal volumeWeight;
    /**
     * 包装高度
     */
    private BigDecimal packageHeight;
    /**
     * sku市场供应状态
     */
    private String marketSupplyStatus;
    /**
     * 是否清仓标识
     */
    private Integer isClearStock;
    private String status;
    private Integer sort;
    private Date createDate;
    private String chineseColor;
    private Integer isClash;

    public Integer getSpecId() {
        return specId;
    }

    public void setSpecId(Integer specId) {
        this.specId = specId;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getSpecSku() {
        return specSku;
    }

    public void setSpecSku(String specSku) {
        this.specSku = specSku;
    }

    public String getSpu() {
        return spu;
    }

    public void setSpu(String spu) {
        this.spu = spu;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getSizeCode() {
        return sizeCode;
    }

    public void setSizeCode(String sizeCode) {
        this.sizeCode = sizeCode;
    }

    public Integer getSizeDictionaryId() {
        return sizeDictionaryId;
    }

    public void setSizeDictionaryId(Integer sizeDictionaryId) {
        this.sizeDictionaryId = sizeDictionaryId;
    }

    public String getUsSize() {
        return usSize;
    }

    public void setUsSize(String usSize) {
        this.usSize = usSize;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getColorCode() {
        return colorCode;
    }

    public void setColorCode(String colorCode) {
        this.colorCode = colorCode;
    }

    public String getColorSku() {
        return colorSku;
    }

    public void setColorSku(String colorSku) {
        this.colorSku = colorSku;
    }

    public Integer getErpSpecId() {
        return erpSpecId;
    }

    public void setErpSpecId(Integer erpSpecId) {
        this.erpSpecId = erpSpecId;
    }

    public BigDecimal getPurchasePrice() {
        return purchasePrice;
    }

    public void setPurchasePrice(BigDecimal purchasePrice) {
        this.purchasePrice = purchasePrice;
    }

    public BigDecimal getUserPrice() {
        return userPrice;
    }

    public void setUserPrice(BigDecimal userPrice) {
        this.userPrice = userPrice;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getSettlementPrice() {
        return settlementPrice;
    }

    public void setSettlementPrice(BigDecimal settlementPrice) {
        this.settlementPrice = settlementPrice;
    }

    public BigDecimal getEstimateWeight() {
        return estimateWeight;
    }

    public void setEstimateWeight(BigDecimal estimateWeight) {
        this.estimateWeight = estimateWeight;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public BigDecimal getVolumeWeight() {
        return volumeWeight;
    }

    public void setVolumeWeight(BigDecimal volumeWeight) {
        this.volumeWeight = volumeWeight;
    }

    public BigDecimal getPackageHeight() {
        return packageHeight;
    }

    public void setPackageHeight(BigDecimal packageHeight) {
        this.packageHeight = packageHeight;
    }

    public String getMarketSupplyStatus() {
        return marketSupplyStatus;
    }

    public void setMarketSupplyStatus(String marketSupplyStatus) {
        this.marketSupplyStatus = marketSupplyStatus;
    }

    public Integer getIsClearStock() {
        return isClearStock;
    }

    public void setIsClearStock(Integer isClearStock) {
        this.isClearStock = isClearStock;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getChineseColor() {
        return chineseColor;
    }

    public void setChineseColor(String chineseColor) {
        this.chineseColor = chineseColor;
    }

    public Integer getIsClash() {
        return isClash;
    }

    public void setIsClash(Integer isClash) {
        this.isClash = isClash;
    }
}
