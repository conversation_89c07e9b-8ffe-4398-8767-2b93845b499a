package com.nsy.oms.business.manage.erp.domain;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: linCheng
 * @create: 2024-09-09 09:04
 **/
public class SampleOrder {

    @JsonProperty("StoreId")
    private Integer storeId;
    @JsonProperty("StoreName")
    private String storeName;
    @JsonProperty("BuyerNick")
    private String buyerNick;
    @JsonProperty("BuyerUid")
    private String buyerUid;
    @JsonProperty("PlatformOrderNo")
    private String platformOrderNo;
    @JsonProperty("OrderCreateDate")
    private Date orderCreateDate;
    @JsonProperty("Location")
    private String location;
    @JsonProperty("BusinessType")
    private String businessType;
    @JsonProperty("SampleOrderItemList")
    private List<SampleOrderItem> sampleOrderItemList;

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getBuyerNick() {
        return buyerNick;
    }

    public void setBuyerNick(String buyerNick) {
        this.buyerNick = buyerNick;
    }

    public String getBuyerUid() {
        return buyerUid;
    }

    public void setBuyerUid(String buyerUid) {
        this.buyerUid = buyerUid;
    }

    public Date getOrderCreateDate() {
        return orderCreateDate;
    }

    public void setOrderCreateDate(Date orderCreateDate) {
        this.orderCreateDate = orderCreateDate;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public List<SampleOrderItem> getSampleOrderItemList() {
        return sampleOrderItemList;
    }

    public void setSampleOrderItemList(List<SampleOrderItem> sampleOrderItemList) {
        this.sampleOrderItemList = sampleOrderItemList;
    }

    public String getPlatformOrderNo() {
        return platformOrderNo;
    }

    public void setPlatformOrderNo(String platformOrderNo) {
        this.platformOrderNo = platformOrderNo;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }
}
