package com.nsy.oms.business.service.celebrity.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.privilege.LoginInfoService;
import com.nsy.oms.business.domain.request.celebrity.SaveInternetCelebritySampleOrderItemPostRequest;
import com.nsy.oms.business.domain.response.celebrity.SyncInternetCelebritySampleOrderItemPost;
import com.nsy.oms.business.manage.businessbase.BusinessBaseApiService;
import com.nsy.oms.business.manage.businessbase.request.WcRuTaskBaseInfo;
import com.nsy.oms.business.manage.businessbase.request.WcRuTaskCompleteRequest;
import com.nsy.oms.business.service.celebrity.IInternetCelebritySampleOrderItemPostService;
import com.nsy.oms.business.service.celebrity.IInternetCelebritySampleOrderItemService;
import com.nsy.oms.business.service.celebrity.IInternetCelebritySampleOrderService;
import com.nsy.oms.enums.activiti.WorkCenterTaskEnum;
import com.nsy.oms.enumstable.InternetCelebritySampleTaskTypeEnum;
import com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderItemEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderItemPostEntity;
import com.nsy.oms.repository.sql.mapper.celebrity.InternetCelebritySampleOrderItemPostMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 网红样衣订单明细发帖信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-29
 */
@Service
public class InternetCelebritySampleOrderItemPostServiceImpl extends ServiceImpl<InternetCelebritySampleOrderItemPostMapper, InternetCelebritySampleOrderItemPostEntity> implements IInternetCelebritySampleOrderItemPostService {

    @Autowired
    private LoginInfoService loginInfoService;

    @Autowired
    private IInternetCelebritySampleOrderItemService internetCelebritySampleOrderItemService;

    @Autowired
    private IInternetCelebritySampleOrderService internetCelebritySampleOrderService;

    @Autowired
    private BusinessBaseApiService businessBaseApiService;

    @Override
    public List<InternetCelebritySampleOrderItemPostEntity> getByInternetCelebritySampleOrderItemIds(List<Integer> internetCelebritySampleOrderItemIds) {
        if (CollectionUtils.isEmpty(internetCelebritySampleOrderItemIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<InternetCelebritySampleOrderItemPostEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(InternetCelebritySampleOrderItemPostEntity::getInternetCelebritySampleOrderItemId, internetCelebritySampleOrderItemIds);
        return this.list(lambdaQueryWrapper);
    }

    @Override
    public void saveOrUpdate(SaveInternetCelebritySampleOrderItemPostRequest request) {
        InternetCelebritySampleOrderItemPostEntity internetCelebritySampleOrderItemPostEntity = Optional.ofNullable(this.getById(request.getInternetCelebritySampleOrderItemPostId())).map(entity -> {
            entity.setUpdateBy(loginInfoService.getName());
            entity.setUpdateDate(new Date());
            return entity;
        }).orElseGet(() -> {
            InternetCelebritySampleOrderItemPostEntity entity = new InternetCelebritySampleOrderItemPostEntity();
            BeanUtils.copyProperties(request, entity);
            entity.setCreateBy(loginInfoService.getName());
            entity.setCreateDate(new Date());
            return entity;
        });
        internetCelebritySampleOrderItemPostEntity.setInternetCelebritySampleOrderItemId(request.getInternetCelebritySampleOrderItemId());
        this.saveOrUpdate(internetCelebritySampleOrderItemPostEntity);
        updateFirstPostDate(internetCelebritySampleOrderItemPostEntity.getInternetCelebritySampleOrderItemId());
    }

    @Override
    public void deleteById(Integer internetCelebritySampleOrderItemPostId) {
        InternetCelebritySampleOrderItemPostEntity internetCelebritySampleOrderItemPostEntity = this.getById(internetCelebritySampleOrderItemPostId);
        if (!Optional.ofNullable(internetCelebritySampleOrderItemPostEntity).isPresent()) {
            throw new BusinessServiceException("订单明细发帖信息不存在");
        }
        this.removeById(internetCelebritySampleOrderItemPostId);
        updateFirstPostDate(internetCelebritySampleOrderItemPostEntity.getInternetCelebritySampleOrderItemId());
    }


    private void updateFirstPostDate(Integer internetCelebritySampleOrderItemId) {
        InternetCelebritySampleOrderItemEntity internetCelebritySampleOrderItemEntity = internetCelebritySampleOrderItemService.getById(internetCelebritySampleOrderItemId);
        if (!Optional.ofNullable(internetCelebritySampleOrderItemEntity).isPresent()) {
            throw new BusinessServiceException("订单明细不存在");
        }
        List<InternetCelebritySampleOrderItemEntity> internetCelebritySampleOrderItemEntities = internetCelebritySampleOrderItemService.getList(internetCelebritySampleOrderItemEntity.getInternetCelebritySampleOrderId());
        List<InternetCelebritySampleOrderItemPostEntity> internetCelebritySampleOrderItemPostEntities = getByInternetCelebritySampleOrderItemIds(internetCelebritySampleOrderItemEntities.stream().map(InternetCelebritySampleOrderItemEntity::getInternetCelebritySampleOrderItemId).collect(Collectors.toList()));

        InternetCelebritySampleOrderEntity internetCelebritySampleOrder = internetCelebritySampleOrderService.getById(internetCelebritySampleOrderItemEntity.getInternetCelebritySampleOrderId());
        if (!Optional.ofNullable(internetCelebritySampleOrder).isPresent()) {
            throw new BusinessServiceException("订单明细不存在");
        }

        try {
            if (InternetCelebritySampleTaskTypeEnum.WITH_COMPLETE.getCode().equals(internetCelebritySampleOrder.getTaskType())) {
                List<WcRuTaskBaseInfo> wcRuTaskBaseInfoList = new ArrayList<>();
                WcRuTaskBaseInfo wcRuTaskBaseInfo = new WcRuTaskBaseInfo();
                wcRuTaskBaseInfo.setTaskKey(WorkCenterTaskEnum.INTERNET_CELEBRITY_SAMPLE_POST_WARN.getProcessKey());
                wcRuTaskBaseInfo.setBusinessKey(String.valueOf(internetCelebritySampleOrder.getInternetCelebritySampleOrderId()));
                wcRuTaskBaseInfoList.add(wcRuTaskBaseInfo);
                WcRuTaskCompleteRequest wcRuTaskCompleteRequest = new WcRuTaskCompleteRequest();
                wcRuTaskCompleteRequest.setTaskCompleteList(wcRuTaskBaseInfoList);
                businessBaseApiService.completeWcRuTask(wcRuTaskCompleteRequest);

                internetCelebritySampleOrder.setTaskType(InternetCelebritySampleTaskTypeEnum.COMPLETE.getCode());
            }
        } catch (Exception e) {
            log.error("internetCelebritySampleOrder.complete.error", e);
        }
        internetCelebritySampleOrder.setFirstPostDate(CollectionUtils.isEmpty(internetCelebritySampleOrderItemPostEntities) ? null : internetCelebritySampleOrderItemPostEntities.stream().map(InternetCelebritySampleOrderItemPostEntity::getPostDate).filter(Objects::nonNull).min(Date::compareTo).orElse(null));
        internetCelebritySampleOrderService.updateById(internetCelebritySampleOrder);
    }


    @Override
    public void syncInternetCelebritySampleOrderItemPostByImport(Integer internetCelebritySampleOrderItemId, List<SyncInternetCelebritySampleOrderItemPost> syncInternetCelebritySampleOrderItemPosts) {

        List<InternetCelebritySampleOrderItemPostEntity> internetCelebritySampleOrderItemPostEntities = this.list(new LambdaQueryWrapper<InternetCelebritySampleOrderItemPostEntity>().eq(InternetCelebritySampleOrderItemPostEntity::getInternetCelebritySampleOrderItemId, internetCelebritySampleOrderItemId));

        List<InternetCelebritySampleOrderItemPostEntity> internetCelebritySampleOrderItemPostEntityList = new ArrayList<>();
        Map<String, InternetCelebritySampleOrderItemPostEntity> internetCelebritySampleOrderItemPostMap = internetCelebritySampleOrderItemPostEntities.stream().collect(Collectors.toMap(InternetCelebritySampleOrderItemPostEntity::getVideoUrl, a -> a, (k1, k2) -> k1));
        syncInternetCelebritySampleOrderItemPosts.forEach(syncInternetCelebritySampleOrderItemPost -> {
            if (StringUtils.isEmpty(syncInternetCelebritySampleOrderItemPost.getVideoUrl())
                    && StringUtils.isEmpty(syncInternetCelebritySampleOrderItemPost.getVideoCode())
                    && !Optional.ofNullable(syncInternetCelebritySampleOrderItemPost.getPostDate()).isPresent()) {
                return;
            }
            InternetCelebritySampleOrderItemPostEntity internetCelebritySampleOrderItemPostEntity = Optional.ofNullable(internetCelebritySampleOrderItemPostMap.get(syncInternetCelebritySampleOrderItemPost.getVideoUrl())).map(entity -> {
                BeanUtils.copyProperties(syncInternetCelebritySampleOrderItemPost, entity);
                entity.setInternetCelebritySampleOrderItemId(internetCelebritySampleOrderItemId);
                entity.setUpdateDate(new Date());
                entity.setUpdateBy(loginInfoService.getName());
                return entity;
            }).orElseGet(() -> {
                InternetCelebritySampleOrderItemPostEntity entity = new InternetCelebritySampleOrderItemPostEntity();
                BeanUtils.copyProperties(syncInternetCelebritySampleOrderItemPost, entity);
                entity.setInternetCelebritySampleOrderItemId(internetCelebritySampleOrderItemId);
                entity.setCreateDate(new Date());
                entity.setCreateBy(loginInfoService.getName());
                return entity;
            });
            internetCelebritySampleOrderItemPostEntityList.add(internetCelebritySampleOrderItemPostEntity);
        });
        this.saveOrUpdateBatch(internetCelebritySampleOrderItemPostEntityList);
    }

    @Override
    public List<InternetCelebritySampleOrderItemPostEntity> listByIdList(List<Integer> postIds) {
        if (CollectionUtils.isEmpty(postIds)) {
            return Collections.emptyList();
        }
        return this.listByIds(postIds);
    }

    @Override
    public List<InternetCelebritySampleOrderItemPostEntity> getListByVideoUrl(String videoUrl) {
        LambdaQueryWrapper<InternetCelebritySampleOrderItemPostEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(InternetCelebritySampleOrderItemPostEntity::getVideoUrl, videoUrl);
        return this.list(lambdaQueryWrapper);
    }

    @Override
    public InternetCelebritySampleOrderItemPostEntity findTopByStoreIdAndVideoCode(Integer storeId, String videoCode) {
        LambdaQueryWrapper<InternetCelebritySampleOrderItemPostEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(InternetCelebritySampleOrderItemPostEntity::getStoreId, storeId)
                .eq(InternetCelebritySampleOrderItemPostEntity::getVideoCode, videoCode)
                .last("limit 1");
        return this.getOne(lambdaQueryWrapper);
    }

}
