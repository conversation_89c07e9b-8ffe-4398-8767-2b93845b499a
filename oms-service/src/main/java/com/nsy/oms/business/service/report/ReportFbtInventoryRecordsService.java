package com.nsy.oms.business.service.report;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nsy.api.transfer.domain.common.TiktokAuthResponse;
import com.nsy.oms.business.domain.dto.FbtInventRecordsParameterDTO;
import com.nsy.oms.repository.entity.report.ReportFbtInventoryRecordsEntity;

import java.util.List;

/**
 * FBT库存变动报表Service接口
 */
public interface ReportFbtInventoryRecordsService extends IService<ReportFbtInventoryRecordsEntity> {
    /**
     * 保存库存接口
     */
    void saveRecords(List<TiktokAuthResponse> authList, FbtInventRecordsParameterDTO parameterDTO);
}