package com.nsy.oms.business.service.upload.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.nsy.api.oms.dto.request.order.SampleOrderRequest;
import com.nsy.api.oms.dto.response.order.SampleOrderItemResponse;
import com.nsy.api.oms.dto.response.order.SampleOrderResponse;
import com.nsy.api.pms.dto.product.ProductSpecDTO;
import com.nsy.oms.business.domain.request.upload.UploadRequest;
import com.nsy.oms.business.domain.response.celebrity.SyncInternetCelebritySampleOrder;
import com.nsy.oms.business.domain.response.celebrity.SyncInternetCelebritySampleOrderItem;
import com.nsy.oms.business.domain.response.celebrity.SyncInternetCelebritySampleOrderItemPost;
import com.nsy.oms.business.domain.response.upload.UploadResponse;
import com.nsy.oms.business.domain.upload.InternetCelebritySampleOrderImport;
import com.nsy.oms.business.manage.erp.ErpApiService;
import com.nsy.oms.business.manage.erp.domain.GetSampleOrderResponse;
import com.nsy.oms.business.manage.erp.domain.SampleOrder;
import com.nsy.oms.business.manage.erp.domain.SampleOrderItem;
import com.nsy.oms.business.manage.erp.request.GetSampleOrderRequest;
import com.nsy.oms.business.manage.pms.PmsApiService;
import com.nsy.oms.business.manage.pms.request.ConfigInternetCelebrityRequest;
import com.nsy.oms.business.manage.pms.response.ConfigInternetCelebrityResponse;
import com.nsy.oms.business.manage.product.ProductApiService;
import com.nsy.oms.business.manage.product.request.InternetCelebrityBatchSaveInfo;
import com.nsy.oms.business.manage.product.request.InternetCelebrityBatchSaveRequest;
import com.nsy.oms.business.manage.product.response.InternetCelebritySaveResultInfo;
import com.nsy.oms.business.manage.user.UserApiService;
import com.nsy.oms.business.manage.user.response.SysDepartment;
import com.nsy.oms.business.manage.user.response.SysUserInfo;
import com.nsy.oms.business.service.celebrity.IInternetCelebritySampleOrderService;
import com.nsy.oms.business.service.platform.PlatformOrderService;
import com.nsy.oms.business.service.sa.SaStoreService;
import com.nsy.oms.business.service.upload.IUploadService;
import com.nsy.oms.constants.upload.QuartzUploadQueueTypeEnum;
import com.nsy.oms.enums.sa.StoreReleaseMethodChannelEnum;
import com.nsy.oms.enumstable.InternetCelebritySampleOrderDeliveryTypeEnum;
import com.nsy.oms.repository.entity.sa.SaStoreEntity;
import com.nsy.oms.utils.JsonMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: linCheng
 * @create: 2024-09-12 15:39
 **/
@Slf4j
@Service
public class InternetCelebritySampleOrderUploadService implements IUploadService {
    @Autowired
    private PlatformOrderService platformOrderService;
    @Autowired
    private SaStoreService saStoreService;
    @Autowired
    UserApiService userApiService;
    @Autowired
    private ErpApiService erpApiService;
    @Resource
    private IInternetCelebritySampleOrderService internetCelebritySampleOrderService;
    @Autowired
    private PmsApiService pmsApiService;
    @Autowired
    private ProductApiService productapiService;

    @Override
    public QuartzUploadQueueTypeEnum uploadType() {
        return QuartzUploadQueueTypeEnum.OMS_INTERNET_CELEBRITY_SAMPLE_ORDER_IMPORT;
    }

    @Override
    public UploadResponse processUploadData(UploadRequest request) {
        String location = request.getLocation();

        log.info("OrderRuleSkcRemoveUploadService.processUploadData.request:{}", JSON.toJSONString(request));
        UploadResponse uploadResponse = new UploadResponse();
        if (StringUtils.isEmpty(request.getDataJsonStr())) {
            return uploadResponse;
        }

        List<InternetCelebritySampleOrderImport> importList = JsonMapper.jsonStringToObjectArray(request.getDataJsonStr(), InternetCelebritySampleOrderImport.class).stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(importList)) {
            return uploadResponse;
        }

        List<InternetCelebritySampleOrderImport> errorList = new ArrayList<>();
        check(importList, errorList);
        if (CollectionUtils.isNotEmpty(errorList)) {
            uploadResponse.setDataJsonStr(JsonMapper.toJson(errorList));
            return uploadResponse;
        }

        //存数据
        process(importList, location);

        return uploadResponse;
    }


    private void process(List<InternetCelebritySampleOrderImport> importList, String location) {
        List<SyncInternetCelebritySampleOrder> syncInternetCelebritySampleOrderList = new ArrayList<>();
        Map<String, List<InternetCelebritySampleOrderImport>> platformOrderNoMap = importList.stream().collect(Collectors.groupingBy(InternetCelebritySampleOrderImport::getPlatformOrderNo));
        platformOrderNoMap.forEach((platformOrderNo, internetCelebritySampleOrderImports) -> {
            InternetCelebritySampleOrderImport internetCelebritySampleOrderImport = internetCelebritySampleOrderImports.stream().findFirst().orElse(null);
            if (!Optional.ofNullable(internetCelebritySampleOrderImport).isPresent()) {
                return;
            }
            SyncInternetCelebritySampleOrder syncInternetCelebritySampleOrder = new SyncInternetCelebritySampleOrder();
            BeanUtils.copyProperties(internetCelebritySampleOrderImport, syncInternetCelebritySampleOrder);

            //查询fba与fbt订单数据
            if (StringUtils.isEmpty(internetCelebritySampleOrderImport.getOrderType()) && StringUtils.isNotEmpty(internetCelebritySampleOrderImport.getPlatformOrderNo())) {
                GetSampleOrderResponse getSampleOrderResponse = erpApiService.getSampleOrder(new GetSampleOrderRequest(internetCelebritySampleOrderImport.getPlatformOrderNo(), location));
                if (Optional.ofNullable(getSampleOrderResponse).isPresent() && CollectionUtils.isNotEmpty(getSampleOrderResponse.getSampleOrderList())) {
                    setFbaInfo(getSampleOrderResponse, internetCelebritySampleOrderImports, syncInternetCelebritySampleOrder);
                } else {
                    List<SampleOrderResponse> sampleOrderResponses = platformOrderService.getSampleOrders(new SampleOrderRequest(internetCelebritySampleOrderImport.getPlatformOrderNo(), 0, location));
                    getFbtInfo(sampleOrderResponses, internetCelebritySampleOrderImports, syncInternetCelebritySampleOrder);
                }
            }

            //封装数据 order
            setSyncInternetCelebritySampleOrder(syncInternetCelebritySampleOrder, internetCelebritySampleOrderImport);

            //封装数据 orderItem
            setSyncInternetCelebritySampleOrderItem(internetCelebritySampleOrderImports, syncInternetCelebritySampleOrder, syncInternetCelebritySampleOrderList);
        });

        //图片信息
        List<String> skus = syncInternetCelebritySampleOrderList.stream().flatMap(item -> item.getSyncInternetCelebritySampleOrderItems().stream().filter(Objects::nonNull)).map(SyncInternetCelebritySampleOrderItem::getSku).distinct().collect(Collectors.toList());
        List<ProductSpecDTO> productSpecDTOList = pmsApiService.specInfo(skus);
        Map<String, String> specSkuMap = productSpecDTOList.stream().filter(productSpecDTO -> StringUtils.isNotEmpty(productSpecDTO.getImageUrl())).collect(Collectors.toMap(ProductSpecDTO::getSpecSku, ProductSpecDTO::getImageUrl, (value1, value2) -> value2));

        syncInternetCelebritySampleOrderList.forEach(syncInternetCelebritySampleOrder -> {
            //img
            syncInternetCelebritySampleOrder.getSyncInternetCelebritySampleOrderItems().forEach(syncInternetCelebritySampleOrderItem -> {
                if (StringUtils.isEmpty(syncInternetCelebritySampleOrderItem.getSku())) {
                    return;
                }
                syncInternetCelebritySampleOrderItem.setSkuPictureUrl(specSkuMap.getOrDefault(syncInternetCelebritySampleOrderItem.getSku(), ""));
            });
            //官网包裹信息
            internetCelebritySampleOrderService.setPackageInfo(syncInternetCelebritySampleOrder);
        });

        //保存
        internetCelebritySampleOrderService.syncInternetCelebritySampleOrderByImport(syncInternetCelebritySampleOrderList);
    }

    private void setFbaInfo(GetSampleOrderResponse getSampleOrderResponse, List<InternetCelebritySampleOrderImport> internetCelebritySampleOrderImports, SyncInternetCelebritySampleOrder syncInternetCelebritySampleOrder) {
        SampleOrder sampleOrder = getSampleOrderResponse.getSampleOrderList().get(0);
        if (CollectionUtils.isNotEmpty(sampleOrder.getSampleOrderItemList())) {
            Map<String, SampleOrderItem> sampleOrderItemMap = sampleOrder.getSampleOrderItemList().stream().collect(Collectors.toMap(SampleOrderItem::getSku, a -> a, (k1, k2) -> k1));
            internetCelebritySampleOrderImports.forEach(itemInfo -> {
                SampleOrderItem sampleOrderItem = sampleOrderItemMap.get(itemInfo.getSku());
                Optional.ofNullable(sampleOrderItem).ifPresent(item -> {
                    itemInfo.setQty(item.getQty());
                    itemInfo.setSellerSku(item.getSellerSku());
                    itemInfo.setDeliveryStoreId(item.getDeliveryStoreId());
                    itemInfo.setOrderDeliveryDate(item.getOrderDeliveryDate());
                    itemInfo.setPlatformOriginalOrderNo(item.getPlatformOriginalOrderNo());
                    itemInfo.setOrderType(InternetCelebritySampleOrderDeliveryTypeEnum.FBA.getDesc().equals(item.getDeliveryType()) ? InternetCelebritySampleOrderDeliveryTypeEnum.FBA.getDescription() : InternetCelebritySampleOrderDeliveryTypeEnum.OVERSEAS_WAREHOUSE.getDescription());
                });
            });
        }
        syncInternetCelebritySampleOrder.setBuyerNick(sampleOrder.getBuyerNick());
        syncInternetCelebritySampleOrder.setOrderCreateDate(sampleOrder.getOrderCreateDate());
        syncInternetCelebritySampleOrder.setPlatformOrderNo(sampleOrder.getPlatformOrderNo());
    }

    private void getFbtInfo(List<SampleOrderResponse> sampleOrderResponses, List<InternetCelebritySampleOrderImport> internetCelebritySampleOrderImports, SyncInternetCelebritySampleOrder syncInternetCelebritySampleOrder) {
        if (CollectionUtils.isNotEmpty(sampleOrderResponses)) {
            SampleOrderResponse sampleOrderResponse = sampleOrderResponses.get(0);
            if (CollectionUtils.isNotEmpty(sampleOrderResponse.getOrderItems())) {
                Map<String, SampleOrderItemResponse> sampleOrderItemMap = sampleOrderResponse.getOrderItems().stream().collect(Collectors.toMap(SampleOrderItemResponse::getSku, a -> a, (k1, k2) -> k1));
                internetCelebritySampleOrderImports.forEach(itemInfo -> {
                    SampleOrderItemResponse sampleOrderItemResponse = sampleOrderItemMap.get(itemInfo.getSku());
                    Optional.ofNullable(sampleOrderItemResponse).ifPresent(item -> {
                        itemInfo.setQty(item.getQty());
                        itemInfo.setSellerSku(item.getSellerSku());
                        itemInfo.setOrderDeliveryDate(sampleOrderResponse.getOrderDeliverDate());
                        itemInfo.setDeliveryStoreId(sampleOrderResponse.getStoreId());
                        itemInfo.setOrderType(InternetCelebritySampleOrderDeliveryTypeEnum.FBT.getDesc());
                        itemInfo.setPlatformOriginalOrderNo(sampleOrderResponse.getPlatformOriginalOrderNo());
                    });
                });
            }
            syncInternetCelebritySampleOrder.setOrderCreateDate(sampleOrderResponse.getOrderCreateDate());
            syncInternetCelebritySampleOrder.setBuyerNick(sampleOrderResponse.getBuyerNick());
            syncInternetCelebritySampleOrder.setPlatformOrderNo(sampleOrderResponse.getPlatformOriginalOrderNo());
        }
    }

    private void setSyncInternetCelebritySampleOrder(SyncInternetCelebritySampleOrder syncInternetCelebritySampleOrder,
                                                     InternetCelebritySampleOrderImport internetCelebritySampleOrderImport) {
        syncInternetCelebritySampleOrder.setInternetCelebrityDeptId(internetCelebritySampleOrderImport.getInternetCelebrityDeptId());
        syncInternetCelebritySampleOrder.setInternetCelebrityDeptName(internetCelebritySampleOrderImport.getInternetCelebrityDeptName());
        syncInternetCelebritySampleOrder.setInternetCelebrityId(internetCelebritySampleOrderImport.getConfigInternetCelebrityResponse().getId());
        syncInternetCelebritySampleOrder.setInternetCelebrityNickname(internetCelebritySampleOrderImport.getConfigInternetCelebrityResponse().getNickName());
        syncInternetCelebritySampleOrder.setInternetCelebrityPlatform(internetCelebritySampleOrderImport.getConfigInternetCelebrityResponse().getPlatform());
        syncInternetCelebritySampleOrder.setOwnerCode(internetCelebritySampleOrderImport.getConfigInternetCelebrityResponse().getOwnerCode());
        syncInternetCelebritySampleOrder.setOwnerName(internetCelebritySampleOrderImport.getConfigInternetCelebrityResponse().getOwnerName());

        syncInternetCelebritySampleOrder.setDeptId(internetCelebritySampleOrderImport.getConfigInternetCelebrityResponse().getDeptId());
        syncInternetCelebritySampleOrder.setUserId(internetCelebritySampleOrderImport.getConfigInternetCelebrityResponse().getUserId());
        syncInternetCelebritySampleOrder.setOwnerUserId(internetCelebritySampleOrderImport.getConfigInternetCelebrityResponse().getOwnerUserId());
        syncInternetCelebritySampleOrder.setOwnerDeptId(internetCelebritySampleOrderImport.getConfigInternetCelebrityResponse().getOwnerDeptId());
    }

    private void setSyncInternetCelebritySampleOrderItem(List<InternetCelebritySampleOrderImport> internetCelebritySampleOrderImports,
                                                         SyncInternetCelebritySampleOrder syncInternetCelebritySampleOrder,
                                                         List<SyncInternetCelebritySampleOrder> syncInternetCelebritySampleOrderList) {
        Map<String, List<InternetCelebritySampleOrderImport>> skuMap = internetCelebritySampleOrderImports.stream().collect(Collectors.groupingBy(InternetCelebritySampleOrderImport::getSku));
        List<SyncInternetCelebritySampleOrderItem> syncInternetCelebritySampleOrderItems = new ArrayList<>();
        skuMap.forEach((sku, orderImports) -> {
            SyncInternetCelebritySampleOrderItem syncInternetCelebritySampleOrderItem = new SyncInternetCelebritySampleOrderItem();
            InternetCelebritySampleOrderImport orderImport = orderImports.stream().findFirst().orElse(null);
            if (!Optional.ofNullable(orderImport).isPresent()) {
                return;
            }
            syncInternetCelebritySampleOrderItem.setSku(orderImport.getSku());
            syncInternetCelebritySampleOrderItem.setQty(orderImport.getQty());
            syncInternetCelebritySampleOrderItem.setSellerSku(orderImport.getSellerSku());
            List<SyncInternetCelebritySampleOrderItemPost> syncInternetCelebritySampleOrderItemPosts = new ArrayList<>();
            orderImports.forEach(itemPost -> {
                SyncInternetCelebritySampleOrderItemPost syncInternetCelebritySampleOrderItemPost = new SyncInternetCelebritySampleOrderItemPost();
                syncInternetCelebritySampleOrderItemPost.setPostDate(Optional.ofNullable(itemPost.getPostDate()).isPresent() ? itemPost.getPostDate() : null);
                syncInternetCelebritySampleOrderItemPost.setVideoCode(StringUtils.isNotEmpty(itemPost.getVideoCode()) ? itemPost.getVideoCode() : "");
                syncInternetCelebritySampleOrderItemPost.setVideoUrl(StringUtils.isNotEmpty(itemPost.getVideoUrl()) ? itemPost.getVideoUrl() : "");
                syncInternetCelebritySampleOrderItemPosts.add(syncInternetCelebritySampleOrderItemPost);
            });
            syncInternetCelebritySampleOrderItem.setSyncInternetCelebritySampleOrderItemPosts(syncInternetCelebritySampleOrderItemPosts);
            syncInternetCelebritySampleOrderItems.add(syncInternetCelebritySampleOrderItem);
        });
        syncInternetCelebritySampleOrder.setSyncInternetCelebritySampleOrderItems(syncInternetCelebritySampleOrderItems);
        syncInternetCelebritySampleOrderList.add(syncInternetCelebritySampleOrder);
    }


    private void check(List<InternetCelebritySampleOrderImport> importList,
                       List<InternetCelebritySampleOrderImport> errorList) {
        //店铺
        List<String> storeNames = importList.stream().map(item -> Arrays.asList(item.getStoreName(), item.getDeliveryStoreName())).flatMap(item -> item.stream().filter(Objects::nonNull)).distinct().collect(Collectors.toList());
        Map<String, Integer> storeMap = CollectionUtils.isEmpty(storeNames) ? MapUtil.empty() : saStoreService.getStoreByStoreNames(storeNames)
                .stream().filter(saStoreEntity -> Integer.valueOf(1).equals(saStoreEntity.getStatus())).collect(Collectors.toMap(store -> store.getErpStoreName().toUpperCase(Locale.ROOT), SaStoreEntity::getId, (value1, value2) -> value2));

        Map<String, ConfigInternetCelebrityResponse> nickNameMap = getConfigInternetCelebrityList(importList).stream().collect(Collectors.toMap(ConfigInternetCelebrityResponse::getNickName, a -> a, (k1, k2) -> k1));

        HashMap<String, SysUserInfo> sysUserInfoMap = new HashMap<>();
        HashMap<Integer, SysDepartment> sysDepartmentMap = new HashMap<>();
        importList.forEach(internetCelebritySampleOrderImport -> {

            if (checkStore(storeMap, internetCelebritySampleOrderImport, errorList)) {
                return;
            }
            if (StringUtils.isEmpty(internetCelebritySampleOrderImport.getOwnerName())) {
                ConfigInternetCelebrityResponse configInternetCelebrityResponse = nickNameMap.get(internetCelebritySampleOrderImport.getInternetCelebrityNickname());
                if (!Optional.ofNullable(configInternetCelebrityResponse).isPresent()) {
                    internetCelebritySampleOrderImport.setErrorMsg("网红系统不存在");
                    errorList.add(internetCelebritySampleOrderImport);
                    return;
                } else {
                    internetCelebritySampleOrderImport.setOwnerName(configInternetCelebrityResponse.getOwnerName());
                }
            }

            SysUserInfo sysUserInfo = Optional.ofNullable(sysUserInfoMap.get(internetCelebritySampleOrderImport.getOwnerName())).orElseGet(() -> {
                SysUserInfo userInfo = userApiService.getUserInfo(internetCelebritySampleOrderImport.getOwnerName());
                sysUserInfoMap.put(userInfo.getUserName(), userInfo);
                return userInfo;
            });
            if (ObjectUtils.isEmpty(sysUserInfo) || ObjectUtils.isEmpty(sysUserInfo.getUserId())) {
                internetCelebritySampleOrderImport.setErrorMsg("网红负责人系统不存在");
                errorList.add(internetCelebritySampleOrderImport);
                return;
            }
            SysDepartment sysDepartment = Optional.ofNullable(sysDepartmentMap.get(sysUserInfo.getDepartmentId())).orElseGet(() -> {
                SysDepartment department = userApiService.getFirstLevelDepartmentByDeptId(sysUserInfo.getDepartmentId());
                sysDepartmentMap.put(sysUserInfo.getDepartmentId(), department);
                return department;
            });
            if (ObjectUtils.isEmpty(sysDepartment)) {
                internetCelebritySampleOrderImport.setErrorMsg("网红负责人一级部门系统不存在");
                errorList.add(internetCelebritySampleOrderImport);
                return;
            }
            internetCelebritySampleOrderImport.setSysUserInfo(sysUserInfo);
            internetCelebritySampleOrderImport.setOwnerDeptId(sysUserInfo.getDepartmentId());
            internetCelebritySampleOrderImport.setInternetCelebrityDeptId(sysDepartment.getDepartmentId());
            internetCelebritySampleOrderImport.setInternetCelebrityDeptName(sysDepartment.getShortName());


            if (StringUtils.isEmpty(internetCelebritySampleOrderImport.getPlatformOrderNo())) {
                internetCelebritySampleOrderImport.setOrderType(InternetCelebritySampleOrderDeliveryTypeEnum.NOT_ORDER.getDesc());
                internetCelebritySampleOrderImport.setPlatformOrderNo(String.format("%s_%s", InternetCelebritySampleOrderDeliveryTypeEnum.NOT_ORDER.getDescription(), UUID.randomUUID()));
            }
            internetCelebritySampleOrderImport.setSku(StringUtils.isNotEmpty(internetCelebritySampleOrderImport.getSku()) ? internetCelebritySampleOrderImport.getSku() : "");
        });
        if (CollectionUtils.isNotEmpty(errorList)) {
            return;
        }
        saveInternetCelebrity(importList, errorList);
    }

    private List<ConfigInternetCelebrityResponse> getConfigInternetCelebrityList(List<InternetCelebritySampleOrderImport> importList) {
        List<ConfigInternetCelebrityResponse> configInternetCelebrityList = new ArrayList<>();
        List<String> buyerNicks = importList.stream().filter(internetCelebritySampleOrderImport -> StringUtils.isEmpty(internetCelebritySampleOrderImport.getOwnerName())).map(InternetCelebritySampleOrderImport::getInternetCelebrityNickname).distinct().collect(Collectors.toList());
        ListUtils.partition(buyerNicks, 200).forEach(nicks -> {
            ConfigInternetCelebrityRequest configInternetCelebrityRequest = new ConfigInternetCelebrityRequest();
            configInternetCelebrityRequest.setNickNameList(nicks);
            configInternetCelebrityList.addAll(pmsApiService.getConfigInternetCelebrityList(configInternetCelebrityRequest));
        });
        return configInternetCelebrityList;
    }

    private Boolean checkStore(Map<String, Integer> storeMap,
                               InternetCelebritySampleOrderImport internetCelebritySampleOrderImport,
                               List<InternetCelebritySampleOrderImport> errorList) {
        Integer storeId = storeMap.getOrDefault(StringUtils.isEmpty(internetCelebritySampleOrderImport.getStoreName()) ? "" : internetCelebritySampleOrderImport.getStoreName().toUpperCase(Locale.ROOT), 0);
        Integer deliveryStoreId = storeMap.getOrDefault(StringUtils.isEmpty(internetCelebritySampleOrderImport.getDeliveryStoreName()) ? "" : internetCelebritySampleOrderImport.getDeliveryStoreName().toUpperCase(Locale.ROOT), 0);
        if (InternetCelebritySampleOrderDeliveryTypeEnum.FBA.getDesc().equals(internetCelebritySampleOrderImport.getOrderType())
                || InternetCelebritySampleOrderDeliveryTypeEnum.FBT.getDesc().equals(internetCelebritySampleOrderImport.getOrderType())) {
            if (StringUtils.isEmpty(internetCelebritySampleOrderImport.getPlatformOrderNo())) {
                internetCelebritySampleOrderImport.setErrorMsg("FBA或FBT,订单号不允许为空");
                errorList.add(internetCelebritySampleOrderImport);
                return Boolean.TRUE;
            }
            if (!Optional.ofNullable(storeId).isPresent()) {
                internetCelebritySampleOrderImport.setErrorMsg("订单店铺系统不存在");
                errorList.add(internetCelebritySampleOrderImport);
                return Boolean.TRUE;
            }
            if (!Optional.ofNullable(deliveryStoreId).isPresent()) {
                internetCelebritySampleOrderImport.setErrorMsg("发货店铺系统不存在");
                errorList.add(internetCelebritySampleOrderImport);
                return Boolean.TRUE;
            }
            internetCelebritySampleOrderImport.setPlatformOriginalOrderNo(internetCelebritySampleOrderImport.getPlatformOrderNo());
        }
        internetCelebritySampleOrderImport.setStoreId(storeId);
        internetCelebritySampleOrderImport.setDeliveryStoreId(deliveryStoreId);
        return Boolean.FALSE;
    }

    private void saveInternetCelebrity(List<InternetCelebritySampleOrderImport> importList,
                                       List<InternetCelebritySampleOrderImport> errorList) {
        List<String> buyerNicks = importList.stream().map(InternetCelebritySampleOrderImport::getInternetCelebrityNickname).distinct().collect(Collectors.toList());
        List<Integer> ownerDeptIds = importList.stream().map(InternetCelebritySampleOrderImport::getOwnerDeptId).distinct().collect(Collectors.toList());

        List<ConfigInternetCelebrityResponse> configInternetCelebrityList = new ArrayList<>();
        ListUtils.partition(buyerNicks, 200).forEach(nicks -> configInternetCelebrityList.addAll(pmsApiService.getConfigInternetCelebrityList(new ConfigInternetCelebrityRequest(nicks, ownerDeptIds))));

        importList.forEach(internetCelebritySampleOrderImport -> configInternetCelebrityList.stream()
                .filter(configInternetCelebrity -> configInternetCelebrity.getNickName().equals(internetCelebritySampleOrderImport.getInternetCelebrityNickname()) && configInternetCelebrity.getOwnerDeptId().equals(internetCelebritySampleOrderImport.getOwnerDeptId()))
                .findFirst().ifPresent(internetCelebritySampleOrderImport::setConfigInternetCelebrityResponse));


        List<InternetCelebrityBatchSaveInfo> saveInfoList = new ArrayList<>();
        importList.forEach(internetCelebritySampleOrderImport -> {
            List<ConfigInternetCelebrityResponse> configInternetCelebrityResponse = configInternetCelebrityList.stream()
                    .filter(Objects::nonNull)
                    .filter(configInternetCelebrity -> configInternetCelebrity.getNickName().equals(internetCelebritySampleOrderImport.getInternetCelebrityNickname())
                            && configInternetCelebrity.getOwnerDeptId().equals(internetCelebritySampleOrderImport.getOwnerDeptId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(configInternetCelebrityResponse)) {
                internetCelebritySampleOrderImport.setFlag(true);
                saveInfoList.add(setInternetCelebrityBatchSaveInfo(internetCelebritySampleOrderImport));
            } else if (configInternetCelebrityResponse.stream().noneMatch(configInternetCelebrity -> configInternetCelebrity.getOwnerCode().equals(internetCelebritySampleOrderImport.getSysUserInfo().getUserAccount()))) {
                internetCelebritySampleOrderImport.setErrorMsg("网红与网红专员不匹配");
                errorList.add(internetCelebritySampleOrderImport);
            }
        });
        if (CollectionUtils.isNotEmpty(saveInfoList)) {

            Map<String, InternetCelebrityBatchSaveInfo> internetCelebrityBatchSaveInfoMap = saveInfoList.stream().collect(Collectors.toMap(info -> String.format("%s_%s", info.getNickName(), info.getOwnerName()), a -> a, (k1, k2) -> k1));
            List<InternetCelebrityBatchSaveInfo> internetCelebrityBatchSaveInfos = new ArrayList<>(internetCelebrityBatchSaveInfoMap.values());

            List<InternetCelebritySaveResultInfo> internetCelebritySaveResultInfos = productapiService.batchSaveInternetCelebrity(new InternetCelebrityBatchSaveRequest(internetCelebrityBatchSaveInfos));

            Map<String, InternetCelebritySaveResultInfo> saveResultInfoMap = internetCelebritySaveResultInfos.stream().collect(Collectors.toMap(entity -> String.format("%s_%s", entity.getNickName(), entity.getOwnerDeptId()), a -> a, (k1, k2) -> k1));
            importList.forEach(internetCelebritySampleOrderImport -> {
                if (!(Optional.ofNullable(internetCelebritySampleOrderImport.getFlag()).isPresent() && internetCelebritySampleOrderImport.getFlag())) {
                    return;
                }
                InternetCelebritySaveResultInfo internetCelebritySaveResultInfo = saveResultInfoMap.get(String.format("%s_%s", internetCelebritySampleOrderImport.getInternetCelebrityNickname(), internetCelebritySampleOrderImport.getOwnerDeptId()));
                if (!Optional.ofNullable(internetCelebritySaveResultInfo).isPresent()) {
                    internetCelebritySampleOrderImport.setErrorMsg("网红创建失败");
                    errorList.add(internetCelebritySampleOrderImport);
                    return;
                }
                if (!internetCelebritySaveResultInfo.isSuccess()) {
                    internetCelebritySampleOrderImport.setErrorMsg(internetCelebritySaveResultInfo.getErrorMessage());
                    errorList.add(internetCelebritySampleOrderImport);
                    return;
                }
                ConfigInternetCelebrityResponse configInternetCelebrityResponse = new ConfigInternetCelebrityResponse();
                configInternetCelebrityResponse.setId(internetCelebritySaveResultInfo.getId());
                configInternetCelebrityResponse.setNickName(internetCelebritySaveResultInfo.getNickName());
                configInternetCelebrityResponse.setPlatform(internetCelebritySaveResultInfo.getPlatform());
                configInternetCelebrityResponse.setOwnerCode(internetCelebritySaveResultInfo.getOwnerCode());
                configInternetCelebrityResponse.setOwnerName(internetCelebritySaveResultInfo.getOwnerName());
                configInternetCelebrityResponse.setDeptId(internetCelebritySaveResultInfo.getDeptId());
                configInternetCelebrityResponse.setUserId(internetCelebritySaveResultInfo.getUserId());
                configInternetCelebrityResponse.setOwnerUserId(internetCelebritySaveResultInfo.getOwnerUserId());
                configInternetCelebrityResponse.setOwnerDeptId(internetCelebritySaveResultInfo.getOwnerDeptId());
                internetCelebritySampleOrderImport.setConfigInternetCelebrityResponse(configInternetCelebrityResponse);
            });
        }
    }

    private InternetCelebrityBatchSaveInfo setInternetCelebrityBatchSaveInfo(InternetCelebritySampleOrderImport internetCelebritySampleOrderImport) {
        InternetCelebrityBatchSaveInfo internetCelebrityBatchSaveInfo = new InternetCelebritySaveResultInfo();
        internetCelebrityBatchSaveInfo.setNickName(internetCelebritySampleOrderImport.getInternetCelebrityNickname());
        internetCelebrityBatchSaveInfo.setSex("女");
        internetCelebrityBatchSaveInfo.setCountry("美国");
        internetCelebrityBatchSaveInfo.setEmail("");
        internetCelebrityBatchSaveInfo.setPlatform(StoreReleaseMethodChannelEnum.TIKTOK.getDesc());
        internetCelebrityBatchSaveInfo.setSocialHomePage(String.format("https://www.tiktok.com/@%s", internetCelebritySampleOrderImport.getInternetCelebrityNickname()));
        internetCelebrityBatchSaveInfo.setRating("A");
        internetCelebrityBatchSaveInfo.setOwnerCode(internetCelebritySampleOrderImport.getSysUserInfo().getUserAccount());
        internetCelebrityBatchSaveInfo.setOwnerName(internetCelebritySampleOrderImport.getSysUserInfo().getUserName());
        internetCelebrityBatchSaveInfo.setCooperationState("合作中");
        internetCelebrityBatchSaveInfo.setType("带货网红");
        internetCelebrityBatchSaveInfo.setUser(internetCelebritySampleOrderImport.getSysUserInfo());
        return internetCelebrityBatchSaveInfo;
    }


}
