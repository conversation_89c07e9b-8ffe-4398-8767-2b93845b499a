package com.nsy.oms.business.manage.user.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class OssResponse {

    @JsonProperty("accessKeyId")
    private String accessKeyId;

    @JsonProperty("accessKeySecret")
    private String accessKeySecret;

    @JsonProperty("bucket")
    private String bucket;

    @JsonProperty("regionId")
    private String regionId = "cn-hangzhou";

    @JsonProperty("url")
    private List<String> url;

    public String getAccessKeyId() {
        return accessKeyId;
    }

    public void setAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
    }

    public String getAccessKeySecret() {
        return accessKeySecret;
    }

    public void setAccessKeySecret(String accessKeySecret) {
        this.accessKeySecret = accessKeySecret;
    }

    public String getBucket() {
        return bucket;
    }

    public void setBucket(String bucket) {
        this.bucket = bucket;
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public List<String> getUrl() {
        return url;
    }

    public void setUrl(List<String> url) {
        this.url = url;
    }
}
