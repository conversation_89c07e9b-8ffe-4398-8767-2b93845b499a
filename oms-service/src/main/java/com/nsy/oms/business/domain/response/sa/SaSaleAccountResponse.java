package com.nsy.oms.business.domain.response.sa;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.base.secure.DesensitizationProp;
import com.nsy.base.secure.enums.SensitiveTypeEnum;
import com.nsy.oms.business.domain.request.sa.SaSaleAccountMarketMappingRequest;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/2 13:45
 */
public class SaSaleAccountResponse {
    @ApiModelProperty("id")
    private Integer id;
    @ApiModelProperty("账号")
    @DesensitizationProp(SensitiveTypeEnum.BANK_CARD)
    private String accountNum;
    @ApiModelProperty("账号名")
    private String accountName;
    @ApiModelProperty("所属平台")
    private String platformName;
    @ApiModelProperty("平台id")
    private Integer platformId;
    @ApiModelProperty("所属部门")
    private String department;
    @ApiModelProperty("所属部门id")
    private Integer departmentId;
    @ApiModelProperty("市场")
    private String marketName;
    @ApiModelProperty("市场id")
    private Integer marketId;
    @ApiModelProperty("小组id")
    private String teamId;
    @ApiModelProperty("小组")
    private String team;
    @ApiModelProperty("公司主体")
    private String company;
    @ApiModelProperty("状态")
    private String enableName;
    @ApiModelProperty("状态")
    private Integer enable;
    @ApiModelProperty("创建时间")
    private Date createDate;
    @ApiModelProperty("账号平台创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date establishDate;
    @ApiModelProperty("主体id")
    private Integer subjectId;
    /**
     * 状态 0 停用 1 启用
     */
    @ApiModelProperty("是否销售店铺0否1是")
    private Integer isSaleStore;
    @ApiModelProperty("是否销售店铺-显示使用")
    private String saleStore;
    /**
     * 部门(数据字典名称)
     */
    @ApiModelProperty("二级部门")
    private String secondDepartmentId;

    @ApiModelProperty("二级部门")
    private String secondDepartment;
    /**
     * 区域编码
     */
    @ApiModelProperty("区域编码")
    private String areaCode;
    @ApiModelProperty("主体")
    private String saleAccountName;
    @ApiModelProperty("邮箱id")
    private Integer emailId;
    @ApiModelProperty("邮箱")
    private String email;
    @ApiModelProperty("电话id")
    private Integer phoneId;
    @ApiModelProperty("邮箱")
    private String phone;

    /**
     * 区域名称
     */
    @ApiModelProperty("区域名称")
    private String areaName;
    @ApiModelProperty("市场")
    private List<SaSaleAccountMarketMappingRequest> marketList;

    public Integer getPhoneId() {
        return phoneId;
    }

    public void setPhoneId(Integer phoneId) {
        this.phoneId = phoneId;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getEmailId() {
        return emailId;
    }

    public void setEmailId(Integer emailId) {
        this.emailId = emailId;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getSaleStore() {
        return saleStore;
    }

    public String getSaleAccountName() {
        return saleAccountName;
    }

    public void setSaleAccountName(String saleAccountName) {
        this.saleAccountName = saleAccountName;
    }

    public String getSecondDepartment() {
        return secondDepartment;
    }

    public void setSecondDepartment(String secondDepartment) {
        this.secondDepartment = secondDepartment;
    }

    public String getSecondDepartmentId() {
        return secondDepartmentId;
    }

    public void setSecondDepartmentId(String secondDepartmentId) {
        this.secondDepartmentId = secondDepartmentId;
    }

    public void setSaleStore(String saleStore) {
        this.saleStore = saleStore;
    }

    public List<SaSaleAccountMarketMappingRequest> getMarketList() {
        return marketList;
    }



    public void setMarketList(List<SaSaleAccountMarketMappingRequest> marketList) {
        this.marketList = marketList;
    }

    public Integer getIsSaleStore() {
        return isSaleStore;
    }

    public void setIsSaleStore(Integer isSaleStore) {
        this.isSaleStore = isSaleStore;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getAccountNum() {
        return accountNum;
    }

    public void setAccountNum(String accountNum) {
        this.accountNum = accountNum;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public Integer getPlatformId() {
        return platformId;
    }

    public void setPlatformId(Integer platformId) {
        this.platformId = platformId;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public String getMarketName() {
        return marketName;
    }

    public void setMarketName(String marketName) {
        this.marketName = marketName;
    }

    public Integer getMarketId() {
        return marketId;
    }

    public void setMarketId(Integer marketId) {
        this.marketId = marketId;
    }

    public String getTeamId() {
        return teamId;
    }

    public void setTeamId(String teamId) {
        this.teamId = teamId;
    }

    public String getTeam() {
        return team;
    }

    public void setTeam(String team) {
        this.team = team;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getEnableName() {
        return enableName;
    }

    public void setEnableName(String enableName) {
        this.enableName = enableName;
    }

    public Integer getEnable() {
        return enable;
    }

    public void setEnable(Integer enable) {
        this.enable = enable;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getEstablishDate() {
        return establishDate;
    }

    public void setEstablishDate(Date establishDate) {
        this.establishDate = establishDate;
    }

    public Integer getSubjectId() {
        return subjectId;
    }

    public void setSubjectId(Integer subjectId) {
        this.subjectId = subjectId;
    }
}
