package com.nsy.oms.business.service.celebrity;

import com.nsy.api.pms.dto.request.BaseListRequest;
import com.nsy.oms.business.domain.request.celebrity.InternetCelebritySyncAdRequest;
import com.nsy.oms.business.domain.request.celebrity.InternetCelebritySyncCreatorRequest;
import com.nsy.oms.business.domain.request.celebrity.InternetCelebritySyncMaxAdRequest;
import com.nsy.oms.business.domain.request.celebrity.InternetCelebritySyncVideoRequest;
import com.nsy.oms.business.domain.response.celebrity.InternetCelebrityCreatorNoInfoResponse;
import com.nsy.oms.business.domain.upload.InternetCelebrityVideoImport;

public interface InternetCelebritySyncService {

    void syncCreator(InternetCelebritySyncCreatorRequest request);

    void syncVideo(InternetCelebritySyncVideoRequest request);

    void syncVideoByImport(InternetCelebrityVideoImport videoImport, String creator);

    void syncAd(InternetCelebritySyncAdRequest request);

    void syncMaxAd(InternetCelebritySyncMaxAdRequest request);

    InternetCelebrityCreatorNoInfoResponse creatorNoInfoPage(BaseListRequest request);

}