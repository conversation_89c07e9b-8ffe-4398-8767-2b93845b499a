package com.nsy.oms.business.domain.request.shein;


import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

public class SheinRecommendSaleSkcPushRequest {
    @NotEmpty
    private List<Integer> websiteIds;
    @NotNull
    private Integer productId;
    @NotEmpty
    private List<String> colorList;

    private Boolean isForcedPush;

    private String createBy;

    public Boolean getIsForcedPush() {
        return isForcedPush;
    }

    public void setIsForcedPush(Boolean isForcedPush) {
        this.isForcedPush = isForcedPush;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public List<Integer> getWebsiteIds() {
        return websiteIds;
    }

    public void setWebsiteIds(List<Integer> websiteIds) {
        this.websiteIds = websiteIds;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public List<String> getColorList() {
        return colorList;
    }

    public void setColorList(List<String> colorList) {
        this.colorList = colorList;
    }
}