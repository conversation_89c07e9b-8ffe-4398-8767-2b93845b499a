package com.nsy.oms.business.manage.pms.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * <AUTHOR>
 */
@ApiModel(description = "简单的spec基础信息")
public class SimpleProductSpecResponse {
    @ApiModelProperty(value = "specId", name = "specId")
    private Integer specId;
    @ApiModelProperty(value = "productId", name = "productId")
    private Integer productId;
    @ApiModelProperty(value = "规格编码", name = "specSku")
    private String specSku;
    @ApiModelProperty(value = "skc", name = "skc")
    private String colorSku;

    public String getColorSku() {
        return colorSku;
    }

    public void setColorSku(String colorSku) {
        this.colorSku = colorSku;
    }
    public Integer getSpecId() {
        return specId;
    }

    public void setSpecId(Integer specId) {
        this.specId = specId;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getSpecSku() {
        return specSku;
    }

    public void setSpecSku(String specSku) {
        this.specSku = specSku;
    }

}
