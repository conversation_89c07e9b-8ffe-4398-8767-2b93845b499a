package com.nsy.oms.business.service.order.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.oms.business.service.order.SaleOrderReceiverService;
import com.nsy.oms.repository.entity.order.SaleOrderReceiverEntity;
import com.nsy.oms.repository.sql.mapper.order.SaleOrderReceiverMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【order_receiver(订单收件人表)】的数据库操作Service实现
* @createDate 2024-06-13 14:52:05
*/
@Service
public class SaleOrderReceiverServiceImpl extends ServiceImpl<SaleOrderReceiverMapper, SaleOrderReceiverEntity>
    implements SaleOrderReceiverService {
    @Override
    public SaleOrderReceiverEntity getByOrderId(Integer orderId) {
        return getBaseMapper().selectOne(Wrappers.<SaleOrderReceiverEntity>lambdaQuery().eq(SaleOrderReceiverEntity::getOrderId, orderId));
    }
}




