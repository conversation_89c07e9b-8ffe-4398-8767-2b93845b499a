package com.nsy.oms.business.service.rule;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nsy.oms.business.domain.ao.rule.ConfigSkcStockShareSaveOrUpdateItemAO;
import com.nsy.oms.business.domain.vo.rule.ConfigSkcStockShareItemVO;
import com.nsy.oms.repository.entity.rule.ConfigSkcStockShareItemEntity;

import java.util.List;

/**
 * <p>
 * skc库存共享明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-01
 */
public interface IConfigSkcStockShareItemService extends IService<ConfigSkcStockShareItemEntity> {

    void saveOrUpdate(Integer configSkcStockShareId, List<ConfigSkcStockShareSaveOrUpdateItemAO> configSkcStockShareItemAOS);

    List<ConfigSkcStockShareItemVO> detail(List<Integer> configSkcStockShareIds);

    void deleteByShareId(List<Integer> configSkcStockShareIds);
}
