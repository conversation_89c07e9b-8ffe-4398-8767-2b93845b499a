package com.nsy.oms.business.service.order.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.core.apicore.constant.enums.QueueStatusEnum;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.privilege.LoginInfoService;
import com.nsy.api.core.apicore.util.NsyCollUtil;
import com.nsy.api.transfer.enums.PlatformTypeEnum;
import com.nsy.oms.business.domain.ao.RepairAO;
import com.nsy.oms.business.domain.request.order.OrderMissedQueueRequest;
import com.nsy.oms.business.domain.request.order.RepairRequest;
import com.nsy.oms.business.domain.response.base.PageResponse;
import com.nsy.oms.business.domain.response.order.OrderMissedQueueResponse;
import com.nsy.oms.business.service.order.OrderMissedQueueService;
import com.nsy.oms.business.service.sa.SaStoreConfigService;
import com.nsy.oms.business.service.sa.SaStoreService;
import com.nsy.oms.constants.MybatisQueryConstant;
import com.nsy.oms.constants.StoreConstant;
import com.nsy.oms.repository.dao.order.OrderMissedQueueDao;
import com.nsy.oms.repository.entity.order.OrderMissedQueueEntity;
import com.nsy.oms.repository.entity.sa.SaStoreConfigEntity;
import com.nsy.oms.repository.entity.sa.SaStoreEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @createDate 2024-06-13 14:52:05
 */
@Service
public class OrderMissedQueueServiceImpl implements OrderMissedQueueService {
    @Autowired
    private OrderMissedQueueDao orderMissedQueueDao;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private SaStoreService saStoreService;
    @Autowired
    private SaStoreConfigService saStoreConfigService;

    @Override
    public PageResponse<OrderMissedQueueResponse> list(OrderMissedQueueRequest request) {
        Page<OrderMissedQueueResponse> page = orderMissedQueueDao.getList(request);
        PageResponse<OrderMissedQueueResponse> response = new PageResponse<>();
        response.setTotalCount(page.getTotal());
        response.setContent(Optional.of(page.getRecords()).orElse(new ArrayList<>()).stream().map(item -> {
            OrderMissedQueueResponse queueResponse = new OrderMissedQueueResponse();
            BeanUtils.copyProperties(item, queueResponse);
            queueResponse.setQueueStatusName(QueueStatusEnum.getDescriptionByCode(item.getQueueStatus()));
            SaStoreEntity saStoreEntity = saStoreService.getByStoreId(item.getStoreId());
            queueResponse.setStoreName(ObjectUtil.isNotEmpty(saStoreEntity) ? saStoreEntity.getErpStoreName() : StringUtils.EMPTY);
            return queueResponse;
        }).collect(Collectors.toList()));
        return response;
    }

    @Transactional(rollbackFor = BusinessServiceException.class)
    @Override
    public void add(OrderMissedQueueRequest request) {
        if (CollUtil.isEmpty(request.getOrderNoList())) {
            throw new BusinessServiceException("订单不能为空");
        }
        List<OrderMissedQueueEntity> orderMissedQueueEntityList = new ArrayList<>();
        request.getOrderNoList().forEach(item -> {
            OrderMissedQueueEntity orderMissedQueueEntity = new OrderMissedQueueEntity();
            orderMissedQueueEntity.setStoreId(request.getStoreId());
            checkStore(request.getStoreId());
            orderMissedQueueEntity.setOrderNo(item);
            orderMissedQueueEntity.setQueueStatus(QueueStatusEnum.INIT.getCode());
            orderMissedQueueEntity.setCreateBy(loginInfoService.getName());
            orderMissedQueueEntityList.add(orderMissedQueueEntity);
        });
        if (CollUtil.isNotEmpty(orderMissedQueueEntityList)) {
            orderMissedQueueDao.saveBatch(orderMissedQueueEntityList);
        }
    }

    private void checkStore(Integer storeId) {
        SaStoreEntity saStoreEntity = saStoreService.getByStoreId(storeId);
        if (saStoreEntity.getStatus() < 1) {
            throw new BusinessServiceException(saStoreEntity.getErpStoreName() + "店铺处于关店无需抓单");
        }
        SaStoreConfigEntity saStoreConfigEntity = saStoreConfigService.getInfo(storeId);
        if (saStoreConfigEntity.getProhibitSynchronizedOrder() > 0) {
            throw new BusinessServiceException(saStoreEntity.getErpStoreName() + "店铺禁止同步订单无需抓单");
        }
        if (StoreConstant.getRestockStoreType().contains(saStoreEntity.getAchievementAttribution())) {
            throw new BusinessServiceException(saStoreEntity.getErpStoreName() + "亚马逊海外仓或沃尔玛WFS或海外代发无需抓单");
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void retry(OrderMissedQueueRequest request) {
        if (CollUtil.isEmpty(request.getIdList())) {
            throw new BusinessServiceException("重试的数据不能为空");
        }
        List<OrderMissedQueueEntity> orderMissedQueueEntities = orderMissedQueueDao.list(Wrappers.<OrderMissedQueueEntity>lambdaQuery().in(OrderMissedQueueEntity::getOrderMissedId, request.getIdList()));
        orderMissedQueueDao.updateBatchById(Optional.ofNullable(orderMissedQueueEntities).orElse(new ArrayList<>()).stream().peek(item -> {
            item.setQueueStatus(QueueStatusEnum.INIT.getCode());
            item.setUpdateBy(loginInfoService.getName());
        }).collect(Collectors.toList()));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void ignore(OrderMissedQueueRequest request) {
        if (CollUtil.isEmpty(request.getIdList())) {
            throw new BusinessServiceException("忽略的数据不能为空");
        }
        List<OrderMissedQueueEntity> orderMissedQueueEntities = orderMissedQueueDao.list(Wrappers.<OrderMissedQueueEntity>lambdaQuery().in(OrderMissedQueueEntity::getOrderMissedId, request.getIdList()));
        orderMissedQueueDao.updateBatchById(Optional.ofNullable(orderMissedQueueEntities).orElse(new ArrayList<>()).stream().peek(item -> {
            item.setQueueStatus(QueueStatusEnum.IGNORE.getCode());
            item.setUpdateBy(loginInfoService.getName());
        }).collect(Collectors.toList()));

    }

    @Override
    public List<OrderMissedQueueEntity> getList(Integer fetchCount) {
        return orderMissedQueueDao.list(Wrappers.<OrderMissedQueueEntity>lambdaQuery()
                .eq(OrderMissedQueueEntity::getQueueStatus, QueueStatusEnum.INIT.getCode())
                .orderByDesc(OrderMissedQueueEntity::getOrderMissedId)
                .last(String.format(MybatisQueryConstant.LIMIT, fetchCount)));
    }

    @Override
    public void updateQueueStatus(OrderMissedQueueEntity item) {
        orderMissedQueueDao.updateById(item);
    }

    @Override
    public void repair(RepairRequest request) {
        List<RepairAO> repairList = request.getRepairList();
        LambdaQueryWrapper<OrderMissedQueueEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OrderMissedQueueEntity::getOrderNo, repairList.stream().map(RepairAO::getOrderNo).distinct().collect(Collectors.toList()));
        queryWrapper.in(OrderMissedQueueEntity::getStoreId, repairList.stream().map(RepairAO::getStoreId).distinct().collect(Collectors.toList()));
        List<OrderMissedQueueEntity> orderMissedQueueEntities = orderMissedQueueDao.list(queryWrapper);

        Map<String, OrderMissedQueueEntity> orderMissedQueueEntityMap = orderMissedQueueEntities.stream().collect(Collectors.toMap(e -> String.format("%s_%s", e.getStoreId(), e.getOrderNo()), Function.identity(), (k1, k2) -> k1));

        List<OrderMissedQueueEntity> orderMissedQueueEntityList = new ArrayList<>();
        repairList.forEach(repairAO -> {
            if (!Optional.ofNullable(orderMissedQueueEntityMap.get(String.format("%s_%s", repairAO.getStoreId(), repairAO.getOrderNo()))).isPresent()) {
                OrderMissedQueueEntity entity = new OrderMissedQueueEntity();
                BeanUtils.copyProperties(repairAO, entity);
                entity.setQueueStatus(QueueStatusEnum.INIT.getCode());
                entity.setCreateDate(new Date());
                orderMissedQueueEntityList.add(entity);
            }
        });

        orderMissedQueueDao.saveOrUpdateBatch(orderMissedQueueEntityList);
    }

    @Override
    public void updateBatchById(List<OrderMissedQueueEntity> partitionStoreList) {
        orderMissedQueueDao.updateBatchById(partitionStoreList);
    }

    @Override
    public void updateQueueStatusByOrderId(Integer status, String orderNo, Integer storeId) {
        LambdaQueryWrapper<OrderMissedQueueEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(OrderMissedQueueEntity::getQueueStatus, ListUtil.toList(QueueStatusEnum.INIT.getCode(), QueueStatusEnum.EXECUTING.getCode()))
                .eq(OrderMissedQueueEntity::getOrderNo, orderNo)
                .eq(OrderMissedQueueEntity::getStoreId, storeId).orderByAsc(OrderMissedQueueEntity::getQueueStatus).last("limit 1");
        OrderMissedQueueEntity orderMissedQueueEntity = orderMissedQueueDao.getOne(lambdaQueryWrapper);
        if (ObjectUtil.isNotNull(orderMissedQueueEntity)) {
            orderMissedQueueEntity.setQueueStatus(status);
            orderMissedQueueDao.updateById(orderMissedQueueEntity);
        }
    }

    /**
     * 保存 订单号队列
     *
     * @param storeEntity     店铺
     * @param platformOrderNo 订单号
     */
    @Override
    public void saveOrderMissedQueue(SaStoreEntity storeEntity, String platformOrderNo, String updateBy) {
        if (ObjectUtil.isNull(storeEntity) || StringUtils.isBlank(platformOrderNo)) {
            throw new IllegalArgumentException("storeEntity or platformOrderNo cannot be null");
        }
        OrderMissedQueueEntity queueEntity = new OrderMissedQueueEntity();
        queueEntity.setStoreId(storeEntity.getId());
        queueEntity.setLocation(storeEntity.getLocation());
        queueEntity.setOrderNo(platformOrderNo);
        queueEntity.setQueueStatus(QueueStatusEnum.INIT.getCode());
        queueEntity.setPriority(0);
        queueEntity.setRetryCount(0);
        queueEntity.setCreateBy(updateBy);
        queueEntity.setUpdateBy(updateBy);

        List<OrderMissedQueueEntity> list = orderMissedQueueDao.list(Wrappers.<OrderMissedQueueEntity>lambdaQuery()
                .eq(OrderMissedQueueEntity::getStoreId, queueEntity.getStoreId())
                .eq(OrderMissedQueueEntity::getOrderNo, queueEntity.getOrderNo())
                .ne(OrderMissedQueueEntity::getQueueStatus, QueueStatusEnum.EXECUTE_SUCCESS.getCode()));

        if (NsyCollUtil.isNotEmpty(list)) {
            list.forEach(queue -> {
                queue.setQueueStatus(QueueStatusEnum.IGNORE.getCode());
                queue.setUpdateBy(queueEntity.getUpdateBy());
            });
            orderMissedQueueDao.updateBatchById(list);
        }
        orderMissedQueueDao.save(queueEntity);
    }

    /**
     * 更新订单缺失队列信息
     *
     * @param orderMissedQueueEntityList 订单缺失队列实体列表，包含待更新的订单信息
     * @param queueStatusEnum            队列状态枚举，表示更新后的队列状态
     * @param remark                     备注信息，记录订单状态变更的原因或说明
     * @param updateBy                   更新人标识，记录最后更新操作的执行者
     */
    @Override
    public void updateOrderMissedQueue(List<OrderMissedQueueEntity> orderMissedQueueEntityList, QueueStatusEnum queueStatusEnum, String remark, String updateBy) {
        if (NsyCollUtil.isEmpty(orderMissedQueueEntityList)) {
            return;
        }
        if (ObjectUtil.isNull(queueStatusEnum)) {
            throw new IllegalArgumentException("queueStatusEnum cannot be null");
        }
        orderMissedQueueEntityList.forEach(queue -> {
            queue.setQueueStatus(queueStatusEnum.getCode());
            queue.setRetryCount(queue.getRetryCount() + 1);
            queue.setRemark(StringUtils.isNotBlank(remark) && remark.length() > 200 ? remark.substring(0, 200) : remark);
            queue.setUpdateBy(updateBy);
        });
        orderMissedQueueDao.updateBatchById(orderMissedQueueEntityList);
    }

    /**
     * 根据平台Id或者店铺Id 区域查询符合条件的队列
     */
    @Override
    public List<OrderMissedQueueEntity> getOrderQueueList(String platform, Integer storeId, String location, List<Integer> notInStoreIdList, Integer fetchCount) {
        List<Integer> platformIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(platform)) {
            platformIdList = PlatformTypeEnum.getPlatformIdList(platform);
        }
        return orderMissedQueueDao.getOrderQueueList(platformIdList, storeId, location, notInStoreIdList, fetchCount);
    }
}




