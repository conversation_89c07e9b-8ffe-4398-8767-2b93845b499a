package com.nsy.oms.business.domain.response.bd.brand;

import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 分公司skc品牌表
 *
 * @TableName bd_brand_location_skc
 */
public class BdBrandLocationSkc {

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    private Integer brandLocationSkcId;
    /**
     * 品牌id
     */
    @ApiModelProperty("品牌id")
    private Integer brandId;
    /**
     * 品牌名
     */
    @ApiModelProperty("品牌名")
    private String brandName;
    /**
     * spu
     */
    @ApiModelProperty("spu")
    private String spu;
    /**
     * skc
     */
    @ApiModelProperty("skc")
    private String skc;
    /**
     * 商品分类id
     */
    @ApiModelProperty("商品分类id")
    private Integer categoryId;
    /**
     * 商品分类名
     */
    @ApiModelProperty("商品分类名")
    private String categoryName;
    /**
     * 是否开启采购挂牌：1是0否
     */
    @ApiModelProperty("是否开启采购挂牌：1是0否")
    private Integer isOpenPurchaseBrand;
    /**
     * 是否job拉取的数据
     */
    @ApiModelProperty("是否job拉取的数据")
    private Integer isJobFetch;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createDate;
    /**
     * 创建者
     */
    @ApiModelProperty("创建者")
    private String createBy;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateDate;
    /**
     * 更新者
     */
    @ApiModelProperty("更新者")
    private String updateBy;
    /**
     * 地区
     */
    @ApiModelProperty("地区")
    private String location;
    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    private Integer version;

    public Integer getBrandLocationSkcId() {
        return brandLocationSkcId;
    }

    public void setBrandLocationSkcId(Integer brandLocationSkcId) {
        this.brandLocationSkcId = brandLocationSkcId;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getSpu() {
        return spu;
    }

    public void setSpu(String spu) {
        this.spu = spu;
    }

    public String getSkc() {
        return skc;
    }

    public void setSkc(String skc) {
        this.skc = skc;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public Integer getIsOpenPurchaseBrand() {
        return isOpenPurchaseBrand;
    }

    public void setIsOpenPurchaseBrand(Integer isOpenPurchaseBrand) {
        this.isOpenPurchaseBrand = isOpenPurchaseBrand;
    }

    public Integer getIsJobFetch() {
        return isJobFetch;
    }

    public void setIsJobFetch(Integer isJobFetch) {
        this.isJobFetch = isJobFetch;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }
}
