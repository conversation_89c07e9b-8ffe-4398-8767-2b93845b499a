package com.nsy.oms.business.service.amazon;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.oms.dto.base.BaseListRequestOrResponse;
import com.nsy.api.oms.dto.request.amazon.CalculateGrossProfitRateRequest;
import com.nsy.api.oms.dto.request.amazon.CalculateVariantProfitRequest;
import com.nsy.api.oms.dto.response.bd.CalculateGrossProfitRateResponse;
import com.nsy.api.oms.dto.response.bd.IncreaseProfitResponse;
import com.nsy.api.oms.dto.response.bd.VariantProfitResponse;
import com.nsy.oms.enums.AmazonPackageStandardEnum;
import com.nsy.oms.enumstable.FbaCostTypeEnum;
import com.nsy.oms.repository.dao.bd.BdFbaCostConfigDao;
import com.nsy.oms.repository.entity.bd.BdFbaCostConfigEntity;
import com.nsy.oms.utils.BigDecimalUtil;
import com.nsy.oms.utils.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-03-03
 */
@Service
public class AmazonProfitCalculateService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AmazonProfitCalculateService.class);
    private static final BigDecimal DECIMAL_3 = new BigDecimal("3");
    private static final BigDecimal DECIMAL_4536 = new BigDecimal("453.6");
    @Autowired
    private BdFbaCostConfigDao bdFbaCostConfigDao;

    public BaseListRequestOrResponse<CalculateGrossProfitRateResponse> calculateGrossProfitRateByRequest(BaseListRequestOrResponse<CalculateGrossProfitRateRequest> request) {
        LOGGER.info("计算净毛利润率请求={}", JsonMapper.toJson(request));
        List<CalculateGrossProfitRateResponse> responseList = Lists.newArrayList();
        for (CalculateGrossProfitRateRequest calculateRequest : request.getList()) {
            CalculateGrossProfitRateResponse calculateResponse = calculateGrossProfitRate(calculateRequest);
            responseList.add(calculateResponse);
        }
        LOGGER.info("计算净毛利润率结果={}", JsonMapper.toJson(responseList));
        return BaseListRequestOrResponse.of(responseList);
    }

    public CalculateGrossProfitRateResponse calculateGrossProfitRate(CalculateGrossProfitRateRequest calculateRequest) {
        return calculateGrossProfitRate(calculateRequest, calculateRequest.getReducedLogisticsCost(), getCommissionRate(calculateRequest.getPriceAfterDiscount()));
    }

    public CalculateGrossProfitRateResponse calculateGrossProfitRate(CalculateGrossProfitRateRequest calculateRequest, BigDecimal reducedLogisticsCost, BigDecimal commissionRate) {
        CalculateGrossProfitRateResponse response = new CalculateGrossProfitRateResponse();
        // 计费重量(磅)
        BigDecimal chargingWeight = calculateChargingWeight(calculateRequest.getStandard(), calculateVolumeWeight(calculateRequest), calculateRequest.getWeight());
        response.setType(calculateRequest.getType());
        response.setChargingWeight(chargingWeight);
        response.setFbaDeliveryCost(calculateFbaDeliveryCost(calculateRequest.getStandard(), chargingWeight, reducedLogisticsCost, calculateRequest.getReturnRate()));
        response.setFbaReturnCost(calculateFbaReturnCost(calculateRequest.getStandard(), chargingWeight, calculateRequest.getReturnRate()));
        response.setFbaFirstPostageCost(calculateFbaFirstPostageCost(calculateRequest.getWeight()));
        response.setStorageCost(calculateStorageCost(calculateRequest.getLength(), calculateRequest.getWidth(), calculateRequest.getHeight()));
        response.setDisposalCost(calculateDisposalCost(calculateRequest.getProductCost(), calculateRequest.getReturnRate(), calculateRequest.getReturnNotForSaleRate()));
        response.setHandlingCost(calculateHandlingCost(calculateRequest.getPriceAfterDiscount(), commissionRate));
        response.setAdvertisingCost(calculateAdvertisingCost(calculateRequest.getPriceAfterDiscount(), calculateRequest.getAdvertisingRate()));
        response.setCouponCost(calculateCouponCost(calculateRequest));
        response.setReturnPlatformDeductsCost(calculateReturnPlatformDeductsCost(calculateRequest.getPriceAfterDiscount(), calculateRequest.getReturnRate(), commissionRate));
        response.setGrossProfitRate(calculateGrossProfitRate(calculateRequest, response));
        return response;
    }

    /**
     * 净毛利润率 = 1 - (商品成本 + FBA配送费 + FBA退货处理费 + FBA头程邮费 + 仓租费 + 废弃成本 + 平台拥金率及兑换手续费 + 广告费 + 测评费用+秒杀COUPON等费用 + 退货平台扣除佣金) / 折后价
     */
    private BigDecimal calculateGrossProfitRate(CalculateGrossProfitRateRequest request, CalculateGrossProfitRateResponse response) {
        BigDecimal sum = request.getProductCost()
                .add(response.getFbaDeliveryCost())
                .add(response.getFbaReturnCost())
                .add(response.getFbaFirstPostageCost())
                .add(response.getStorageCost())
                .add(response.getDisposalCost())
                .add(response.getHandlingCost())
                .add(response.getAdvertisingCost())
                .add(response.getCouponCost())
                .add(response.getReturnPlatformDeductsCost());
        // 返回的利润率用于展示百分比（带四位小数），所以这边要留6位
        return BigDecimal.ONE.subtract(sum.divide(request.getPriceAfterDiscount(), 6, RoundingMode.HALF_UP));
    }

    /**
     * 退货平台扣除佣金 = 折后价 * 退货率 * 佣金率 * 20%
     */
    private BigDecimal calculateReturnPlatformDeductsCost(BigDecimal priceAfterDiscount, BigDecimal returnRate, BigDecimal commissionRate) {
        return priceAfterDiscount
                .multiply(returnRate)
                .multiply(commissionRate)
                .multiply(new BigDecimal("0.2"))
                .setScale(4, RoundingMode.HALF_UP);
    }

    /**
     * 测评费用+秒杀COUPON等费用 = 折后价 * 测评费用及优惠费率（固定2%）
     */
    private BigDecimal calculateCouponCost(CalculateGrossProfitRateRequest calculateRequest) {
        return calculateRequest.getPriceAfterDiscount()
                .multiply(Optional.ofNullable(calculateRequest.getCouponRate()).orElse(new BigDecimal("0.02")))
                .setScale(4, RoundingMode.HALF_UP);
    }

    /**
     * 广告费 = 折后价 * 广告费率
     */
    private BigDecimal calculateAdvertisingCost(BigDecimal priceAfterDiscount, BigDecimal advertisingRate) {
        return priceAfterDiscount
                .multiply(advertisingRate)
                .setScale(4, RoundingMode.HALF_UP);
    }

    /**
     * 平台拥金率及兑换手续费 = 折后价 * 佣金率（折后价匹配右侧配置取值）
     */
    private BigDecimal calculateHandlingCost(BigDecimal priceAfterDiscount, BigDecimal commissionRate) {
        return priceAfterDiscount
                .multiply(commissionRate)
                .setScale(4, RoundingMode.HALF_UP);
    }

    /**
     * 拥金率
     * 0<=x<15 -----5.00%
     * 15<=x<20 -----10.00%
     * 20<=x -----17.00%
     */
    private BigDecimal getCommissionRate(BigDecimal priceAfterDiscount) {
        BigDecimal commissionRate;
        if (priceAfterDiscount.compareTo(BigDecimal.ZERO) >= 0 && priceAfterDiscount.compareTo(new BigDecimal("15")) < 0) {
            commissionRate = new BigDecimal("0.05");
        } else if (priceAfterDiscount.compareTo(new BigDecimal("15")) >= 0 && priceAfterDiscount.compareTo(new BigDecimal("20")) < 0) {
            commissionRate = new BigDecimal("0.1");
        } else if (priceAfterDiscount.compareTo(new BigDecimal("20")) >= 0) {
            commissionRate = new BigDecimal("0.17");
        } else {
            throw new BusinessServiceException("计算佣金率失败");
        }
        return commissionRate;
    }

    /**
     * 废弃成本 = 商品成本*退货率*品类退货不可售比例*2.5
     * <p>
     * 说明：2.5固定值。40%被动销毁 60%主动销毁，【商品成本*退货率*品类退货不可售比例】计算的是被动销毁成本，则总成本=被动销毁成本*2.5
     */
    private BigDecimal calculateDisposalCost(BigDecimal productCost, BigDecimal returnRate, BigDecimal returnNotForSaleRate) {
        return productCost
                .multiply(returnRate)
                .multiply(returnNotForSaleRate)
                .multiply(new BigDecimal("2.5"))
                .setScale(4, RoundingMode.HALF_UP);
    }

    /**
     * 仓租费 = 0.78*(实际长*实际宽*实际高)*0.0000353*4
     * <p>
     * 说明：4是指4个月的周转
     * 0.78*0.0000353是单位换算
     */
    private BigDecimal calculateStorageCost(BigDecimal length, BigDecimal width, BigDecimal height) {
        return new BigDecimal("0.78")
                .multiply(length.multiply(width).multiply(height))
                .multiply(new BigDecimal("0.0000353"))
                .multiply(new BigDecimal("4"))
                .setScale(4, RoundingMode.HALF_UP);
    }

    /**
     * FBA头程邮费 = 实际重量*0.004
     * <p>
     * 说明：0.004是预估头程费用每kg 4美元，后面有可能会改
     */
    private BigDecimal calculateFbaFirstPostageCost(BigDecimal weight) {
        return weight
                .multiply(new BigDecimal("0.004"))
                .setScale(4, RoundingMode.HALF_UP);
    }

    /**
     * FBA退货处理费 = 根据包裹类型+计费重量，查找表格的退货处理费，=(金额+额外收费*系数)*退货率
     * <p>
     * 系数取值：
     * 1 - 标准小件/UsSmallStandardSize，系数=0
     * 2 - 标准大件/UsLargeStandardSize 并且 计费重量(磅)>3时，系数=CEILING((计费重量(磅)-3)/0.5,1)；否则(计费重量(磅)<=3)系数为0-- 超出3磅每半磅0.1
     * 3 - 小型超大件/LargeBulky 并且 计费重量(磅)>1时，系数=ROUNDUP(计费重量(磅)-1,0) ；否则(计费重量(磅)<=1)系数为0-- 超出首磅的部分每磅 $0.32
     */
    public BigDecimal calculateFbaReturnCost(String standard, BigDecimal chargingWeight, BigDecimal returnRate) {
        // 拿退货处理费配置
        List<BdFbaCostConfigEntity> costConfigEntities = bdFbaCostConfigDao.listByCostTypeAndStandard(FbaCostTypeEnum.FBA_RETURN_COST.getDesc(), standard);
        BdFbaCostConfigEntity config = costConfigEntities.stream().filter(e -> {
            // 规则：0 <= x < 0.25，以此类推
            // 如果配置的max为空，则不限制最大
            return chargingWeight.compareTo(e.getWeightMin()) >= 0 && (e.getWeightMax() == null || chargingWeight.compareTo(e.getWeightMax()) < 0);
        }).findAny().orElseThrow(() -> new BusinessServiceException("计算FBA退货处理费失败"));
        // (金额+额外收费*系数)*退货率
        BigDecimal extraCost = config.getExtraCost().multiply(calculateCoefficient(standard, chargingWeight));
        BigDecimal cost = config.getCost().add(extraCost);
        return cost
                .multiply(returnRate)
                .setScale(4, RoundingMode.HALF_UP);
    }

    /**
     * FBA配送费 = 根据包裹类型+计费重量，查找表格的配送费，=(金额+额外收费*系数-低价减免物流费)/(1-退货率)
     * <p>
     * 系数取值：
     * 1 - 标准小件/UsSmallStandardSize，系数=0
     * 2 - 标准大件/UsLargeStandardSize 并且 计费重量(磅)>3时，系数=CEILING((计费重量(磅)-3)/0.5,1)；否则(计费重量(磅)<=3)系数为0 -- 超出3磅每半磅0.16
     * 3 - 小型超大件/LargeBulky 并且 计费重量(磅)>1时，系数=ROUNDUP(计费重量(磅)-1,0) ；否则(计费重量(磅)<=1)系数为0-- 超出首磅每磅0.38
     * <p>
     */
    public BigDecimal calculateFbaDeliveryCost(String standard, BigDecimal chargingWeight, BigDecimal reducedLogisticsCost, BigDecimal returnRate, boolean divide) {
        // 拿配送费配置
        List<BdFbaCostConfigEntity> costConfigEntities = bdFbaCostConfigDao.listByCostTypeAndStandard(FbaCostTypeEnum.FBA_DELIVERY_COST.getDesc(), standard);
        BdFbaCostConfigEntity config = costConfigEntities.stream().filter(e -> {
            // 规则：0 <= x < 0.25，以此类推
            // 如果配置的max为空，则不限制最大
            return chargingWeight.compareTo(e.getWeightMin()) >= 0 && (e.getWeightMax() == null || chargingWeight.compareTo(e.getWeightMax()) < 0);
        }).findAny().orElseThrow(() -> new BusinessServiceException("计算FBA配送费失败"));
        // (金额+额外收费*系数-低价减免物流费)/(1-退货率)
        BigDecimal extra = config.getExtraCost().multiply(calculateCoefficient(standard, chargingWeight));
        return divide ? defaultDivide(config.getCost().add(extra).subtract(reducedLogisticsCost), BigDecimal.ONE.subtract(returnRate)) : config.getCost().add(extra).subtract(reducedLogisticsCost);
    }

    public BigDecimal calculateFbaDeliveryCost(String standard, BigDecimal chargingWeight, BigDecimal reducedLogisticsCost, BigDecimal returnRate) {
        return calculateFbaDeliveryCost(standard, chargingWeight, reducedLogisticsCost, returnRate, true);
    }

    /**
     * 计算系数
     * <p>
     * 1 - 标准小件/UsSmallStandardSize，系数=0
     * 2 - 标准大件/UsLargeStandardSize 并且 计费重量(磅)>3时，系数=CEILING((计费重量(磅)-3)/0.5,1)；否则(计费重量(磅)<=3)系数为0
     * 3 - 小型超大件/LargeBulky 并且 计费重量(磅)>1时，系数=ROUNDUP(计费重量(磅)-1,0) ；否则(计费重量(磅)<=1)系数为0
     */
    private BigDecimal calculateCoefficient(String standard, BigDecimal chargingWeight) {
        BigDecimal coefficient;
        if (AmazonPackageStandardEnum.US_SMALL_STANDARD_SIZE.getName().equals(standard)) {
            coefficient = BigDecimal.ZERO;
        } else if (AmazonPackageStandardEnum.US_LARGE_STANDARD_SIZE.getName().equals(standard)) {
            if (chargingWeight.compareTo(DECIMAL_3) > 0) {
                BigDecimal ceiling = ceiling(defaultDivide(chargingWeight.subtract(DECIMAL_3), new BigDecimal("0.5")));
                coefficient = ceiling.subtract(DECIMAL_3);
            } else {
                coefficient = BigDecimal.ZERO;
            }
        } else if (AmazonPackageStandardEnum.LARGE_BULKY.getName().equals(standard)) {
            if (chargingWeight.compareTo(BigDecimal.ONE) > 0) {
                coefficient = chargingWeight.subtract(BigDecimal.ONE).setScale(0, RoundingMode.UP);
            } else {
                coefficient = BigDecimal.ZERO;
            }
        } else {
            throw new BusinessServiceException("计算系数失败");
        }
        return coefficient;
    }

    private BigDecimal ceiling(BigDecimal number) {
        // 将number和significance都除以significance，进行向上取整，然后再乘以significance
        return number.divide(BigDecimal.ONE, 0, RoundingMode.UP).multiply(BigDecimal.ONE);
    }

    /**
     * 计费重量(磅)
     * 当包裹类型=‘标准小件’，=实际重量/453.6
     * 当包裹类型<>‘标准小件’，=取max(体积重，实际重量）/453.6
     */
    private BigDecimal calculateChargingWeight(String standard, BigDecimal volumeWeight, BigDecimal weight) {
        if (AmazonPackageStandardEnum.US_SMALL_STANDARD_SIZE.getName().equalsIgnoreCase(standard)) {
            return defaultDivide(weight, DECIMAL_4536);
        }
        return defaultDivide(volumeWeight.compareTo(weight) > 0 ? volumeWeight : weight, DECIMAL_4536);
    }

    /**
     * 体积重 = ROUNDUP((实际长cm*实际宽cm*实际高cm)*0.0610238*453.59237/139,0)
     */
    private BigDecimal calculateVolumeWeight(CalculateGrossProfitRateRequest request) {
        BigDecimal dividend = request.getLength()
                .multiply(request.getWidth())
                .multiply(request.getHeight())
                .multiply(new BigDecimal("0.0610238"))
                .multiply(new BigDecimal("453.59237"));
        return dividend.divide(new BigDecimal("139"), 0, RoundingMode.UP);
    }

    /**
     * 计算过程默认都是留4位小数（四舍五入）
     */
    private BigDecimal defaultDivide(BigDecimal dividend, BigDecimal divisor) {
        return dividend.divide(divisor, 4, RoundingMode.HALF_UP);
    }

    public BaseListRequestOrResponse<IncreaseProfitResponse> calculateIncreaseProfit(CalculateGrossProfitRateRequest request) {
        BaseListRequestOrResponse<IncreaseProfitResponse> response = new BaseListRequestOrResponse<>();
        response.setList(calculateIncreaseProfit(request, calculateGrossProfitRate(request)));
        return response;
    }

    public List<IncreaseProfitResponse> calculateIncreaseProfit(CalculateGrossProfitRateRequest request, CalculateGrossProfitRateResponse actualGrossProfitRateResponse) {
        List<IncreaseProfitResponse> responseList = new ArrayList<>(4);
        for (int level = 1; level <= 4; level++) {
            responseList.add(calculateIncreaseProfitResponse(level, request, actualGrossProfitRateResponse.getGrossProfitRate()));
        }
        return responseList;
    }

    private IncreaseProfitResponse calculateIncreaseProfitResponse(Integer level, CalculateGrossProfitRateRequest actualRequest, BigDecimal actualGrossProfitRate) {
        IncreaseProfitResponse response = new IncreaseProfitResponse();
        response.setLevel(level);

        CalculateGrossProfitRateRequest assumptionRequest = new CalculateGrossProfitRateRequest();
        BeanUtil.copyProperties(actualRequest, assumptionRequest);
        switch (level) {
            case 1:
                // 折后价=(1-coupon)*折前价
                assumptionRequest.setPriceAfterDiscount(BigDecimal.ONE.subtract(actualRequest.getDiscountRate()).multiply(new BigDecimal("9.99")).setScale(2, RoundingMode.HALF_UP));

                response.setCommissionRate(new BigDecimal("0.05"));
                response.setRange("0~9.99");
                response.setHit(actualRequest.getPriceBeforeDiscount().compareTo(new BigDecimal("9.99")) <= 0);
                break;
            case 2:
                assumptionRequest.setPriceAfterDiscount(new BigDecimal("14.99"));

                response.setCommissionRate(new BigDecimal("0.05"));
                response.setRange("10~14.99");
                response.setHit(actualRequest.getPriceAfterDiscount().compareTo(BigDecimal.TEN) >= 0
                        && actualRequest.getPriceAfterDiscount().compareTo(new BigDecimal("14.99")) <= 0);
                break;
            case 3:
                assumptionRequest.setPriceAfterDiscount(new BigDecimal("19.99"));

                response.setCommissionRate(new BigDecimal("0.1"));
                response.setRange("15~19.99");
                response.setHit(actualRequest.getPriceAfterDiscount().compareTo(new BigDecimal("15")) >= 0
                        && actualRequest.getPriceAfterDiscount().compareTo(new BigDecimal("19.99")) <= 0);
                break;
            case 4:
                assumptionRequest.setPriceAfterDiscount(new BigDecimal("20"));

                response.setCommissionRate(new BigDecimal("0.17"));
                response.setRange(">=20");
                response.setHit(actualRequest.getPriceAfterDiscount().compareTo(new BigDecimal("20")) >= 0);
                break;
            default:
                break;
        }
        response.setDiscountPrice(assumptionRequest.getPriceAfterDiscount());

        BigDecimal reducedLogisticsCost = level != 1 ? BigDecimal.ZERO : new BigDecimal("0.77");
        CalculateGrossProfitRateResponse assumptionResponse = calculateGrossProfitRate(assumptionRequest, reducedLogisticsCost, response.getCommissionRate());
        response.setProfitRate(assumptionResponse.getGrossProfitRate());
        // 档位的利润率大于等于0的时候才需要算盈亏平衡价和利润增加额
        if (assumptionResponse.getGrossProfitRate().compareTo(BigDecimal.ZERO) >= 0) {
            // 盈亏平衡价=0利润率反算这一档的折后价
            response.setBalancePrice(calculateBalancePrice(actualRequest, reducedLogisticsCost, response.getCommissionRate(), level));
            // 假设利润额=折后价*利润率
            BigDecimal assumptionProfit = assumptionRequest.getPriceAfterDiscount().multiply(assumptionResponse.getGrossProfitRate());
            // 实际利润额=商品折后价*预估净毛利率
            BigDecimal actualProfit = actualRequest.getPriceAfterDiscount().multiply(actualGrossProfitRate);
            // 利润增加额=假设利润额(折后价*利润率)-实际利润额(商品折后价*预估净毛利率)
            response.setIncreaseProfit(assumptionProfit.subtract(actualProfit).setScale(2, RoundingMode.HALF_UP));
        }
        return response;
    }

    public BigDecimal calculateBalancePrice(CalculateGrossProfitRateRequest request, BigDecimal reducedLogisticsCost, BigDecimal commissionRate, Integer level) {
        // 计费重量(磅)
        BigDecimal chargingWeight = calculateChargingWeight(request.getStandard(), calculateVolumeWeight(request), request.getWeight());
        // FBA配送费/(1-退货率)
        BigDecimal factor2 = calculateFbaDeliveryCost(request.getStandard(), chargingWeight, reducedLogisticsCost, request.getReturnRate());
        // 被除数：商品成本+FBA配送费/(1-退货率)+FBA退货处理费+头程单价*实际重量(kg)+ 0.78 × (长 × 宽 × 高cm) × 0.0000353 × 4 +废弃成本
        BigDecimal dividend = request.getProductCost()
                .add(factor2)
                .add(calculateFbaReturnCost(request.getStandard(), chargingWeight, request.getReturnRate()))
                .add(calculateFbaFirstPostageCost(request.getWeight()))
                .add(calculateStorageCost(request.getLength(), request.getWidth(), request.getHeight()))
                .add(calculateDisposalCost(request.getProductCost(), request.getReturnRate(), request.getReturnNotForSaleRate()));
        // 平台佣金率+广告费率+测评+秒杀COUPON等费率
        BigDecimal factor4 = commissionRate
                .add(request.getAdvertisingRate())
                .add(new BigDecimal("0.02"));
        // 佣金率*退货率*20%
        BigDecimal factor5 = commissionRate
                .multiply(request.getReturnRate())
                .multiply(new BigDecimal("0.2"));
        // 平台佣金率+广告费率+测评+秒杀COUPON等费率-佣金率*退货率*20%
        BigDecimal factor6 = factor4.add(factor5);
        // 除数：1-(平台佣金率+广告费率+测评+秒杀COUPON等费率+佣金率*退货率*20%)
        BigDecimal divisor = BigDecimal.ONE.subtract(factor6);
        // 计算折后售价：商品成本+FBA配送费/(1-退货率)+FBA退货处理费+头程单价*实际重量(kg)+ 0.78 × (长 × 宽 × 高cm) × 0.0000353 × 4 +废弃成本】/【1-利润率-(平台佣金率+广告费率+测评+秒杀COUPON等费率+佣金率*退货率*20%)】
        BigDecimal result = dividend.divide(divisor, 2, RoundingMode.HALF_UP);
        // 盈亏平衡价要在对应的档次区间
        if (level == 1 && result.compareTo(new BigDecimal("9.99")) <= 0
                || level == 2 && result.compareTo(BigDecimal.TEN) >= 0 && result.compareTo(new BigDecimal("14.99")) <= 0
                || level == 3 && result.compareTo(new BigDecimal("15")) >= 0 && result.compareTo(new BigDecimal("19.99")) <= 0
                || level == 4 && result.compareTo(new BigDecimal("20")) >= 0) {
            return result.setScale(2, RoundingMode.HALF_UP);
        }
        return null;
    }

    public BaseListRequestOrResponse<VariantProfitResponse> calculateVariantProfit(BaseListRequestOrResponse<CalculateVariantProfitRequest> request) {
        List<VariantProfitResponse> variantProfitResponseList = new ArrayList<>(request.getList().size());
        request.getList().forEach(req -> {


            VariantProfitResponse variantProfitResponse = new VariantProfitResponse();
            variantProfitResponse.setVariant(req.getVariant());
            variantProfitResponse.setPrice(req.getPriceBeforeDiscount());
            variantProfitResponse.setCoupon(req.getDiscountRate());
            variantProfitResponse.setDiscountPrice(req.getPriceAfterDiscount());
            CalculateGrossProfitRateResponse grossProfitRateResponse = calculateGrossProfitRate(req);
            variantProfitResponse.setProfitRate(grossProfitRateResponse.getGrossProfitRate());
            // 降档利润增加额：取最高的那一档
            // 变体列表的降档利润增加额需排除同档和更高档的增加额，另外负利润也不用展示
            List<IncreaseProfitResponse> increaseProfitResponseList = calculateIncreaseProfit(req, grossProfitRateResponse);
            List<BigDecimal> downshiftIncreaseProfitList = new ArrayList<>(4);
            for (IncreaseProfitResponse response : increaseProfitResponseList) {
                if (response.isHit()) {
                    break;
                }
                if (BigDecimalUtil.isValid(response.getIncreaseProfit())) {
                    downshiftIncreaseProfitList.add(response.getIncreaseProfit());
                }
            }
            variantProfitResponse.setDownshiftIncreaseProfit(downshiftIncreaseProfitList.stream().max(BigDecimal::compareTo).orElse(null));
            variantProfitResponseList.add(variantProfitResponse);
        });
        BaseListRequestOrResponse<VariantProfitResponse> response = new BaseListRequestOrResponse<>();
        response.setList(variantProfitResponseList);
        return response;
    }
}
