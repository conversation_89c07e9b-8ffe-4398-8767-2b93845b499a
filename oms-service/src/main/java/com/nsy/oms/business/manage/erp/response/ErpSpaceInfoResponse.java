package com.nsy.oms.business.manage.erp.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * ERP仓库
 * <AUTHOR>
 * @date 2023-09-22
 */
public class ErpSpaceInfoResponse {
    @JsonProperty("SpaceInfoList")
    private List<ErpSpaceInfo> spaceInfoList;

    public List<ErpSpaceInfo> getSpaceInfoList() {
        return spaceInfoList;
    }

    public void setSpaceInfoList(List<ErpSpaceInfo> spaceInfoList) {
        this.spaceInfoList = spaceInfoList;
    }

    public static class ErpSpaceInfo {
        @JsonProperty("SpaceId")
        private Integer spaceId;
        @JsonProperty("SpaceName")
        private String spaceName;
        @JsonProperty("BrandType")
        private String brandType;
        @JsonProperty("SpaceFeatures")
        private String spaceFeatures;
        @JsonProperty("IsSendFba")
        private Integer isSendFba;
        @JsonProperty("IsCrossWarehouse")
        private Integer isCrossWarehouse;
        @JsonProperty("DeliveryLocation")
        private String deliveryLocation;

        public String getSpaceFeatures() {
            return spaceFeatures;
        }

        public void setSpaceFeatures(String spaceFeatures) {
            this.spaceFeatures = spaceFeatures;
        }

        public Integer getSpaceId() {
            return spaceId;
        }

        public void setSpaceId(Integer spaceId) {
            this.spaceId = spaceId;
        }

        public String getSpaceName() {
            return spaceName;
        }

        public void setSpaceName(String spaceName) {
            this.spaceName = spaceName;
        }

        public String getBrandType() {
            return brandType;
        }

        public void setBrandType(String brandType) {
            this.brandType = brandType;
        }

        public Integer getIsSendFba() {
            return isSendFba;
        }

        public void setIsSendFba(Integer isSendFba) {
            this.isSendFba = isSendFba;
        }

        public Integer getIsCrossWarehouse() {
            return isCrossWarehouse;
        }

        public void setIsCrossWarehouse(Integer isCrossWarehouse) {
            this.isCrossWarehouse = isCrossWarehouse;
        }

        public String getDeliveryLocation() {
            return deliveryLocation;
        }

        public void setDeliveryLocation(String deliveryLocation) {
            this.deliveryLocation = deliveryLocation;
        }
    }
}
