package com.nsy.oms.business.manage.amazon;


import com.nsy.api.core.apicore.util.DateUtils;
import com.nsy.api.core.apicore.util.NsyDateUtil;
import com.nsy.api.core.apicore.util.NsyJacksonUtils;
import com.nsy.oms.business.domain.response.base.PageResponse;
import com.nsy.oms.business.manage.amazon.request.AmazonStoreProductSkuInfoRequest;
import com.nsy.oms.business.manage.amazon.request.AmazonStoreProductSkuSaveRequest;
import com.nsy.oms.business.manage.amazon.request.GetSkcSalespersonUserAccountRequest;
import com.nsy.oms.business.manage.amazon.request.GetStoreActiveSkuBrandsRequest;
import com.nsy.oms.business.manage.amazon.request.PlatformOrderPriceRequest;
import com.nsy.oms.business.manage.amazon.request.ReportGetFlatFileAllOrdersDataRequest;
import com.nsy.oms.business.manage.amazon.request.StaWaitingApplyShipmentPlanCreateRequest;
import com.nsy.oms.business.manage.amazon.request.TransparencyOpenOrpRequest;
import com.nsy.oms.business.manage.amazon.response.AmazonStoreProductSkuInfo;
import com.nsy.oms.business.manage.amazon.response.GetStoreProductBrandResponse;
import com.nsy.oms.business.manage.amazon.response.MarketplaceModel;
import com.nsy.oms.business.manage.amazon.response.PlatformOrderPriceResponse;
import com.nsy.oms.business.manage.amazon.response.ReportGetFlatFileAllOrdersDataResponse;
import com.nsy.oms.business.manage.amazon.response.SellerSkuSaleManMappingDto;
import com.nsy.oms.business.manage.amazon.response.SkuPrefixDto;
import com.nsy.oms.business.manage.amazon.response.StaWaitingApplyShipmentPlanCreateResponse;
import com.nsy.oms.business.manage.amazon.response.StoreModel;
import com.nsy.oms.constants.StringConstant;
import com.nsy.oms.utils.JsonMapper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Service
public class AmazonApiService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AmazonApiService.class);
    private static final String STORE_URL_PREFIX = "https://www.amazon.com/dp/";
    @Autowired
    private RestTemplate restTemplate;
    @Value("${nsy.service.url.amazon}")
    private String amazonServiceUrl;

    /**
     * 获取marketplace信息
     */
    public List<MarketplaceModel> amazonMarketplaces() {
        String uri = String.format("%s/basic-data/marketplace", amazonServiceUrl);
        ResponseEntity<PageResponse> respEntity = this.restTemplate.getForEntity(uri, PageResponse.class);
        if (respEntity.getBody() != null) {
            return JsonMapper.jsonStringToObjectArray(JsonMapper.toJson(respEntity.getBody().getContent()), MarketplaceModel.class);
        }
        return Collections.emptyList();
    }

    /**
     * 获取亚马逊店铺
     */
    public List<StoreModel> amazonStores(List<String> storeNames) {
        String uri = String.format("%s/amazon-store/list-by-name", amazonServiceUrl);
        try {
            ResponseEntity<String> respEntity = this.restTemplate.postForEntity(uri, storeNames, String.class);
            return JsonMapper.jsonStringToObjectArray(respEntity.getBody(), StoreModel.class);
        } catch (Exception e) {
            LOGGER.error("根据店铺名称获取亚马逊店铺失败");
            LOGGER.error(e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    public StoreModel getAmazonStoreById(Integer amazonStoreId) {
        if (amazonStoreId == null) {
            return null;
        }
        String uri = String.format("%s/amazon-store/info/%s", amazonServiceUrl, amazonStoreId);
        try {
            ResponseEntity<StoreModel> respEntity = this.restTemplate.getForEntity(uri, StoreModel.class);
            return respEntity.getBody();
        } catch (Exception e) {
            LOGGER.error("根据id获取亚马逊店铺失败");
            LOGGER.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据sku获取亚马逊上架商品信息
     */
    public AmazonStoreProductSkuInfo getAmazonSkuInfo(AmazonStoreProductSkuInfoRequest request) {
        String uri = String.format("%s/amazon-product/store-product-sku-info", amazonServiceUrl);
        try {
            ResponseEntity<AmazonStoreProductSkuInfo> respEntity = this.restTemplate.postForEntity(uri, request, AmazonStoreProductSkuInfo.class);
            return respEntity.getBody();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return null;
    }


    public String transparencyOpenOrp(TransparencyOpenOrpRequest request) {
        String uri = String.format("%s/amazon-product/transparencyOpenOrp", amazonServiceUrl);
        try {
            ResponseEntity<String> respEntity = this.restTemplate.postForEntity(uri, request, String.class);
            return respEntity.getBody();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return null;
    }


    public String getAmazonStoreUrl(Integer storeId, String erpSku) {
        AmazonStoreProductSkuInfoRequest amazonStoreProductSkuInfoRequest = new AmazonStoreProductSkuInfoRequest();
        amazonStoreProductSkuInfoRequest.setStoreId(storeId);
        amazonStoreProductSkuInfoRequest.setErpSku(erpSku);
        return Optional.ofNullable(getAmazonSkuInfo(amazonStoreProductSkuInfoRequest))
                .filter(a -> StringUtils.isNotBlank(a.getAsin1()))
                .map(a -> String.format("%s%s", STORE_URL_PREFIX, a.getAsin1()))
                .orElse(StringConstant.EMPTY);
    }

    public String getOpenDate(Integer storeId, String erpSku) {
        if (storeId == null || StringUtils.isBlank(erpSku)) {
            return StringConstant.EMPTY;
        }
        AmazonStoreProductSkuInfoRequest request = new AmazonStoreProductSkuInfoRequest();
        request.setStoreId(storeId);
        request.setErpSku(erpSku);
        return Optional.ofNullable(getAmazonSkuInfo(request))
                .filter(a -> a.getOpenDate() != null)
                .map(a -> NsyDateUtil.format(a.getOpenDate(), DateUtils.DATE_FORMAT_DATE))
                .orElse(StringConstant.EMPTY);
    }

    public List<AmazonStoreProductSkuInfo> listBySellerSku(String sellerSku) {
        String uri = String.format("%s/amazon-product/list-by-seller-sku?sellerSku=%s", amazonServiceUrl, sellerSku);
        try {
            ResponseEntity<String> respEntity = this.restTemplate.getForEntity(uri, String.class);
            return JsonMapper.jsonStringToObjectArray(respEntity.getBody(), AmazonStoreProductSkuInfo.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    /**
     * 插入亚马逊店铺的sku映射关系
     */
    public void insertSkuMapping(AmazonStoreProductSkuSaveRequest request) {
        if (StringUtils.isBlank(request.getSellerSku())) {
            return;
        }
        LOGGER.info("插入amazon新sku映射:{}", JsonMapper.toJson(request));
        String uri = String.format("%s/amazon-product/save-by-manual", amazonServiceUrl);
        this.restTemplate.postForEntity(uri, request, String.class);
    }

    /**
     * 创建fba补货单
     */
    public void createWaitingApplyInboundShipmentPlan(StaWaitingApplyShipmentPlanCreateRequest request) {
        LOGGER.info("创建补货单:{}", JsonMapper.toJson(request));
        String uri = String.format("%s/sta/shipment/plan/waiting-apply/create", amazonServiceUrl);
        this.restTemplate.postForEntity(uri, request, StaWaitingApplyShipmentPlanCreateResponse.class);
    }


    public List<GetStoreProductBrandResponse> getStoreActiveSkuBrands(List<Integer> storeIds) {
        GetStoreActiveSkuBrandsRequest request = new GetStoreActiveSkuBrandsRequest();
        request.setIds(storeIds);
        String uri = String.format("%s/amazon-report/finance/store-active-sku-brand", amazonServiceUrl);
        try {
            ResponseEntity<String> respEntity = this.restTemplate.postForEntity(uri, request, String.class);
            return JsonMapper.jsonStringToObjectArray(respEntity.getBody(), GetStoreProductBrandResponse.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据店铺id获取店铺sku前缀
     */
    public List<SkuPrefixDto> getSkuPrefixListByStoreIds(List<Integer> storeIds) {
        String uri = String.format("%s/sku-prefix/list-by-store-ids", amazonServiceUrl);
        try {
            ResponseEntity<String> respEntity = this.restTemplate.postForEntity(uri, storeIds, String.class);
            return JsonMapper.jsonStringToObjectArray(respEntity.getBody(), SkuPrefixDto.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public List<ReportGetFlatFileAllOrdersDataResponse> getReportGetFlatFileAllOrdersData(ReportGetFlatFileAllOrdersDataRequest request) {
        String uri = String.format("%s/currency-report/get-report-get-flat-file-all-orders-data", amazonServiceUrl);
        try {
            ResponseEntity<String> respEntity = this.restTemplate.postForEntity(uri, request, String.class);
            return JsonMapper.jsonStringToObjectArray(respEntity.getBody(), ReportGetFlatFileAllOrdersDataResponse.class);
        } catch (Exception e) {
            LOGGER.error("AmazonApiService getReportGetFlatFileAllOrdersData error: {}", e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public PlatformOrderPriceResponse getReportOrderPrice(PlatformOrderPriceRequest request) {
        String uri = String.format("%s/currency-report/get-report-order-price", amazonServiceUrl);
        try {
            ResponseEntity<String> respEntity = this.restTemplate.postForEntity(uri, request, String.class);
            if (StringUtils.isBlank(respEntity.getBody())) {
                return null;
            }
            return NsyJacksonUtils.toObj(respEntity.getBody(), PlatformOrderPriceResponse.class);
        } catch (Exception e) {
            LOGGER.error("AmazonApiService getReportOrderPrice error: {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 查询订单是否退款
     *
     * @param storeId
     * @param platformOrderNo
     * @return
     */
    public Boolean isReturnOrder(Integer storeId, String platformOrderNo) {
        String uri = String.format("%s/order-refund/is-return-order?storeId=%s&platformOrderNo=%s", amazonServiceUrl, storeId, platformOrderNo);
        try {
            ResponseEntity<String> respEntity = this.restTemplate.getForEntity(uri, String.class);
            if (StringUtils.isBlank(respEntity.getBody())) {
                return null;
            }
            return NsyJacksonUtils.toObj(respEntity.getBody(), Boolean.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 获取skc对应的业务员
     */
    public List<SellerSkuSaleManMappingDto> getSalespersonUserAccountList(GetSkcSalespersonUserAccountRequest request) {
        String uri = String.format("%s/skc-salesperson-user-account-list", amazonServiceUrl);
        try {
            ResponseEntity<String> respEntity = this.restTemplate.postForEntity(uri, request, String.class);
            if (StringUtils.isBlank(respEntity.getBody())) {
                return null;
            }
            return NsyJacksonUtils.jsonToList(respEntity.getBody(), SellerSkuSaleManMappingDto.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return Collections.emptyList();
        }
    }
}
