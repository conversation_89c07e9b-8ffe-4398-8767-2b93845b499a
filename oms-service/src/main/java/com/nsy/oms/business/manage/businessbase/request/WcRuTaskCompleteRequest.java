package com.nsy.oms.business.manage.businessbase.request;

import io.swagger.annotations.ApiModel;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @Author: lhh
 * @Date: 2024-03-21
 * @Description:
 */
@ApiModel("完成待办req")
public class WcRuTaskCompleteRequest {
    @NotEmpty(message = "任务信息不能为空")
    List<WcRuTaskBaseInfo> taskCompleteList;

    public List<WcRuTaskBaseInfo> getTaskCompleteList() {
        return taskCompleteList;
    }

    public void setTaskCompleteList(List<WcRuTaskBaseInfo> taskCompleteList) {
        this.taskCompleteList = taskCompleteList;
    }
}
