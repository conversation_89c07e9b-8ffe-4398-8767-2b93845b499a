package com.nsy.oms.business.domain.request.celebrity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @Author: lhh
 * @Date: 2025-04-18
 * @Description: 网红查询请求类
 */
@NoArgsConstructor
@Data
public class InternetCelebritySyncAdRequest {

    @NotEmpty
    @JsonProperty("data")
    private List<DataDTO> data;

    @NoArgsConstructor
    @Data
    public static class DataDTO {
        @JsonProperty("stat_data")
        private StatDataDTO statData;
        @JsonProperty("row_data")
        private RowDataDTO rowData;
        @JsonProperty("analysis_date")
        private String analysisDate;
        @JsonProperty("analysis_date_end")
        private String analysisDateEnd;
        @JsonProperty("store_id")
        private String storeId;
        @JsonProperty("store_name")
        private String storeName;
        @JsonProperty("aweme_item_id")
        private String videoId;

        @NoArgsConstructor
        @Data
        public static class StatDataDTO {
            @JsonProperty("ad_id")
            private String adId;
            @JsonProperty("creative_id")
            private String creativeId;
        }

        @NoArgsConstructor
        @Data
        public static class RowDataDTO {
            @JsonProperty("stat_cost")
            private String statCost;
            @JsonProperty("time_attr_total_onsite_shopping_value")
            private String timeAttrTotalOnsiteShoppingValue;
        }
    }
}
