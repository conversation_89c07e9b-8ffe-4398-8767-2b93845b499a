package com.nsy.oms.business.service.inbound;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.privilege.LoginInfoService;
import com.nsy.oms.business.domain.request.inbound.UploadShipmentRequest;
import com.nsy.oms.business.manage.thirdparty.ThirdPartyApiService;
import com.nsy.oms.business.manage.thirdparty.auth.TiktokAuth;
import com.nsy.oms.business.manage.thirdparty.response.InboundOrder;
import com.nsy.oms.business.manage.thirdparty.response.InboundOrderDate;
import com.nsy.oms.business.manage.thirdparty.response.InboundOrdersResponse;
import com.nsy.oms.business.manage.thirdparty.response.PlannedGood;
import com.nsy.oms.business.manage.wms.WmsApiService;
import com.nsy.oms.business.manage.wms.request.StaBoxInfoDto;
import com.nsy.oms.business.manage.wms.response.StaBoxInfoDetailResponse;
import com.nsy.oms.business.manage.wms.response.StaBoxItemInfoDto;
import com.nsy.oms.business.service.auth.SauPlatformAuthConfigService;
import com.nsy.oms.enums.inbound.InboundPlanStatusEnum;
import com.nsy.oms.enums.inbound.ShipmentStatusEnum;
import com.nsy.oms.mq.KafkaConstant;
import com.nsy.oms.mq.message.OmsShipmentBoxRelationDto;
import com.nsy.oms.mq.message.OmsShipmentBoxRelationMessage;
import com.nsy.oms.mq.producer.MessageProducer;
import com.nsy.oms.repository.dao.sa.SaStoreDao;
import com.nsy.oms.repository.entity.inbound.InboundPlanEntity;
import com.nsy.oms.repository.entity.inbound.InboundPlanItemEntity;
import com.nsy.oms.repository.entity.inbound.InboundShipmentEntity;
import com.nsy.oms.repository.entity.inbound.InboundShipmentItemEntity;
import com.nsy.oms.utils.HttpResponseUtils;
import com.nsy.oms.utils.pdf.PdfCodeExtractor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;


@Service
@Slf4j
public class InboundIOService {
    @Autowired
    private InboundPlanService inboundPlanService;
    @Autowired
    private InboundPlanItemService inboundPlanItemService;
    @Resource
    private WmsApiService wmsApiService;
    @Resource
    private InboundShipmentService shipmentEntityService;
    @Resource
    private InboundShipmentItemService shipmentItemEntityService;
    @Resource
    private InboundPlanLogService logEntityService;
    @Resource
    private LoginInfoService loginInfoService;
    @Resource
    private ThirdPartyApiService thirdPartyApiService;
    @Resource
    private MessageProducer<String> messageProducer;
    @Resource
    private SauPlatformAuthConfigService sauPlatformAuthConfigService;
    @Resource
    private SaStoreDao saStoreDao;

    public void writePackageInfoResponse(Integer planId, HttpServletResponse response) throws Exception {
        InboundPlanEntity inboundPlanEntity = inboundPlanService.getById(planId);
        String templateName = "/excel/tk_package_info_us.xlsx";
        InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(templateName);
        if (inputStream == null) {
            throw new BusinessServiceException("找不到模板文件: " + templateName);
        }
        XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
        this.fillPackageDocumentInfo(inboundPlanEntity, workbook);
        HttpResponseUtils.write(String.format("%s.xlsx", planId), response, workbook::write);
    }

    private XSSFWorkbook fillPackageDocumentInfo(InboundPlanEntity planEntity, XSSFWorkbook workbook) {
        StaBoxInfoDetailResponse staBoxInfoDetailByErpTid = wmsApiService.getStaBoxInfoDetailByErpTid(planEntity.getErpTid());
        List<StaBoxInfoDto> boxInfoEntities = staBoxInfoDetailByErpTid.getStaBoxInfoDtoList();
        //单位转换
        this.unitConversion(boxInfoEntities);
        List<InboundPlanItemEntity> planItemEntityList = inboundPlanItemService.getByPlanId(planEntity.getId());
        // 获取所有sellerSku的实际发货数量
        Map<String, Integer> skuActualQuantityMap = boxInfoEntities.stream()
                .flatMap(box -> box.getStaBoxItemInfoDtoList().stream())
                .collect(Collectors.groupingBy(
                        StaBoxItemInfoDto::getSellerSku,
                        Collectors.summingInt(StaBoxItemInfoDto::getQuantity)
                ));

        //获取该单号的所有的packageInfo
        if (boxInfoEntities.isEmpty()) {
            return workbook;
        }
        XSSFSheet xssfSheet = workbook.getSheet("Sheet1");
        //设置  Total SKUs字段
        XSSFCell totalSkusCell = xssfSheet.getRow(2).getCell(0);
        String totalSkusFormat = "Total Goods: {}, ({} units)";
        long totalSkus = skuActualQuantityMap.size();
        //将skuActualQuantityMap所有的value加起来
        long totalUnits = skuActualQuantityMap.values().stream().mapToInt(Integer::intValue).sum();
//        checkQuantity(boxInfoEntities, planItemEntityList);
        totalSkusCell.setCellValue(StrUtil.format(totalSkusFormat, totalSkus, totalUnits));
        XSSFCell totalBoxCountCell = xssfSheet.getRow(2).getCell(10);
        //设置箱子数量
        int totalBoxCount = boxInfoEntities.size();
        totalBoxCountCell.setCellValue(totalBoxCount);
        int boxTitleStartIndex = 10;
        //创建出标题后面的box
        XSSFRow skuTitleRow = xssfSheet.getRow(4);
        for (int i = 0; i < totalBoxCount; i++) {
            XSSFCell cell = skuTitleRow.createCell(boxTitleStartIndex + i);
            cell.setCellStyle(skuTitleRow.getRowStyle());
            //box名称,新版这边是P1 - B1
            cell.setCellValue(StrUtil.format("C{}", StrUtil.padPre(String.valueOf(i + 1), 4, '0')));
        }
        //将箱子index从小到大排序
        boxInfoEntities.sort(Comparator.comparing(StaBoxInfoDto::getBoxIndex));
        //先填写具体的box weight,length那些数据
        populateBoxInfo(boxInfoEntities, xssfSheet, boxTitleStartIndex);
        //从第6（在excel中指第7行）行到第11（在excel中指第12行）行全部向下移skuBoxesMap.size()行
        xssfSheet.shiftRows(5, 11, (int) totalSkus);
        //生成skulIST
//        planItemEntityList 修改数据
        List<InboundPlanItemEntity> filterNoQuantitySku = planItemEntityList.stream()
                .peek(entity -> entity.setActualShipmentQuantity(skuActualQuantityMap.getOrDefault(entity.getSellerSku(), 0)))
                .filter(entity -> entity.getActualShipmentQuantity() != 0).collect(Collectors.toList());
        populateSkuList(filterNoQuantitySku, xssfSheet);
        //生成skuQuantity
        populateSkuBoxQuantity(boxInfoEntities, totalSkus, xssfSheet);
        return workbook;
    }

    private void unitConversion(List<StaBoxInfoDto> packageInfos) {
        packageInfos.forEach(item -> {
            item.setBoxWeight(Optional.ofNullable(item.getBoxWeight()).map(weight -> weight.multiply(new BigDecimal("2.2"))).orElse(null));
            item.setBoxLength(item.getBoxLength().divide(new BigDecimal("2.54"), 2, RoundingMode.HALF_UP));
            item.setBoxWidth(item.getBoxWidth().divide(new BigDecimal("2.54"), 2, RoundingMode.HALF_UP));
            item.setBoxHeight(item.getBoxHeight().divide(new BigDecimal("2.54"), 2, RoundingMode.HALF_UP));
        });
    }

//    private void checkQuantity(List<StaBoxInfoDto> boxInfoEntities, List<InboundPlanItemEntity> planItemEntityList) {
//        //将seller
//        int planTotalSkus = (int) planItemEntityList.stream().map(InboundPlanItemEntity::getSellerSku).count();
//        //计算和
//        int planTotalUnits = planItemEntityList.stream().mapToInt(InboundPlanItemEntity::getEstimatedShipmentQuantity).sum();
//        Set<String> boxItemSkus = new HashSet<>();
//        //计算sku所有的实际发货数量
//        long boxTotalQuantity = 0;
//        for (StaBoxInfoDto boxInfoEntity : boxInfoEntities) {
//            List<StaBoxItemInfoDto> staBoxItemInfoDtoList = boxInfoEntity.getStaBoxItemInfoDtoList();
//            for (StaBoxItemInfoDto staBoxItemInfoDto : staBoxItemInfoDtoList) {
//                boxItemSkus.add(staBoxItemInfoDto.getSellerSku());
//                boxTotalQuantity = boxTotalQuantity + staBoxItemInfoDto.getQuantity();
//            }
//        }
//        int boxSkuSize = boxItem
//        Skus.size();
//        if (planTotalSkus != boxSkuSize || planTotalUnits != boxTotalQuantity) {
//            throw new BusinessServiceException(StrUtil.format("发货计划单发货数量与装箱清单数量不同,请联系管理员,planItem[planTotalSkus:{},planTotalUnits:{}],box[boxTotalSkus:{},boxTotalUnits:{}]",
//                    boxSkuSize, planTotalUnits, boxSkuSize, boxTotalQuantity));
//        }
//    }


    private void populateBoxInfo(List<StaBoxInfoDto> boxInfoEntities, XSSFSheet xssfSheet, Integer boxInfoStartIndex) {
        // 设置Box weight
        XSSFRow boxWeightRow = xssfSheet.getRow(6);
        for (int i = 0; i < boxInfoEntities.size(); i++) {
            BigDecimal weight = boxInfoEntities.get(i).getBoxWeight();
            if (ObjectUtil.isEmpty(weight)) {
                weight = BigDecimal.ZERO;
            }
            boxWeightRow.createCell(boxInfoStartIndex + i).setCellValue(weight.setScale(2, RoundingMode.HALF_DOWN).doubleValue());
        }
        // 设置Box length
        XSSFRow boxLengthRow = xssfSheet.getRow(7);
        for (int i = 0; i < boxInfoEntities.size(); i++) {
            BigDecimal length = boxInfoEntities.get(i).getBoxLength();
            if (ObjectUtil.isEmpty(length)) {
                length = BigDecimal.ZERO;
            }
            boxLengthRow.createCell(boxInfoStartIndex + i).setCellValue(length.setScale(2, RoundingMode.HALF_DOWN).doubleValue());
        }
        // 设置Box width
        XSSFRow boxWidthRow = xssfSheet.getRow(8);
        for (int i = 0; i < boxInfoEntities.size(); i++) {
            BigDecimal width = boxInfoEntities.get(i).getBoxWidth();
            if (ObjectUtil.isEmpty(width)) {
                width = BigDecimal.ZERO;
            }
            boxWidthRow.createCell(boxInfoStartIndex + i).setCellValue(width.setScale(2, RoundingMode.HALF_DOWN).doubleValue());
        }
        // 设置Box height
        XSSFRow boxHeightRow = xssfSheet.getRow(9);
        for (int i = 0; i < boxInfoEntities.size(); i++) {
            BigDecimal height = boxInfoEntities.get(i).getBoxHeight();
            if (ObjectUtil.isEmpty(height)) {
                height = BigDecimal.ZERO;
            }
            boxHeightRow.createCell(boxInfoStartIndex + i).setCellValue(height.setScale(2, RoundingMode.HALF_DOWN).doubleValue());
        }
    }

    private void populateSkuList(List<InboundPlanItemEntity> planItemEntityList, XSSFSheet xssfSheet) {
        for (int i = 0; i < planItemEntityList.size(); i++) {
            XSSFRow xssfRow = xssfSheet.createRow(5 + i);
            xssfSheet.addMergedRegion(new CellRangeAddress(5 + i, 5 + i, 1, 5));
            InboundPlanItemEntity inboundPlanItemEntity = planItemEntityList.get(i);

            //Good Id
            xssfRow.createCell(0).setCellValue(inboundPlanItemEntity.getGoodsId());
            //GoodsName
            xssfRow.createCell(1).setCellValue(inboundPlanItemEntity.getGoodsName());
            //Goods code
            xssfRow.createCell(6).setCellValue(inboundPlanItemEntity.getSellerSku());
            //barCode
            xssfRow.createCell(7).setCellValue(inboundPlanItemEntity.getBarcode());
            //estimated_shipment_quantity
            xssfRow.createCell(8).setCellValue(inboundPlanItemEntity.getActualShipmentQuantity());
            //Quantity in carton 需校验一致
            xssfRow.createCell(9).setCellValue(inboundPlanItemEntity.getActualShipmentQuantity());
        }
    }

    private void populateSkuBoxQuantity(List<StaBoxInfoDto> boxInfoEntities, Long totalSkus, XSSFSheet xssfSheet) {
        //从第六行开始
        int startIndex = 5;
        for (int i = 0; i < boxInfoEntities.size(); i++) {
            //获取这个box下面的所有item
            List<StaBoxItemInfoDto> itemInfoMpEntities = boxInfoEntities.get(i).getStaBoxItemInfoDtoList();
            for (int j = 0; j < totalSkus; j++) {
                //获取sku名称
                String sellerSku = xssfSheet.getRow(startIndex + j).getCell(6).toString();
                List<StaBoxItemInfoDto> collect = itemInfoMpEntities.stream().filter(x -> x.getSellerSku().equals(sellerSku)).collect(Collectors.toList());
                XSSFCell cell = xssfSheet.getRow(startIndex + j).createCell(10 + i);
                CellStyle style = cell.getCellStyle();
                style.setAlignment(HorizontalAlignment.LEFT);
                if (CollUtil.isEmpty(collect)) {
                    cell.setCellValue("0");
                } else {
                    cell.setCellValue(collect.stream().mapToDouble(StaBoxItemInfoDto::getQuantity).sum());
                }
            }
        }
    }

    @Transactional
    public void uploadShipment(UploadShipmentRequest request) {
        Integer planId = request.getPlanId();
        InboundPlanEntity fbaInboundShipmentPlanEntity = inboundPlanService.getById(planId);
        if (ObjectUtil.isEmpty(fbaInboundShipmentPlanEntity) || !InboundPlanStatusEnum.BOXED.status().equals(fbaInboundShipmentPlanEntity.getStatus())) {
            throw new BusinessServiceException("该plan单不是已装箱");
        }
        Map<String, String> shipmentIdUrlMap = new HashMap<>();
        Map<String, List<Integer>> shipmentIdBoxIdxMap = new HashMap<>();
        List<InboundPlanItemEntity> planItemEntities = inboundPlanItemService.getByPlanId(fbaInboundShipmentPlanEntity.getId());
        List<StaBoxInfoDto> planAllBoxList = wmsApiService.getStaBoxInfoDetailByErpTid(fbaInboundShipmentPlanEntity.getErpTid()).getStaBoxInfoDtoList();
        //解析上传文件
        analyseUploadDataToMap(request, shipmentIdUrlMap, shipmentIdBoxIdxMap, planAllBoxList);
        //校验解析完的文件数据
        checkUploadData(planAllBoxList);
        //处理上传文件
        dealUploadData(fbaInboundShipmentPlanEntity, planItemEntities, shipmentIdUrlMap, shipmentIdBoxIdxMap);
        // 修改plan状态为待发货
        fbaInboundShipmentPlanEntity.setStatus(InboundPlanStatusEnum.WAITING_SHIPMENT.status());
        //路径
        fbaInboundShipmentPlanEntity.setUrls(CollUtil.isNotEmpty(request.getUrls()) ? StringUtils.substring(StringUtils.join(request.getUrls(), ","), 0, 1024) : null);
        inboundPlanService.updateById(fbaInboundShipmentPlanEntity);
        //日志
        logEntityService.saveFbaInboundShipmentPlanLog(fbaInboundShipmentPlanEntity.getId(), "上传shipment清单", "补货单-" + fbaInboundShipmentPlanEntity.getStatus(), fbaInboundShipmentPlanEntity.getStatus());
        //发送同步mq
        sendBoxMessage(planId, shipmentIdBoxIdxMap);
    }

    private void checkUploadData(List<StaBoxInfoDto> planBoxInfoList) {
        // 检查所有箱子是否都关联了shipmentId
        checkAllBoxesHaveShipmentId(planBoxInfoList);
        // 检查箱子中的商品数量是否与计划单一致
//        checkQuantityConsistency(planItemEntityList, planBoxInfoList);
    }

    private void checkAllBoxesHaveShipmentId(List<StaBoxInfoDto> planBoxInfoList) {
        List<Integer> unassociatedBoxIndices = planBoxInfoList.stream()
                .filter(box -> ObjectUtil.isEmpty(box.getFbaShipmentId()))
                .map(StaBoxInfoDto::getBoxIndex)
                .collect(Collectors.toList());

        if (!unassociatedBoxIndices.isEmpty()) {
            throw new BusinessServiceException(StrUtil.format("以下箱子未关联shipmentId，请检查上传文件：箱子序号[{}]",
                    String.join(", ", unassociatedBoxIndices.stream().map(String::valueOf).collect(Collectors.toList()))));
        }
    }

//    private void checkQuantityConsistency(List<InboundPlanItemEntity> planItemEntityList, List<StaBoxInfoDto> planBoxInfoList) {
//        // 计算箱子中的商品总数
//        int totalBoxQuantity = planBoxInfoList.stream()
//                .flatMap(box -> box.getStaBoxItemInfoDtoList().stream())
//                .mapToInt(StaBoxItemInfoDto::getQuantity)
//                .sum();
//
//        // 计算计划单中的商品总数
//        int totalPlanQuantity = planItemEntityList.stream()
//                .mapToInt(InboundPlanItemEntity::getEstimatedShipmentQuantity)
//                .sum();
//
//        if (totalBoxQuantity != totalPlanQuantity) {
//            throw new BusinessServiceException(StrUtil.format(
//                    "装箱清单数量与计划单数量不一致，请检查上传文件。装箱清单总数：{}，计划单总数：{}",
//                    totalBoxQuantity,
//                    totalPlanQuantity));
//        }
//    }

    public void analyseUploadDataToMap(UploadShipmentRequest request,
                                       Map<String, String> shipmentIdUrlMap,
                                       Map<String, List<Integer>> shipmentIdBoxIdxMap,
                                       List<StaBoxInfoDto> planAllBoxList) {
        PdfCodeExtractor extractor = new PdfCodeExtractor();
        extractor.configureScanners("barcode");
        for (String url : request.getUrls()) {
            Set<String> strings = extractor.extractCodesFromUrl(url);
            //shipment跟箱子序号的的关联
            Map<String, List<Integer>> shipmementBoxIdxMap = parseShipmentBoxList(new ArrayList<>(strings));
            shipmentIdUrlMap.put(shipmementBoxIdxMap.keySet().toArray()[0].toString(), url);
            shipmentIdBoxIdxMap.putAll(shipmementBoxIdxMap);
            shipmementBoxIdxMap.forEach((shipmentId, shipmentBoxIdx) -> {
                // 遍历每个箱子,如果箱子序号在当前shipmentId对应的boxIndexList中,就设置shipmentId
                planAllBoxList.stream().filter(box -> shipmentBoxIdx.contains(box.getBoxIndex())).forEach(box -> box.setFbaShipmentId(shipmentId));
            });
        }
        if (shipmentIdBoxIdxMap.isEmpty()) {
            throw new BusinessServiceException("上传箱唛未检测到Barcode,请检查文件");
        }

    }


    public void dealUploadData(InboundPlanEntity fbaInboundShipmentPlanEntity,
                               List<InboundPlanItemEntity> planItemEntities,
                               Map<String, String> shipmentUrlMap,
                               Map<String, List<Integer>> shipmentIdBoxIdxMap) {
        //通过亚马逊获取shipmentId的信息
        Integer storeId = fbaInboundShipmentPlanEntity.getStoreId();
        TiktokAuth tiktokAuth = sauPlatformAuthConfigService.getTikTokToken(saStoreDao.getAssociatedStoreIdWithThrowEx(storeId));
        InboundOrdersResponse inboundOrders = thirdPartyApiService.getInboundOrders(tiktokAuth, new ArrayList<>(shipmentUrlMap.keySet()));
//        InboundOrdersResponse inboundOrders = JSONUtil.toBean("{\"result\":null,\"message\":null,\"nextPageInfo\":null,\"hasNextPage\":null,\"responseTokenInfo\":{\"accessToken\":\"TTP_oYHiwQAAAACnhmz6TqMA3fVTs-vVCmgMWjI32NMkO-4Imdi7ncs-jxO8J-ms6fd9jvnhvwWiLE641LN60An4IgFJr88eOCeScUZ8XInDMtrQq7m76RNHZFRjbzfj_wBoQ8WpOASDztUo_z1xzSRRa0jiB4Mvwkr58EhdyKUdeSwmr5kt4MRYRA\",\"accessTokenTimeOut\":\"1744870059\",\"refreshToken\":\"TTP_1RVQggAAAAAXydiMpJE5q9iB1z56KM6TxLaQM-Mb07FX1H3h1FG2S1ar5zLZ119cFAf5X3YpKug\",\"refreshTokenTimeout\":null},\"total\":null,\"storeId\":null,\"location\":null,\"orderList\":null,\"failedList\":[],\"inboundOrderDate\":{\"inboundOrders\":[{\"id\":\"IBR5765076987019301126\",\"type\":\"REPLENISHMENT\",\"merchant\":{\"id\":\"7330663924858749190\",\"name\":\"Dokotoo\"},\"createTime\":1739501127072,\"shipTime\":1739501860140,\"expectArriveTime\":1740895200000,\"actualArriveTime\":1742489662000,\"warehouse\":{\"fbtWarehouseId\":\"7400370674856363819\",\"warehouseIds\":[\"7400367759450900267\"],\"name\":\"TikTok_Carol(IL)_FC\",\"type\":\"PLATFORM_WAREHOUSE\"},\"plannedGoods\":[{\"id\":\"8667313740038\",\"referenceCode\":\"**********-P5-M\",\"name\":\"Dokotoo Women's Casual Summer T Shirts Short Sleeve V Neck Tops Tshirts\",\"quantity\":5,\"skuIds\":[\"1729440015238468597\"]},{\"id\":\"8667311118598\",\"referenceCode\":\"**********-16-M\",\"name\":\"Dokotoo Women's Casual Summer T Shirts Short Sleeve V Neck Tops Tshirts\",\"quantity\":10,\"skuIds\":[\"1729440015237157877\"]},{\"id\":\"1193600620806\",\"referenceCode\":\"**********-P1010-2XL\",\"name\":\"Dokotoo Women's 2024 Fashion Tops 3/4 Sleeve T-Shirts Cute Crewneck Basic Business Tees Blouses Light Pink 2XL\",\"quantity\":7,\"skuIds\":[\"1729497765614294005\"]},{\"id\":\"8868311603462\",\"referenceCode\":\"LC6122206-P820-XL\",\"name\":\"LC6122206-P820-XL\",\"quantity\":10,\"skuIds\":[\"1729966303742432245\"]},{\"id\":\"8659784440070\",\"referenceCode\":\"**********-4-M\",\"name\":\"Dokotoo Women's Casual Summer T Shirts Short Sleeve V Neck Tops Tshirts\",\"quantity\":7,\"skuIds\":[\"1729440015233553397\"]},{\"id\":\"2417615968518\",\"referenceCode\":\"LC25126123-P1-S\",\"name\":\"Dokotoo Womens Tunic Tops V Neck Casual Loose 3/4 Sleeve Shirts\",\"quantity\":10,\"skuIds\":[\"1729445592260514805\"]},{\"id\":\"1193598785798\",\"referenceCode\":\"**********-P404-L\",\"name\":\"Dokotoo Women's 2024 Fashion Tops 3/4 Sleeve T-Shirts Cute Crewneck Basic Business Tees Blouses Turquoise L\",\"quantity\":8,\"skuIds\":[\"1729497765613835253\"]},{\"id\":\"8651696511238\",\"referenceCode\":\"LC6413831-P320-XL\",\"name\":\"Dokotoo Rompers for Women 2025 Casual Loose Short Overalls One Piece Sleeveless Jumpsuits\",\"quantity\":11,\"skuIds\":[\"1729960648684966901\"]},{\"id\":\"8836115601670\",\"referenceCode\":\"LC6122206-P320-L\",\"name\":\"LC6122206-P320-L\",\"quantity\":6,\"skuIds\":[\"1729966303742235637\"]},{\"id\":\"8836626520326\",\"referenceCode\":\"LC6122206-P820-M\",\"name\":\"LC6122206-P820-M\",\"quantity\":11,\"skuIds\":[\"1729966303742366709\"]},{\"id\":\"1193598523654\",\"referenceCode\":\"**********-P404-M\",\"name\":\"Dokotoo Women's 2024 Fashion Tops 3/4 Sleeve T-Shirts Cute Crewneck Basic Business Tees Blouses Turquoise M\",\"quantity\":8,\"skuIds\":[\"1729497765613769717\"]},{\"id\":\"8659783653638\",\"referenceCode\":\"**********-10-XL\",\"name\":\"Dokotoo Women's Casual Summer T Shirts Short Sleeve V Neck Tops Tshirts\",\"quantity\":34,\"skuIds\":[\"1729440015237616629\"]},{\"id\":\"************\",\"referenceCode\":\"**********-P2-XL\",\"name\":\"Dokotoo Women's 2024 Fashion Tops 3/4 Sleeve T-Shirts Cute Crewneck Basic Business Tees Blouses Black XL\",\"quantity\":0,\"skuIds\":[\"1729440024052798453\"]},{\"id\":\"8659781818630\",\"referenceCode\":\"**********-2-M\",\"name\":\"Dokotoo Women's Casual Summer T Shirts Short Sleeve V Neck Tops Tshirts\",\"quantity\":8,\"skuIds\":[\"1729440015236502517\"]},{\"id\":\"8659782080774\",\"referenceCode\":\"**********-2-L\",\"name\":\"Dokotoo Women's Casual Summer T Shirts Short Sleeve V Neck Tops Tshirts\",\"quantity\":18,\"skuIds\":[\"1729440015236568053\"]},{\"id\":\"8659786012934\",\"referenceCode\":\"**********-17-L\",\"name\":\"Dokotoo Women's Casual Summer T Shirts Short Sleeve V Neck Tops Tshirts\",\"quantity\":5,\"skuIds\":[\"1729440015233946613\"]},{\"id\":\"8659780770054\",\"referenceCode\":\"**********-1-L\",\"name\":\"Dokotoo Women's Casual Summer T Shirts Short Sleeve V Neck Tops Tshirts\",\"quantity\":20,\"skuIds\":[\"1729440015237878773\"]},{\"id\":\"8649022642438\",\"referenceCode\":\"LC25126123-P503-L\",\"name\":\"Dokotoo Womens Tunic Tops V Neck Casual Loose 3/4 Sleeve Shirts Dressy Blouses Tops Overiszed T Shirts\",\"quantity\":14,\"skuIds\":[\"1729718235346539509\"]},{\"id\":\"8836114815238\",\"referenceCode\":\"LC6122206-P220-XL\",\"name\":\"LC6122206-P220-XL\",\"quantity\":7,\"skuIds\":[\"1729966303742956533\"]},{\"id\":\"8659786275078\",\"referenceCode\":\"**********-17-XL\",\"name\":\"Dokotoo Women's Casual Summer T Shirts Short Sleeve V Neck Tops Tshirts\",\"quantity\":18,\"skuIds\":[\"1729440015234012149\"]},{\"id\":\"8868310554886\",\"referenceCode\":\"LC6118879-P1109-XL\",\"name\":\"LC6118879-P1109-XL\",\"quantity\":10,\"skuIds\":[\"1729588903226741749\"]},{\"id\":\"1193600358662\",\"referenceCode\":\"**********-P1010-XL\",\"name\":\"Dokotoo Women's 2024 Fashion Tops 3/4 Sleeve T-Shirts Cute Crewneck Basic Business Tees Blouses Light Pink XL\",\"quantity\":22,\"skuIds\":[\"1729497765614228469\"]},{\"id\":\"************\",\"referenceCode\":\"**********-P11-XL\",\"name\":\"Dokotoo Women's 2024 Fashion Tops 3/4 Sleeve T-Shirts Cute Crewneck Basic Business Tees Blouses Gray XL\",\"quantity\":12,\"skuIds\":[\"1729440024050832373\"]},{\"id\":\"8651694151942\",\"referenceCode\":\"LC6413831-P209-2XL\",\"name\":\"Dokotoo Rompers for Women 2025 Casual Loose Short Overalls One Piece Sleeveless Jumpsuits\",\"quantity\":6,\"skuIds\":[\"1729960583713362933\"]},{\"id\":\"1164253600006\",\"referenceCode\":\"LC6118603-P120-2XL\",\"name\":\"Dokotoo Women's Casual Loose Bohemian Floral Dresses 2024 Long Puff Sleeve V Neck Ruffle A Line Flowy Summer Beach Dress White 2\",\"quantity\":41,\"skuIds\":[\"1729439010827047925\"]},{\"id\":\"8788032362758\",\"referenceCode\":\"LC6118879-P5-2XL\",\"name\":\"LC6118879-P5-2XL\",\"quantity\":8,\"skuIds\":[\"1729588903226217461\"]},{\"id\":\"2417616230662\",\"referenceCode\":\"LC25126123-P1-M\",\"name\":\"Dokotoo Womens Tunic Tops V Neck Casual Loose 3/4 Sleeve Shirts\",\"quantity\":17,\"skuIds\":[\"1729445592260580341\"]},{\"id\":\"2417616754950\",\"referenceCode\":\"LC25126123-P1-XL\",\"name\":\"Dokotoo Womens Tunic Tops V Neck Casual Loose 3/4 Sleeve Shirts\",\"quantity\":10,\"skuIds\":[\"1729445592260711413\"]},{\"id\":\"8788971624710\",\"referenceCode\":\"LC6118879-P6-XL\",\"name\":\"Dokotoo Womens Summer Tunic Dress Casual Loose Short\",\"quantity\":8,\"skuIds\":[\"1729440021047907317\"]},{\"id\":\"8651696249094\",\"referenceCode\":\"LC6413831-P320-L\",\"name\":\"Dokotoo Rompers for Women 2025 Casual Loose Short Overalls One Piece Sleeveless Jumpsuits\",\"quantity\":9,\"skuIds\":[\"1729960583713756149\"]},{\"id\":\"2415396133126\",\"referenceCode\":\"LC6121364-P2-XL\",\"name\":\"Dokotoo Womens 2024 Casual Dress Tassel V Neck Long Sleeve A Line Hollow Out Lace Ruffle Party Dresses\",\"quantity\":34,\"skuIds\":[\"1729596430126388213\"]},{\"id\":\"8868311079174\",\"referenceCode\":\"LC6118879-P605-L\",\"name\":\"LC6118879-P605-L\",\"quantity\":8,\"skuIds\":[\"1730055783628379125\"]},{\"id\":\"8868310292742\",\"referenceCode\":\"LC6118879-P1010-S\",\"name\":\"LC6118879-P1010-S\",\"quantity\":12,\"skuIds\":[\"1729588903227200501\"]},{\"id\":\"8788967430406\",\"referenceCode\":\"LC6118879-P1308-L\",\"name\":\"Dokotoo Womens Summer Tunic Dress Casual Loose Short\",\"quantity\":12,\"skuIds\":[\"1729588903227659253\"]},{\"id\":\"8868310817030\",\"referenceCode\":\"LC6118879-P209-M\",\"name\":\"LC6118879-P209-M\",\"quantity\":15,\"skuIds\":[\"1729440021047448565\"]},{\"id\":\"8651698084102\",\"referenceCode\":\"LC6413831-P520-2XL\",\"name\":\"Dokotoo Rompers for Women 2025 Casual Loose Short Overalls One Piece Sleeveless Jumpsuits\",\"quantity\":5,\"skuIds\":[\"1729960583712773109\"]},{\"id\":\"8651700705542\",\"referenceCode\":\"LC6413831-P920-2XL\",\"name\":\"Dokotoo Rompers for Women 2025 Casual Loose Short Overalls One Piece Sleeveless Jumpsuits\",\"quantity\":5,\"skuIds\":[\"1729960648685425653\"]},{\"id\":\"8659784964358\",\"referenceCode\":\"**********-4-XL\",\"name\":\"Dokotoo Women's Casual Summer T Shirts Short Sleeve V Neck Tops Tshirts\",\"quantity\":8,\"skuIds\":[\"1729440015233684469\"]},{\"id\":\"8659787847942\",\"referenceCode\":\"**********-P804-2XL\",\"name\":\"Dokotoo Women's Casual Summer T Shirts Short Sleeve V Neck Tops Tshirts\",\"quantity\":17,\"skuIds\":[\"1729440015234405365\"]},{\"id\":\"8667310594310\",\"referenceCode\":\"**********-3-2XL\",\"name\":\"Dokotoo Women's Casual Summer T Shirts Short Sleeve V Neck Tops Tshirts\",\"quantity\":6,\"skuIds\":[\"1729440015237026805\"]},{\"id\":\"8788971362566\",\"referenceCode\":\"LC6118879-P6-L\",\"name\":\"Dokotoo Womens Summer Tunic Dress Casual Loose Short\",\"quantity\":9,\"skuIds\":[\"1729440021047841781\"]},{\"id\":\"************\",\"referenceCode\":\"**********-P2-L\",\"name\":\"Dokotoo Women's 2024 Fashion Tops 3/4 Sleeve T-Shirts Cute Crewneck Basic Business Tees Blouses Black L\",\"quantity\":8,\"skuIds\":[\"1729440024052732917\"]},{\"id\":\"8651703851270\",\"referenceCode\":\"LC6413831-P20520-M\",\"name\":\"Dokotoo Rompers for Women 2025 Casual Loose Short Overalls One Piece Sleeveless Jumpsuits\",\"quantity\":6,\"skuIds\":[\"1729960583712379893\"]},{\"id\":\"8836625471750\",\"referenceCode\":\"LC6122206-P220-L\",\"name\":\"LC6122206-P220-L\",\"quantity\":23,\"skuIds\":[\"1729966303742104565\"]},{\"id\":\"8649022904582\",\"referenceCode\":\"LC25126123-P503-XL\",\"name\":\"Dokotoo Womens Tunic Tops V Neck Casual Loose 3/4 Sleeve Shirts Dressy Blouses Tops Overiszed T Shirts\",\"quantity\":59,\"skuIds\":[\"1729718235346605045\"]},{\"id\":\"8667314002182\",\"referenceCode\":\"**********-P5-L\",\"name\":\"Dokotoo Women's Casual Summer T Shirts Short Sleeve V Neck Tops Tshirts\",\"quantity\":13,\"skuIds\":[\"1729440015238534133\"]},{\"id\":\"8659781294342\",\"referenceCode\":\"**********-1-2XL\",\"name\":\"Dokotoo Women's Casual Summer T Shirts Short Sleeve V Neck Tops Tshirts\",\"quantity\":19,\"skuIds\":[\"1729440015238009845\"]},{\"id\":\"8868311341318\",\"referenceCode\":\"LC6118879-P609-M\",\"name\":\"LC6118879-P609-M\",\"quantity\":5,\"skuIds\":[\"1729588903226938357\"]},{\"id\":\"8651701491974\",\"referenceCode\":\"LC6413831-P1120-L\",\"name\":\"Dokotoo Rompers for Women 2025 Casual Loose Short Overalls One Piece Sleeveless Jumpsuits\",\"quantity\":6,\"skuIds\":[\"1729960648684639221\"]},{\"id\":\"************\",\"referenceCode\":\"LC6121368-P1720-L\",\"name\":\"Dokotoo Womens Ruffle Chiffon Elegant Dresses Brown L\",\"quantity\":25,\"skuIds\":[\"1729490416697185269\"]},{\"id\":\"8659783129350\",\"referenceCode\":\"**********-10-M\",\"name\":\"Dokotoo Women's Casual Summer T Shirts Short Sleeve V Neck Tops Tshirts\",\"quantity\":8,\"skuIds\":[\"1729440015237485557\"]}],\"receivedBatches\":[{\"id\":\"5765110158803439878\",\"goodsId\":\"2417616230662\",\"normalQuantity\":17,\"defectiveQuantity\":0,\"productIds\":[\"1729445592259793909\"],\"skuIds\":[\"1729445592260580341\"],\"totalQuantity\":17},{\"id\":\"5765110102062829830\",\"goodsId\":\"8649022642438\",\"normalQuantity\":14,\"defectiveQuantity\":0,\"productIds\":[\"1729445592259793909\"],\"skuIds\":[\"1729718235346539509\"],\"totalQuantity\":14},{\"id\":\"5765110155320922374\",\"goodsId\":\"8788971624710\",\"normalQuantity\":8,\"defectiveQuantity\":0,\"productIds\":[\"1729440021047317493\"],\"skuIds\":[\"1729440021047907317\"],\"totalQuantity\":8},{\"id\":\"5765110101280198918\",\"goodsId\":\"2417615968518\",\"normalQuantity\":10,\"defectiveQuantity\":0,\"productIds\":[\"1729445592259793909\"],\"skuIds\":[\"1729445592260514805\"],\"totalQuantity\":10},{\"id\":\"5765109770138849542\",\"goodsId\":\"2417616754950\",\"normalQuantity\":10,\"defectiveQuantity\":0,\"productIds\":[\"1729445592259793909\"],\"skuIds\":[\"1729445592260711413\"],\"totalQuantity\":10},{\"id\":\"5765107788472160518\",\"goodsId\":\"1193600358662\",\"normalQuantity\":22,\"defectiveQuantity\":0,\"productIds\":[\"1729440023390819317\"],\"skuIds\":[\"1729497765614228469\"],\"totalQuantity\":22},{\"id\":\"5765107754373517574\",\"goodsId\":\"************\",\"normalQuantity\":8,\"defectiveQuantity\":0,\"productIds\":[\"1729440023390819317\"],\"skuIds\":[\"1729440024052732917\"],\"totalQuantity\":8},{\"id\":\"5765107773672558854\",\"goodsId\":\"************\",\"normalQuantity\":12,\"defectiveQuantity\":0,\"productIds\":[\"1729440023390819317\"],\"skuIds\":[\"1729440024050832373\"],\"totalQuantity\":12},{\"id\":\"5765107773671379206\",\"goodsId\":\"1193600620806\",\"normalQuantity\":7,\"defectiveQuantity\":0,\"productIds\":[\"1729440023390819317\"],\"skuIds\":[\"1729497765614294005\"],\"totalQuantity\":7},{\"id\":\"5765106980738339078\",\"goodsId\":\"8788032362758\",\"normalQuantity\":8,\"defectiveQuantity\":0,\"productIds\":[\"1729440021047317493\"],\"skuIds\":[\"1729588903226217461\"],\"totalQuantity\":8},{\"id\":\"5765106980737945862\",\"goodsId\":\"8868310817030\",\"normalQuantity\":15,\"defectiveQuantity\":0,\"productIds\":[\"1729440021047317493\"],\"skuIds\":[\"1729440021047448565\"],\"totalQuantity\":15},{\"id\":\"5765106976398610694\",\"goodsId\":\"8788971362566\",\"normalQuantity\":9,\"defectiveQuantity\":0,\"productIds\":[\"1729440021047317493\"],\"skuIds\":[\"1729440021047841781\"],\"totalQuantity\":9},{\"id\":\"5765106978572112134\",\"goodsId\":\"8788967430406\",\"normalQuantity\":12,\"defectiveQuantity\":0,\"productIds\":[\"1729440021047317493\"],\"skuIds\":[\"1729588903227659253\"],\"totalQuantity\":12},{\"id\":\"5765106975191109894\",\"goodsId\":\"8868310292742\",\"normalQuantity\":12,\"defectiveQuantity\":0,\"productIds\":[\"1729440021047317493\"],\"skuIds\":[\"1729588903227200501\"],\"totalQuantity\":12},{\"id\":\"5765106974962585862\",\"goodsId\":\"8868311341318\",\"normalQuantity\":5,\"defectiveQuantity\":0,\"productIds\":[\"1729440021047317493\"],\"skuIds\":[\"1729588903226938357\"],\"totalQuantity\":5},{\"id\":\"5765106969656987910\",\"goodsId\":\"1164253600006\",\"normalQuantity\":41,\"defectiveQuantity\":0,\"productIds\":[\"1729439010826064885\"],\"skuIds\":[\"1729439010827047925\"],\"totalQuantity\":41},{\"id\":\"5765106974961012998\",\"goodsId\":\"8659782080774\",\"normalQuantity\":18,\"defectiveQuantity\":0,\"productIds\":[\"1729440015233422325\"],\"skuIds\":[\"1729440015236568053\"],\"totalQuantity\":18},{\"id\":\"5765106974664724742\",\"goodsId\":\"8659784964358\",\"normalQuantity\":8,\"defectiveQuantity\":0,\"productIds\":[\"1729440015233422325\"],\"skuIds\":[\"1729440015233684469\"],\"totalQuantity\":8},{\"id\":\"5765106974256828678\",\"goodsId\":\"8659781818630\",\"normalQuantity\":8,\"defectiveQuantity\":0,\"productIds\":[\"1729440015233422325\"],\"skuIds\":[\"1729440015236502517\"],\"totalQuantity\":8},{\"id\":\"5765106969655415046\",\"goodsId\":\"8659787847942\",\"normalQuantity\":17,\"defectiveQuantity\":0,\"productIds\":[\"1729440015233422325\"],\"skuIds\":[\"1729440015234405365\"],\"totalQuantity\":17},{\"id\":\"5765106969654890758\",\"goodsId\":\"8659783129350\",\"normalQuantity\":8,\"defectiveQuantity\":0,\"productIds\":[\"1729440015233422325\"],\"skuIds\":[\"1729440015237485557\"],\"totalQuantity\":8},{\"id\":\"5765106974255386886\",\"goodsId\":\"8667313740038\",\"normalQuantity\":5,\"defectiveQuantity\":0,\"productIds\":[\"1729440015233422325\"],\"skuIds\":[\"1729440015238468597\"],\"totalQuantity\":5},{\"id\":\"5765105711856324870\",\"goodsId\":\"8836625471750\",\"normalQuantity\":23,\"defectiveQuantity\":0,\"productIds\":[\"1729966303741252597\"],\"skuIds\":[\"1729966303742104565\"],\"totalQuantity\":23},{\"id\":\"5765105870811533574\",\"goodsId\":\"8836626520326\",\"normalQuantity\":11,\"defectiveQuantity\":0,\"productIds\":[\"1729966303741252597\"],\"skuIds\":[\"1729966303742366709\"],\"totalQuantity\":11},{\"id\":\"5765105623385608454\",\"goodsId\":\"8836114815238\",\"normalQuantity\":7,\"defectiveQuantity\":0,\"productIds\":[\"1729966303741252597\"],\"skuIds\":[\"1729966303742956533\"],\"totalQuantity\":7},{\"id\":\"5765105635231830278\",\"goodsId\":\"************\",\"normalQuantity\":25,\"defectiveQuantity\":0,\"productIds\":[\"1729490416696660981\"],\"skuIds\":[\"1729490416697185269\"],\"totalQuantity\":25},{\"id\":\"5765105513266712838\",\"goodsId\":\"8659783653638\",\"normalQuantity\":34,\"defectiveQuantity\":0,\"productIds\":[\"1729440015233422325\"],\"skuIds\":[\"1729440015237616629\"],\"totalQuantity\":34},{\"id\":\"5765105426217537798\",\"goodsId\":\"1193598523654\",\"normalQuantity\":8,\"defectiveQuantity\":0,\"productIds\":[\"1729440023390819317\"],\"skuIds\":[\"1729497765613769717\"],\"totalQuantity\":8},{\"id\":\"5765105425750003974\",\"goodsId\":\"1193598785798\",\"normalQuantity\":8,\"defectiveQuantity\":0,\"productIds\":[\"1729440023390819317\"],\"skuIds\":[\"1729497765613835253\"],\"totalQuantity\":8},{\"id\":\"5765105073514975494\",\"goodsId\":\"8651701491974\",\"normalQuantity\":6,\"defectiveQuantity\":0,\"productIds\":[\"1729960648684508149\"],\"skuIds\":[\"1729960648684639221\"],\"totalQuantity\":6},{\"id\":\"5765104963581350150\",\"goodsId\":\"8651696249094\",\"normalQuantity\":9,\"defectiveQuantity\":0,\"productIds\":[\"1729960648684508149\"],\"skuIds\":[\"1729960583713756149\"],\"totalQuantity\":9},{\"id\":\"5765105003559883014\",\"goodsId\":\"8651696511238\",\"normalQuantity\":11,\"defectiveQuantity\":0,\"productIds\":[\"1729960648684508149\"],\"skuIds\":[\"1729960648684966901\"],\"totalQuantity\":11},{\"id\":\"5765105003558506758\",\"goodsId\":\"8836115601670\",\"normalQuantity\":6,\"defectiveQuantity\":0,\"productIds\":[\"1729966303741252597\"],\"skuIds\":[\"1729966303742235637\"],\"totalQuantity\":6},{\"id\":\"5765104963578269958\",\"goodsId\":\"8868311603462\",\"normalQuantity\":10,\"defectiveQuantity\":0,\"productIds\":[\"1729966303741252597\"],\"skuIds\":[\"1729966303742432245\"],\"totalQuantity\":10},{\"id\":\"5765105066324758790\",\"goodsId\":\"8651694151942\",\"normalQuantity\":6,\"defectiveQuantity\":0,\"productIds\":[\"1729960648684508149\"],\"skuIds\":[\"1729960583713362933\"],\"totalQuantity\":6},{\"id\":\"5765104963103920390\",\"goodsId\":\"8651698084102\",\"normalQuantity\":5,\"defectiveQuantity\":0,\"productIds\":[\"1729960648684508149\"],\"skuIds\":[\"1729960583712773109\"],\"totalQuantity\":5},{\"id\":\"5765104963103396102\",\"goodsId\":\"8651703851270\",\"normalQuantity\":6,\"defectiveQuantity\":0,\"productIds\":[\"1729960648684508149\"],\"skuIds\":[\"1729960583712379893\"],\"totalQuantity\":6},{\"id\":\"5765105066323775750\",\"goodsId\":\"8651700705542\",\"normalQuantity\":5,\"defectiveQuantity\":0,\"productIds\":[\"1729960648684508149\"],\"skuIds\":[\"1729960648685425653\"],\"totalQuantity\":5},{\"id\":\"5765104956604125446\",\"goodsId\":\"8659786275078\",\"normalQuantity\":18,\"defectiveQuantity\":0,\"productIds\":[\"1729440015233422325\"],\"skuIds\":[\"1729440015234012149\"],\"totalQuantity\":18},{\"id\":\"5765104956603666694\",\"goodsId\":\"8667314002182\",\"normalQuantity\":13,\"defectiveQuantity\":0,\"productIds\":[\"1729440015233422325\"],\"skuIds\":[\"1729440015238534133\"],\"totalQuantity\":13},{\"id\":\"5765105012230820102\",\"goodsId\":\"2415396133126\",\"normalQuantity\":34,\"defectiveQuantity\":0,\"productIds\":[\"1729596430125601781\"],\"skuIds\":[\"1729596430126388213\"],\"totalQuantity\":34},{\"id\":\"5765104991293968646\",\"goodsId\":\"8659784440070\",\"normalQuantity\":7,\"defectiveQuantity\":0,\"productIds\":[\"1729440015233422325\"],\"skuIds\":[\"1729440015233553397\"],\"totalQuantity\":7},{\"id\":\"5765104908840243462\",\"goodsId\":\"8659786012934\",\"normalQuantity\":5,\"defectiveQuantity\":0,\"productIds\":[\"1729440015233422325\"],\"skuIds\":[\"1729440015233946613\"],\"totalQuantity\":5},{\"id\":\"5765104991293509894\",\"goodsId\":\"8667310594310\",\"normalQuantity\":6,\"defectiveQuantity\":0,\"productIds\":[\"1729440015233422325\"],\"skuIds\":[\"1729440015237026805\"],\"totalQuantity\":6},{\"id\":\"5765104860484571398\",\"goodsId\":\"8667311118598\",\"normalQuantity\":10,\"defectiveQuantity\":0,\"productIds\":[\"1729440015233422325\"],\"skuIds\":[\"1729440015237157877\"],\"totalQuantity\":10},{\"id\":\"5765104927862460678\",\"goodsId\":\"8649022904582\",\"normalQuantity\":59,\"defectiveQuantity\":0,\"productIds\":[\"1729445592259793909\"],\"skuIds\":[\"1729718235346605045\"],\"totalQuantity\":59},{\"id\":\"5765104846913638662\",\"goodsId\":\"8659780770054\",\"normalQuantity\":20,\"defectiveQuantity\":0,\"productIds\":[\"1729440015233422325\"],\"skuIds\":[\"1729440015237878773\"],\"totalQuantity\":20},{\"id\":\"5765104860723450118\",\"goodsId\":\"8659781294342\",\"normalQuantity\":19,\"defectiveQuantity\":0,\"productIds\":[\"1729440015233422325\"],\"skuIds\":[\"1729440015238009845\"],\"totalQuantity\":19},{\"id\":\"5765104846912262406\",\"goodsId\":\"8868311079174\",\"normalQuantity\":8,\"defectiveQuantity\":0,\"productIds\":[\"1729440021047317493\"],\"skuIds\":[\"1730055783628379125\"],\"totalQuantity\":8},{\"id\":\"5765104778744598790\",\"goodsId\":\"8868310554886\",\"normalQuantity\":10,\"defectiveQuantity\":0,\"productIds\":[\"1729440021047317493\"],\"skuIds\":[\"1729588903226741749\"],\"totalQuantity\":10}],\"orderOperationLogs\":[{\"operateTime\":1741987168000,\"orderStatus\":\"RECEIVING\"},{\"operateTime\":1739501860141,\"orderStatus\":\"TO_BE_RECEIVED\"},{\"operateTime\":1739501127126,\"orderStatus\":\"READY_TO_SHIP\"}],\"carriers\":[]}]}}", InboundOrdersResponse.class);
        InboundOrderDate inboundOrderDate = inboundOrders.getInboundOrderDate();
        if (inboundOrderDate == null) {
            throw new BusinessServiceException("获取shipmentId信息失败");
        }
        String userName = loginInfoService.getUserName();
        Map<String, InboundPlanItemEntity> sellerSkuMap = planItemEntities.stream().collect(Collectors.toMap(InboundPlanItemEntity::getSellerSku, a -> a, (k1, k2) -> k1));
        List<StaBoxInfoDto> planAllBoxList = wmsApiService.getStaBoxInfoDetailByErpTid(fbaInboundShipmentPlanEntity.getErpTid()).getStaBoxInfoDtoList();
        inboundOrderDate.getInboundOrders().forEach(inboundShipmentInfo -> {
            buildAndSaveShipment(fbaInboundShipmentPlanEntity, shipmentUrlMap, shipmentIdBoxIdxMap, inboundShipmentInfo, userName, planAllBoxList, sellerSkuMap);
        });
    }

    private void buildAndSaveShipment(InboundPlanEntity fbaInboundShipmentPlanEntity, Map<String, String> shipmentUrlMap, Map<String, List<Integer>> shipmentIdBoxIdxMap, InboundOrder inboundShipmentInfo, String userName, List<StaBoxInfoDto> planAllBoxList, Map<String, InboundPlanItemEntity> sellerSkuMap) {
        String shipmentId = inboundShipmentInfo.getId();
        InboundShipmentEntity fbaInboundShipmentEntity = new InboundShipmentEntity();
        fbaInboundShipmentEntity.setStatus(ShipmentStatusEnum.WORKING.status());
        fbaInboundShipmentEntity.setStoreId(fbaInboundShipmentPlanEntity.getStoreId());
        fbaInboundShipmentEntity.setLocation(fbaInboundShipmentPlanEntity.getLocation());
        fbaInboundShipmentEntity.setStoreName(fbaInboundShipmentPlanEntity.getStoreName());
        fbaInboundShipmentEntity.setShipmentId(shipmentId);
        fbaInboundShipmentEntity.setPlanId(fbaInboundShipmentPlanEntity.getId());
        fbaInboundShipmentEntity.setPlatformStatus(inboundShipmentInfo.getOrderOperationLogs().get(0).getOrderStatus());
        fbaInboundShipmentEntity.setLogisticsName(fbaInboundShipmentPlanEntity.getLogisticsCompanyName());
        fbaInboundShipmentEntity.setFileUrl(shipmentUrlMap.get(shipmentId));
        fbaInboundShipmentEntity.setBoxCount(shipmentIdBoxIdxMap.get(shipmentId).size());
        fbaInboundShipmentEntity.setPlatform(fbaInboundShipmentPlanEntity.getPlatform());
        fbaInboundShipmentEntity.setDestinationFulfillmentCenter(inboundShipmentInfo.getWarehouse().getName());
        fbaInboundShipmentEntity.setCreateBy(userName);
        fbaInboundShipmentEntity.setUpdateBy(userName);
        shipmentEntityService.save(fbaInboundShipmentEntity);
        shipmentItemEntityService.deleteByShipmentIds(Collections.singletonList(shipmentId));
        List<Integer> boxIdx = shipmentIdBoxIdxMap.get(shipmentId);
        List<StaBoxInfoDto> shipmentBox = planAllBoxList.stream().filter(box -> boxIdx.contains(box.getBoxIndex())).collect(Collectors.toList());
        Map<String, Integer> shipmentSellerSKuTotalUnitMap = shipmentBox.stream()
                .flatMap(box -> box.getStaBoxItemInfoDtoList().stream())
                .collect(Collectors.groupingBy(StaBoxItemInfoDto::getSellerSku, Collectors.summingInt(StaBoxItemInfoDto::getQuantity)));

        List<PlannedGood> plannedGoods = inboundShipmentInfo.getPlannedGoods();
        List<InboundShipmentItemEntity> shipmentItemEntities = new ArrayList<>();

        for (PlannedGood plannedGood : plannedGoods) {
            String sellerSku = plannedGood.getReferenceCode();
            InboundPlanItemEntity planItem = sellerSkuMap.get(sellerSku);
            if (planItem == null) {
                planItem = new InboundPlanItemEntity();
            }
            InboundShipmentItemEntity shipmentItemEntity = new InboundShipmentItemEntity();
            shipmentItemEntity.setShipmentId(shipmentId);
            shipmentItemEntity.setQuantityReceived(0);
            shipmentItemEntity.setEstimatedShipmentQuantity(planItem.getEstimatedShipmentQuantity());
            shipmentItemEntity.setActualShipmentQuantity(shipmentSellerSKuTotalUnitMap.get(sellerSku));
            shipmentItemEntity.setSellerSku(sellerSku);
            shipmentItemEntity.setCreateBy(userName);
            shipmentItemEntity.setUpdateBy(userName);
            shipmentItemEntity.setErpSku(planItem.getErpSku());
            shipmentItemEntity.setBarcode(planItem.getBarcode());
            shipmentItemEntity.setGoodsId(plannedGood.getId());
            shipmentItemEntity.setGoodsName(plannedGood.getName());
            shipmentItemEntities.add(shipmentItemEntity);
        }
        shipmentItemEntityService.saveBatch(shipmentItemEntities);
    }

    public static Map<String, List<Integer>> parseShipmentBoxList(List<String> codes) {
        return codes.stream()
                .filter(code -> code.matches("^.+\\-C\\d+$")) // 过滤无效格式
                .collect(Collectors.groupingBy(
                        code -> code.substring(0, code.lastIndexOf('-')), // 提取 shipmentId
                        Collectors.mapping(
                                code -> Integer.parseInt(code.substring(code.lastIndexOf('C') + 1)), // 提取 boxSeq
                                Collectors.toList()
                        )
                ));
    }

    @Transactional(rollbackFor = Exception.class)
    public void sendBoxMessage(Integer shipmentPlanId, Map<String, List<Integer>> shipmentIdBoxIdxMap) {
        InboundPlanEntity shipmentPlan = inboundPlanService.getById(shipmentPlanId);
        if (shipmentPlan == null) {
            log.warn("shipmentPlanId[{}]在表fba_inbound_shipment_plan中不存在!", shipmentPlanId);
            return;
        }
        OmsShipmentBoxRelationMessage message = new OmsShipmentBoxRelationMessage();
        message.setErpTid(shipmentPlan.getErpTid());
        message.setLocation(shipmentPlan.getLocation());
        List<InboundShipmentEntity> planShipments = shipmentEntityService.getByPlanIds(Collections.singletonList(shipmentPlanId));
        List<OmsShipmentBoxRelationDto> staShipmentBoxRelationDtoList = new ArrayList<>();
        for (InboundShipmentEntity shipmentMpEntity : planShipments) {
            String shipmentId = shipmentMpEntity.getShipmentId();
            OmsShipmentBoxRelationDto relationDto = new OmsShipmentBoxRelationDto();
            relationDto.setShipmentId(shipmentId);
            relationDto.setLabelUrl(shipmentMpEntity.getFileUrl());
            relationDto.setShipToCountryCode(shipmentMpEntity.getShipToCountryCode());
            relationDto.setShipToPostalCode(shipmentMpEntity.getShipToPostalCode());
            relationDto.setShipToStateOrProvinceCode(shipmentMpEntity.getShipToStateOrProvinceCode());
            relationDto.setShipToCity(shipmentMpEntity.getShipToCity());
            relationDto.setShipToName(shipmentMpEntity.getShipToName());
            relationDto.setShipToAddressLine1(shipmentMpEntity.getShipToAddressLine1());
            relationDto.setCreateBy(shipmentMpEntity.getCreateBy());
            relationDto.setBoxIndexList(shipmentIdBoxIdxMap.get(shipmentId));
            relationDto.setDestinationFulfillmentCenterId(shipmentMpEntity.getDestinationFulfillmentCenter());
            staShipmentBoxRelationDtoList.add(relationDto);
        }
        message.setOmsShipmentBoxRelationDtoList(staShipmentBoxRelationDtoList);
        messageProducer.sendMessage("同步FBTShipment箱子关联关系", KafkaConstant.SYNC_OMS_SHIPMENT_AND_BOX_RELATIONSHIP_TOPIC, message);
    }

}
