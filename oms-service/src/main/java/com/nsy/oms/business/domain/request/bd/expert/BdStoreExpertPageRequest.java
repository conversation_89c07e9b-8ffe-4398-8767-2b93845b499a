package com.nsy.oms.business.domain.request.bd.expert;


import com.nsy.oms.business.manage.search.request.BaseListRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel("搜索店铺达人请求")
public class BdStoreExpertPageRequest extends BaseListRequest {
    @ApiModelProperty("昵称")
    private String nickname;
    @ApiModelProperty("邮箱")
    private String email;
    @ApiModelProperty("发送邮件状态：1成功2失败")
    private Integer contactStatus;
    @ApiModelProperty("粉丝性别比例")
    private String fansRatio;
    @ApiModelProperty("粉丝性别比例条件：1大于等于2小于")
    private Integer fansRatioCondition;
    @ApiModelProperty("粉丝性别比例类型：1男粉丝2女粉丝")
    private Integer fansRatioType;
    @ApiModelProperty("粉丝年龄分布")
    private String fansAgeRatio;
    @ApiModelProperty("粉丝年龄分布条件：1大于等于2小于")
    private Integer fansAgeRatioCondition;
    @ApiModelProperty("粉丝性别比例类型：1/2/3")
    private Integer fansAgeRatioType;

    @ApiModelProperty("昵称集合")
    private List<String> nicknameList;

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getContactStatus() {
        return contactStatus;
    }

    public void setContactStatus(Integer contactStatus) {
        this.contactStatus = contactStatus;
    }

    public String getFansRatio() {
        return fansRatio;
    }

    public void setFansRatio(String fansRatio) {
        this.fansRatio = fansRatio;
    }

    public Integer getFansRatioCondition() {
        return fansRatioCondition;
    }

    public void setFansRatioCondition(Integer fansRatioCondition) {
        this.fansRatioCondition = fansRatioCondition;
    }

    public Integer getFansRatioType() {
        return fansRatioType;
    }

    public void setFansRatioType(Integer fansRatioType) {
        this.fansRatioType = fansRatioType;
    }

    public String getFansAgeRatio() {
        return fansAgeRatio;
    }

    public void setFansAgeRatio(String fansAgeRatio) {
        this.fansAgeRatio = fansAgeRatio;
    }

    public Integer getFansAgeRatioCondition() {
        return fansAgeRatioCondition;
    }

    public void setFansAgeRatioCondition(Integer fansAgeRatioCondition) {
        this.fansAgeRatioCondition = fansAgeRatioCondition;
    }

    public Integer getFansAgeRatioType() {
        return fansAgeRatioType;
    }

    public void setFansAgeRatioType(Integer fansAgeRatioType) {
        this.fansAgeRatioType = fansAgeRatioType;
    }

    public List<String> getNicknameList() {
        return nicknameList;
    }

    public void setNicknameList(List<String> nicknameList) {
        this.nicknameList = nicknameList;
    }
}

