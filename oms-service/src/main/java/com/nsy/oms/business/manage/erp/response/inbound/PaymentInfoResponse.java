package com.nsy.oms.business.manage.erp.response.inbound;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: linCheng
 * @create: 2025-05-12 14:19
 **/
@Data
public class PaymentInfoResponse {

    @JsonProperty("RefundRequestId")
    private Integer refundRequestId;

    @JsonProperty("StoreId")
    private Integer storeId;

    @JsonProperty("TradePaymentInfoList")
    private List<TradePaymentInfo> tradePaymentInfoList;
}
