package com.nsy.oms.business.service.celebrity.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.oms.business.domain.response.celebrity.InternetCelebrityPostDailySearchRequest;
import com.nsy.oms.business.service.celebrity.InternetCelebritySampleOrderItemPostDailyService;
import com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderItemPostDailyEntity;
import com.nsy.oms.repository.sql.mapper.celebrity.InternetCelebritySampleOrderItemPostDailyMapper;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
public class InternetCelebritySampleOrderItemPostDailyServiceImpl
        extends ServiceImpl<InternetCelebritySampleOrderItemPostDailyMapper, InternetCelebritySampleOrderItemPostDailyEntity>
        implements InternetCelebritySampleOrderItemPostDailyService {

    @Override
    public List<InternetCelebritySampleOrderItemPostDailyEntity> postDailyGmv(List<Integer> postIds, InternetCelebrityPostDailySearchRequest request) {
        LambdaQueryWrapper<InternetCelebritySampleOrderItemPostDailyEntity> queryWrapper = new LambdaQueryWrapper<InternetCelebritySampleOrderItemPostDailyEntity>()
                .in(InternetCelebritySampleOrderItemPostDailyEntity::getPostId, postIds);
        if (Objects.nonNull(request.getGmvStartDate())) {
            queryWrapper.ge(InternetCelebritySampleOrderItemPostDailyEntity::getGmvDate, request.getGmvStartDate());
        }
        if (Objects.nonNull(request.getGmvEndDate())) {
            queryWrapper.le(InternetCelebritySampleOrderItemPostDailyEntity::getGmvDate, request.getGmvEndDate());
        }
        return this.list(queryWrapper);
    }

    @Override
    public InternetCelebritySampleOrderItemPostDailyEntity findTopByPostIdAndDate(Integer postId, Date date) {
        return this.getOne(new QueryWrapper<InternetCelebritySampleOrderItemPostDailyEntity>().lambda()
                .eq(InternetCelebritySampleOrderItemPostDailyEntity::getPostId, postId)
                .eq(InternetCelebritySampleOrderItemPostDailyEntity::getGmvDate, date)
                .last("limit 1")
        );
    }

    @Override
    public List<InternetCelebritySampleOrderItemPostDailyEntity> findAllByPostIdIn(List<Integer> postIds) {
        return this.list(new QueryWrapper<InternetCelebritySampleOrderItemPostDailyEntity>().lambda()
                .in(InternetCelebritySampleOrderItemPostDailyEntity::getPostId, postIds)
        );
    }
}