package com.nsy.oms.business.service.platform.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.oms.business.service.bd.BdMarketplaceService;
import com.nsy.oms.business.service.platform.PlatformOrderExtendService;
import com.nsy.oms.repository.entity.bd.BdMarketplaceEntity;
import com.nsy.oms.repository.entity.platform.PlatformOrderEntity;
import com.nsy.oms.repository.entity.platform.PlatformOrderExtendEntity;
import com.nsy.oms.repository.sql.mapper.platform.PlatformOrderExtendMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【platform_order_extend(平台订单扩展表)】的数据库操作Service实现
* @createDate 2024-10-17 14:16:26
*/
@Service
public class PlatformOrderExtendServiceImpl extends ServiceImpl<PlatformOrderExtendMapper, PlatformOrderExtendEntity>
    implements PlatformOrderExtendService {
    @Autowired
    private BdMarketplaceService marketplaceService;

    @Override
    public PlatformOrderExtendEntity getByPlatformOrderNo(String platformOrderNo) {
        return getOne(Wrappers.<PlatformOrderExtendEntity>lambdaQuery().eq(PlatformOrderExtendEntity::getPlatformOrderNo, platformOrderNo));
    }

    /**
     * 保存(平台仓发货)订单表头扩展表
     */
    @Override
    public void saveOrUpdatePlatformExtend(PlatformOrderEntity platformOrderEntity, String updateBy) {
        if (ObjectUtil.isNull(platformOrderEntity) && StringUtils.isBlank(platformOrderEntity.getMarketCode())) {
            return;
        }
        BdMarketplaceEntity marketplaceEntity = marketplaceService.getByMarketplaceId(platformOrderEntity.getMarketCode());
        if (ObjectUtil.isNull(marketplaceEntity)) {
            return;
        }
        PlatformOrderExtendEntity orderExtendEntity = this.getByPlatformOrderNo(platformOrderEntity.getPlatformOrderNo());
        if (ObjectUtil.isNull(orderExtendEntity)) {
            orderExtendEntity = new PlatformOrderExtendEntity();
            orderExtendEntity.setCreateBy(updateBy);
        }
        if (ObjectUtil.isNull(orderExtendEntity.getPlatformOrderId())) {
            orderExtendEntity.setPlatformOrderId(platformOrderEntity.getPlatformOrderId());
            orderExtendEntity.setPlatformOrderNo(platformOrderEntity.getPlatformOrderNo());
        }
        if (ObjectUtil.isNotNull(platformOrderEntity.getOrderCreateDate())) {
            orderExtendEntity.setOrderCreateDateTimeZone(com.nsy.oms.utils.DateUtils.getTimeByTimeZone(platformOrderEntity.getOrderCreateDate(), marketplaceEntity.getTimeZoneId()));
        }
        if (ObjectUtil.isNotNull(platformOrderEntity.getOrderDeliverDate())) {
            orderExtendEntity.setOrderDeliverDateTimeZone(com.nsy.oms.utils.DateUtils.getTimeByTimeZone(platformOrderEntity.getOrderDeliverDate(), marketplaceEntity.getTimeZoneId()));
        }
        if (ObjectUtil.isNotNull(platformOrderEntity.getOrderPaymentDate())) {
            orderExtendEntity.setOrderPaymentDateTimeZone(com.nsy.oms.utils.DateUtils.getTimeByTimeZone(platformOrderEntity.getOrderPaymentDate(), marketplaceEntity.getTimeZoneId()));
        }
        if (ObjectUtil.isNotNull(platformOrderEntity.getOrderCancelDate())) {
            orderExtendEntity.setOrderCancelDateTimeZone(com.nsy.oms.utils.DateUtils.getTimeByTimeZone(platformOrderEntity.getOrderCancelDate(), marketplaceEntity.getTimeZoneId()));
        }
        orderExtendEntity.setLocation(platformOrderEntity.getLocation());
        orderExtendEntity.setUpdateBy(updateBy);
        this.saveOrUpdate(orderExtendEntity);
    }
}




