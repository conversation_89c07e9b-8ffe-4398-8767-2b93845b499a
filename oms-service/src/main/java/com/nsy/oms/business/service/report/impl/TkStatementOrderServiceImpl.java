package com.nsy.oms.business.service.report.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.transfer.domain.common.TiktokAuthResponse;
import com.nsy.api.transfer.domain.request.platform.tiktok.TikTokShopStatementOrdersRequest;
import com.nsy.api.transfer.domain.response.platform.tiktok.statement.TikTokShopOrderTransactionsResponse;
import com.nsy.api.transfer.enums.PlatformTypeEnum;
import com.nsy.oms.business.domain.dto.TkStatementOrderTransactionParameterDTO;
import com.nsy.oms.business.manage.transfer.TransferApiService;
import com.nsy.oms.business.service.report.TkStatementOrderService;
import com.nsy.oms.repository.mongo.document.tiktok.ReportTkStatementOrderTransactionDocument;
import com.nsy.oms.repository.mongo.document.tiktok.ReportTkStatementTransactionDocument;
import com.nsy.oms.repository.mongo.repository.tiktok.ReportTkStatementOrderTransactionDocumentRepository;
import com.nsy.oms.repository.mongo.repository.tiktok.ReportTkStatementTransactionDocumentRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class TkStatementOrderServiceImpl implements TkStatementOrderService {

    @Autowired
    private ReportTkStatementTransactionDocumentRepository reportTkStatementTransactionDocumentRepository;

    @Autowired
    private ReportTkStatementOrderTransactionDocumentRepository reportTkStatementOrderTransactionDocumentRepository;

    @Autowired
    private TransferApiService transferApiService;

    @Override
    public void saveTransactionOrderRecords(List<TiktokAuthResponse> data, TkStatementOrderTransactionParameterDTO baseParameterDTO) {
        if (!CollectionUtils.isEmpty(data)) {
            data.forEach(tiktokAuthResponse -> {
                // 如果不为空就只执行一个店铺
                if (Objects.nonNull(baseParameterDTO.getStoreId()) && !tiktokAuthResponse.getStoreId().equals(baseParameterDTO.getStoreId())) {
                    return;
                }
                doProcess(tiktokAuthResponse, baseParameterDTO);
            });
        }


    }

    private void doProcess(TiktokAuthResponse item, TkStatementOrderTransactionParameterDTO baseParameterDTO) {
        if (StringUtils.isNotBlank(item.getAccessToken())) {
            Pageable pageable = PageRequest.of(0, 200);
            List<ReportTkStatementTransactionDocument> list = reportTkStatementTransactionDocumentRepository.findAllByOrderIdStatusOrderByCreateDateDesc(0, pageable);
            if (!CollectionUtils.isEmpty(list)) {
                Lists.partition(list, baseParameterDTO.getFetchCount()).forEach(batch -> {
                    batch.forEach(reportTkStatementTransactionDocument -> {
                        try {
                            saveRecord(item, reportTkStatementTransactionDocument);
                        } catch (InterruptedException e) {
                            throw new BusinessServiceException("获取tk账单定单详情失败！");
                        }
                    });

                });
            }
        }
    }

    private void saveRecord(TiktokAuthResponse item, ReportTkStatementTransactionDocument reportTkStatementTransactionDocument) throws InterruptedException {
        PlatformTypeEnum platformTypeEnum = PlatformTypeEnum.getPlatform(item.getPlatformId());
        if (platformTypeEnum == null) {
            log.error("根据ID获取平台失败！");
            return;
        }
        TikTokShopOrderTransactionsResponse tikTokResponse = null;
        int maxRetries = 5;
        int currentRetry = 0;
        boolean success = false;

        while (!success && currentRetry < maxRetries) {
            try {
                TikTokShopStatementOrdersRequest request = new TikTokShopStatementOrdersRequest();
                request.setPlatform(platformTypeEnum);
                request.setLocation(reportTkStatementTransactionDocument.getLocation());
                request.setOrderId(reportTkStatementTransactionDocument.getOrderId());
                request.setStoreId(item.getStoreId());
                tikTokResponse = transferApiService.getStatementTransactionOrder(platformTypeEnum.getUrl(), request, item);
                success = true;
                currentRetry = 0; // 请求成功后重置重试次数
            } catch (Exception e) {
                currentRetry++;
                log.error("获取tk账单交易详情失败，当前重试次数：{}，错误信息：{}", currentRetry, e.getMessage());
                if (currentRetry < maxRetries) {
                    Thread.sleep(5000); // 等待5秒后重试
                } else {
                    log.error("获取tk账单交易详情失败，已达到最大重试次数：{}", maxRetries);
                    throw new BusinessServiceException("获取tk账单交易详情失败，已达到最大重试次数");
                }
            }
        }
        if (Objects.nonNull(tikTokResponse) && Objects.nonNull(tikTokResponse.getData()) && tikTokResponse.getCode() == 0) {
            ReportTkStatementOrderTransactionDocument document = reportTkStatementOrderTransactionDocumentRepository.findAllByStoreIdAndOrderId(reportTkStatementTransactionDocument.getStoreId(), reportTkStatementTransactionDocument.getOrderId());
            if (Objects.nonNull(document)) {
                reportTkStatementOrderTransactionDocumentRepository.delete(document);
            }
            ReportTkStatementOrderTransactionDocument entity = new ReportTkStatementOrderTransactionDocument();
            entity.setStoreId(reportTkStatementTransactionDocument.getStoreId());
            entity.setStoreName(reportTkStatementTransactionDocument.getStoreName());
            entity.setOrderId(reportTkStatementTransactionDocument.getOrderId());
            entity.setContent(JSONObject.toJSONString(tikTokResponse.getData()));
            entity.setLocation(reportTkStatementTransactionDocument.getLocation());
            entity.setCreateDate(new Date());
            entity.setUpdateDate(new Date());
            reportTkStatementOrderTransactionDocumentRepository.save(entity);
            reportTkStatementTransactionDocument.setOrderIdStatus(1);
            reportTkStatementTransactionDocumentRepository.save(reportTkStatementTransactionDocument);
            Thread.sleep(2000); // 等待2秒
        }

    }
}
