package com.nsy.oms.business.domain.request.rule;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @description:
 * @author: linCheng
 * @create: 2024-11-20 11:54
 **/
@Data
public class GetStockShareRequest {
    /**
     * : B2C、内贸、B2B、分公司
     */
    @ApiModelProperty("部门名称")
    @NotBlank(message = "部门名称不允许为空")
    private String departmentName;

}
