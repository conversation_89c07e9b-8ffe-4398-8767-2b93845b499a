package com.nsy.oms.business.service.upload.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.pms.dto.product.ProductSpecDTO;
import com.nsy.oms.business.domain.request.upload.UploadRequest;
import com.nsy.oms.business.domain.response.upload.UploadResponse;
import com.nsy.oms.business.domain.upload.OrderRuleCodeImport;
import com.nsy.oms.business.manage.erp.ErpApiService;
import com.nsy.oms.business.manage.erp.domain.SpaceInfo;
import com.nsy.oms.business.manage.erp.response.SpaceInfoResponse;
import com.nsy.oms.business.manage.pms.PmsApiService;
import com.nsy.oms.business.service.bd.BdOrderRuleCodeService;
import com.nsy.oms.business.service.bd.BdOrderRuleItemService;
import com.nsy.oms.business.service.upload.IUploadService;
import com.nsy.oms.constants.upload.QuartzUploadQueueTypeEnum;
import com.nsy.oms.enumstable.OrderRuleCodeRuleTypeEnum;
import com.nsy.oms.repository.entity.bd.BdOrderRuleCodeEntity;
import com.nsy.oms.repository.entity.bd.BdOrderRuleItemEntity;
import com.nsy.oms.utils.JsonMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: linCheng
 * @create: 2024-05-28 15:53
 **/
@Slf4j
@Service
public class OrderRuleCodeBySpaceEliminateUploadService implements IUploadService {

    @Autowired
    private ErpApiService erpApiService;
    @Autowired
    private PmsApiService pmsApiService;
    @Autowired
    private BdOrderRuleCodeService bdOrderRuleCodeService;
    @Autowired
    private BdOrderRuleItemService orderRuleItemService;

    @Override
    public QuartzUploadQueueTypeEnum uploadType() {
        return QuartzUploadQueueTypeEnum.ORDER_RULE_CODE_BY_SPACE_ELIMINATE;
    }


    @Override
    public UploadResponse processUploadData(UploadRequest request) {
        log.error("OrderRuleCodeBySpaceEliminateUploadService.processUploadData.request:{}", JSON.toJSONString(request));

        UploadResponse uploadResponse = new UploadResponse();
        if (!StringUtils.hasText(request.getDataJsonStr()) || !StringUtils.hasText(request.getUploadParams())) {
            return uploadResponse;
        }

        List<OrderRuleCodeImport> importList = JsonMapper.jsonStringToObjectArray(request.getDataJsonStr(), OrderRuleCodeImport.class).stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(importList)) {
            return uploadResponse;
        }

        Map paramMap = JsonMapper.fromJson(request.getUploadParams(), Map.class);
        String orderRuleId = (String) paramMap.get("orderRuleId");
        String codeType = (String) paramMap.get("codeType");
        if (!StringUtils.hasText(orderRuleId)) {
            throw new BusinessServiceException("订单规则不能为空");
        }
        if (!StringUtils.hasText(codeType)) {
            throw new BusinessServiceException("订单规则编码类型不能为空");
        }

        //校验
        SpaceInfoResponse spaceInfoResponse = erpApiService.querySpaceInfoList(Collections.singletonList(request.getLocation().toUpperCase(Locale.ROOT)), "HWDF");
        Map<String, Integer> spaceMap = spaceInfoResponse.getSpaceInfoList().stream().collect(Collectors.toMap(SpaceInfo::getSpaceName, SpaceInfo::getSpaceId, (value1, value2) -> value2));

        List<OrderRuleCodeImport> errorList = new ArrayList<>();
        check(spaceMap, orderRuleId, codeType, importList, errorList);
        if (CollectionUtils.isNotEmpty(errorList)) {
            uploadResponse.setDataJsonStr(JsonMapper.toJson(errorList));
            return uploadResponse;
        }

        //存数据
        process(importList, orderRuleId, codeType, request.getCreateBy());

        return uploadResponse;
    }

    private void process(List<OrderRuleCodeImport> importList,
                         String orderRuleId,
                         String codeType,
                         String createBy) {
        Map<Integer, List<OrderRuleCodeImport>> orderRuleCodeMap = importList.stream().collect(Collectors.groupingBy(OrderRuleCodeImport::getSpaceId));
        orderRuleCodeMap.forEach((spaceId, orderRuleCodeImports) -> {
            List<BdOrderRuleCodeEntity> bdOrderRuleCodeEntities = new ArrayList<>();
            List<BdOrderRuleCodeEntity> bdOrderRuleCodeEntityList = bdOrderRuleCodeService.getByOrderRuleIdAndCodeTypeAndRuleType(Integer.valueOf(orderRuleId), codeType, OrderRuleCodeRuleTypeEnum.SPACE.getCode());
            Map<String, BdOrderRuleCodeEntity> bdOrderRuleCodeMap = bdOrderRuleCodeEntityList.stream().collect(Collectors.toMap(item -> StrUtil.format("{}_{}", item.getSpaceName(), item.getCode()), Function.identity(), (k1, k2) -> k1));
            orderRuleCodeImports.forEach(orderRuleCodeImport -> {
                BdOrderRuleCodeEntity bdOrderRuleCodeEntity = Optional.ofNullable(bdOrderRuleCodeMap.get(StrUtil.format("{}_{}", orderRuleCodeImport.getSpaceName(), orderRuleCodeImport.getCode()))).map(entity -> {
                    setBdOrderRuleCode(entity, orderRuleCodeImport);
                    entity.setOrderRuleId(Integer.valueOf(orderRuleId));
                    entity.setCodeType(codeType);
                    entity.setUpdateBy(createBy);
                    entity.setUpdateDate(new Date());
                    return entity;
                }).orElseGet(() -> {
                    BdOrderRuleCodeEntity entity = new BdOrderRuleCodeEntity();
                    setBdOrderRuleCode(entity, orderRuleCodeImport);
                    entity.setOrderRuleId(Integer.valueOf(orderRuleId));
                    entity.setCodeType(codeType);
                    entity.setCreateBy(createBy);
                    entity.setCreateDate(new Date());
                    return entity;
                });
                bdOrderRuleCodeEntity.setRuleType(OrderRuleCodeRuleTypeEnum.SPACE.getCode());
                bdOrderRuleCodeEntities.add(bdOrderRuleCodeEntity);
            });
            bdOrderRuleCodeService.saveOrUpdateBatch(bdOrderRuleCodeEntities);
        });

    }

    private void setBdOrderRuleCode(BdOrderRuleCodeEntity bdOrderRuleCodeEntity, OrderRuleCodeImport orderRuleCodeImport) {
        bdOrderRuleCodeEntity.setCode(orderRuleCodeImport.getCode());
        bdOrderRuleCodeEntity.setSpaceName(orderRuleCodeImport.getSpaceName());
        bdOrderRuleCodeEntity.setSpaceId(orderRuleCodeImport.getSpaceId());
        bdOrderRuleCodeEntity.setIsDelivery(!Optional.ofNullable(orderRuleCodeImport.getIsDelivery()).isPresent() || 1 == orderRuleCodeImport.getIsDelivery() ? Boolean.TRUE : Boolean.FALSE);
        bdOrderRuleCodeEntity.setReserveInventoryType(Optional.ofNullable(orderRuleCodeImport.getReserveInventoryType()).isPresent() ? orderRuleCodeImport.getReserveInventoryType() : 1);
        bdOrderRuleCodeEntity.setReserveInventory(Optional.ofNullable(orderRuleCodeImport.getReserveInventory()).isPresent() ? orderRuleCodeImport.getReserveInventory() : 0);
    }

    private void check(Map<String, Integer> spaceMap,
                       String orderRuleId,
                       String codeType,
                       List<OrderRuleCodeImport> importList,
                       List<OrderRuleCodeImport> errorList) {

        List<BdOrderRuleItemEntity> itemEntities = orderRuleItemService.getByOrderRuleIdAndItemType(Integer.valueOf(orderRuleId), OrderRuleCodeRuleTypeEnum.SPACE.getCode());
        List<Integer> itemTargetIds = CollectionUtils.isEmpty(itemEntities) ? Collections.emptyList() : itemEntities.stream().map(BdOrderRuleItemEntity::getItemTargetId).collect(Collectors.toList());

        List<String> codeList = new ArrayList<>();
        if ("skc".equalsIgnoreCase(codeType)) {
            codeList = pmsApiService.getSkcInfo(importList.stream().map(OrderRuleCodeImport::getCode).collect(Collectors.toList())).stream().map(ProductSpecDTO::getColorSku).distinct().collect(Collectors.toList());
        } else if ("sku".equalsIgnoreCase(codeType)) {
            codeList = pmsApiService.specInfo(importList.stream().map(OrderRuleCodeImport::getCode).collect(Collectors.toList())).stream().map(ProductSpecDTO::getSpecSku).distinct().collect(Collectors.toList());
        }

        for (OrderRuleCodeImport orderRuleCodeImport : importList) {
            Integer spaceId = spaceMap.get(orderRuleCodeImport.getSpaceName());
            if (!Optional.ofNullable(spaceId).isPresent()) {
                orderRuleCodeImport.setErrorMsg("海外仓不存在");
                errorList.add(orderRuleCodeImport);
                continue;
            } else {
                orderRuleCodeImport.setSpaceId(spaceId);
            }
            if (!codeList.contains(orderRuleCodeImport.getCode())) {
                orderRuleCodeImport.setErrorMsg(String.format("%s不存在", codeType));
                errorList.add(orderRuleCodeImport);
            }
            if (Optional.ofNullable(orderRuleCodeImport.getReserveInventory()).isPresent() && !Optional.ofNullable(orderRuleCodeImport.getReserveInventoryType()).isPresent()) {
                orderRuleCodeImport.setErrorMsg("库存有值,保留库存类型为必填");
                errorList.add(orderRuleCodeImport);
            }
            if (Optional.ofNullable(orderRuleCodeImport.getReserveInventoryType()).isPresent()
                    && 1 == orderRuleCodeImport.getReserveInventoryType()
                    && Optional.ofNullable(orderRuleCodeImport.getReserveInventory()).isPresent()
                    && orderRuleCodeImport.getReserveInventory() > 100) {
                orderRuleCodeImport.setErrorMsg("按比例不能大于100");
                errorList.add(orderRuleCodeImport);
            }
            if (!itemTargetIds.contains(orderRuleCodeImport.getSpaceId())) {
                orderRuleCodeImport.setErrorMsg("发货渠道未配置对应海外仓");
                errorList.add(orderRuleCodeImport);
            }
        }
    }
}
