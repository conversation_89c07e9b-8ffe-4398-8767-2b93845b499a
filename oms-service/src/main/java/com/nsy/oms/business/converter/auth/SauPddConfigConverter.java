package com.nsy.oms.business.converter.auth;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.oms.business.domain.request.auth.SauPddConfigRequest;
import com.nsy.oms.business.domain.response.auth.SauPddConfigPageResponse;
import com.nsy.oms.business.domain.response.auth.SauPddConfigResponse;
import com.nsy.oms.repository.entity.auth.SauPddConfigEntity;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SauPddConfigConverter {

    SauPddConfigConverter INSTANCE = Mappers.getMapper(SauPddConfigConverter.class);

    SauPddConfigPageResponse toSauPddConfigPageResponse(SauPddConfigEntity bo);

    SauPddConfigEntity toSauPddConfigEntity(SauPddConfigEntity bo);

    SauPddConfigEntity toSauPddConfigEntity(SauPddConfigRequest request);

    SauPddConfigResponse toSauPddConfigResponse(SauPddConfigEntity bo);

    @InheritConfiguration(name = "toSauPddConfigPageResponse")
    Page<SauPddConfigPageResponse> toSauPddConfigPageResponsePage(IPage<SauPddConfigEntity> page);

}
