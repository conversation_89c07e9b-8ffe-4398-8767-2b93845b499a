package com.nsy.oms.business.domain.request.download;


import com.nsy.api.oms.enums.QuartzDownloadQueueTypeEnum;

public class DownloadRequest {
    /**
     * 类型
     */
    private QuartzDownloadQueueTypeEnum type;

    /**
     * 请求内容
     */
    private String requestContent;

    /**
     * 每页条数
     */
    private Integer pageSize;

    /**
     * 页号
     */
    private Integer pageIndex;

    /**
     * 地区
     */
    private String location;

    /**
     * 创建人姓名
     */
    private String requestedBy;

    public String getRequestedBy() {
        return requestedBy;
    }

    public void setRequestedBy(String requestedBy) {
        this.requestedBy = requestedBy;
    }

    public QuartzDownloadQueueTypeEnum getType() {
        return type;
    }

    public void setType(QuartzDownloadQueueTypeEnum type) {
        this.type = type;
    }

    public String getRequestContent() {
        return requestContent;
    }

    public void setRequestContent(String requestContent) {
        this.requestContent = requestContent;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }
}

