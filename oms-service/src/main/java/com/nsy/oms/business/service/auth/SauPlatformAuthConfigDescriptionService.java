package com.nsy.oms.business.service.auth;

import com.nsy.oms.business.domain.request.auth.PlatformAuthConfigDescriptionPageRequest;
import com.nsy.oms.business.domain.request.auth.PlatformAuthConfigDescriptionRequest;
import com.nsy.oms.business.domain.response.auth.PlatformAuthConfigDescriptionPageResponse;
import com.nsy.oms.business.domain.response.auth.SauPlatformAuthConfigDescriptionResponse;
import com.nsy.oms.business.domain.response.base.PageResponse;

/**
 * <AUTHOR>
 * @date 2023/3/21 11:02
 */
public interface SauPlatformAuthConfigDescriptionService {
    /**
     * 获取列表
     * @param request
     * @return
     */
    PageResponse<PlatformAuthConfigDescriptionPageResponse> pageList(PlatformAuthConfigDescriptionPageRequest request);

    /**
     * 保存数据
     * @param request
     */
    void save(PlatformAuthConfigDescriptionRequest request);

    /**
     * 更新数据
     * @param request
     */
    void update(PlatformAuthConfigDescriptionRequest request);

    /**
     * 获取详情
     * @param id
     * @return
     */
    SauPlatformAuthConfigDescriptionResponse info(Integer id);

    /**
     * 获取平台的描述
     * @param platformId
     * @return
     */
    SauPlatformAuthConfigDescriptionResponse getPlatformDesc(String platformId);

    /**
     * 判断是否存在
     * @param platformId
     * @return
     */
    boolean exist(String platformId);
}
