package com.nsy.oms.business.manage.erp;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSON;
import com.nsy.api.core.apicore.util.NsyJacksonUtils;
import com.nsy.oms.business.manage.erp.domain.GetCompanyStockViewDto;
import com.nsy.oms.business.manage.erp.domain.GetSampleOrderResponse;
import com.nsy.oms.business.manage.erp.domain.UserStore;
import com.nsy.oms.business.manage.erp.request.GetCompanyStockViewRequest;
import com.nsy.oms.business.manage.erp.request.GetSampleOrderRequest;
import com.nsy.oms.business.manage.erp.request.GetSkuInfoListRequest;
import com.nsy.oms.business.manage.erp.request.GetSpaceStockRequest;
import com.nsy.oms.business.manage.erp.request.PrivateStockByBusinessTypeAndSkuRequest;
import com.nsy.oms.business.manage.erp.request.SpaceRequest;
import com.nsy.oms.business.manage.erp.request.inbound.AddFbaShipmentPlanRequest;
import com.nsy.oms.business.manage.erp.request.inbound.CancelFbaShipmentPlanRequest;
import com.nsy.oms.business.manage.erp.request.inbound.GetPaymentInfoRequest;
import com.nsy.oms.business.manage.erp.request.inbound.SetRefundOrderInfoRequest;
import com.nsy.oms.business.manage.erp.response.DistributorResponse;
import com.nsy.oms.business.manage.erp.response.ErpSpaceInfoResponse;
import com.nsy.oms.business.manage.erp.response.GetCompanyStockViewResponse;
import com.nsy.oms.business.manage.erp.response.GetSkuInfoListResponse;
import com.nsy.oms.business.manage.erp.response.GetSpaceStockResponse;
import com.nsy.oms.business.manage.erp.response.PrivateStockByBusinessTypeAndSkuResponse;
import com.nsy.oms.business.manage.erp.response.Sku;
import com.nsy.oms.business.manage.erp.response.SpaceInfoResponse;
import com.nsy.oms.business.manage.erp.response.StoreData;
import com.nsy.oms.business.manage.erp.response.StoreListResponse;
import com.nsy.oms.business.manage.erp.response.inbound.AddFbaShipmentPlanResponse;
import com.nsy.oms.business.manage.erp.response.inbound.PaymentInfoResponse;
import com.nsy.oms.business.manage.erp.response.store.RrpStoreDeliveryAddressResponse;
import com.nsy.oms.utils.JsonMapper;
import com.nsy.oms.utils.mp.LocationContext;
import org.apache.commons.collections4.ListUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class ErpApiService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ErpApiService.class);
    @Inject
    ErpApiRestClient erpApiRestClient;

    @Value("${nsy.service.url.erp}")
    private String erpApiServiceUrl;

    /**
     * 获取分销商管理数据
     *
     * @param location
     */
    public List<DistributorResponse> getDistributors(String location) {
        try {
            Map<String, String> param = new HashMap<>();
            param.put("Location", location);
            String response = erpApiRestClient.buildApi(erpApiServiceUrl, "/Store/GetDistributorsByQuery").post(param, String.class);
            List<DistributorResponse> distributorResponseList = NsyJacksonUtils.jsonToList(response, DistributorResponse.class);
            LOGGER.debug("response的 数据为 {}", NsyJacksonUtils.toJson(distributorResponseList));
            return distributorResponseList;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return new ArrayList<>();
    }


    /**
     * 获取默认发布地址
     */
    public List<RrpStoreDeliveryAddressResponse> getDeliveryAddress() {
        List<RrpStoreDeliveryAddressResponse> erpStoreResponses = new ArrayList<>();
        try {
            String response = erpApiRestClient.buildApi(erpApiServiceUrl, "/Store/GetDeliveryAddress").get(String.class);
            if (StringUtil.isNotBlank(response)) {
                erpStoreResponses = NsyJacksonUtils.jsonToList(response, RrpStoreDeliveryAddressResponse.class);
                LOGGER.debug("response的 数据为 {}", erpStoreResponses.size());
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return erpStoreResponses;
    }

    public List<GetCompanyStockViewDto> getCompanyStockBySkus(List<String> skuList) {
        try {
            GetCompanyStockViewRequest request = new GetCompanyStockViewRequest();
            request.setSkuList(skuList);
            request.setLocation(LocationContext.getLocation());
            GetCompanyStockViewResponse response = erpApiRestClient
                    .buildApi(erpApiServiceUrl, "/Stock/GetCompanyStockBySkus").post(request, GetCompanyStockViewResponse.class);
            return Optional.ofNullable(response)
                    .map(GetCompanyStockViewResponse::getCompanyStockViewList)
                    .orElseGet(Collections::emptyList);
        } catch (RuntimeException e) {
            LOGGER.error("getCompanyStockBySkus error, cause = {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    public PrivateStockByBusinessTypeAndSkuResponse queryPrivateStockByBusinessTypeAndSku(String businessType, List<String> skuList) {
        PrivateStockByBusinessTypeAndSkuRequest request = new PrivateStockByBusinessTypeAndSkuRequest();
        request.setBusinessType(businessType);
        request.setSkuList(skuList);
        request.setLocation(LocationContext.getLocation());
        PrivateStockByBusinessTypeAndSkuResponse response = new PrivateStockByBusinessTypeAndSkuResponse();
        try {
            String resStr = erpApiRestClient.buildApi(erpApiServiceUrl, "/Stock/PrivateStockByBusinessTypeAndSku").post(request, String.class);
            response.setData(JsonMapper.jsonStringToObjectArray(resStr, PrivateStockByBusinessTypeAndSkuResponse.PrivateStockByBusinessTypeAndSkuDto.class));
        } catch (RuntimeException e) {
            LOGGER.error("PrivateStockByBusinessTypeAndSku error, cause = {}", e.getMessage(), e);
        }
        return response;
    }

    /**
     * 获取分销商管理数据
     */
    public List<ErpSpaceInfoResponse.ErpSpaceInfo> getErpSpaceInfoList() {
        try {
            ErpSpaceInfoResponse response = erpApiRestClient.buildApi(erpApiServiceUrl, "/Space/GetSpaceList").get(ErpSpaceInfoResponse.class);
            return response.getSpaceInfoList();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    public UserStore getErpUserStoreResponse(String username) {
        try {
            UserStore userStore = erpApiRestClient.buildApi(erpApiServiceUrl, String.format("/User/GetUserStoreList?userName=%s&filterDisALock=false", username))
                    .get(UserStore.class);
            return Optional.ofNullable(userStore).orElseGet(UserStore::new);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return new UserStore();
    }

    /**
     * 获取用户（权限过滤） 的店铺数据
     */
    public List<StoreData> getPermissionStoreList(String userName) {
        try {
            StoreListResponse response = erpApiRestClient.buildApi(erpApiServiceUrl,
                    "/User/GetPermissionStoreList?userName=" + userName).get(StoreListResponse.class);
            return response.getList();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    public GetSampleOrderResponse getSampleOrder(GetSampleOrderRequest request) {
        try {
            LOGGER.info("ErpApiService.GetSampleOrder.request 数据为:{}", NsyJacksonUtils.toJson(request));
            GetSampleOrderResponse getTradeInfoByEmailResponse = erpApiRestClient.buildApi(erpApiServiceUrl, "/Trade/GetSampleOrder").post(request, GetSampleOrderResponse.class);
            LOGGER.info("ErpApiService.GetSampleOrder.response的 数据为:{}", NsyJacksonUtils.toJson(getTradeInfoByEmailResponse));
            return getTradeInfoByEmailResponse;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return new GetSampleOrderResponse();
    }

    public List<Sku> getSkuInfoList(String... skuArray) {
        return ListUtils.partition(Arrays.asList(skuArray), 100).stream()
                .map(skuList -> {
                    GetSkuInfoListRequest skuInfoListRequest = new GetSkuInfoListRequest();
                    skuInfoListRequest.setErpSkuInfoList(skuList.stream().map(erpSku -> {
                        GetSkuInfoListRequest.ErpSkuInfoObj erpSkuInfoObj = new GetSkuInfoListRequest.ErpSkuInfoObj();
                        erpSkuInfoObj.setSettlementPriceDate(new Date());
                        erpSkuInfoObj.setErpSku(erpSku);
                        return erpSkuInfoObj;
                    }).collect(Collectors.toList()));
                    return getSkuInfoList(skuInfoListRequest);
                }).flatMap(List::stream).collect(Collectors.toList());
    }


    public List<Sku> getSkuInfoList(GetSkuInfoListRequest getSkuInfoListRequest) {
        GetSkuInfoListResponse response = erpApiRestClient.buildApi(erpApiServiceUrl, "/Product/GetSkuInfoList")
                .post(getSkuInfoListRequest, GetSkuInfoListResponse.class);
        return Optional.ofNullable(response.getErpSku()).orElseGet(Collections::emptyList);
    }


    public GetSpaceStockResponse getStockInfoBySpaceList(GetSpaceStockRequest getSpaceStockRequest) {
        LOGGER.info("ErpApiNewService.getStockInfoBySpaceList.getSpaceStockRequest:{}", JSON.toJSONString(getSpaceStockRequest));
        GetSpaceStockResponse getSpaceStockResponse = erpApiRestClient.buildApi(erpApiServiceUrl, "/Stock/GetSpaceStockList").post(getSpaceStockRequest, GetSpaceStockResponse.class);
        LOGGER.info("ErpApiNewService.getStockInfoBySpaceList.getSpaceStockResponse:{}", JSON.toJSONString(getSpaceStockResponse));
        return getSpaceStockResponse;
    }

    public GetSpaceStockResponse getStockInfo(GetSpaceStockRequest getSpaceStockRequest) {
        LOGGER.info("ErpApiNewService.getStockInfo.getSpaceStockRequest:{}", JSON.toJSONString(getSpaceStockRequest));
        GetSpaceStockResponse getSpaceStockResponse = erpApiRestClient.buildApi(erpApiServiceUrl, "/Stock/GetSpaceStock")
                .post(getSpaceStockRequest, GetSpaceStockResponse.class);
        LOGGER.info("ErpApiNewService.getStockInfo.getSpaceStockResponse:{}", JSON.toJSONString(getSpaceStockResponse));
        return getSpaceStockResponse;
    }

    /**
     * 补货单创建， 为了占库存
     */
    public AddFbaShipmentPlanResponse addFbaShipmentPlan(AddFbaShipmentPlanRequest addFbaShipmentPlanRequest) {
        return erpApiRestClient.buildApi(erpApiServiceUrl, "/Trade/AddFbaShipmentPlan").post(addFbaShipmentPlanRequest, AddFbaShipmentPlanResponse.class);
    }

    public void cancelFbaShipmentPlan(List<String> deliveryNumbers, Integer type) {
        if (deliveryNumbers.isEmpty()) {
            return;
        }
        CancelFbaShipmentPlanRequest cancelFbaShipmentPlanRequest = new CancelFbaShipmentPlanRequest();
        cancelFbaShipmentPlanRequest.setTids(deliveryNumbers);
        cancelFbaShipmentPlanRequest.setType(type);
        erpApiRestClient.buildApi(erpApiServiceUrl, "/Trade/CancelFbaShipmentPlan").post(cancelFbaShipmentPlanRequest);
    }


    /**
     * 查询仓库信息
     *
     * @param spaceRequest 请求体
     * @return 仓库信息
     */
    public SpaceInfoResponse querySpaceInfoList(List<String> locationList, String spaceFeatures) {
        SpaceRequest spaceRequest = new SpaceRequest(locationList, spaceFeatures);
        SpaceInfoResponse post = erpApiRestClient.buildApi(erpApiServiceUrl, "/Space/GetSpaceListByQuery").post(spaceRequest, SpaceInfoResponse.class);
        return Optional.ofNullable(post).orElse(new SpaceInfoResponse());
    }


    public PaymentInfoResponse getPaymentInfo(GetPaymentInfoRequest getPaymentInfoRequest) {
        LOGGER.info("ErpApiNewService.getPaymentInfo.getPaymentInfoRequest:{}", JSON.toJSONString(getPaymentInfoRequest));
        PaymentInfoResponse post = erpApiRestClient.buildApi(erpApiServiceUrl, "/Trade/GetPaymentInfo").post(getPaymentInfoRequest, PaymentInfoResponse.class);
        PaymentInfoResponse paymentInfoResponse = Optional.ofNullable(post).orElse(new PaymentInfoResponse());
        LOGGER.info("ErpApiNewService.getPaymentInfo.paymentInfoResponse:{}", JSON.toJSONString(paymentInfoResponse));
        return paymentInfoResponse;
    }

    public void setRefundOrderInfo(SetRefundOrderInfoRequest setRefundOrderInfoRequest) {
        LOGGER.info("ErpApiNewService.setRefundOrderInfo.setRefundOrderInfoRequest:{}", JSON.toJSONString(setRefundOrderInfoRequest));
        erpApiRestClient.buildApi(erpApiServiceUrl, "/Trade/SetRefundOrderInfo").post(setRefundOrderInfoRequest);
    }

}