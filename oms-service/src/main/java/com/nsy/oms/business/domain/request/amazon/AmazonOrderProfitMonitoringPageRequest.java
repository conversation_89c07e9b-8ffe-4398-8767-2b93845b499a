package com.nsy.oms.business.domain.request.amazon;

import com.nsy.api.pms.dto.request.BaseListRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-08
 */
@ApiModel("亚马逊订单利润率监控分页请求")
public class AmazonOrderProfitMonitoringPageRequest extends BaseListRequest {
    @ApiModelProperty("搜索类型：SPU/SKC/SKU")
    private String searchType;
    @ApiModelProperty("SKU")
    private String sku;
    @ApiModelProperty("父asin")
    private String parentAsin;
    @ApiModelProperty("店铺id")
    private List<Integer> storeIds;
    @ApiModelProperty("销售标签")
    private List<String> labelNames;
    @ApiModelProperty("建议标签")
    private List<String> suggestLabelNames;
    @ApiModelProperty("新款标签")
    private List<String> developSeasonList;
    @ApiModelProperty("最小利润率")
    private BigDecimal minProfitRate;
    @ApiModelProperty("最大利润率")
    private BigDecimal maxProfitRate;
    @ApiModelProperty("权限店铺，前端不用传")
    private List<Integer> permissionStoreIds;
    @ApiModelProperty("勾选的id")
    private List<Integer> ids;
    @ApiModelProperty("是否选中无标签")
    private Integer isNoLabel;

    public String getSearchType() {
        return searchType;
    }

    public void setSearchType(String searchType) {
        this.searchType = searchType;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getParentAsin() {
        return parentAsin;
    }

    public void setParentAsin(String parentAsin) {
        this.parentAsin = parentAsin;
    }

    public List<Integer> getStoreIds() {
        return storeIds;
    }

    public void setStoreIds(List<Integer> storeIds) {
        this.storeIds = storeIds;
    }

    public List<String> getLabelNames() {
        return labelNames;
    }

    public void setLabelNames(List<String> labelNames) {
        this.labelNames = labelNames;
    }

    public List<String> getSuggestLabelNames() {
        return suggestLabelNames;
    }

    public void setSuggestLabelNames(List<String> suggestLabelNames) {
        this.suggestLabelNames = suggestLabelNames;
    }

    public BigDecimal getMinProfitRate() {
        return minProfitRate;
    }

    public void setMinProfitRate(BigDecimal minProfitRate) {
        this.minProfitRate = minProfitRate;
    }

    public BigDecimal getMaxProfitRate() {
        return maxProfitRate;
    }

    public void setMaxProfitRate(BigDecimal maxProfitRate) {
        this.maxProfitRate = maxProfitRate;
    }

    public List<Integer> getPermissionStoreIds() {
        return permissionStoreIds;
    }

    public void setPermissionStoreIds(List<Integer> permissionStoreIds) {
        this.permissionStoreIds = permissionStoreIds;
    }

    public List<Integer> getIds() {
        return ids;
    }

    public void setIds(List<Integer> ids) {
        this.ids = ids;
    }

    public List<String> getDevelopSeasonList() {
        return developSeasonList;
    }

    public void setDevelopSeasonList(List<String> developSeasonList) {
        this.developSeasonList = developSeasonList;
    }

    public Integer getIsNoLabel() {
        return isNoLabel;
    }

    public void setIsNoLabel(Integer isNoLabel) {
        this.isNoLabel = isNoLabel;
    }
}
