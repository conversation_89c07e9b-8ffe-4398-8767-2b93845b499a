package com.nsy.oms.business.domain.response.bd;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author: linCheng
 * @create: 2024-05-31 16:51
 **/
@Data
public class OrderRuleItemResponse {
    /**
     * 主键id
     */
    private Integer bdOrderRuleItemId;

    /**
     * 主表id
     */
    private Integer orderRuleId;

    /**
     * 子项对应类型id
     */
    private Integer itemTargetId;

    /**
     * 子项对应类型名称
     */
    private String itemTargetName;

    /**
     * 是否主发货(0为否,1为是)
     */
    private Integer primaryType;

    /**
     * 子项类型(0为店铺,1为仓库)
     */
    private Integer itemType;

    /**
     * 预配顺序(0最靠前)
     */
    private Integer allocateOrder;
    @ApiModelProperty("分配规则触发条件")
    private Integer triggerConditions;
    @ApiModelProperty("库存分配比例")
    private Double inventoryAllocateRatio;
    @ApiModelProperty("保留库存")
    private Integer reserveInventory;
    @ApiModelProperty("保留库存类型(0：按件数，1：按比例)")
    private Integer reserveInventoryType;
}
