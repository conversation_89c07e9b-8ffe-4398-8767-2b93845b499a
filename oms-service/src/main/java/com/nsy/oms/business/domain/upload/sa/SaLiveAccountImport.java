package com.nsy.oms.business.domain.upload.sa;
import com.nsy.oms.business.domain.upload.BaseErrorTemplate;
import io.swagger.annotations.ApiModelProperty;


import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/11/11 14:03
 */
public class SaLiveAccountImport extends BaseErrorTemplate {
    /**
     * 部门
     */
    @ApiModelProperty("部门")
    @NotBlank(message = "部门不能为空")
    private String department;

    /**
     * 二级部门
     */
    @ApiModelProperty("二级部门")
    private String secondDepartment;

    /**
     * 社媒账号类型
     */
    @ApiModelProperty("直播账号类型")
    @NotBlank(message = "直播账号类型不能为空")
    private String accountTypeConfig;

    /**
     * 账号
     */
    @ApiModelProperty("直播号")
    @NotBlank(message = "直播号不能为空")
    private String account;

    /**
     * 注册邮箱
     */
    @ApiModelProperty("注册邮箱")
    private String email;

    /**
     * 手机号码
     */
    @ApiModelProperty("手机号码")
    private String phone;

    /**
     * 分配人员
     */
    @ApiModelProperty("分配人员")
    private String allocateUser;
    /**
     * 是否关联护照或绿卡 0 否 1是
     */
    @ApiModelProperty("是否关联护照或绿卡")
    private String associatedGreenCardOrPassport;

    /**
     * 正式运营时间
     */
    @ApiModelProperty("正式运营时间")
    private Date officialOperationDate;

    @ApiModelProperty("申请时间")
    private Date createDate;

    public String getAssociatedGreenCardOrPassport() {
        return associatedGreenCardOrPassport;
    }

    public void setAssociatedGreenCardOrPassport(String associatedGreenCardOrPassport) {
        this.associatedGreenCardOrPassport = associatedGreenCardOrPassport;
    }

    public Date getOfficialOperationDate() {
        return officialOperationDate;
    }

    public void setOfficialOperationDate(Date officialOperationDate) {
        this.officialOperationDate = officialOperationDate;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getSecondDepartment() {
        return secondDepartment;
    }

    public void setSecondDepartment(String secondDepartment) {
        this.secondDepartment = secondDepartment;
    }

    public String getAccountTypeConfig() {
        return accountTypeConfig;
    }

    public void setAccountTypeConfig(String accountTypeConfig) {
        this.accountTypeConfig = accountTypeConfig;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getAllocateUser() {
        return allocateUser;
    }

    public void setAllocateUser(String allocateUser) {
        this.allocateUser = allocateUser;
    }
}
