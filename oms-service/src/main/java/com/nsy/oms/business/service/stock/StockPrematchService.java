package com.nsy.oms.business.service.stock;

import com.nsy.oms.business.domain.stock.StockInfo;
import com.nsy.oms.business.manage.erp.response.GetStoreSpaceListResponse;
import com.nsy.oms.business.manage.pms.PmsApiService;
import com.nsy.oms.business.manage.pms.request.GetEmbryoSkuListRequest;
import com.nsy.oms.business.manage.pms.response.EmbryoSku;
import com.nsy.oms.business.manage.pms.response.GetEmbryoSkuListResponse;
import com.nsy.oms.business.service.sa.SaStorePreAllocateSpaceConfigService;
import com.nsy.oms.business.service.sa.SaStoreService;
import com.nsy.oms.business.service.stock.impl.StockServiceImpl;
import com.nsy.oms.constants.StoreConstant;
import com.nsy.oms.enums.stock.StockBrandTypeEnum;
import com.nsy.oms.enums.stock.StockOperateTypeEnum;
import com.nsy.oms.enums.stock.StockTypeEnum;
import com.nsy.oms.repository.entity.sa.SaStoreEntity;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/*
 * 获取库存，用于预配
 * */
@Service
public class StockPrematchService {

    @Autowired
    private SaStoreService saStoreService;
    @Autowired
    private SaStorePreAllocateSpaceConfigService saStorePreAllocateSpaceConfigService;
    @Autowired
    private PmsApiService pmsApiService;
    @Autowired
    private StockServiceImpl stockServiceImpl;


    public List<StockInfo> getStockList(Integer storeId, Integer spaceId, List<Integer> specIdList, StockOperateTypeEnum stockOperateTypeEnum, StockBrandTypeEnum stockBrandTypeEnum) {
        List<StockInfo> stockInfoList = new ArrayList<>();

        storeId = getSaleStoreId(storeId);

        //根据 枚举类型 StockOperateTypeEnum 分别处理
        switch (stockOperateTypeEnum) {
            case SALE_ORDER:
                // 销售订单：根据配置获取跨仓包含本身款式和肧款库存
                stockInfoList = getSaleOrderStock(storeId, spaceId, specIdList);
                break;
            case PROCESS_ORDER:
                // 加工订单：根据配置获取跨仓肧款库存
                stockInfoList = getProcessOrderStock(storeId, spaceId, specIdList);
                break;
            case PLATFORM_RESTOCK:
                // 平台仓补货：根据配置获取跨仓包含本身款式，同时需要区分品牌和非品牌
                stockInfoList = getPlatformRestockStock(storeId, spaceId, specIdList, stockBrandTypeEnum);
                break;
            case OVERSEAS_RESTOCK:
                // 海外仓补货：根据配置获取跨仓包含本身款式和肧款库存，需要排除海外仓
                stockInfoList = getOverseasRestockStock(storeId, spaceId, specIdList);
                break;
            case SINGLE_SPACE:
                // 单仓库存：单仓包含本身款式和肧款库存，不获取跨仓
                stockInfoList = getSingleSpaceStock(storeId, spaceId, specIdList);
                break;
            default:
                // 默认情况，可以记录日志或抛出异常
                throw new IllegalArgumentException("不支持的库存操作类型: " + stockOperateTypeEnum);
        }

        return stockInfoList;
    }

    /*
     * 如果是补货店铺，需要获取销售店铺上的店铺Id
     * */
    private Integer getSaleStoreId(Integer storeId) {
        SaStoreEntity store = saStoreService.getByStoreId(storeId);
        if (StoreConstant.getRestockStoreType().contains(store.getAchievementAttribution()) && store.getAssociatedStoreId() > 0) {
            return store.getAssociatedStoreId();
        }

        return storeId;
    }

    private List<StockInfo> getSaleOrderStock(Integer storeId, Integer spaceId, List<Integer> specIdList) {
        List<StockInfo> stockInfoList = new ArrayList<>();
        if (spaceId == 0 || specIdList.isEmpty()) {
            return stockInfoList;
        }

        List<Integer> spaceIdList = getSpaceIdList(storeId, spaceId);
        List<GetEmbryoSkuListResponse> embryoSkuList = getEmbryoSkuList(specIdList);
        List<Integer> embryoSpecIdList = getEmbryoSpecIdList(embryoSkuList);
        if (!embryoSpecIdList.isEmpty()) {
            specIdList.addAll(embryoSpecIdList);
        }

        stockInfoList = stockServiceImpl.getBaseMapper().searchSkuStockInfoBySpaceIdListAndSpecIdList(spaceIdList, specIdList);

        return stockInfoList;
    }

    /*
     * 获取跨仓仓库Id
     * */
    private List<Integer> getSpaceIdList(Integer storeId, Integer spaceId) {
        List<Integer> spaceIdList = new ArrayList<>();
        spaceIdList.add(spaceId);
        if (storeId == 0) {
            return spaceIdList;
        }

        GetStoreSpaceListResponse storeSpaceList = saStorePreAllocateSpaceConfigService.getStoreSpaceConfig(storeId, spaceId);
        if (storeSpaceList != null && storeSpaceList.getPreMatchAllocateSpaceConfigList() != null) {
            storeSpaceList.getPreMatchAllocateSpaceConfigList().forEach(item -> spaceIdList.add(item.getSpaceId()));
        }

        return spaceIdList.stream().distinct().collect(Collectors.toList());
    }

    private List<GetEmbryoSkuListResponse> getEmbryoSkuList(List<Integer> specIdList) {
        GetEmbryoSkuListRequest request = new GetEmbryoSkuListRequest();
        request.setCustomSpecIdLlist(specIdList);

        return pmsApiService.getEmbryoSkuList(request);
    }

    private List<Integer> getEmbryoSpecIdList(List<GetEmbryoSkuListResponse> embryoSkuList) {
        List<Integer> embryoSpecIdList = new ArrayList<>();
        if (embryoSkuList == null || embryoSkuList.isEmpty()) {
            return embryoSpecIdList;
        }

        embryoSkuList.forEach(mapping -> {
            embryoSpecIdList.addAll(mapping.getMappingList().stream().map(EmbryoSku::getEmbryoSpecId).collect(Collectors.toList()));
        });

        return embryoSpecIdList.stream().distinct().collect(Collectors.toList());
    }

    private List<StockInfo> getSaleStockInfoList(List<StockInfo> stockInfoList, int storeId) {
        SaStoreEntity store = saStoreService.getByStoreId(storeId);
        //获取本身有权限的库存， 店铺级别、部门级别、公司级别
        List<StockInfo> saleStockInfoList = stockInfoList.stream().filter(item -> (item.getStoreId().equals(storeId) && item.getStockType().equals(StockTypeEnum.STORE.name())) ||
                (item.getBusinessType() == store.getDepartment() && item.getStockType().equals(StockTypeEnum.DEPARTMENT.name())) ||
                item.getStockType().equals(StockTypeEnum.COMPANY.name())).collect(Collectors.toList());
        //获取其他人共享库存
        List<StockInfo> sharStockInfoList = stockInfoList.stream().filter(item -> item.getShareStock() > 0 && !saleStockInfoList.contains(item)).collect(Collectors.toList());
        for (StockInfo item : sharStockInfoList) {
            item.setStock(item.getShareStock());
            item.setPrematchQty(item.getPrematchShareQty());
        }
        saleStockInfoList.addAll(sharStockInfoList);

        return saleStockInfoList;
    }

    private List<StockInfo> getSkcShareStockInfoList(List<Integer> specIdList, int storeId) {
        List<StockInfo> saleStockInfoList = new ArrayList<>();


        return saleStockInfoList;
    }

    private List<StockInfo> getProcessOrderStock(Integer storeId, Integer spaceId, List<Integer> specIdList) {
        List<StockInfo> stockInfoList = new ArrayList<>();
        return stockInfoList;
    }

    private List<StockInfo> getPlatformRestockStock(Integer storeId, Integer spaceId, List<Integer> specIdList, StockBrandTypeEnum stockBrandTypeEnum) {
        List<StockInfo> stockInfoList = new ArrayList<>();
        return stockInfoList;
    }

    private List<StockInfo> getOverseasRestockStock(Integer storeId, Integer spaceId, List<Integer> specIdList) {
        List<StockInfo> stockInfoList = new ArrayList<>();
        return stockInfoList;
    }

    private List<StockInfo> getSingleSpaceStock(Integer storeId, Integer spaceId, List<Integer> specIdList) {
        List<StockInfo> stockInfoList = new ArrayList<>();
        return stockInfoList;
    }
}
