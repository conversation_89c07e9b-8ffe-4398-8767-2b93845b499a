package com.nsy.oms.business.domain.request.order;

import com.nsy.oms.business.domain.request.base.PageRequest;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/17 13:54
 */
public class OrderMissedQueueRequest extends PageRequest {
    /**
     * 订单号
     */
    @ApiModelProperty("重试和忽略使用")
    private List<Integer> idList;

    @ApiModelProperty("erp店铺名称")
    private String storeName;
    @ApiModelProperty("订单号")
    private String orderNo;
    @ApiModelProperty("初始化：0，执行中：1，执行成功：2，忽略：3，执行失败：-1")
    private Integer queueStatus;
    @ApiModelProperty("查询日期 - 开始")
    private Date createTimeStart;
    @ApiModelProperty("查询日期 - 结束")
    private Date createTimeEnd;
    @ApiModelProperty("订单号-新增场景使用")
    private List<String> orderNoList;

    public List<String> getOrderNoList() {
        return orderNoList;
    }

    public List<Integer> getIdList() {
        return idList;
    }

    public void setIdList(List<Integer> idList) {
        this.idList = idList;
    }

    public void setOrderNoList(List<String> orderNoList) {
        this.orderNoList = orderNoList;
    }

    private Integer storeId;

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getQueueStatus() {
        return queueStatus;
    }

    public void setQueueStatus(Integer queueStatus) {
        this.queueStatus = queueStatus;
    }

    public Date getCreateTimeStart() {
        return createTimeStart;
    }

    public void setCreateTimeStart(Date createTimeStart) {
        this.createTimeStart = createTimeStart;
    }

    public Date getCreateTimeEnd() {
        return createTimeEnd;
    }

    public void setCreateTimeEnd(Date createTimeEnd) {
        this.createTimeEnd = createTimeEnd;
    }
}
