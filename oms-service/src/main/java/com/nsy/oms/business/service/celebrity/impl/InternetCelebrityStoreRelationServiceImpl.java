package com.nsy.oms.business.service.celebrity.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.pms.dto.request.BaseListRequest;
import com.nsy.oms.business.domain.request.celebrity.CelebrityAddStoreRelationRequest;
import com.nsy.oms.business.domain.request.celebrity.SetAdIntentionRequest;
import com.nsy.oms.business.domain.request.celebrity.SetBdEmailRequest;
import com.nsy.oms.business.domain.request.celebrity.SetDateRequest;
import com.nsy.oms.business.domain.request.celebrity.SetRelationStatusRequest;
import com.nsy.oms.business.service.celebrity.CelebrityBusinessDeveloperService;
import com.nsy.oms.business.service.celebrity.IInternetCelebritySampleOrderItemPostService;
import com.nsy.oms.business.service.celebrity.IInternetCelebritySampleOrderItemService;
import com.nsy.oms.business.service.celebrity.IInternetCelebritySampleOrderService;
import com.nsy.oms.business.service.celebrity.InternetCelebrityOrderItemPostMappingService;
import com.nsy.oms.business.service.celebrity.InternetCelebrityService;
import com.nsy.oms.business.service.celebrity.InternetCelebrityStoreRelationService;
import com.nsy.oms.enums.tkcreator.InternetCelebrityRelationStatus;
import com.nsy.oms.repository.entity.celebrity.CelebrityBusinessDeveloperEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebrityEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebrityOrderItemPostMappingEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderItemEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderItemPostEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebrityStoreRelationEntity;
import com.nsy.oms.repository.sql.mapper.celebrity.InternetCelebrityStoreRelationMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class InternetCelebrityStoreRelationServiceImpl
        extends ServiceImpl<InternetCelebrityStoreRelationMapper, InternetCelebrityStoreRelationEntity>
        implements InternetCelebrityStoreRelationService {
    @Inject
    private InternetCelebrityOrderItemPostMappingService internetCelebrityOrderItemPostMappingService;
    @Inject
    private IInternetCelebritySampleOrderItemService internetCelebritySampleOrderItemService;
    @Inject
    private IInternetCelebritySampleOrderService internetCelebritySampleOrderService;
    @Inject
    private CelebrityBusinessDeveloperService celebrityBusinessDeveloperService;
    @Inject
    private InternetCelebrityService internetCelebrityService;
    @Inject
    private IInternetCelebritySampleOrderItemPostService internetCelebritySampleOrderItemPostService;

    @Override
    public List<InternetCelebrityStoreRelationEntity> listByCelebrityIdsAndStoreId(List<Integer> ids, Integer storeId) {
        return this.list(new LambdaQueryWrapper<InternetCelebrityStoreRelationEntity>()
                .in(InternetCelebrityStoreRelationEntity::getInternetCelebrityId, ids)
                .eq(InternetCelebrityStoreRelationEntity::getStoreId, storeId));
    }

    @Transactional
    @Override
    public void setBdEmail(SetBdEmailRequest request) {
        CelebrityBusinessDeveloperEntity entity = celebrityBusinessDeveloperService.findByEmail(request.getBdEmail());
        if (Objects.isNull(entity)) {
            throw new BusinessServiceException("bd不存在!");
        }
        List<InternetCelebrityStoreRelationEntity> list = this.listByIds(request.getStoreRelationIds());
        list.forEach(s -> {
            s.setBdAccount(entity.getBdAccount());
            s.setBdEmail(entity.getBdEmail());
            s.setBdId(entity.getId());
            s.setBdName(entity.getBdName());
        });
        updateBatchById(list);
    }

    @Transactional
    @Override
    public void setAdIntention(SetAdIntentionRequest request) {
        List<Integer> postIds = internetCelebrityOrderItemPostMappingService.findAllByRelationIdIn(request.getStoreRelationIds())
                .stream()
                .map(InternetCelebrityOrderItemPostMappingEntity::getInternetCelebritySampleOrderItemPostId)
                .filter(s -> s > 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(postIds)) {
            return;
        }
        List<InternetCelebritySampleOrderItemPostEntity> list = internetCelebritySampleOrderItemPostService.listByIds(postIds);
        list.forEach(s -> s.setAdIntention(request.getAdIntention()));
        internetCelebritySampleOrderItemPostService.updateBatchById(list);
    }

    @Transactional
    @Override
    public void setRelationStatus(SetRelationStatusRequest request) {
        List<InternetCelebrityStoreRelationEntity> list = this.listByIds(request.getStoreRelationIds());
        list.forEach(s -> s.setRelationStatus(request.getRelationStatus()));
        this.updateBatchById(list);
    }

    @Transactional
    @Override
    public void setAdDate(SetDateRequest request) {
        List<Integer> postIds = internetCelebrityOrderItemPostMappingService.findAllByRelationIdIn(request.getStoreRelationIds())
                .stream()
                .map(InternetCelebrityOrderItemPostMappingEntity::getInternetCelebritySampleOrderItemPostId)
                .filter(s -> s > 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(postIds)) {
            return;
        }
        List<InternetCelebritySampleOrderItemPostEntity> list = internetCelebritySampleOrderItemPostService.listByIds(postIds);
        list.forEach(s -> s.setAdDate(request.getDate()));
        internetCelebritySampleOrderItemPostService.updateBatchById(list);
    }

    @Transactional
    @Override
    public void setOrderCompromiseDate(SetDateRequest request) {
        if (CollectionUtils.isEmpty(request.getInternetCelebritySampleOrderItemIds())) {
            throw new BusinessServiceException("无订单明细id，请重新选择");
        }
        List<InternetCelebritySampleOrderItemEntity> orderItemList = internetCelebritySampleOrderItemService.listByIds(request.getInternetCelebritySampleOrderItemIds());
        if (CollectionUtils.isEmpty(orderItemList)) {
            throw new BusinessServiceException("无订单明细信息");
        }
        orderItemList.forEach(s -> s.setOrderCompromiseDate(request.getDate()));
        internetCelebritySampleOrderItemService.updateBatchById(orderItemList);
        List<InternetCelebritySampleOrderEntity> orderList = internetCelebritySampleOrderService.listByIdList(orderItemList.stream().map(InternetCelebritySampleOrderItemEntity::getInternetCelebritySampleOrderId).distinct().collect(Collectors.toList()));
        orderList.forEach(order -> internetCelebritySampleOrderService.saveSampleOrderDate(order));
    }



    @Transactional
    @Override
    public void updateRelationBdEmail(CelebrityAddStoreRelationRequest request) {
        CelebrityBusinessDeveloperEntity entity = celebrityBusinessDeveloperService.findByEmail(request.getBdEmail());
        if (Objects.isNull(entity)) {
            throw new BusinessServiceException("bd不存在!");
        }
        List<InternetCelebrityStoreRelationEntity> list = this.listByCelebrityIdsAndStoreId(request.getIds(), request.getStoreId());
        list.forEach(s -> {
            s.setBdAccount(entity.getBdAccount());
            s.setBdEmail(entity.getBdEmail());
            s.setBdId(entity.getId());
            s.setBdName(entity.getBdName());
        });
        updateBatchById(list);
    }

    @Override
    public List<InternetCelebrityStoreRelationEntity> listByIdList(List<Integer> storeRelationIds) {
        if (CollectionUtils.isEmpty(storeRelationIds)) {
            return Collections.emptyList();
        }
        return this.listByIds(storeRelationIds);
    }

    @Transactional
    @Override
    public void addStoreRelation(CelebrityAddStoreRelationRequest request) {
        List<InternetCelebrityStoreRelationEntity> list = this.listByCelebrityIdsAndStoreId(request.getIds(), request.getStoreId());
        if (!CollectionUtils.isEmpty(list)) {
            throw new BusinessServiceException(String.format("达人:[%s]已存在该店铺的关联关系!", list.stream().map(InternetCelebrityStoreRelationEntity::getInternetCelebrityName).collect(Collectors.joining(","))));
        }
        CelebrityBusinessDeveloperEntity entity = celebrityBusinessDeveloperService.findByEmail(request.getBdEmail());
        if (Objects.isNull(entity)) {
            throw new BusinessServiceException("bd不存在!");
        }
        List<InternetCelebrityEntity> internetCelebrityEntityList = internetCelebrityService.listByIds(request.getIds());
        this.saveBatch(internetCelebrityEntityList.stream().map(s -> {
            InternetCelebrityStoreRelationEntity relationEntity = new InternetCelebrityStoreRelationEntity();
            relationEntity.setInternetCelebrityName(s.getInternetCelebrityName());
            relationEntity.setInternetCelebrityNo(s.getInternetCelebrityNo());
            relationEntity.setInternetCelebrityId(s.getId());
            relationEntity.setStoreId(request.getStoreId());
            relationEntity.setStoreName(request.getStoreName());
            relationEntity.setBdId(entity.getId());
            relationEntity.setBdAccount(entity.getBdAccount());
            relationEntity.setBdName(entity.getBdName());
            relationEntity.setBdEmail(entity.getBdEmail());
            relationEntity.setRelationStatus(InternetCelebrityRelationStatus.NOT_RELATION.getValue());
            relationEntity.setRelationDate(new Date());
            relationEntity.setCommissionRate(new BigDecimal("0.12"));
            return relationEntity;
        }).collect(Collectors.toList()));
    }

    @Override
    public List<InternetCelebrityStoreRelationEntity> listByBdIds(List<Integer> bdIds) {
        return this.list(new LambdaQueryWrapper<InternetCelebrityStoreRelationEntity>()
                .in(InternetCelebrityStoreRelationEntity::getBdId, bdIds));
    }

    @Override
    public List<InternetCelebrityStoreRelationEntity> listByCelebrityIds(List<Integer> celebrityIds) {
        return this.list(new LambdaQueryWrapper<InternetCelebrityStoreRelationEntity>()
                .in(InternetCelebrityStoreRelationEntity::getInternetCelebrityId, celebrityIds));
    }

    @Override
    public Page<InternetCelebrityStoreRelationEntity> pageNoCelebrityId(BaseListRequest baseListResponse) {
        return this.page(new Page<>(baseListResponse.getPageIndex(), baseListResponse.getPageSize()),
                new LambdaQueryWrapper<InternetCelebrityStoreRelationEntity>().eq(InternetCelebrityStoreRelationEntity::getInternetCelebrityId, 0));
    }

    @Override
    public List<InternetCelebrityStoreRelationEntity> listByCelebrityId(Integer celebrityId) {
        return this.list(new LambdaQueryWrapper<InternetCelebrityStoreRelationEntity>()
                .eq(InternetCelebrityStoreRelationEntity::getInternetCelebrityId, celebrityId));
    }

    @Override
    public List<InternetCelebrityStoreRelationEntity> findAllByIdGT(Integer maxId, Integer size) {
        return this.list(new LambdaQueryWrapper<InternetCelebrityStoreRelationEntity>()
                .gt(InternetCelebrityStoreRelationEntity::getId, maxId)
                .last(String.format("limit %d", size)));
    }

    @Override
    public InternetCelebrityStoreRelationEntity findTopByCelebrityNoAndStoreId(String celebrityNo, Integer storeId) {
        return this.getOne(new LambdaQueryWrapper<InternetCelebrityStoreRelationEntity>()
                .eq(InternetCelebrityStoreRelationEntity::getInternetCelebrityNo, celebrityNo)
                .eq(InternetCelebrityStoreRelationEntity::getStoreId, storeId)
                .last("limit 1")
        );
    }

    @Override
    public InternetCelebrityStoreRelationEntity findTopByCelebrityNameAndStoreId(String celebrityName, Integer storeId) {
        return this.getOne(new LambdaQueryWrapper<InternetCelebrityStoreRelationEntity>()
                .eq(InternetCelebrityStoreRelationEntity::getInternetCelebrityName, celebrityName)
                .eq(InternetCelebrityStoreRelationEntity::getStoreId, storeId)
                .last("limit 1")
        );
    }

    @Override
    public InternetCelebrityStoreRelationEntity findTopByCelebrityIdAndStoreId(Integer celebrityId, Integer storeId) {
        return this.getOne(new LambdaQueryWrapper<InternetCelebrityStoreRelationEntity>()
                .eq(InternetCelebrityStoreRelationEntity::getInternetCelebrityId, celebrityId)
                .eq(InternetCelebrityStoreRelationEntity::getStoreId, storeId)
                .last("limit 1")
        );
    }

    @Override
    public InternetCelebrityStoreRelationEntity findTopByOrderId(Integer orderId) {
        return this.getBaseMapper().findTopByOrderId(orderId);
    }
}