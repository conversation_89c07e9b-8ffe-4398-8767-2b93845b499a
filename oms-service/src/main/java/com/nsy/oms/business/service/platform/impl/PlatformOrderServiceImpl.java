package com.nsy.oms.business.service.platform.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.constant.enums.QueueStatusEnum;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.privilege.LoginInfoService;
import com.nsy.api.core.apicore.util.MD5Util;
import com.nsy.api.core.apicore.util.NsyCollUtil;
import com.nsy.api.core.apicore.util.NsyJacksonUtils;
import com.nsy.api.oms.dto.request.order.SampleOrderRequest;
import com.nsy.api.oms.dto.response.order.SampleOrderResponse;
import com.nsy.api.transfer.enums.PlatformTypeEnum;
import com.nsy.business.base.enums.LocationEnum;
import com.nsy.oms.business.domain.dto.CommonOrderDTO;
import com.nsy.oms.business.domain.request.external.CrmOrderPageListRequest;
import com.nsy.oms.business.domain.request.platform.GetPlatformOrderByPageRequest;
import com.nsy.oms.business.domain.response.base.PageResponse;
import com.nsy.oms.business.domain.response.external.CrmOrderInoResponse;
import com.nsy.oms.business.domain.response.external.CrmOrderPageListResponse;
import com.nsy.oms.business.domain.response.order.GetPlatformOrderItemResponse;
import com.nsy.oms.business.domain.response.order.GetPlatformOrderListResponse;
import com.nsy.oms.business.domain.response.order.GetPlatformOrderResponse;
import com.nsy.oms.business.domain.response.platform.PlatformOrderItemResponse;
import com.nsy.oms.business.domain.response.platform.PlatformOrderResponse;
import com.nsy.oms.business.manage.amazon.AmazonApiService;
import com.nsy.oms.business.manage.amazon.request.PlatformOrderPriceRequest;
import com.nsy.oms.business.manage.amazon.response.PlatformOrderPriceResponse;
import com.nsy.oms.business.manage.amazon.response.ReportGetFlatFileAllOrdersDataResponse;
import com.nsy.oms.business.manage.erp.ErpApiService;
import com.nsy.oms.business.manage.erp.response.Sku;
import com.nsy.oms.business.manage.user.UserApiService;
import com.nsy.oms.business.service.bd.BdMarketplaceService;
import com.nsy.oms.business.service.order.OrderGrabStatusService;
import com.nsy.oms.business.service.order.OrderIdempotentService;
import com.nsy.oms.business.service.order.OrderItemGrabQueueService;
import com.nsy.oms.business.service.platform.PlatformOrderExtendService;
import com.nsy.oms.business.service.platform.PlatformOrderItemService;
import com.nsy.oms.business.service.platform.PlatformOrderPushKingdeeQueueService;
import com.nsy.oms.business.service.platform.PlatformOrderService;
import com.nsy.oms.business.service.platform.PlatformReceiverService;
import com.nsy.oms.business.service.privilege.AccessControlService;
import com.nsy.oms.business.service.replenishment.FbaReplenishmentSkcService;
import com.nsy.oms.business.service.sa.SaStoreService;
import com.nsy.oms.business.service.sync.AsyncService;
import com.nsy.oms.enums.order.ItemStatusEnum;
import com.nsy.oms.enums.order.OrderTypeEnum;
import com.nsy.oms.enums.platform.PlatformOrderItemStatusEnum;
import com.nsy.oms.enums.platform.PlatformOrderStatusEnum;
import com.nsy.oms.enums.platform.PlatformShippingTypeEnum;
import com.nsy.oms.enums.sa.PlatformEnum;
import com.nsy.oms.repository.entity.bd.BdMarketplaceEntity;
import com.nsy.oms.repository.entity.order.OrderGrabStatusEntity;
import com.nsy.oms.repository.entity.order.OrderIdempotentEntity;
import com.nsy.oms.repository.entity.order.OrderItemGrabQueueEntity;
import com.nsy.oms.repository.entity.platform.PlatformOrderEntity;
import com.nsy.oms.repository.entity.platform.PlatformOrderExtendEntity;
import com.nsy.oms.repository.entity.platform.PlatformOrderItemEntity;
import com.nsy.oms.repository.entity.platform.PlatformOrderPushKingdeeQueueEntity;
import com.nsy.oms.repository.entity.platform.PlatformReceiverEntity;
import com.nsy.oms.repository.entity.sa.SaStoreEntity;
import com.nsy.oms.repository.sql.mapper.platform.PlatformOrderMapper;
import com.nsy.oms.utils.DateUtils;
import com.nsy.oms.utils.SetValueUtil;
import com.nsy.oms.utils.mp.LocationContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【platform_order(平台订单表)】的数据库操作Service
 * @createDate 2024-05-09 16:01:37
 */
@Service
public class PlatformOrderServiceImpl extends ServiceImpl<PlatformOrderMapper, PlatformOrderEntity> implements PlatformOrderService {
    private static final Logger LOGGER = LoggerFactory.getLogger(PlatformOrderServiceImpl.class);
    @Autowired
    private PlatformReceiverService platformReceiverService;
    @Autowired
    private PlatformOrderItemService platformOrderItemService;
    @Autowired
    private SaStoreService storeService;
    @Autowired
    private BdMarketplaceService marketplaceService;
    @Autowired
    private AsyncService asyncService;
    @Autowired
    private PlatformOrderServiceImpl platformOrderService;
    @Autowired
    private OrderIdempotentService orderIdempotentService;
    @Autowired
    private OrderItemGrabQueueService orderItemGrabQueueService;
    @Autowired
    private OrderGrabStatusService orderGrabStatusService;
    @Autowired
    private PlatformOrderExtendService platformOrderExtendService;
    @Autowired
    private ErpApiService erpApiService;
    @Autowired
    private AmazonApiService amazonApiService;
    @Autowired
    private AccessControlService accessControlService;
    @Autowired
    private UserApiService userApiService;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private PlatformOrderPushKingdeeQueueService pushKingdeeQueueService;

    @Override
    public PlatformOrderResponse getPlatformOrder(String platformOrderNo) {
        return getBaseMapper().getPlatformOrder(platformOrderNo);
    }

    @Override
    public void syncOrder(GetPlatformOrderListResponse messageContent) {
        List<GetPlatformOrderResponse> orderList = messageContent.getOrderList();

        if (NsyCollUtil.isEmpty(orderList)) {
            return;
        }

        orderList.stream().filter(order -> Objects.nonNull(order)
                        && StringUtils.isNotBlank(order.getShippingType())
                        && (order.getShippingType().contains(PlatformShippingTypeEnum.AFN.name())
                        || order.getShippingType().contains(PlatformShippingTypeEnum.WFS.name())
                        || order.getShippingType().contains(PlatformShippingTypeEnum.TIKTOK.name())))
                .forEach(order -> {
                    try {
                        // 付款时间一年前不处理
                        if (Objects.nonNull(order.getPaymentDate()) && order.getPaymentDate().before(DateUtil.offset(DateUtil.beginOfDay(new Date()), DateField.YEAR, -1))) {
                            return;
                        }
                        LocationContext.setLocation(messageContent.getLocation());
                        platformOrderService.savePlatformOrder(messageContent.getStoreId(), order);
                    } catch (Exception e) {
                        LOGGER.error(String.format("保存平台订单失败：%s", e.getMessage()), e);
                    }
                });
    }


    @Transactional(rollbackFor = Exception.class)
    public void savePlatformOrder(Integer storeId, GetPlatformOrderResponse orderResponse) {
        SaStoreEntity storeEntity = storeService.getByStoreId(storeId);
        // 如果是Tiktok直邮 店铺的平台ID 71 不保存
        if (orderResponse.getShippingType().contains(PlatformShippingTypeEnum.TIKTOK.name()) && storeEntity.getPlatformId() == 71) {
            return;
        }

        String platformOrderNo = String.format("%s-%s", orderResponse.getOrderId(), storeEntity.getId());
        PlatformOrderEntity orderEntity = Optional.ofNullable(getByPlatformOrderNo(platformOrderNo)).orElse(new PlatformOrderEntity());
        orderEntity.setPlatformOrderNo(platformOrderNo);
        // 订单已发货，不更新
        if (PlatformOrderStatusEnum.DELIVERY.getCode().equals(orderEntity.getOrderStatus())) {
            return;
        }
        // 保存订单
        saveOrderEntity(storeEntity, orderResponse, orderEntity);

        // 寄件人信息
        saveReceiverEntity(orderResponse, orderEntity);

        // 已发货生成推送金蝶队列
        deliveryStatusPushTokingdee(orderEntity);

        List<PlatformOrderItemEntity> existItemList = platformOrderItemService.listByPlatformOrderId(orderEntity.getPlatformOrderId());
        // 存在
        if (NsyCollUtil.isNotEmpty(existItemList)) {
            if (!PlatformOrderStatusEnum.CANCEL.getCode().equals(orderEntity.getOrderStatus())) {
                return;
            }
            existItemList.forEach(itemEntity -> itemEntity.setItemStatus(ItemStatusEnum.DELETE.getCode()));
            platformOrderItemService.updateBatchById(existItemList);
            return;
        }

        List<PlatformOrderItemEntity> itemEntityList = orderResponse.getOrderItemInfos().stream().map(item ->
                buildItemEntity(orderEntity.getPlatformOrderId(), item, new PlatformOrderItemEntity(), orderEntity.getLocation())
        ).collect(Collectors.toList());

        // 异步映射本地sku
        asyncService.platformOrderMatchLocalSku(storeId, itemEntityList);
        platformOrderItemService.saveBatch(itemEntityList);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrder(Integer storeId, GetPlatformOrderResponse orderResponse) {
        // 检验订单幂等
        OrderIdempotentEntity orderIdempotentEntity = orderIdempotentService.getByStoreIdAndOrderNo(storeId, orderResponse.getOrderId());
        String orderCrypt = MD5Util.crypt(NsyJacksonUtils.toJson(orderResponse));

        if (Objects.nonNull(orderIdempotentEntity)) {
            LOGGER.info("店铺ID_订单号_幂等表加密_单据加密:{}", String.format("%s_%s_%s_%s", storeId, orderResponse.getOrderId(), orderIdempotentEntity.getContent(), orderCrypt));
            if (orderIdempotentEntity.getContent().equals(orderCrypt)) {
                return;
            }
        }
        SaStoreEntity storeEntity = storeService.getByStoreId(storeId);

        String platformOrderNo = String.format("%s-%s", orderResponse.getOrderId(), storeEntity.getId());
        PlatformOrderEntity orderEntity = Optional.ofNullable(getByPlatformOrderNo(platformOrderNo)).orElse(new PlatformOrderEntity());
        orderEntity.setPlatformOrderNo(platformOrderNo);
        // 订单已发货，不更新
        if (PlatformOrderStatusEnum.DELIVERY.getCode().equals(orderEntity.getOrderStatus())) {
            return;
        }
        // 币种为空，调用report获取
        processCurrency(storeId, orderResponse, orderEntity);

        // 保存订单
        saveOrderEntity(storeEntity, orderResponse, orderEntity);
        // 寄件人信息
        saveReceiverEntity(orderResponse, orderEntity);

        // 保存幂等表
        saveOrderIdempotent(orderResponse.getOrderId(), orderCrypt, orderEntity, orderIdempotentEntity);

        // 生成队列
        OrderItemGrabQueueEntity queueEntity = new OrderItemGrabQueueEntity();
        queueEntity.setStoreId(orderEntity.getStoreId());
        queueEntity.setPlatformId(orderEntity.getPlatformId());
        queueEntity.setOrderId(orderEntity.getPlatformOrderId());
        queueEntity.setOrderNo(orderEntity.getPlatformOriginalOrderNo());
        queueEntity.setOrderType(OrderTypeEnum.FBA.getCode());
        queueEntity.setLocation(orderEntity.getLocation());
        orderItemGrabQueueService.generateQueue(queueEntity);

        // 保存或更新抓单状态表
        OrderGrabStatusEntity grabStatusEntity = new OrderGrabStatusEntity();
        grabStatusEntity.setStoreId(orderEntity.getStoreId());
        grabStatusEntity.setOrderId(orderEntity.getPlatformOrderId());
        grabStatusEntity.setOrderNo(orderEntity.getPlatformOriginalOrderNo());
        grabStatusEntity.setOrderType(OrderTypeEnum.FBA.getCode());
        grabStatusEntity.setLocation(orderEntity.getLocation());
        orderGrabStatusService.saveOrUpdateEntity(grabStatusEntity);

        // 已发货生成推送金蝶队列
        deliveryStatusPushTokingdee(orderEntity);
    }

    public void deliveryStatusPushTokingdee(PlatformOrderEntity orderEntity) {
        // 不是泉州地区，不推送
        if (!LocationEnum.QUANZHOU.name().equals(orderEntity.getLocation())) {
            return;
        }
        // 订单不是已发货，不用推送
        if (!PlatformOrderStatusEnum.DELIVERY.getCode().equals(orderEntity.getOrderStatus())) {
            return;
        }
        PlatformOrderPushKingdeeQueueEntity queueEntity = new PlatformOrderPushKingdeeQueueEntity();
        queueEntity.setPlatformId(orderEntity.getPlatformId());
        queueEntity.setPlatformName(orderEntity.getPlatformName());
        queueEntity.setStoreId(orderEntity.getStoreId());
        queueEntity.setStoreName(orderEntity.getStoreName());
        queueEntity.setOrderStatus(orderEntity.getOrderStatus());
        queueEntity.setCommissionFee(orderEntity.getCommissionFee());
        queueEntity.setFreightFee(orderEntity.getFreightFee());
        queueEntity.setPlatformOrderNo(orderEntity.getPlatformOrderNo());
        queueEntity.setOrderType(orderEntity.getOrderType());
        queueEntity.setLocation(orderEntity.getLocation());

        pushKingdeeQueueService.save(queueEntity);
    }




    public void processCurrency(Integer storeId, GetPlatformOrderResponse orderResponse, PlatformOrderEntity orderEntity) {
        if (StringUtils.isBlank(orderEntity.getCurrency())) {
            PlatformOrderPriceRequest request = new PlatformOrderPriceRequest();
            request.setPlatformOrderNo(orderResponse.getOrderId());
            request.setStoreId(storeId);
            PlatformOrderPriceResponse reportOrderPrice = amazonApiService.getReportOrderPrice(request);
            if (Objects.isNull(reportOrderPrice)) {
                return;
            }
            orderEntity.setCurrency(reportOrderPrice.getCurrency());
            orderEntity.setPaymentAmount(reportOrderPrice.getPaymentAmount());
            orderEntity.setProductTotalAmount(reportOrderPrice.getProductTotalAmount());
            orderEntity.setProductDiscountAmount(reportOrderPrice.getProductDiscountAmount());
            orderEntity.setFreightFee(reportOrderPrice.getFreightFee());
        }
    }


    public void saveOrderEntity(SaStoreEntity storeEntity, GetPlatformOrderResponse orderResponse, PlatformOrderEntity orderEntity) {
        BdMarketplaceEntity marketplaceEntity = null;
        if (StringUtils.isNotBlank(orderResponse.getMarketplaceId())) {
            orderEntity.setMarketCode(orderResponse.getMarketplaceId());
            marketplaceEntity = marketplaceService.getByMarketplaceId(orderResponse.getMarketplaceId());
        }
        if (Objects.nonNull(marketplaceEntity)) {
            orderEntity.setMarketName(marketplaceEntity.getMarketplace());
        }

        orderEntity.setPlatformOriginalOrderNo(orderResponse.getOrderId());
        orderEntity.setShippingType(orderResponse.getShippingType());
        // 订单状态
        orderEntity.setOrderStatus(PlatformOrderStatusEnum.getCodeByOutCode(orderResponse.getStatus()));
        orderEntity.setCurrency(orderResponse.getCurrency());
        orderEntity.setProductTotalAmount(SetValueUtil.getOptimizeBigDecimal(orderResponse.getTotalFee()));
        orderEntity.setPaymentAmount(SetValueUtil.getOptimizeBigDecimal(orderResponse.getPaymentFee()));
        orderEntity.setProductDiscountAmount(SetValueUtil.getOptimizeBigDecimal(orderResponse.getDiscountFee()));
        orderEntity.setFreightFee(SetValueUtil.getOptimizeBigDecimal(orderResponse.getPostFee()));
        orderEntity.setCommissionFee(SetValueUtil.getOptimizeBigDecimal(orderResponse.getCommissionFee()));

        orderEntity.setStoreId(storeEntity.getId());
        orderEntity.setStoreName(storeEntity.getErpStoreName());
        orderEntity.setPlatformId(storeEntity.getPlatformId());
        orderEntity.setPlatformName(storeEntity.getPlatformName());
        orderEntity.setLocation(storeEntity.getLocation());

        orderEntity.setOrderCreateDate(orderResponse.getCreateDate());
        orderEntity.setOrderPaymentDate(Objects.nonNull(orderResponse.getPaymentDate()) ? orderResponse.getPaymentDate() : new Date());
        // 发货时间
        orderEntity.setOrderDeliverDate(orderResponse.getUpdateDate());
        orderEntity.setOrderCancelDate(orderResponse.getCancelledDate());
        orderEntity.setBuyerRemark(orderResponse.getBuyerMemo());
        // 样品订单
        if (StringUtils.isNotBlank(orderResponse.getType())) {
            orderEntity.setOrderType(Integer.parseInt(orderResponse.getType()));
        }
        if (!LocationEnum.QUANZHOU.name().equals(orderEntity.getLocation())) {
            orderEntity.setPushStockoutOrderStatus(QueueStatusEnum.IGNORE.getCode());
            orderEntity.setPushCostOrderStatus(QueueStatusEnum.IGNORE.getCode());
            orderEntity.setPushOtherStockoutOrderStatus(QueueStatusEnum.IGNORE.getCode());
        }
        orderEntity.setBusinessOrder(orderResponse.getBusinessOrder());
        platformOrderService.saveOrUpdate(orderEntity);

        // 根据市场区域id, 保存区域时间
        if (Objects.nonNull(marketplaceEntity)) {
            PlatformOrderExtendEntity orderExtendEntity = Optional.ofNullable(platformOrderExtendService.getByPlatformOrderNo(orderEntity.getPlatformOrderNo())).orElse(new PlatformOrderExtendEntity());
            if (Objects.isNull(orderExtendEntity.getPlatformOrderId())) {
                orderExtendEntity.setPlatformOrderId(orderEntity.getPlatformOrderId());
                orderExtendEntity.setPlatformOrderNo(orderEntity.getPlatformOrderNo());
            }
            if (Objects.nonNull(orderEntity.getOrderCreateDate())) {
                orderExtendEntity.setOrderCreateDateTimeZone(DateUtils.getTimeByTimeZone(orderEntity.getOrderCreateDate(), marketplaceEntity.getTimeZoneId()));
            }
            if (Objects.nonNull(orderEntity.getOrderDeliverDate())) {
                orderExtendEntity.setOrderDeliverDateTimeZone(DateUtils.getTimeByTimeZone(orderEntity.getOrderDeliverDate(), marketplaceEntity.getTimeZoneId()));
            }
            if (Objects.nonNull(orderEntity.getOrderPaymentDate())) {
                orderExtendEntity.setOrderPaymentDateTimeZone(DateUtils.getTimeByTimeZone(orderEntity.getOrderPaymentDate(), marketplaceEntity.getTimeZoneId()));
            }
            if (Objects.nonNull(orderEntity.getOrderCancelDate())) {
                orderExtendEntity.setOrderCancelDateTimeZone(DateUtils.getTimeByTimeZone(orderEntity.getOrderCancelDate(), marketplaceEntity.getTimeZoneId()));
            }
            orderExtendEntity.setLocation(orderEntity.getLocation());
            platformOrderExtendService.saveOrUpdate(orderExtendEntity);
        }
    }

    /**
     * 保存幂等
     */
    public void saveOrderIdempotent(String orderId, String orderCrypt, PlatformOrderEntity orderEntity, OrderIdempotentEntity orderIdempotentEntity) {
        OrderIdempotentEntity idempotentEntity = Optional.ofNullable(orderIdempotentEntity).orElse(new OrderIdempotentEntity());
        idempotentEntity.setStoreId(orderEntity.getStoreId());
        idempotentEntity.setOrderNo(orderId);
        idempotentEntity.setContent(orderCrypt);
        idempotentEntity.setLocation(orderEntity.getLocation());
        orderIdempotentService.saveOrUpdate(idempotentEntity);
    }


    public static PlatformOrderItemEntity buildItemEntity(Integer platformOrderId, GetPlatformOrderItemResponse itemMessage, PlatformOrderItemEntity itemEntity, String location) {
        itemEntity.setSellerProductId(itemMessage.getProductId());
        itemEntity.setSellerSkuId(itemMessage.getOuterSkuId());
        itemEntity.setLocation(location);
        itemEntity.setSellerSku(itemMessage.getSku());
        itemEntity.setPlatformOrderId(platformOrderId);
        itemEntity.setUnitPrice(SetValueUtil.getOptimizeBigDecimal(itemMessage.getPrice()));
        itemEntity.setQty(SetValueUtil.getOptimizeInteger(itemMessage.getQty()));
        if (Objects.nonNull(itemEntity.getUnitPrice()) && Objects.nonNull(itemEntity.getQty())) {
            itemEntity.setTotalAmount(SetValueUtil.getOptimizeBigDecimal(itemEntity.getUnitPrice().multiply(BigDecimal.valueOf(itemEntity.getQty()))));
        }
        if (Objects.nonNull(itemMessage.getTotalDiscount())) {
            if (Objects.nonNull(itemEntity.getTotalAmount())) {
                itemEntity.setPaymentAmount(SetValueUtil.getOptimizeBigDecimal(itemEntity.getTotalAmount().subtract(itemMessage.getTotalDiscount())));
            }
            if (Objects.nonNull(itemEntity.getQty())) {
                itemEntity.setUnitDiscount(SetValueUtil.getOptimizeBigDecimal(itemMessage.getTotalDiscount().divide(BigDecimal.valueOf(itemMessage.getQty()), 4, RoundingMode.HALF_UP)));
            }
        } else {
            itemEntity.setPaymentAmount(SetValueUtil.getOptimizeBigDecimal(itemEntity.getTotalAmount()));
        }
        itemEntity.setItemStatus(PlatformOrderStatusEnum.CANCEL.getOutCode().equalsIgnoreCase(itemMessage.getStatus()) ? PlatformOrderItemStatusEnum.DELETE.getCode() : PlatformOrderItemStatusEnum.NORMAL.getCode());
        if (StringUtils.isNotBlank(itemMessage.getOrderItemId()) && !BigDecimal.ZERO.toString().equals(itemMessage.getOrderItemId())) {
            itemEntity.setItemId(itemMessage.getOrderItemId());
        }
        return itemEntity;
    }


    /**
     * 保存寄件人
     *
     * @param orderMessage
     * @param orderEntity
     */
    public void saveReceiverEntity(GetPlatformOrderResponse orderMessage, PlatformOrderEntity orderEntity) {
        PlatformReceiverEntity receiverEntity = Optional.ofNullable(platformReceiverService.getByPlatformOrderId(orderEntity.getPlatformOrderId())).orElse(new PlatformReceiverEntity());

        receiverEntity.setPlatformOrderId(orderEntity.getPlatformOrderId());
        receiverEntity.setReceiverName(orderMessage.getReceiverName());
        receiverEntity.setCountry(orderMessage.getReceiverCountry());
        receiverEntity.setProvince(orderMessage.getReceiverState());
        receiverEntity.setCity(orderMessage.getReceiverCity());
        receiverEntity.setArea(orderMessage.getReceiverDistrict());
        receiverEntity.setAddress(orderMessage.getReceiverAddress());
        receiverEntity.setMobile(orderMessage.getReceiverMobile());
        receiverEntity.setPhone(orderMessage.getReceiverPhone());
        receiverEntity.setPostCode(orderMessage.getReceiverZip());
        receiverEntity.setBuyerNick(orderMessage.getBuyerNick());
        receiverEntity.setBuyerEmail(orderMessage.getBuyerEmail());
        receiverEntity.setBuyerUid(orderMessage.getBuyerUid());
        receiverEntity.setLocation(orderEntity.getLocation());
        platformReceiverService.saveOrUpdate(receiverEntity);
    }

    public PlatformOrderEntity getByPlatformOrderNo(String platformOrderNo) {
        return this.getOne(Wrappers.<PlatformOrderEntity>lambdaQuery().eq(PlatformOrderEntity::getPlatformOrderNo, platformOrderNo));
    }

    @Override
    public PlatformOrderEntity getOrderByStoreIdAndOrderNo(Integer storeId, String orderNo) {
        return getOne(Wrappers.<PlatformOrderEntity>lambdaQuery().eq(PlatformOrderEntity::getStoreId, storeId).eq(PlatformOrderEntity::getPlatformOriginalOrderNo, orderNo));
    }

    @Override
    public PlatformOrderEntity getOrderByOrderNo(String orderNo) {
        return getOne(Wrappers.<PlatformOrderEntity>lambdaQuery().eq(PlatformOrderEntity::getPlatformOriginalOrderNo, orderNo));
    }

    @Override
    public void saveOrderItems(CommonOrderDTO orderDTO, List<GetPlatformOrderItemResponse> orderItemList) {
        List<PlatformOrderItemEntity> existItemList = platformOrderItemService.listByPlatformOrderId(orderDTO.getOrderId());
        List<PlatformOrderItemEntity> itemEntityList;
        // 存在
        if (NsyCollUtil.isNotEmpty(existItemList)) {
            PlatformOrderEntity platformOrderEntity = getById(orderDTO.getOrderId());
            if (Objects.isNull(platformOrderEntity)) {
                throw new BusinessServiceException(String.format("查询不到平台订单，订单id: %s", orderDTO.getOrderId()));
            }
            itemEntityList = new ArrayList<>();
            existItemList.forEach(existItem ->
                    orderItemList.stream().filter(item ->
                            StringUtils.isNoneBlank(item.getOrderItemId(), existItem.getItemId()) && item.getOrderItemId().equals(existItem.getItemId())
                                    || StringUtils.isNotBlank(item.getSku()) && item.getSku().equals(existItem.getSellerSku())
                    ).findFirst().ifPresent(item -> {
                        PlatformOrderItemEntity newEntity = new PlatformOrderItemEntity();
                        BeanUtils.copyProperties(existItem, newEntity);
                        itemEntityList.add(buildItemEntity(orderDTO.getOrderId(), item, newEntity, orderDTO.getLocation()));
                    })
            );
        } else {
            itemEntityList = orderItemList.stream().map(item ->
                    buildItemEntity(orderDTO.getOrderId(), item, new PlatformOrderItemEntity(), orderDTO.getLocation())
            ).collect(Collectors.toList());
        }
        if (NsyCollUtil.isNotEmpty(itemEntityList)) {
            // 异步映射本地sku
            asyncService.platformOrderMatchLocalSku(orderDTO.getStoreId(), itemEntityList);
            platformOrderItemService.saveOrUpdateBatch(itemEntityList);
        }
    }

    @Override
    public void orderItemFetchSuccessToUpdateOrder(CommonOrderDTO orderDTO) {
        PlatformOrderEntity orderEntity = this.getById(orderDTO.getOrderId());
        if (Objects.isNull(orderEntity)) {
            throw new BusinessServiceException(String.format("查询不到平台订单, 店铺ID: %s, 订单ID: %s", orderDTO.getStoreId(), orderDTO.getOrderId()));
        }
        if (Objects.nonNull(orderEntity.getProductDiscountAmount()) && orderEntity.getProductDiscountAmount().compareTo(BigDecimal.ZERO) == 0) {
            orderEntity.setProductDiscountAmount(SetValueUtil.getOptimizeBigDecimal(orderDTO.getDiscountFee()));
        }
        if (Objects.nonNull(orderEntity.getProductTotalAmount()) && orderEntity.getProductTotalAmount().compareTo(BigDecimal.ZERO) == 0) {
            orderEntity.setProductTotalAmount(SetValueUtil.getOptimizeBigDecimal(orderDTO.getTotalFee()));
        }
        if (Objects.nonNull(orderEntity.getFreightFee()) && orderEntity.getFreightFee().compareTo(BigDecimal.ZERO) == 0) {
            orderEntity.setFreightFee(SetValueUtil.getOptimizeBigDecimal(orderDTO.getPostFee()));
        }
        this.updateById(orderEntity);
    }

    @Override
    public List<PlatformOrderEntity> getListByStoreIdAndOrderNos(Integer storeId, List<String> orderNos) {
        LambdaQueryWrapper<PlatformOrderEntity> lambdaQueryWrapper = Wrappers.<PlatformOrderEntity>lambdaQuery()
                .eq(PlatformOrderEntity::getStoreId, storeId)
                .in(PlatformOrderEntity::getPlatformOriginalOrderNo, orderNos);
        return this.list(lambdaQueryWrapper);
    }

    @Override
    public boolean isCancelOrder(Integer storeId, String orderNo) {
        Long count = count(Wrappers.<PlatformOrderEntity>lambdaQuery()
                .eq(PlatformOrderEntity::getStoreId, storeId)
                .eq(PlatformOrderEntity::getPlatformOriginalOrderNo, orderNo)
                .eq(PlatformOrderEntity::getOrderStatus, PlatformOrderStatusEnum.CANCEL.getCode())
        );

        return count > 0;
    }

    @Override
    public List<PlatformOrderEntity> getFbaPushOrder(Date startDate, Date endDate, Integer fetchCount) {
        return getBaseMapper().getFbaPushOrder(startDate, endDate, fetchCount);
    }

    @Override
    public List<PlatformOrderEntity> cancelOrders(Integer storeId, List<String> orderNoList) {
        return list(Wrappers.<PlatformOrderEntity>lambdaQuery()
                .eq(PlatformOrderEntity::getStoreId, storeId)
                .in(PlatformOrderEntity::getPlatformOriginalOrderNo, orderNoList)
                .eq(PlatformOrderEntity::getOrderStatus, PlatformOrderStatusEnum.CANCEL.getCode()));
    }

    @Override
    public PlatformOrderEntity getOriginPlatformOrder(String orderId) {
        return this.getOne(Wrappers.<PlatformOrderEntity>lambdaQuery().eq(PlatformOrderEntity::getPlatformOriginalOrderNo, orderId));
    }

    @Override
    public List<SampleOrderResponse> getSampleOrders(SampleOrderRequest request) {
        return this.baseMapper.getSampleOrders(request);
    }

    @Override
    public List<String> getByPlatformOrderNos(List<String> platformOrderNos) {
        return this.baseMapper.getByPlatformOrderNos(platformOrderNos);
    }

    @Override
    public List<PlatformOrderEntity> getByPlatformOrderNoList(List<String> platformOrderNoList) {
        return list(Wrappers.<PlatformOrderEntity>lambdaQuery().in(PlatformOrderEntity::getPlatformOrderNo, platformOrderNoList));
    }

    @Override
    public void buildPlatformOrderPrice(PlatformOrderEntity orderEntity, List<ReportGetFlatFileAllOrdersDataResponse> dataList) {
        BigDecimal postFee = dataList.stream().map(item ->
                SetValueUtil.getOptimizeBigDecimal(item.getShippingPrice()).subtract(SetValueUtil.getOptimizeBigDecimal(item.getShipPromotionDiscount()))).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        orderEntity.setFreightFee(SetValueUtil.getOptimizeBigDecimal(postFee));
        BigDecimal totalFee = dataList.stream().filter(data -> Objects.nonNull(data.getItemPrice())).map(ReportGetFlatFileAllOrdersDataResponse::getItemPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        orderEntity.setProductTotalAmount(SetValueUtil.getOptimizeBigDecimal(totalFee)); //商品总金额
        orderEntity.setProductDiscountAmount(SetValueUtil.getOptimizeBigDecimal(dataList.stream().filter(data -> Objects.nonNull(data.getItemPromotionDiscount())).map(ReportGetFlatFileAllOrdersDataResponse::getItemPromotionDiscount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))); //商品折扣
        // 付款金额：等于 折后商品总金额+折后运费
        orderEntity.setPaymentAmount(SetValueUtil.getOptimizeBigDecimal(
                orderEntity.getProductTotalAmount().subtract(orderEntity.getProductDiscountAmount()).add(orderEntity.getFreightFee())
        ));
        orderEntity.setCurrency(dataList.get(0).getCurrency());
    }

    @Override
    public Page<CrmOrderPageListResponse> crmOrderPageList(CrmOrderPageListRequest request) {
        return this.baseMapper.crmOrderPage(new Page<>(request.getPageIndex(), request.getPageSize()), request);
    }

    @Override
    public CrmOrderInoResponse getCrmOrderInfo(String orderNo, List<Integer> storeIdList) {
        return this.baseMapper.getCrmOrderInfo(orderNo, storeIdList);
    }

    @Override
    public PageResponse<PlatformOrderResponse> getPlatformOrderByPage(GetPlatformOrderByPageRequest request) {
        request.setPlatformIds(getPlatformIds(request.getPlatforms()));
        List<Integer> permissionStoreIds = accessControlService.isAdmin() ? null : accessControlService.doPrivileged(new FbaReplenishmentSkcService.FbaReplenishmentSkcPrivilegeAction(userApiService, loginInfoService.getUserName()));
        IPage<PlatformOrderResponse> page = this.baseMapper.getPlatformOrderPage(new Page<>(request.getPageIndex(), request.getPageSize()), request, permissionStoreIds);

        PageResponse<PlatformOrderResponse> pageResponse = PageResponse.of(page.getTotal(), page.getPages());
        List<PlatformOrderResponse> platformOrderResponseList = page.getRecords();
        if (CollectionUtils.isEmpty(platformOrderResponseList)) {
            return PageResponse.empty();
        }

        List<Integer> platformOrderIds = platformOrderResponseList.stream().map(PlatformOrderResponse::getPlatformOrderId).collect(Collectors.toList());

        List<PlatformOrderItemEntity> platformOrderItemEntities = platformOrderItemService.getListByOrderIds(platformOrderIds);
        Map<Integer, List<PlatformOrderItemEntity>> platformOrderItemMap = CollectionUtils.isEmpty(platformOrderItemEntities) ? Collections.emptyMap() : platformOrderItemEntities.stream().collect(Collectors.groupingBy(PlatformOrderItemEntity::getPlatformOrderId));

        List<Sku> erpSkuInfoList = erpApiService.getSkuInfoList(platformOrderItemEntities.stream().map(PlatformOrderItemEntity::getSku).distinct().toArray(String[]::new));
        Map<String, Sku> skuMap = erpSkuInfoList.stream().collect(Collectors.toMap(Sku::getSku, Function.identity(), (k1, k2) -> k2));

        List<PlatformReceiverEntity> platformReceiverEntities = platformReceiverService.getByPlatformOrderIds(platformOrderIds);
        Map<Integer, PlatformReceiverEntity> platformReceiverMap = CollectionUtils.isEmpty(platformReceiverEntities) ? Collections.emptyMap() : platformReceiverEntities.stream().collect(Collectors.toMap(PlatformReceiverEntity::getPlatformOrderId, a -> a, (k1, k2) -> k1));

        platformOrderResponseList.forEach(platformOrderResponse -> {
            platformOrderResponse.setOrderStatusName(PlatformOrderStatusEnum.getDescByCode(platformOrderResponse.getOrderStatus()));
            platformOrderResponse.setPlatformName(getTypeByPlatformId(platformOrderResponse.getPlatformId()));
            Optional.ofNullable(platformReceiverMap.get(platformOrderResponse.getPlatformOrderId())).ifPresent(entity -> {
                platformOrderResponse.setCountry(entity.getCountry());
                platformOrderResponse.setReceiverName(entity.getReceiverName());
                platformOrderResponse.setBuyerEmail(entity.getBuyerEmail());
            });
            if (CollectionUtils.isNotEmpty(platformOrderItemMap.get(platformOrderResponse.getPlatformOrderId()))) {
                List<PlatformOrderItemResponse> platformOrderItemResponses = BeanUtil.copyToList(platformOrderItemMap.get(platformOrderResponse.getPlatformOrderId()), PlatformOrderItemResponse.class);
                platformOrderItemResponses.forEach(platformOrderItemResponse -> {
                    Sku erpSku = skuMap.get(platformOrderItemResponse.getSku());
                    if (Objects.nonNull(erpSku)) {
                        platformOrderItemResponse.setImageUrl(erpSku.getImageUrl());
                    }
                    platformOrderItemResponse.setCurrency(platformOrderResponse.getCurrency());
                    platformOrderItemResponse.setOrderPaymentAmount(platformOrderResponse.getPaymentAmount());
                    platformOrderItemResponse.setOrderProductTotalAmount(platformOrderResponse.getProductTotalAmount());
                });
                platformOrderResponse.setItemList(platformOrderItemResponses);
            }
        });

        pageResponse.setContent(platformOrderResponseList);
        return pageResponse;
    }


    private String getTypeByPlatformId(Integer platformId) {
        Map<Integer, String> platformMap = new HashMap<>();
        platformMap.put(PlatformEnum.AMAZON_AMERICA.getCode(), PlatformTypeEnum.Amazon.getDesc());
        platformMap.put(PlatformEnum.AMAZON_BRITAIN.getCode(), PlatformTypeEnum.Amazon.getDesc());
        platformMap.put(PlatformEnum.AMAZON_JAPAN.getCode(), PlatformTypeEnum.Amazon.getDesc());
        platformMap.put(PlatformEnum.AMAZON_CANADA.getCode(), PlatformTypeEnum.Amazon.getDesc());
        platformMap.put(PlatformEnum.AMAZON_GERMANY.getCode(), PlatformTypeEnum.Amazon.getDesc());
        platformMap.put(PlatformEnum.AMAZON_SPAIN.getCode(), PlatformTypeEnum.Amazon.getDesc());
        platformMap.put(PlatformEnum.AMAZON_FRANCE.getCode(), PlatformTypeEnum.Amazon.getDesc());
        platformMap.put(PlatformEnum.AMAZON_INDIA.getCode(), PlatformTypeEnum.Amazon.getDesc());
        platformMap.put(PlatformEnum.AMAZON_ITALY.getCode(), PlatformTypeEnum.Amazon.getDesc());
        platformMap.put(PlatformEnum.AMAZON_MEXICO.getCode(), PlatformTypeEnum.Amazon.getDesc());
        platformMap.put(PlatformEnum.AMAZON_AUSTRALIA.getCode(), PlatformTypeEnum.Amazon.getDesc());
        platformMap.put(PlatformEnum.AMAZON_EUROPE.getCode(), PlatformTypeEnum.Amazon.getDesc());
        platformMap.put(PlatformEnum.AMAZON_NORTHAMERICA.getCode(), PlatformTypeEnum.Amazon.getDesc());
        platformMap.put(PlatformEnum.WALMART.getCode(), PlatformTypeEnum.Walmart.getDesc());
        platformMap.put(PlatformEnum.TIKTOK.getCode(), PlatformTypeEnum.TikTok.getDesc());
        return platformMap.getOrDefault(platformId, "未知");
    }

    private List<Integer> getPlatformIds(List<String> platforms) {
        List<Integer> platformIds = new ArrayList<>();
        if (CollectionUtils.isEmpty(platforms)) {
            return platformIds;
        }
        platforms.forEach(platform -> {
            if (PlatformTypeEnum.Amazon.name().equalsIgnoreCase(platform)) {
                platformIds.addAll(Arrays.asList(PlatformEnum.AMAZON_AMERICA.getCode(),
                        PlatformEnum.AMAZON_BRITAIN.getCode(),
                        PlatformEnum.AMAZON_JAPAN.getCode(),
                        PlatformEnum.AMAZON_CANADA.getCode(),
                        PlatformEnum.AMAZON_GERMANY.getCode(),
                        PlatformEnum.AMAZON_SPAIN.getCode(),
                        PlatformEnum.AMAZON_FRANCE.getCode(),
                        PlatformEnum.AMAZON_INDIA.getCode(),
                        PlatformEnum.AMAZON_ITALY.getCode(),
                        PlatformEnum.AMAZON_MEXICO.getCode(),
                        PlatformEnum.AMAZON_AUSTRALIA.getCode(),
                        PlatformEnum.AMAZON_EUROPE.getCode(),
                        PlatformEnum.AMAZON_NORTHAMERICA.getCode()));
            }
            if (PlatformTypeEnum.Walmart.name().equalsIgnoreCase(platform)) {
                platformIds.add(PlatformEnum.WALMART.getCode());
            }
            if (PlatformTypeEnum.TikTok.name().equalsIgnoreCase(platform)) {
                platformIds.add(PlatformEnum.TIKTOK.getCode());
            }
        });
        return platformIds;
    }

}


