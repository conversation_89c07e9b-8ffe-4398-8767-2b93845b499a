package com.nsy.oms.business.service.platform;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nsy.api.oms.dto.request.order.SampleOrderRequest;
import com.nsy.api.oms.dto.response.order.SampleOrderResponse;
import com.nsy.oms.business.domain.dto.CommonOrderDTO;
import com.nsy.oms.business.domain.request.platform.GetPlatformOrderByPageRequest;
import com.nsy.oms.business.domain.response.base.PageResponse;
import com.nsy.oms.business.domain.response.platform.PlatformOrderResponse;
import com.nsy.oms.business.domain.request.external.CrmOrderPageListRequest;
import com.nsy.oms.business.domain.response.external.CrmOrderInoResponse;
import com.nsy.oms.business.domain.response.external.CrmOrderPageListResponse;
import com.nsy.oms.business.domain.response.order.GetPlatformOrderItemResponse;
import com.nsy.oms.business.domain.response.order.GetPlatformOrderListResponse;
import com.nsy.oms.business.domain.response.order.GetPlatformOrderResponse;
import com.nsy.oms.business.manage.amazon.response.ReportGetFlatFileAllOrdersDataResponse;
import com.nsy.oms.repository.entity.platform.PlatformOrderEntity;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【platform_order(平台订单表)】的数据库操作Service
 * @createDate 2024-05-09 16:01:37
 */
public interface PlatformOrderService extends IService<PlatformOrderEntity> {

    /**
     * 通过平台订单号，查询平台订单
     */
    PlatformOrderResponse getPlatformOrder(String platformOrderNo);

    void syncOrder(GetPlatformOrderListResponse messageContent);

    /**
     * 订单保存
     *
     * @param orderResponse
     */
    void saveOrder(Integer storeId, GetPlatformOrderResponse orderResponse);

    /**
     * 通过店铺ID, 订单号查询订单
     *
     * @param storeId
     * @param orderNo
     */
    PlatformOrderEntity getOrderByStoreIdAndOrderNo(Integer storeId, String orderNo);

    PlatformOrderEntity getOrderByOrderNo(String orderNo);

    void saveOrderItems(CommonOrderDTO orderDTO, List<GetPlatformOrderItemResponse> orderItemList);

    List<PlatformOrderEntity> getListByStoreIdAndOrderNos(Integer storeId, List<String> orderNos);

    /**
     * 订单详情获取成功后，需要回填订单价格，折扣，运费等
     *
     * @param orderDTO
     */
    void orderItemFetchSuccessToUpdateOrder(CommonOrderDTO orderDTO);

    /**
     * 通过店铺ID,订单号查询订单是否取消
     *
     * @param storeId
     * @param orderNo
     * @return
     */
    boolean isCancelOrder(Integer storeId, String orderNo);

    List<PlatformOrderEntity> getFbaPushOrder(Date startDate, Date endDate, Integer fetchCount);

    List<PlatformOrderEntity> cancelOrders(Integer storeId, List<String> orderNoList);

    PlatformOrderEntity getOriginPlatformOrder(String orderId);

    /**
     * 查询已发货的样品订单，订单业务创建时间是昨天的
     *
     * @param request
     * @return
     */
    List<SampleOrderResponse> getSampleOrders(SampleOrderRequest request);

    List<String> getByPlatformOrderNos(List<String> platformOrderNos);

    List<PlatformOrderEntity> getByPlatformOrderNoList(List<String> platformOrderNoList);

    void buildPlatformOrderPrice(PlatformOrderEntity orderEntity, List<ReportGetFlatFileAllOrdersDataResponse> dataList);

    /**
     * crm 订单分页列表
     * @param request
     * @return
     */
    Page<CrmOrderPageListResponse> crmOrderPageList(CrmOrderPageListRequest request);
    /**
     * crm 订单信息
     * @param orderNo
     * @return
     */
    CrmOrderInoResponse getCrmOrderInfo(String orderNo, List<Integer> storeIdList);

    PageResponse<PlatformOrderResponse> getPlatformOrderByPage(GetPlatformOrderByPageRequest request);
}
