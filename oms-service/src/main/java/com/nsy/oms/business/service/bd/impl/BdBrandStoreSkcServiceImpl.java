package com.nsy.oms.business.service.bd.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.exception.InvalidRequestException;
import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.api.core.apicore.privilege.LoginInfoService;
import com.nsy.api.core.apicore.response.CustomExcelResponse;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.DateUtils;
import com.nsy.api.core.apicore.util.NsyExcelUtil;
import com.nsy.api.oms.enums.QuartzDownloadQueueTypeEnum;
import com.nsy.business.base.enums.LocationEnum;
import com.nsy.oms.annotation.IgnoreTenant;
import com.nsy.oms.business.domain.download.bd.BdBrandStoreSkcExport;
import com.nsy.oms.business.domain.request.bd.brand.BdBrandCommonOperateRequest;
import com.nsy.oms.business.domain.request.bd.brand.BdBrandCommonPageRequest;
import com.nsy.oms.business.domain.request.bd.brand.BdBrandQueryRequest;
import com.nsy.oms.business.domain.request.bd.brand.BdBrandStoreSkcFirstOrderAddRequest;
import com.nsy.oms.business.domain.request.bd.brand.BdBrandStoreSkcQueryRequest;
import com.nsy.oms.business.domain.request.bd.brand.BdBrandStoreSkcSaveRequest;
import com.nsy.oms.business.domain.request.download.DownloadRequest;
import com.nsy.oms.business.domain.request.upload.UploadRequest;
import com.nsy.oms.business.domain.response.base.DownloadResponse;
import com.nsy.oms.business.domain.response.bd.brand.BdBrandInfoListResponse;
import com.nsy.oms.business.domain.response.bd.brand.BdBrandStoreSkc;
import com.nsy.oms.business.domain.response.bd.brand.BrandStoreSkcResponse;
import com.nsy.oms.business.domain.response.bd.brand.StoreSkcInfoRequest;
import com.nsy.oms.business.domain.response.upload.UploadResponse;
import com.nsy.oms.business.domain.upload.BrandStoreSkcOperateImport;
import com.nsy.oms.business.manage.amazon.AmazonApiService;
import com.nsy.oms.business.manage.amazon.request.AmazonBrandStoreProductSkuRequest;
import com.nsy.oms.business.manage.amazon.response.AmazonBrandStoreProductSkuResponse;
import com.nsy.oms.business.manage.amazon.response.GetStoreProductBrandResponse;
import com.nsy.oms.business.manage.pms.PmsApiService;
import com.nsy.oms.business.manage.pms.response.GetProductCategoryInfoResponse;
import com.nsy.oms.business.manage.pms.response.ProductInfoBySkuResponse;
import com.nsy.oms.business.service.bd.BdBrandService;
import com.nsy.oms.business.service.bd.BdBrandStoreService;
import com.nsy.oms.business.service.bd.BdBrandStoreSkcService;
import com.nsy.oms.business.service.download.IDownloadService;
import com.nsy.oms.business.service.sa.SaStoreService;
import com.nsy.oms.business.service.upload.IUploadService;
import com.nsy.oms.constants.BrandStoreConstant;
import com.nsy.oms.constants.StringConstant;
import com.nsy.oms.constants.upload.QuartzUploadQueueTypeEnum;
import com.nsy.oms.enums.CommonStateEnum;
import com.nsy.oms.enums.bd.OpenOrCloseEnum;
import com.nsy.oms.enums.bd.PurchaseStyleEnum;
import com.nsy.oms.repository.entity.base.BaseMpEntity;
import com.nsy.oms.repository.entity.bd.BdBrandEntity;
import com.nsy.oms.repository.entity.bd.BdBrandStoreEntity;
import com.nsy.oms.repository.entity.bd.BdBrandStoreSkcEntity;
import com.nsy.oms.repository.entity.sa.SaStoreEntity;
import com.nsy.oms.repository.sql.mapper.bd.BdBrandStoreSkcMapper;
import com.nsy.oms.utils.JsonMapper;
import org.apache.commons.collections4.ListUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 品牌店铺SKC表
 *
 * <AUTHOR>
 * @date 2023-09-21
 */
@Service
public class BdBrandStoreSkcServiceImpl extends ServiceImpl<BdBrandStoreSkcMapper, BdBrandStoreSkcEntity> implements BdBrandStoreSkcService, IUploadService, IDownloadService {
    private static final Logger LOGGER = LoggerFactory.getLogger(BdBrandStoreSkcServiceImpl.class);
    @Autowired
    private BdBrandService bdBrandService;
    @Autowired
    private BdBrandStoreService bdBrandStoreService;
    @Autowired
    PmsApiService pmsApiService;
    @Autowired
    private AmazonApiService amazonApiService;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private SaStoreService saStoreService;

    @Override
    public PageResponse<BdBrandStoreSkc> page(BdBrandCommonPageRequest request) {
        LambdaQueryWrapper<BdBrandStoreSkcEntity> wrapper = new LambdaQueryWrapper<>();
        List<String> skcList = Lists.newArrayList();
        if (StringUtils.hasText(request.getSkc())) {
            skcList = Arrays.stream(request.getSkc().split(StringConstant.COMMA)).distinct().collect(Collectors.toList());
        }
        wrapper.in(!CollectionUtils.isEmpty(request.getBrandIds()), BdBrandStoreSkcEntity::getBrandId, request.getBrandIds())
                .in(!CollectionUtils.isEmpty(request.getStoreIds()), BdBrandStoreSkcEntity::getStoreId, request.getStoreIds())
                .eq(StringUtils.hasText(request.getSpu()), BdBrandStoreSkcEntity::getSpu, request.getSpu())
                .in(!CollectionUtils.isEmpty(skcList), BdBrandStoreSkcEntity::getSkc, skcList)
                .eq(request.getIsOpenFbaBrand() != null, BdBrandStoreSkcEntity::getIsOpenFbaBrand, request.getIsOpenFbaBrand())
                .eq(Optional.ofNullable(request.getIsOpenPurchaseBrand()).isPresent(), BdBrandStoreSkcEntity::getIsOpenPurchaseBrand, request.getIsOpenPurchaseBrand())
                .in(!CollectionUtils.isEmpty(request.getBrandStoreSkcIds()), BdBrandStoreSkcEntity::getBrandStoreSkcId, request.getBrandStoreSkcIds())
                .eq(BaseMpEntity::getLocation, StringUtils.hasText(request.getLocation()) ? request.getLocation() : loginInfoService.getLocation());
        Page<BdBrandStoreSkcEntity> page = this.page(new Page<>(request.getPageIndex(), request.getPageSize()), wrapper);
        PageResponse<BdBrandStoreSkc> pageResponse = PageResponse.of(page.getTotal());
        pageResponse.setContent(page.getRecords().stream().map(e -> {
            BdBrandStoreSkc brandStoreSkc = new BdBrandStoreSkc();
            BeanUtilsEx.copyProperties(e, brandStoreSkc);
            return brandStoreSkc;
        }).collect(Collectors.toList()));
        return pageResponse;
    }

    @Override
    @Transactional
    public void openFbaBrand(BdBrandCommonOperateRequest request) {
        List<BdBrandStoreSkcEntity> list = listByIds(request.getIds());
        String name = loginInfoService.getName();
        list.forEach(e -> {
            e.setIsOpenFbaBrand(request.getFlag());
            if (e.getIsOpenFbaBrand() == 1 && e.getOpenFbaBrandDate() == null) {
                e.setOpenFbaBrandDate(new Date());
            }
            e.setUpdateBy(name);
            e.setIsJobFetch(0);
        });
        updateBatchById(list);
    }

    @Override
    @Transactional
    public void openPurchaseBrand(BdBrandCommonOperateRequest request) {
        List<BdBrandStoreSkcEntity> list = listByIds(request.getIds());
        list.forEach(e -> {
            e.setIsOpenPurchaseBrand(request.getFlag());
            if (e.getIsOpenPurchaseBrand() == 1 && e.getOpenPurchaseDate() == null) {
                e.setOpenPurchaseDate(new Date());
            }
            e.setUpdateBy(loginInfoService.getName());
        });
        updateBatchById(list);
    }

    @Override
    public void firstOrderAddSkc(BdBrandStoreSkcFirstOrderAddRequest request) {
        for (BdBrandStoreSkcFirstOrderAddRequest.SkcInfo skcInfo : request.getSkcInfoList()) {
            List<BdBrandStoreSkcEntity> list = this.listBySkc(skcInfo.getBrandId(), skcInfo.getStoreId(), skcInfo.getSkc());
            // 如果该SKU不在品牌店铺商品，就插入，如果已存在就忽略。
            if (!list.isEmpty()) {
                LOGGER.warn("skc={},storeId={},brandId={}已存在数据", skcInfo.getSkc(), skcInfo.getStoreId(), skcInfo.getBrandId());
                continue;
            }
            BdBrandEntity brandEntity = bdBrandService.getById(skcInfo.getBrandId());
            if (brandEntity == null) {
                throw new InvalidRequestException("品牌不存在");
            }
            BdBrandStoreEntity brandStoreEntity = bdBrandStoreService.getByStoreIdAndBrandId(skcInfo.getStoreId(), skcInfo.getBrandId());
            if (brandStoreEntity == null) {
                throw new InvalidRequestException("品牌店铺不存在");
            }

            BdBrandStoreSkcEntity entity = new BdBrandStoreSkcEntity();
            entity.setBrandStoreId(brandStoreEntity.getBrandStoreId());
            entity.setBrandId(brandStoreEntity.getBrandId());
            entity.setBrandName(brandStoreEntity.getBrandName());
            entity.setStoreId(brandStoreEntity.getStoreId());
            entity.setStoreName(brandStoreEntity.getStoreName());
            entity.setSpu(skcInfo.getSpu());
            entity.setSkc(skcInfo.getSkc());
            entity.setCategoryId(skcInfo.getCategoryId());
            entity.setCategoryName(skcInfo.getCategoryName());
            // 首申请单创建的品牌店铺skc默认开启fba挂牌
            entity.setIsOpenFbaBrand(1);
            entity.setOpenFbaBrandDate(new Date());
            entity.setCreateBy(request.getCreateBy());
            entity.setLocation(request.getLocation());
            save(entity);
        }
    }

    @Override
    public void storeSkcBrandSpaceInfo(StoreSkcInfoRequest response, List<BdBrandStoreEntity> bdBrandStoreEntities, StoreSkcInfoRequest request) {
        LambdaQueryWrapper<BdBrandStoreSkcEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(BdBrandStoreSkcEntity::getBrandStoreId, bdBrandStoreEntities.stream().map(BdBrandStoreEntity::getBrandStoreId).collect(Collectors.toList()));
        wrapper.in(BdBrandStoreSkcEntity::getSkc, response.getStoreSkcInfoList().stream().map(StoreSkcInfoRequest.StoreSkcInfo::getSkc).collect(Collectors.toList()));
        List<BdBrandStoreSkcEntity> bdBrandStoreSkcEntityList = this.list(wrapper);

        //品牌店铺商品
        Map<String, BdBrandStoreSkcEntity> brandStoreSkcMap = bdBrandStoreSkcEntityList.stream().collect(Collectors.toMap(e -> String.format("%s_%s", e.getBrandStoreId(), e.getSkc()), a -> a, (k1, k2) -> k1));
        //品牌店铺
        Map<Integer, BdBrandStoreEntity> brandStoreMap = bdBrandStoreEntities.stream().collect(Collectors.toMap(BdBrandStoreEntity::getStoreId, a -> a, (k1, k2) -> k1));
        // 品牌信息
        Map<Integer, BdBrandEntity> brandMap = CollectionUtils.isEmpty(bdBrandStoreEntities) ? Collections.emptyMap()
                : bdBrandService.listByIds(bdBrandStoreEntities.stream().map(BdBrandStoreEntity::getBrandId).collect(Collectors.toSet()))
                .stream().collect(Collectors.toMap(BdBrandEntity::getBrandId, Function.identity()));

        response.getStoreSkcInfoList().forEach(storeSkcInfo -> {
            BdBrandStoreEntity brandStoreEntity = brandStoreMap.get(storeSkcInfo.getStoreId());
            if (!Optional.ofNullable(brandStoreEntity).isPresent()) {
                return;
            }
            BdBrandEntity bdBrandEntity = brandMap.get(brandStoreEntity.getBrandId());
            if (!Optional.ofNullable(bdBrandEntity).isPresent()) {
                return;
            }
            BdBrandStoreSkcEntity bdBrandStoreSkcEntity = brandStoreSkcMap.get(String.format("%s_%s", brandStoreEntity.getBrandStoreId(), storeSkcInfo.getSkc()));
            if (0 == brandStoreEntity.getIsOpenPurchaseBrand() || Optional.ofNullable(bdBrandStoreSkcEntity).isPresent() && 0 == bdBrandStoreSkcEntity.getIsOpenPurchaseBrand()) {
                /*
                a) 如果店铺开启采购挂牌 = 开启，按店铺+SKC查询品牌店铺商品的开启采购挂牌，
                   ii) 如果存在商品开启采购挂牌=关闭，则不返回对应店铺+SKC给scm（即不是品牌）
                b) 如果店铺开启采购挂牌 = 关闭，不返回给scm
                c)如果店铺开启采购挂牌 = 部分开启 按店铺+SKC查询品牌店铺商品的开启采购挂牌，
                    ii) 如果存在商品开启采购挂牌=关闭，则不返回给scm
                */
                return;
            } else if (Optional.ofNullable(bdBrandStoreSkcEntity).isPresent() && 1 == bdBrandStoreSkcEntity.getIsOpenPurchaseBrand()) {
                /*
                a) 如果店铺开启采购挂牌 = 开启，按店铺+SKC查询品牌店铺商品的开启采购挂牌，
                   i) 如果存在商品开启采购挂牌=开启，则返回是品牌给scm
                c)如果店铺开启采购挂牌 = 部分开启 按店铺+SKC查询品牌店铺商品的开启采购挂牌，
                   i) 如果存在商品开启采购挂牌=开启，则返回是品牌给scm
                */
                setStoreSkcInfo(storeSkcInfo, bdBrandEntity);
            } else if (!Optional.ofNullable(bdBrandStoreSkcEntity).isPresent()) {
                /*
                 a)如果店铺开启采购挂牌 = 开启
                    iii) 如果不存在记录，则插入一条数据，默认设置为开启采购挂牌，同时FBA挂牌也默认开启，然后返回是品牌给scm
                 c)如果店铺开启采购挂牌 = 部分开启 按店铺+SKC查询品牌店铺商品的开启采购挂牌，
                    iii) 如果不存在记录，则增加判断商品是否配置了【亚马逊2025春夏款】标签 或者 属于指定品类限制（插入逻辑）（其中：SIDEFEEL的品类限制 = 女装/牛仔 下的所有商品（包括所有三级品类）；其他品牌如evaless等默认没有品类限制）
                        1) 如果是，插入一条数据，默认设置为开启采购挂牌，同时FBA挂牌也默认开启，然后返回是品牌给scm
                        2) 如果否，则不返回给scm
                */
                ProductInfoBySkuResponse skuResponse = pmsApiService.getProductInfo(storeSkcInfo.getSkc(), null);
                if (!Optional.ofNullable(skuResponse).isPresent()) {
                    return;
                }
                //(店铺部分开启、亚马逊2025春夏款、SIDEFEEL、女装/牛仔)
                if (saveSidefeelStoreSkcBrandSpaceInfo(brandStoreEntity, skuResponse, storeSkcInfo, request, bdBrandEntity)) {
                    return;
                }
                if (2 == brandStoreEntity.getIsOpenPurchaseBrand()
                        && (CollectionUtils.isEmpty(skuResponse.getProductStyleList())
                        || !(skuResponse.getProductStyleList().stream().map(ProductInfoBySkuResponse.ProductStyle::getStyleName).collect(Collectors.toList()).contains(PurchaseStyleEnum.AMAZON_2025_AUTUMN_AND_WINTER.getCode())
                        || skuResponse.getProductStyleList().stream().map(ProductInfoBySkuResponse.ProductStyle::getStyleName).collect(Collectors.toList()).contains(PurchaseStyleEnum.AMAZON_2025_SPRING_AND_SUMMER.getCode())))) {
                    return;
                }
                saveStoreSkcBrandSpaceInfo(brandStoreEntity, skuResponse, storeSkcInfo, request, bdBrandEntity);
            }
        });
    }

    @Override
    public StoreSkcInfoRequest storeSkcBrandSpaceInfo(StoreSkcInfoRequest request) {
        StoreSkcInfoRequest response = new StoreSkcInfoRequest();
        BeanUtilsEx.copyProperties(request, response);

        LambdaQueryWrapper<BdBrandStoreSkcEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(BdBrandStoreSkcEntity::getStoreId, request.getStoreSkcInfoList().stream().map(StoreSkcInfoRequest.StoreSkcInfo::getStoreId).collect(Collectors.toSet()))
                .in(BdBrandStoreSkcEntity::getSkc, request.getStoreSkcInfoList().stream().map(StoreSkcInfoRequest.StoreSkcInfo::getSkc).collect(Collectors.toSet()));
        // 品牌店铺商品信息
        List<BdBrandStoreSkcEntity> list = this.list(wrapper);
        if (list.isEmpty()) {
            return response;
        }
        // 品牌店铺信息-要开启采购挂牌的
        List<BdBrandStoreEntity> brandStoreEntities = bdBrandStoreService
                .listByIds(list.stream().map(BdBrandStoreSkcEntity::getBrandStoreId).collect(Collectors.toSet()));
        Set<Integer> openPurchaseBrandStoreIds = brandStoreEntities.stream()
                .filter(s -> s.getIsOpenPurchaseBrand() == 1 || s.getIsOpenPurchaseBrand() == 2).map(BdBrandStoreEntity::getStoreId).collect(Collectors.toSet());
        // 品牌信息
        Map<Integer, BdBrandEntity> brandMap = brandStoreEntities.isEmpty() ? Collections.emptyMap()
                : bdBrandService.listByIds(brandStoreEntities.stream().map(BdBrandStoreEntity::getBrandId).collect(Collectors.toSet()))
                .stream().collect(Collectors.toMap(BdBrandEntity::getBrandId, Function.identity()));

        response.getStoreSkcInfoList().forEach(s -> {
            // 店铺+skc级别，且该店铺对应的品牌店铺配置了开启采购挂牌
            list.stream()
                    .filter(e -> e.getStoreId().equals(s.getStoreId()) && e.getSkc().equals(s.getSkc()) && openPurchaseBrandStoreIds.contains(s.getStoreId()) && e.getIsOpenPurchaseBrand() == 1)
                    .findAny()
                    .flatMap(e -> Optional.ofNullable(brandMap.get(e.getBrandId())))
                    .ifPresent(bdBrandEntity -> {
                        s.setBrandId(bdBrandEntity.getBrandId());
                        s.setBrandName(bdBrandEntity.getBrandName());
                        s.setBrandShortName(bdBrandEntity.getBrandShortName());
                        s.setSpaceId(bdBrandEntity.getSpaceId());
                        s.setSpaceName(bdBrandEntity.getSpaceName());
                    });
        });
        return response;
    }

    private void setStoreSkcInfo(StoreSkcInfoRequest.StoreSkcInfo storeSkcInfo, BdBrandEntity bdBrandEntity) {
        storeSkcInfo.setBrandId(bdBrandEntity.getBrandId());
        storeSkcInfo.setBrandName(bdBrandEntity.getBrandName());
        storeSkcInfo.setBrandShortName(bdBrandEntity.getBrandShortName());
        storeSkcInfo.setSpaceId(bdBrandEntity.getSpaceId());
        storeSkcInfo.setSpaceName(bdBrandEntity.getSpaceName());
    }

    @Override
    public StoreSkcInfoRequest storeSkcBrandSpace(StoreSkcInfoRequest request) {
        LambdaQueryWrapper<BdBrandStoreSkcEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(BdBrandStoreSkcEntity::getStoreId, request.getStoreSkcInfoList().stream().map(StoreSkcInfoRequest.StoreSkcInfo::getStoreId).collect(Collectors.toSet()))
                .in(BdBrandStoreSkcEntity::getSkc, request.getStoreSkcInfoList().stream().map(StoreSkcInfoRequest.StoreSkcInfo::getSkc).collect(Collectors.toSet()))
                .eq(BdBrandStoreSkcEntity::getIsOpenFbaBrand, 1);
        // 品牌店铺商品信息
        List<BdBrandStoreSkcEntity> list = this.list(wrapper);
        if (list.isEmpty()) {
            return request;
        }
        // 品牌店铺信息-需要存在
        List<BdBrandStoreEntity> brandStoreEntities = bdBrandStoreService.listByIds(list.stream().map(BdBrandStoreSkcEntity::getBrandStoreId).collect(Collectors.toSet()));
        Set<Integer> storeIds = brandStoreEntities.stream().map(BdBrandStoreEntity::getStoreId).collect(Collectors.toSet());
        // 品牌信息
        Map<Integer, BdBrandEntity> brandMap = brandStoreEntities.isEmpty() ? Collections.emptyMap()
                : bdBrandService.listByIds(brandStoreEntities.stream().map(BdBrandStoreEntity::getBrandId).collect(Collectors.toSet())).stream().collect(Collectors.toMap(BdBrandEntity::getBrandId, Function.identity()));
        request.getStoreSkcInfoList().forEach(item -> {
            Optional<BdBrandStoreSkcEntity> optional = list.stream().filter(i -> Objects.equals(i.getStoreId(), item.getStoreId()) && i.getSkc().equals(item.getSkc())).findAny();
            if (!optional.isPresent() || !storeIds.contains(item.getStoreId())) return;
            BdBrandEntity bdBrandEntity = brandMap.get(optional.get().getBrandId());
            if (Objects.isNull(bdBrandEntity)) return;
            item.setBrandId(bdBrandEntity.getBrandId());
            item.setBrandName(bdBrandEntity.getBrandName());
            item.setBrandShortName(bdBrandEntity.getBrandShortName());
            item.setSpaceId(bdBrandEntity.getSpaceId());
            item.setSpaceName(bdBrandEntity.getSpaceName());
        });
        return request;
    }

    @Override
    public List<BdBrandStoreSkcEntity> listBySkc(Integer brandId, Integer storeId, String skc) {
        LambdaQueryWrapper<BdBrandStoreSkcEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BdBrandStoreSkcEntity::getBrandId, brandId).eq(BdBrandStoreSkcEntity::getStoreId, storeId).eq(BdBrandStoreSkcEntity::getSkc, skc);
        return list(wrapper);
    }

    @Override
    @Transactional
    public Integer addBdBrandStoreSkc(BdBrandStoreSkcSaveRequest request) {
        List<BdBrandStoreSkcEntity> list = this.listBySkc(request.getBrandId(), request.getStoreId(), request.getSkc());
        if (!list.isEmpty()) {
            throw new InvalidRequestException("品牌店铺商品SKC已存在，请勿重复新增");
        }
        BdBrandStoreEntity brandStoreEntity = bdBrandStoreService.getByStoreIdAndBrandId(request.getStoreId(), request.getBrandId());
        if (brandStoreEntity == null) {
            throw new InvalidRequestException("品牌店铺配置不存在");
        }
        ProductInfoBySkuResponse skuResponse = pmsApiService.getProductInfo(request.getSkc(), null);
        if (skuResponse == null) {
            throw new InvalidRequestException("商品系统不存在该SKC");
        }

        BdBrandStoreSkcEntity entity = new BdBrandStoreSkcEntity();
        entity.setBrandStoreId(brandStoreEntity.getBrandStoreId());
        entity.setBrandId(brandStoreEntity.getBrandId());
        entity.setBrandName(brandStoreEntity.getBrandName());
        entity.setStoreId(brandStoreEntity.getStoreId());
        entity.setStoreName(brandStoreEntity.getStoreName());
        entity.setSpu(skuResponse.getProductSku());
        entity.setSkc(request.getSkc());
        entity.setCategoryId(skuResponse.getCategoryId());
        entity.setCategoryName(skuResponse.getCategoryName());
        entity.setIsOpenFbaBrand(request.getIsOpenFbaBrand());
        entity.setIsOpenPurchaseBrand(request.getIsOpenPurchaseBrand());
        if (request.getIsOpenFbaBrand() == 1) {
            entity.setOpenFbaBrandDate(new Date());
        }
        if (CommonStateEnum.YES.getCode().equals(request.getIsOpenPurchaseBrand())) {
            entity.setOpenPurchaseDate(new Date());
        }
        entity.setCreateBy(loginInfoService.getName());
        entity.setUpdateBy(entity.getCreateBy());
        save(entity);

        bdBrandService.updateAllocateProductNum(entity.getBrandId());
        return entity.getBrandStoreSkcId();
    }

    @Override
    public QuartzUploadQueueTypeEnum uploadType() {
        return QuartzUploadQueueTypeEnum.OMS_BRAND_STORE_SKC_OPERATE_IMPORT;
    }

    @Override
    public UploadResponse processUploadData(UploadRequest request) {
        UploadResponse uploadResponse = new UploadResponse();
        if (!StringUtils.hasText(request.getDataJsonStr())) {
            return uploadResponse;
        }
        List<BrandStoreSkcOperateImport> importList = JsonMapper.jsonStringToObjectArray(request.getDataJsonStr(), BrandStoreSkcOperateImport.class);
        if (CollectionUtils.isEmpty(importList)) {
            return uploadResponse;
        }
        List<BrandStoreSkcOperateImport> errorList = new ArrayList<>();
        LambdaQueryWrapper<BdBrandStoreSkcEntity> wrapper = new LambdaQueryWrapper<>();
        for (BrandStoreSkcOperateImport importData : importList) {
            wrapper.eq(BdBrandStoreSkcEntity::getBrandName, importData.getBrandName())
                    .eq(BdBrandStoreSkcEntity::getStoreName, importData.getStoreName())
                    .eq(BdBrandStoreSkcEntity::getSkc, importData.getSkc());
            List<BdBrandStoreSkcEntity> list = this.list(wrapper);
            wrapper.clear();
            if (list.isEmpty()) {
                try {
                    this.addBdBrandStoreSkcRow(importData, request.getCreateBy());
                } catch (Exception e) {
                    importData.setErrorMsg(e.getMessage());
                    errorList.add(importData);
                }
                continue;
            }
            list.forEach(e -> {
                e.setIsOpenFbaBrand(OpenOrCloseEnum.OPEN.getName().equals(importData.getFbaBrandFlag()) ? 1 : 0);
                if (e.getIsOpenFbaBrand() == 1 && e.getOpenFbaBrandDate() == null) {
                    e.setOpenFbaBrandDate(new Date());
                }
                e.setIsJobFetch(0);
                e.setUpdateBy(request.getCreateBy());
            });
            updateBatchById(list);
        }
        if (!errorList.isEmpty()) {
            uploadResponse.setDataJsonStr(JsonMapper.toJson(errorList));
        }
        return uploadResponse;
    }

    private void addBdBrandStoreSkcRow(BrandStoreSkcOperateImport importData, String createBy) {
        List<SaStoreEntity> saStoreEntities = saStoreService.getStoreByStoreNames(Collections.singletonList(importData.getStoreName()));
        if (CollectionUtils.isEmpty(saStoreEntities)) throw new BusinessServiceException("该店铺不存在");
        BdBrandEntity brand = bdBrandService.getByName(importData.getBrandName(), saStoreEntities.get(0).getLocation());
        if (Objects.isNull(brand)) throw new BusinessServiceException("该品牌不存在");
        BdBrandStoreEntity brandStoreEntity = bdBrandStoreService.getByStoreIdAndBrandId(saStoreEntities.get(0).getId(), brand.getBrandId());
        if (brandStoreEntity == null) {
            throw new InvalidRequestException("品牌店铺配置不存在");
        }
        ProductInfoBySkuResponse skuResponse = pmsApiService.getProductInfo(importData.getSkc(), null);
        if (skuResponse == null) {
            throw new InvalidRequestException("商品系统不存在该SKC");
        }
        BdBrandStoreSkcEntity entity = new BdBrandStoreSkcEntity();
        entity.setBrandStoreId(brandStoreEntity.getBrandStoreId());
        entity.setBrandId(brandStoreEntity.getBrandId());
        entity.setBrandName(brandStoreEntity.getBrandName());
        entity.setStoreId(brandStoreEntity.getStoreId());
        entity.setStoreName(brandStoreEntity.getStoreName());
        entity.setSpu(skuResponse.getProductSku());
        entity.setSkc(importData.getSkc());
        entity.setCategoryId(skuResponse.getCategoryId());
        entity.setCategoryName(skuResponse.getCategoryName());
        entity.setIsOpenFbaBrand(OpenOrCloseEnum.OPEN.getName().equals(importData.getFbaBrandFlag()) ? 1 : 0);
        if (entity.getIsOpenFbaBrand() == 1) {
            entity.setOpenFbaBrandDate(new Date());
        }
        entity.setCreateBy(createBy);
        entity.setUpdateBy(entity.getCreateBy());
        entity.setLocation(saStoreEntities.get(0).getLocation());
        save(entity);
        bdBrandService.updateAllocateProductNum(entity.getBrandId());
    }

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.OMS_BRAND_STORE_SKC_LIST;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        BdBrandCommonPageRequest searchRequest = JsonMapper.fromJson(request.getRequestContent(), BdBrandCommonPageRequest.class);
        searchRequest.setPageIndex(request.getPageIndex());
        searchRequest.setPageSize(request.getPageSize());
        searchRequest.setLocation(request.getLocation());
        PageResponse<BdBrandStoreSkc> pageResponse = this.page(searchRequest);
        if (CollectionUtils.isEmpty(pageResponse.getContent())) {
            return DownloadResponse.of("[]", 0L);
        }
        CustomExcelResponse customExcelResponse = new CustomExcelResponse();
        customExcelResponse.setHeaders(NsyExcelUtil.getCommonHeads(BdBrandStoreSkcExport.class));
        customExcelResponse.setData(pageResponse.getContent().stream()
                .map(e -> NsyExcelUtil.getData(BdBrandStoreSkcExport.class, buildExport(e))).collect(Collectors.toList()));
        return DownloadResponse.of(JsonMapper.toJson(customExcelResponse), pageResponse.getTotalCount());
    }

    private BdBrandStoreSkcExport buildExport(BdBrandStoreSkc brandStoreSkc) {
        BdBrandStoreSkcExport export = new BdBrandStoreSkcExport();
        export.setBrandName(brandStoreSkc.getBrandName());
        export.setStoreName(brandStoreSkc.getStoreName());
        export.setSkc(brandStoreSkc.getSkc());
        export.setCategoryName(brandStoreSkc.getCategoryName());
        export.setIsOpenFbaBrand(brandStoreSkc.getIsOpenFbaBrand() == 1 ? OpenOrCloseEnum.OPEN.getName() : OpenOrCloseEnum.CLOSE.getName());
        export.setOpenFbaBrandDate(DateUtil.format(brandStoreSkc.getOpenFbaBrandDate(), DateUtils.DATE_FORMAT_DATE4));
        export.setCreateDate(DateUtil.format(brandStoreSkc.getCreateDate(), DateUtils.DATE_FORMAT_DATE4));
        return export;
    }

    @Transactional
    @Override
    public void syncStoreBrand(Integer storeId, Set<Integer> changedBrandSet) {
        List<BdBrandStoreSkcEntity> storeSkcEntities = new ArrayList<>();
        SaStoreEntity satore = saStoreService.getById(storeId);
        //从亚马逊请求所有active的数据
        List<GetStoreProductBrandResponse> storeActiveSkuBrands = amazonApiService.getStoreActiveSkuBrands(Collections.singletonList(storeId));
        List<GetStoreProductBrandResponse> distinctProducts = new ArrayList<>(storeActiveSkuBrands.stream()
                .collect(Collectors.toMap(GetStoreProductBrandResponse::getSellerSku, Function.identity(), (existing, replacement) -> existing))
                .values());
        LOGGER.info("[syncStoreBrand] distinctProducts storeId:{} size:{}", storeId, distinctProducts.size());
        List<List<GetStoreProductBrandResponse>> partitionSkuBrands = ListUtils.partition(distinctProducts, 50);
        for (List<GetStoreProductBrandResponse> skuBrandIds : partitionSkuBrands) {
            //请求商品系统获取数据
            List<GetProductCategoryInfoResponse> erpSkuInfos = pmsApiService.getProductCategoryInfo(skuBrandIds.stream().map(GetStoreProductBrandResponse::getErpSku).collect(Collectors.toList()));
            Map<String, GetProductCategoryInfoResponse> erpSkuMap = erpSkuInfos.stream().collect(Collectors.toMap(GetProductCategoryInfoResponse::getErpSku, Function.identity(), (v1, v2) -> v2));
            for (GetStoreProductBrandResponse response : skuBrandIds) {
                BdBrandEntity brandEntity = bdBrandService.getByName(response.getBrand(), satore.getLocation());
                GetProductCategoryInfoResponse productCategoryInfoResponse = erpSkuMap.get(response.getErpSku());
                //判断品牌是否存在,并且在pms里面查询的到
                if (productCategoryInfoResponse != null && ObjectUtil.isNotEmpty(brandEntity)) {
                    Integer brandId = brandEntity.getBrandId();
                    //判断品牌店铺是否存在
                    BdBrandStoreEntity brandStore = bdBrandStoreService.getByStoreIdAndBrandId(response.getStoreId(), brandId);
                    if (brandStore != null) {
                        buildStoreSkc(changedBrandSet, response, brandId, brandStore, productCategoryInfoResponse, storeSkcEntities, brandEntity);
                    }
                }
            }
        }
        LOGGER.info("[syncStoreBrand] storeSkcEntities storeId:{} size:{}", storeId, storeSkcEntities.size());
        //过滤相同skc的数据
        List<BdBrandStoreSkcEntity> distinctSKcProducts = new ArrayList<>(storeSkcEntities.stream()
                .collect(Collectors.toMap(BdBrandStoreSkcEntity::getSkc, Function.identity(), (existing, replacement) -> existing))
                .values());

        List<BdBrandStoreSkcEntity> needSaveData = new ArrayList<>();
        List<BdBrandStoreSkcEntity> storeSkcList = this.getByAmazonStoreId(storeId);
        String skcUniqueKeyFormat = "{}_{}_{}";
        Map<String, BdBrandStoreSkcEntity> storeBrandIdSkcMap = storeSkcList.stream().collect(Collectors.toMap(item -> StrUtil.format(skcUniqueKeyFormat, item.getStoreId(), item.getBrandId(), item.getSkc()), Function.identity(), (k1, k2) -> k1));

        for (BdBrandStoreSkcEntity bdBrandStoreSkcEntity : distinctSKcProducts) {
            //判断数据库里是否已经存在数据
            BdBrandStoreSkcEntity existEntity = storeBrandIdSkcMap.get(StrUtil.format(skcUniqueKeyFormat, bdBrandStoreSkcEntity.getStoreId(), bdBrandStoreSkcEntity.getBrandId(), bdBrandStoreSkcEntity.getSkc()));
            if (existEntity == null) {
                needSaveData.add(bdBrandStoreSkcEntity);
            }
        }
        LOGGER.info("[syncStoreBrand] distinctSKcProducts storeId:{} size:{}", storeId, needSaveData.size());
        this.saveBatch(needSaveData);
    }

    private static void buildStoreSkc(Set<Integer> changedBrandSet, GetStoreProductBrandResponse response, Integer brandId, BdBrandStoreEntity brandStore, GetProductCategoryInfoResponse productCategoryInfoResponse, List<BdBrandStoreSkcEntity> storeSkcEntities, BdBrandEntity brandEntity) {
        BdBrandStoreSkcEntity bdBrandStoreSkcEntity = new BdBrandStoreSkcEntity();
        bdBrandStoreSkcEntity.setBrandId(brandId);
        bdBrandStoreSkcEntity.setBrandStoreId(brandStore.getBrandStoreId());
        bdBrandStoreSkcEntity.setBrandName(brandEntity.getBrandName());
        bdBrandStoreSkcEntity.setStoreId(response.getStoreId());
        bdBrandStoreSkcEntity.setStoreName(response.getStoreName());
        bdBrandStoreSkcEntity.setIsJobFetch(1);
        bdBrandStoreSkcEntity.setSkc(productCategoryInfoResponse.getSkc());
        bdBrandStoreSkcEntity.setSpu(productCategoryInfoResponse.getSpu());
        bdBrandStoreSkcEntity.setCategoryId(productCategoryInfoResponse.getCategoryId());
        bdBrandStoreSkcEntity.setCategoryName(productCategoryInfoResponse.getCategoryName());
        String location = brandStore.getLocation();
        bdBrandStoreSkcEntity.setLocation(location);
        bdBrandStoreSkcEntity.setIsOpenPurchaseBrand(CommonStateEnum.YES.getCode().equals(brandStore.getIsOpenPurchaseBrand()) ? CommonStateEnum.YES.getCode() : CommonStateEnum.NO.getCode());
        if (CommonStateEnum.YES.getCode().equals(bdBrandStoreSkcEntity.getIsOpenPurchaseBrand())) {
            bdBrandStoreSkcEntity.setOpenPurchaseDate(new Date());
        }
        int isOpenFbaBrand = 0;
        if (location.equals(LocationEnum.QUANZHOU.name())) {
            isOpenFbaBrand = 1;
        }
        bdBrandStoreSkcEntity.setIsOpenFbaBrand(isOpenFbaBrand);
        storeSkcEntities.add(bdBrandStoreSkcEntity);
        changedBrandSet.add(brandEntity.getBrandId());
    }


    /**
     * 如果选择了品牌性质的仓库，则上传的 sku 必须是开启该品牌的商品，提示：LCxxx 未开启xxx品牌挂牌，不能从品牌仓发货；
     * 如果选择了非品牌性质的仓库，则上传的 sku 不能是开启任何品牌的商品，提示：LCxxx 已开启xxx品牌挂牌，请从品牌仓发货；
     * 如果选择可以混品牌的仓库，则上传的sku不限制品牌，创建补货单后自动根据sku的品牌拆成不同的单（这个目前应该有支持）
     */
    @Override
    public AmazonBrandStoreProductSkuResponse queryByStoreSku(AmazonBrandStoreProductSkuRequest request) {
        AmazonBrandStoreProductSkuResponse response = new AmazonBrandStoreProductSkuResponse();
        List<AmazonBrandStoreProductSkuResponse.AmazonBrandStoreProductSku> list = new ArrayList<>(request.getList().size());
        request.getList().stream().collect(Collectors.groupingBy(AmazonBrandStoreProductSkuRequest.AmazonBrandStoreProductSku::getStoreId)).forEach((storeId, groupList) -> {
            List<GetProductCategoryInfoResponse> productCategoryInfo =
                    pmsApiService.getProductCategoryInfo(groupList.stream().map(AmazonBrandStoreProductSkuRequest.AmazonBrandStoreProductSku::getSku).collect(Collectors.toList()));
            Map<String, GetProductCategoryInfoResponse> erpSkuMap = productCategoryInfo.stream().collect(Collectors.toMap(GetProductCategoryInfoResponse::getErpSku, Function.identity()));

            groupList.forEach(amazonBrandStoreProductSku -> {
                AmazonBrandStoreProductSkuResponse.AmazonBrandStoreProductSku storeProductSpu = new AmazonBrandStoreProductSkuResponse.AmazonBrandStoreProductSku();
                String erpSku = amazonBrandStoreProductSku.getSku();
                storeProductSpu.setStoreId(storeId);
                storeProductSpu.setSku(erpSku);
                list.add(storeProductSpu);
                GetProductCategoryInfoResponse infoResponse = erpSkuMap.get(erpSku);
                if (Objects.nonNull(infoResponse)) {
                    BdBrandStoreSkcEntity openBySkc = this.getOpenFbaBrandBySkc(infoResponse.getSkc(), storeId);
                    if (Objects.nonNull(openBySkc)) {
                        storeProductSpu.setBrandId(openBySkc.getBrandId());
                        storeProductSpu.setBrandName(openBySkc.getBrandName());
                    }
                }
            });
        });
        response.setList(list);
        return response;
    }

    @Override
    public BdBrandInfoListResponse queryBrandInfo(BdBrandQueryRequest request) {
        BdBrandInfoListResponse response = new BdBrandInfoListResponse();
        response.addBrandInfoList(bdBrandService.listByRequest(request));
        return response;
    }

    @Override
    @IgnoreTenant
    public BdBrandInfoListResponse queryBrandInfoIgnoreTenant(BdBrandQueryRequest request) {
        return this.queryBrandInfo(request);
    }

    @Override
    public BrandStoreSkcResponse queryByStoreAndSkc(BdBrandStoreSkcQueryRequest request) {
        List<BdBrandStoreSkcEntity> bdBrandStoreSkcList = this.list(new LambdaQueryWrapper<BdBrandStoreSkcEntity>()
                .in(BdBrandStoreSkcEntity::getStoreId, request.getStoreIds())
                .in(BdBrandStoreSkcEntity::getSkc, request.getSkcs()));

        return new BrandStoreSkcResponse(bdBrandStoreSkcList.stream().map(v -> {
            BdBrandStoreSkc dto = new BdBrandStoreSkc();
            BeanUtilsEx.copyProperties(v, dto);
            return dto;
        }).collect(Collectors.toList()));
    }

    private BdBrandStoreSkcEntity getOpenFbaBrandBySkc(String skc, Integer storeId) {
        LambdaQueryWrapper<BdBrandStoreSkcEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BdBrandStoreSkcEntity::getIsOpenFbaBrand, 1)
                .eq(BdBrandStoreSkcEntity::getStoreId, storeId)
                .eq(BdBrandStoreSkcEntity::getSkc, skc)
                .last("limit 1");
        return this.getOne(queryWrapper);
    }

    public List<BdBrandStoreSkcEntity> getByAmazonStoreId(Integer storeId) {
        LambdaQueryWrapper<BdBrandStoreSkcEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BdBrandStoreSkcEntity::getStoreId, storeId);
        return this.list(queryWrapper);
    }

    private Boolean saveSidefeelStoreSkcBrandSpaceInfo(BdBrandStoreEntity brandStoreEntity,
                                                       ProductInfoBySkuResponse skuResponse,
                                                       StoreSkcInfoRequest.StoreSkcInfo storeSkcInfo,
                                                       StoreSkcInfoRequest request,
                                                       BdBrandEntity bdBrandEntity) {
        if (BrandStoreConstant.SIDEFEEL.equalsIgnoreCase(brandStoreEntity.getBrandName())) {
            if ((skuResponse.getProductStyleList().stream().map(ProductInfoBySkuResponse.ProductStyle::getStyleName).collect(Collectors.toList()).contains(PurchaseStyleEnum.AMAZON_2025_AUTUMN_AND_WINTER.getCode())
                    || skuResponse.getProductStyleList().stream().map(ProductInfoBySkuResponse.ProductStyle::getStyleName).collect(Collectors.toList()).contains(PurchaseStyleEnum.AMAZON_2025_SPRING_AND_SUMMER.getCode()))
                    && (BrandStoreConstant.DENIM_JEANS_ID.equals(skuResponse.getParentCategoryId()) || BrandStoreConstant.DENIM_JEANS_ID.equals(skuResponse.getCategoryId()))
                    && 2 == brandStoreEntity.getIsOpenPurchaseBrand()) {
                saveStoreSkcBrandSpaceInfo(brandStoreEntity, skuResponse, storeSkcInfo, request, bdBrandEntity);
                return true;
            } else {
                return true;
            }
        }
        return false;
    }

    private void saveStoreSkcBrandSpaceInfo(BdBrandStoreEntity brandStoreEntity,
                                            ProductInfoBySkuResponse skuResponse,
                                            StoreSkcInfoRequest.StoreSkcInfo storeSkcInfo,
                                            StoreSkcInfoRequest request,
                                            BdBrandEntity bdBrandEntity) {
        BdBrandStoreSkcEntity entity = new BdBrandStoreSkcEntity();
        entity.setBrandStoreId(brandStoreEntity.getBrandStoreId());
        entity.setBrandId(brandStoreEntity.getBrandId());
        entity.setBrandName(brandStoreEntity.getBrandName());
        entity.setStoreId(brandStoreEntity.getStoreId());
        entity.setStoreName(brandStoreEntity.getStoreName());
        entity.setSpu(skuResponse.getProductSku());
        entity.setSkc(storeSkcInfo.getSkc());
        entity.setCategoryId(skuResponse.getCategoryId());
        entity.setCategoryName(skuResponse.getCategoryName());
        entity.setIsOpenFbaBrand(1);
        entity.setIsOpenPurchaseBrand(1);
        entity.setOpenFbaBrandDate(new Date());
        entity.setOpenPurchaseDate(new Date());
        entity.setCreateBy(request.getCreateBy());
        entity.setUpdateBy(request.getCreateBy());
        entity.setLocation(request.getLocation());
        save(entity);
        bdBrandService.updateAllocateProductNum(entity.getBrandId());
        setStoreSkcInfo(storeSkcInfo, bdBrandEntity);
    }

}
