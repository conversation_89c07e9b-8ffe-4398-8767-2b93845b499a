package com.nsy.oms.business.service.platform.order.base;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.constant.enums.IsEnum;
import com.nsy.api.core.apicore.constant.enums.QueueStatusEnum;
import com.nsy.api.core.apicore.util.DateUtils;
import com.nsy.api.core.apicore.util.NsyCollUtil;
import com.nsy.api.transfer.domain.request.platform.PlatformOrderRequest;
import com.nsy.api.transfer.domain.response.platform.PlatformOrderInfo;
import com.nsy.api.transfer.domain.response.platform.PlatformOrderItemInfo;
import com.nsy.api.transfer.domain.response.platform.PlatformOrderReceiverInfo;
import com.nsy.api.transfer.enums.PlatformTypeEnum;
import com.nsy.business.base.enums.LocationEnum;
import com.nsy.oms.business.domain.dto.OrderGrabParameterDTO;
import com.nsy.oms.business.domain.dto.StoreDTO;
import com.nsy.oms.business.service.bd.BdMarketplaceService;
import com.nsy.oms.business.service.order.OrderAddressGrabQueueService;
import com.nsy.oms.business.service.order.SaleOrderExtendService;
import com.nsy.oms.business.service.order.OrderGrabStatusService;
import com.nsy.oms.business.service.order.OrderItemGrabQueueService;
import com.nsy.oms.business.service.order.SaleOrderItemService;
import com.nsy.oms.business.service.order.OrderMissedQueueService;
import com.nsy.oms.business.service.order.SaleOrderReceiverService;
import com.nsy.oms.business.service.order.SaleOrderService;
import com.nsy.oms.business.service.platform.PlatformOrderExtendService;
import com.nsy.oms.business.service.platform.PlatformOrderItemService;
import com.nsy.oms.business.service.platform.PlatformReceiverService;
import com.nsy.oms.business.service.platform.impl.PlatformOrderServiceImpl;
import com.nsy.oms.business.service.sa.SaStoreExceptionLogService;
import com.nsy.oms.business.service.sa.SaStoreService;
import com.nsy.oms.constants.ExceptionCodeConstant;
import com.nsy.oms.enums.order.OrderTypeEnum;
import com.nsy.oms.enums.platform.PlatformOrderStatusEnum;
import com.nsy.oms.repository.entity.bd.BdMarketplaceEntity;
import com.nsy.oms.repository.entity.order.OrderAddressGrabQueueEntity;
import com.nsy.oms.repository.entity.order.SaleOrderEntity;
import com.nsy.oms.repository.entity.order.SaleOrderItemEntity;
import com.nsy.oms.repository.entity.order.OrderItemGrabQueueEntity;
import com.nsy.oms.repository.entity.order.OrderMissedQueueEntity;
import com.nsy.oms.repository.entity.order.SaleOrderReceiverEntity;
import com.nsy.oms.repository.entity.platform.PlatformOrderEntity;
import com.nsy.oms.repository.entity.platform.PlatformOrderItemEntity;
import com.nsy.oms.repository.entity.platform.PlatformReceiverEntity;
import com.nsy.oms.repository.entity.sa.SaStoreEntity;
import com.nsy.oms.utils.SetValueUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public abstract class BasePlatformOrderService {
    private static final Logger LOGGER = LoggerFactory.getLogger(BasePlatformOrderService.class);
    @Autowired
    private BdMarketplaceService marketplaceService;
    @Autowired
    private SaStoreService storeService;
    @Autowired
    private SaStoreExceptionLogService saStoreExceptionLogService;
    @Autowired
    private SaleOrderService saleOrderService;
    @Autowired
    private SaleOrderExtendService saleOrderExtendService;
    @Autowired
    private SaleOrderReceiverService saleOrderReceiverService;
    @Autowired
    private SaleOrderItemService saleOrderItemService;
    @Autowired
    private PlatformOrderServiceImpl platformOrderService;
    @Autowired
    private PlatformOrderExtendService platformOrderExtendService;
    @Autowired
    private PlatformReceiverService platformReceiverService;
    @Autowired
    private PlatformOrderItemService platformOrderItemService;
    @Autowired
    private OrderMissedQueueService orderMissedQueueService;
    @Autowired
    private OrderItemGrabQueueService orderItemGrabQueueService;
    @Autowired
    private OrderAddressGrabQueueService orderAddressGrabQueueService;
    @Autowired
    private OrderGrabStatusService orderGrabStatusService;

    /**
     * 平台
     */
    public abstract PlatformTypeEnum platform();

    /**
     * 开关配置，是否插入订单收件人信息
     */
    protected Boolean isSaveOrderReceiver() {
        return true;
    }

    /**
     * 按时间抓单 请求参数 每页的条数
     */
    protected Integer buildPageSize() {
        return 100;
    }

    /**
     * 按订单号 抓单 抓订单明细 每次请求的单量
     */
    protected Integer buildOrderCount() {
        return 20;
    }

    /**
     * 按时间抓单：抓单往前时间（分钟）
     */
    protected Integer getStartTimeBefore() {
        return -5;
    }

    /**
     * 减去n天，设置当前抓单时间，让店铺重新抓前面的订单，抓漏单
     */
    protected Integer getSubtractionOrderCatchDate() {
        return 0;
    }

    /**
     * 订单信息转换
     */
    protected PlatformOrderInfo convertInfo(PlatformOrderInfo info) {
        return info;
    }

    /**
     * 订单明细SKU信息转换
     */
    protected void convertOrderItemSku(List<SaleOrderItemEntity> orderItemList, Integer storeId) {

    }

    /**
     * 订单明细SKU信息转换
     */
    protected void convertPlatformOrderItemSku(List<PlatformOrderItemEntity> orderItemList, Integer storeId) {

    }

    /**
     * 按时间抓单 请求参数 开始和结束时间
     */
    protected void buildSearchDate(Date currentOrderCatchDate, PlatformOrderRequest orderRequest) {
        Date startOrderTime = Objects.isNull(currentOrderCatchDate) ? DateUtil.offsetMinute(new Date(), -30) : DateUtil.offsetMinute(currentOrderCatchDate, getStartTimeBefore());
        Date endOrderTime = DateUtil.offsetMinute(new Date(), -3);
        Date extensionDate = DateUtil.offsetMinute(startOrderTime, 40); //延后时间
        if (DateUtil.compare(endOrderTime, extensionDate) > 0) {
            endOrderTime = extensionDate;
        }
        orderRequest.setStartOrderTime(startOrderTime);
        orderRequest.setEndOrderTime(endOrderTime);
    }

    /**
     * 按时间抓单 封装请求参数
     */
    protected PlatformOrderRequest buildOrderRequestByDate(StoreDTO storeDTO, PlatformTypeEnum platformEnum) {
        PlatformOrderRequest orderRequest = new PlatformOrderRequest();
        orderRequest.setLocation(storeDTO.getLocation());
        orderRequest.setStoreId(storeDTO.getStoreId());
        orderRequest.setPlatform(platformEnum);
        orderRequest.setPageIndex(1);
        orderRequest.setPageSize(buildPageSize());
        buildSearchDate(storeDTO.getCurrentCatchDate(), orderRequest);
        return orderRequest;
    }

    /**
     * 封装按订单号抓单 请求参数
     */
    protected PlatformOrderRequest buildOrderRequestByIdList(SaStoreEntity saStoreEntity, PlatformTypeEnum platformEnum, List<String> orderNoList) {
        PlatformOrderRequest orderRequest = new PlatformOrderRequest();
        orderRequest.setLocation(saStoreEntity.getLocation());
        orderRequest.setStoreId(saStoreEntity.getId());
        orderRequest.setPlatform(platformEnum);
        orderRequest.setOrderIdList(orderNoList);
        return orderRequest;
    }

    /**
     * 查询符合条件的店铺按时间抓单
     */
    public void getOrderByDate(OrderGrabParameterDTO orderGrabParameterDTO, PlatformTypeEnum platformEnum) {
        if (NsyCollUtil.isEmpty(orderGrabParameterDTO.getStoreList())) {
            return;
        }
        orderGrabParameterDTO.getStoreList().forEach(storeDTO -> {
            try {
                PlatformOrderRequest orderRequest = buildOrderRequestByDate(storeDTO, platformEnum);
                SaStoreEntity saStoreEntity = storeService.getById(storeDTO.getStoreId());
                getPlatformOrderByDate(saStoreEntity, orderRequest);
            } catch (Exception e) {
                LOGGER.error(String.format("销售平台抓单重构--根据时间区间抓订单信息 getOrderByDate error, msg: %s", e.getMessage()), e);
                if (StringUtils.isNotBlank(e.getMessage()) && e.getMessage().contains(ExceptionCodeConstant.UNAUTHORIZED)) {
                    saStoreExceptionLogService.saveSaStoreExceptionLog(storeDTO.getStoreId(), storeDTO.getStoreName(), storeDTO.getLocation(), new Date(), HttpStatus.SC_UNAUTHORIZED, "自动抓单");
                }
            } finally {
                LOGGER.info("销售平台抓单重构--根据时间区间抓订单信息 getOrderByDate end: {}", DateUtil.format(new Date(), DateUtils.DATE_FORMAT_DATE4));
            }
        });
    }

    /**
     * 按时间抓单
     */
    protected void getPlatformOrderByDate(SaStoreEntity storeEntity, PlatformOrderRequest orderRequest) {

    }

    /**
     * 按订单号抓单
     */
    public void getOrderByIdList(SaStoreEntity storeEntity, PlatformTypeEnum platformEnum, List<OrderMissedQueueEntity> orderMissedQueueEntityList) {
        //TODO 判断订单号是否存在订单记录，如存在，队列忽略
        List<List<OrderMissedQueueEntity>> partitionQueueList = Lists.partition(orderMissedQueueEntityList, buildOrderCount());
        partitionQueueList.forEach(queues -> {
            List<String> orderNoList = queues.stream().map(OrderMissedQueueEntity::getOrderNo).collect(Collectors.toList());
            try {
                PlatformOrderRequest orderRequest = buildOrderRequestByIdList(storeEntity, platformEnum, orderNoList);
                getPlatformOrderListByIds(storeEntity, orderRequest, queues);
            } catch (Exception e) {
                orderMissedQueueService.updateOrderMissedQueue(queues, QueueStatusEnum.EXECUTE_FAIL, e.getMessage(), "自动抓单");
                LOGGER.error(String.format("销售平台抓单重构--根据订单号抓订单信息 getOrderByIdList error, msg: %s", e.getMessage()), e);
            } finally {
                LOGGER.info("销售平台抓单重构--根据订单号抓订单信息 getOrderByIdList end: {}", DateUtil.format(new Date(), DateUtils.DATE_FORMAT_DATE4));
            }
        });
    }

    /**
     * 按订单号抓单
     */
    protected void getPlatformOrderListByIds(SaStoreEntity storeEntity, PlatformOrderRequest orderRequest, List<OrderMissedQueueEntity> orderMissedQueueEntityList) {

    }

    /**
     * 按订单号抓订单明细
     */
    public void getOrderItemByIdList(SaStoreEntity storeEntity, PlatformTypeEnum platformEnum, List<OrderItemGrabQueueEntity> orderItemGrabQueueEntityList) {
        List<OrderItemGrabQueueEntity> validOrderItemGrabQueueEntityList = ignoreOrderItemGrabQueue(orderItemGrabQueueEntityList);
        if (NsyCollUtil.isEmpty(validOrderItemGrabQueueEntityList)) {
            return;
        }
        List<List<OrderItemGrabQueueEntity>> partitionQueueList = Lists.partition(validOrderItemGrabQueueEntityList, buildOrderCount());
        partitionQueueList.forEach(queues -> {
            List<String> orderNoList = queues.stream().map(OrderItemGrabQueueEntity::getOrderNo).collect(Collectors.toList());
            try {
                PlatformOrderRequest orderRequest = buildOrderRequestByIdList(storeEntity, platformEnum, orderNoList);
                getPlatformOrderItemListByIds(storeEntity, orderRequest, queues);
            } catch (Exception e) {
                orderItemGrabQueueService.updateOrderItemGrabQueue(queues, QueueStatusEnum.EXECUTE_FAIL, e.getMessage(), "自动抓单");
                LOGGER.error(String.format("销售平台抓单重构--按订单号抓订单明细 getOrderItemByIdList error, msg: %s", e.getMessage()), e);
            } finally {
                LOGGER.info("销售平台抓单重构--按订单号抓订单明细 getOrderItemByIdList end: {}", DateUtil.format(new Date(), DateUtils.DATE_FORMAT_DATE4));
            }
        });
    }

    /**
     * 按订单号抓订单明细
     */
    protected void getPlatformOrderItemListByIds(SaStoreEntity storeEntity, PlatformOrderRequest orderRequest, List<OrderItemGrabQueueEntity> orderItemGrabQueueEntityList) {
        orderItemGrabQueueService.updateOrderItemGrabQueue(orderItemGrabQueueEntityList, QueueStatusEnum.EXECUTE_FAIL, "该模式的平台不支持按订单号抓订单明细", "自动抓单");
    }

    /**
     * 根据订单号抓收件人信息
     */
    public void getOrderReceiveByIdList(SaStoreEntity storeEntity, PlatformTypeEnum platformEnum, List<OrderAddressGrabQueueEntity> orderAddressGrabQueueEntityList) {
        List<List<OrderAddressGrabQueueEntity>> partitionQueueList = Lists.partition(orderAddressGrabQueueEntityList, buildOrderCount());
        partitionQueueList.forEach(queues -> {
            List<String> orderNoList = queues.stream().map(OrderAddressGrabQueueEntity::getOrderNo).collect(Collectors.toList());
            try {
                PlatformOrderRequest orderRequest = buildOrderRequestByIdList(storeEntity, platformEnum, orderNoList);
                getPlatformOrderReceiveListByIds(storeEntity, orderRequest, queues);
            } catch (Exception e) {
                orderAddressGrabQueueService.updateOrderAddressGrabQueue(queues, QueueStatusEnum.EXECUTE_FAIL, e.getMessage(), "自动抓单");
                LOGGER.error(String.format("销售平台抓单重构--按订单号抓收件人信息 getOrderReceiveByIdList error, msg: %s", e.getMessage()), e);
            } finally {
                LOGGER.info("销售平台抓单重构--按订单号抓收件人信息 getOrderReceiveByIdList end: {}", DateUtil.format(new Date(), DateUtils.DATE_FORMAT_DATE4));
            }
        });
    }

    /**
     * 根据订单号抓收件人信息
     */
    protected void getPlatformOrderReceiveListByIds(SaStoreEntity storeEntity, PlatformOrderRequest orderRequest, List<OrderAddressGrabQueueEntity> orderAddressGrabQueueEntityList) {
        orderAddressGrabQueueService.updateOrderAddressGrabQueue(orderAddressGrabQueueEntityList, QueueStatusEnum.EXECUTE_FAIL, "该模式的平台不支持按订单号抓订单收件人信息", "自动抓单");
    }

    /**
     * 检查订单是已发货
     */
    protected Boolean checkOrderStatusIsDelivery(String platformOrderNo, Integer orderStatus) {
        if (PlatformOrderStatusEnum.DELIVERY.getCode().equals(orderStatus)) {
            LOGGER.info("订单号:{},系统是已发货状态,不允许修改单据相关信息", platformOrderNo);
            return true;
        }
        return false;
    }

    /**
     * 保存(本地仓发货)订单表头
     */
    protected SaleOrderEntity saveOrder(PlatformOrderInfo platformOrderInfo, SaleOrderEntity oldSaleOrderEntity, SaStoreEntity storeEntity) {
        if (ObjectUtil.isNull(platformOrderInfo) || ObjectUtil.isNull(storeEntity)) {
            throw new IllegalArgumentException("platformOrderInfo or storeEntity cannot be null");
        }
        SaleOrderEntity saleOrderEntity = buildOrderEntity(platformOrderInfo, storeEntity);
        if (ObjectUtil.isNotNull(oldSaleOrderEntity) && oldSaleOrderEntity.getOrderId() > 0) {
            saleOrderEntity.setOrderId(oldSaleOrderEntity.getOrderId());
        }
        saleOrderService.saveOrUpdate(saleOrderEntity);
        saleOrderExtendService.saveOrderExtend(saleOrderEntity, "自动抓单"); //保存(本地仓发货)订单表头扩展表
        return saleOrderEntity;
    }

    /**
     * 封装(本地仓发货)订单表头
     */
    protected SaleOrderEntity buildOrderEntity(PlatformOrderInfo platformOrderInfo, SaStoreEntity storeEntity) {
        if (ObjectUtil.isNull(platformOrderInfo) || ObjectUtil.isNull(storeEntity)) {
            throw new IllegalArgumentException("platformOrderInfo or storeEntity cannot be null");
        }
        SaleOrderEntity saleOrderEntity = new SaleOrderEntity();
        if (StringUtils.isNotBlank(platformOrderInfo.getMarketplaceId())) {
            saleOrderEntity.setMarketCode(platformOrderInfo.getMarketplaceId());
            BdMarketplaceEntity marketplaceEntity = marketplaceService.getByMarketplaceId(platformOrderInfo.getMarketplaceId());
            if (ObjectUtil.isNotNull(marketplaceEntity)) {
                saleOrderEntity.setMarketName(marketplaceEntity.getMarketplace());
            }
        }
        Integer platformId = storeEntity.getSecondPlatformId() > 0 ? storeEntity.getSecondPlatformId() : storeEntity.getPlatformId();
        String platformName = storeEntity.getSecondPlatformId() > 0 ? storeEntity.getSecondPlatformName() : storeEntity.getPlatformName();
        saleOrderEntity.setPlatformId(platformId);
        saleOrderEntity.setPlatformName(platformName);
        saleOrderEntity.setStoreId(storeEntity.getId());
        saleOrderEntity.setStoreName(storeEntity.getErpStoreName());
        saleOrderEntity.setLocation(storeEntity.getLocation());
        saleOrderEntity.setOrderNo(platformOrderInfo.getPlatformOrderNo());
        saleOrderEntity.setPlatformOrderNo(platformOrderInfo.getPlatformOrderNo());
        saleOrderEntity.setBuyerRemark(platformOrderInfo.getBuyerRemark());
        saleOrderEntity.setShippingType(platformOrderInfo.getShippingType());
        saleOrderEntity.setCommissionFee(SetValueUtil.getOptimizeBigDecimal(platformOrderInfo.getCommissionFee()));
        saleOrderEntity.setFreightFee(SetValueUtil.getOptimizeBigDecimal(platformOrderInfo.getFreightFee()));
        saleOrderEntity.setProcessFee(SetValueUtil.getOptimizeBigDecimal(platformOrderInfo.getProcessFee()));
        saleOrderEntity.setPaymentAmount(SetValueUtil.getOptimizeBigDecimal(platformOrderInfo.getPaymentAmount()));
        saleOrderEntity.setProductTotalAmount(SetValueUtil.getOptimizeBigDecimal(platformOrderInfo.getProductTotalAmount()));
        saleOrderEntity.setProductDiscountAmount(SetValueUtil.getOptimizeBigDecimal(platformOrderInfo.getProductDiscountAmount()));
        saleOrderEntity.setCurrency(platformOrderInfo.getCurrency());
        saleOrderEntity.setOrderCreateDate(platformOrderInfo.getOrderCreateDate());
        saleOrderEntity.setOrderPaymentDate(platformOrderInfo.getOrderPaymentDate());
        saleOrderEntity.setOrderCancelDate(platformOrderInfo.getOrderCancelDate());
        saleOrderEntity.setEarliestShipDate(platformOrderInfo.getEarliestShipDate());
        saleOrderEntity.setLatestShipDate(platformOrderInfo.getLatestShipDate());
        saleOrderEntity.setEarliestDeliveryDate(platformOrderInfo.getEarliestDeliveryDate());
        saleOrderEntity.setLatestDeliveryDate(platformOrderInfo.getLatestDeliveryDate());
        saleOrderEntity.setBusinessOrder(platformOrderInfo.getBusinessOrder());
        saleOrderEntity.setFreightVoucher(platformOrderInfo.getFreightVoucher());
        saleOrderEntity.setOrderStatus(Integer.valueOf(platformOrderInfo.getOrderStatus()));
        saleOrderEntity.setCreateBy("自动抓单");
        saleOrderEntity.setUpdateBy("自动抓单");
        return saleOrderEntity;
    }

    /**
     * 保存(平台仓发货)订单表头
     */
    protected PlatformOrderEntity savePlatformOrder(PlatformOrderInfo platformOrderInfo, PlatformOrderEntity oldPlatformOrderEntity, SaStoreEntity storeEntity) {
        if (ObjectUtil.isNull(platformOrderInfo) || ObjectUtil.isNull(storeEntity)) {
            throw new IllegalArgumentException("platformOrderInfo or storeEntity cannot be null");
        }
        PlatformOrderEntity platformOrderEntity = buildPlatformOrderEntity(platformOrderInfo, storeEntity);
        if (ObjectUtil.isNotNull(oldPlatformOrderEntity) && oldPlatformOrderEntity.getPlatformOrderId() > 0) {
            platformOrderEntity.setPlatformOrderId(oldPlatformOrderEntity.getPlatformOrderId());
        }
        platformOrderService.saveOrUpdate(platformOrderEntity);
        platformOrderExtendService.saveOrUpdatePlatformExtend(platformOrderEntity, "自动抓单"); //保存(平台仓发货)订单表头扩展表
        return platformOrderEntity;
    }

    /**
     * 封装(平台仓发货)订单表头
     */
    protected PlatformOrderEntity buildPlatformOrderEntity(PlatformOrderInfo platformOrderInfo, SaStoreEntity storeEntity) {
        if (ObjectUtil.isNull(platformOrderInfo) || ObjectUtil.isNull(storeEntity)) {
            throw new IllegalArgumentException("platformOrderInfo or storeEntity cannot be null");
        }
        PlatformOrderEntity platformOrderEntity = new PlatformOrderEntity();
        if (StringUtils.isNotBlank(platformOrderInfo.getMarketplaceId())) {
            platformOrderEntity.setMarketCode(platformOrderInfo.getMarketplaceId());
            BdMarketplaceEntity marketplaceEntity = marketplaceService.getByMarketplaceId(platformOrderInfo.getMarketplaceId());
            if (ObjectUtil.isNotNull(marketplaceEntity)) {
                platformOrderEntity.setMarketName(marketplaceEntity.getMarketplace());
            }
        }
        Integer platformId = storeEntity.getSecondPlatformId() > 0 ? storeEntity.getSecondPlatformId() : storeEntity.getPlatformId();
        String platformName = storeEntity.getSecondPlatformId() > 0 ? storeEntity.getSecondPlatformName() : storeEntity.getPlatformName();
        platformOrderEntity.setPlatformId(platformId);
        platformOrderEntity.setPlatformName(platformName);
        platformOrderEntity.setStoreId(storeEntity.getId());
        platformOrderEntity.setStoreName(storeEntity.getErpStoreName());
        platformOrderEntity.setLocation(storeEntity.getLocation());
        platformOrderEntity.setPlatformOrderNo(String.format("%s-%s", platformOrderInfo.getPlatformOrderNo(), storeEntity.getId()));
        platformOrderEntity.setPlatformOriginalOrderNo(platformOrderInfo.getPlatformOrderNo());
        platformOrderEntity.setBuyerRemark(platformOrderInfo.getBuyerRemark());
        platformOrderEntity.setShippingType(platformOrderInfo.getShippingType());
        platformOrderEntity.setCommissionFee(SetValueUtil.getOptimizeBigDecimal(platformOrderInfo.getCommissionFee()));
        platformOrderEntity.setFreightFee(SetValueUtil.getOptimizeBigDecimal(platformOrderInfo.getFreightFee()));
        platformOrderEntity.setPaymentAmount(SetValueUtil.getOptimizeBigDecimal(platformOrderInfo.getPaymentAmount()));
        platformOrderEntity.setProductTotalAmount(SetValueUtil.getOptimizeBigDecimal(platformOrderInfo.getProductTotalAmount()));
        platformOrderEntity.setProductDiscountAmount(SetValueUtil.getOptimizeBigDecimal(platformOrderInfo.getProductDiscountAmount()));
        platformOrderEntity.setCurrency(platformOrderInfo.getCurrency());
        platformOrderEntity.setOrderCreateDate(platformOrderInfo.getOrderCreateDate());
        platformOrderEntity.setOrderPaymentDate(platformOrderInfo.getOrderPaymentDate());
        platformOrderEntity.setOrderCancelDate(platformOrderInfo.getOrderCancelDate());
        platformOrderEntity.setOrderDeliverDate(platformOrderInfo.getUpdateDate());
        platformOrderEntity.setFreightVoucher(platformOrderInfo.getFreightVoucher());
        if (!LocationEnum.QUANZHOU.name().equals(storeEntity.getLocation())) {
            platformOrderEntity.setPushStockoutOrderStatus(QueueStatusEnum.IGNORE.getCode());
            platformOrderEntity.setPushCostOrderStatus(QueueStatusEnum.IGNORE.getCode());
            platformOrderEntity.setPushOtherStockoutOrderStatus(QueueStatusEnum.IGNORE.getCode());
        }
        platformOrderEntity.setBusinessOrder(platformOrderInfo.getBusinessOrder());
        platformOrderEntity.setOrderStatus(Integer.valueOf(platformOrderInfo.getOrderStatus()));
        platformOrderEntity.setOrderType(Integer.parseInt(platformOrderInfo.getOrderType()));
        platformOrderEntity.setCreateBy("自动抓单");
        platformOrderEntity.setUpdateBy("自动抓单");
        return platformOrderEntity;
    }

    /**
     * 保存(本地仓发货)订单明细信息
     */
    protected void saveOrderItem(PlatformOrderInfo platformOrderInfo, SaleOrderEntity saleOrderEntity) {
        if (ObjectUtil.isNull(saleOrderEntity) || ObjectUtil.isNull(platformOrderInfo) || NsyCollUtil.isEmpty(platformOrderInfo.getOrderItemInfoList())) {
            throw new IllegalArgumentException("saleOrderEntity or platformOrderInfo or orderItemInfoList cannot be null");
        }
        List<String> platformItemIdList = platformOrderInfo.getOrderItemInfoList().stream().map(PlatformOrderItemInfo::getPlatformItemId).collect(Collectors.toList());
        List<SaleOrderItemEntity> existItemList = saleOrderItemService.getByOrderIdAndPlatformItemId(saleOrderEntity.getOrderId(), platformItemIdList); //查询该订单已经存在的订单明细
        List<SaleOrderItemEntity> itemEntityList = new ArrayList<>();
        platformOrderInfo.getOrderItemInfoList().forEach(platformOrderItemInfo -> {
            SaleOrderItemEntity itemEntity = buildOrderItem(platformOrderItemInfo, saleOrderEntity);
            SaleOrderItemEntity oldItemEntity = existItemList.stream().filter(existItem -> ObjectUtil.isNotNull(existItem.getPlatformItemId()) && existItem.getPlatformItemId().equals(platformOrderItemInfo.getPlatformItemId())).findFirst().orElse(null);
            if (ObjectUtil.isNotNull(oldItemEntity)) {
                itemEntity.setOrderItemId(oldItemEntity.getOrderItemId());
            }
            itemEntityList.add(itemEntity);
        });
        if (NsyCollUtil.isNotEmpty(itemEntityList)) {
            convertOrderItemSku(itemEntityList, saleOrderEntity.getStoreId());
            saleOrderItemService.saveOrUpdateBatch(itemEntityList);
        }
    }

    /**
     * 封装(本地仓发货)订单明细信息
     */
    protected SaleOrderItemEntity buildOrderItem(PlatformOrderItemInfo platformOrderItemInfo, SaleOrderEntity saleOrderEntity) {
        if (ObjectUtil.isNull(platformOrderItemInfo) || ObjectUtil.isNull(saleOrderEntity)) {
            throw new IllegalArgumentException("platformOrderItemInfo or saleOrderEntity cannot be null");
        }
        SaleOrderItemEntity itemEntity = new SaleOrderItemEntity();
        itemEntity.setOrderId(saleOrderEntity.getOrderId());
        itemEntity.setLocation(saleOrderEntity.getLocation());
        itemEntity.setPlatformItemId(platformOrderItemInfo.getPlatformItemId());
        itemEntity.setSellerSku(platformOrderItemInfo.getSellerSku());
        itemEntity.setQty(SetValueUtil.getOptimizeInteger(platformOrderItemInfo.getQty()));
        itemEntity.setUnitPrice(SetValueUtil.getOptimizeBigDecimal(platformOrderItemInfo.getUnitPrice()));
        itemEntity.setUnitDiscount(SetValueUtil.getOptimizeBigDecimal(platformOrderItemInfo.getUnitDiscount()));
        itemEntity.setTotalAmount(SetValueUtil.getOptimizeBigDecimal(platformOrderItemInfo.getTotalAmount()));
        itemEntity.setPaymentAmount(SetValueUtil.getOptimizeBigDecimal(platformOrderItemInfo.getPaymentAmount()));
        itemEntity.setFreightFee(SetValueUtil.getOptimizeBigDecimal(platformOrderItemInfo.getShippingPriceAmount()));
        itemEntity.setFreightFeeDiscount(SetValueUtil.getOptimizeBigDecimal(platformOrderItemInfo.getShippingDiscountAmount()));
        itemEntity.setIsTransparency(ObjectUtil.isNotNull(platformOrderItemInfo.getTransparency()) && Boolean.TRUE.equals(platformOrderItemInfo.getTransparency()) ? 1 : 0);
        itemEntity.setAsin(platformOrderItemInfo.getAsin());
        itemEntity.setRefundId(platformOrderItemInfo.getRefundId());
        itemEntity.setRefundStatus(platformOrderItemInfo.getRefundStatus());
        itemEntity.setIossNumber(platformOrderItemInfo.getIossNumber());
        itemEntity.setItemStatus(platformOrderItemInfo.getItemStatus());
        itemEntity.setCreateBy("自动抓单");
        itemEntity.setUpdateBy("自动抓单");
        return itemEntity;
    }

    /**
     * 保存(平台仓发货)订单明细信息
     */
    protected void savePlatformOrderItem(PlatformOrderInfo platformOrderInfo, PlatformOrderEntity platformOrderEntity) {
        if (ObjectUtil.isNull(platformOrderEntity) || ObjectUtil.isNull(platformOrderInfo) || NsyCollUtil.isEmpty(platformOrderInfo.getOrderItemInfoList())) {
            throw new IllegalArgumentException("platformOrderEntity or platformOrderInfo or orderItemInfoList cannot be null");
        }
        List<String> platformItemIdList = platformOrderInfo.getOrderItemInfoList().stream().map(PlatformOrderItemInfo::getPlatformItemId).collect(Collectors.toList());
        List<PlatformOrderItemEntity> existItemList = platformOrderItemService.getByOrderIdAndPlatformItemId(platformOrderEntity.getPlatformOrderId(), platformItemIdList); //查询该订单已经存在的订单明细
        List<PlatformOrderItemEntity> itemEntityList = new ArrayList<>();
        platformOrderInfo.getOrderItemInfoList().forEach(platformOrderItemInfo -> {
            PlatformOrderItemEntity itemEntity = buildPlatformOrderItemEntity(platformOrderItemInfo, platformOrderEntity);
            PlatformOrderItemEntity oldItemEntity = existItemList.stream().filter(existItem -> ObjectUtil.isNotNull(existItem.getItemId()) && existItem.getItemId().equals(platformOrderItemInfo.getPlatformItemId())).findFirst().orElse(null);
            if (ObjectUtil.isNotNull(oldItemEntity)) {
                itemEntity.setPlatformOrderItemId(oldItemEntity.getPlatformOrderItemId());
            }
            itemEntityList.add(itemEntity);
        });
        if (NsyCollUtil.isNotEmpty(itemEntityList)) {
            // 调用亚马逊匹配本地SKU
            convertPlatformOrderItemSku(itemEntityList, platformOrderEntity.getStoreId());
            platformOrderItemService.saveOrUpdateBatch(itemEntityList);
        }
    }

    /**
     * 封装(平台仓发货)订单明细信息
     */
    protected PlatformOrderItemEntity buildPlatformOrderItemEntity(PlatformOrderItemInfo platformOrderItemInfo, PlatformOrderEntity platformOrderEntity) {
        if (ObjectUtil.isNull(platformOrderItemInfo) || ObjectUtil.isNull(platformOrderEntity)) {
            throw new IllegalArgumentException("platformOrderItemInfo or platformOrderEntity cannot be null");
        }
        PlatformOrderItemEntity itemEntity = new PlatformOrderItemEntity();
        itemEntity.setPlatformOrderId(platformOrderEntity.getPlatformOrderId());
        itemEntity.setLocation(platformOrderEntity.getLocation());
        itemEntity.setItemId(platformOrderItemInfo.getPlatformItemId());
        itemEntity.setSellerSku(platformOrderItemInfo.getSellerSku());
        itemEntity.setQty(SetValueUtil.getOptimizeInteger(platformOrderItemInfo.getQty()));
        itemEntity.setUnitPrice(SetValueUtil.getOptimizeBigDecimal(platformOrderItemInfo.getUnitPrice()));
        itemEntity.setUnitDiscount(SetValueUtil.getOptimizeBigDecimal(platformOrderItemInfo.getUnitDiscount()));
        itemEntity.setTotalAmount(SetValueUtil.getOptimizeBigDecimal(platformOrderItemInfo.getTotalAmount()));
        itemEntity.setPaymentAmount(SetValueUtil.getOptimizeBigDecimal(platformOrderItemInfo.getPaymentAmount()));
        itemEntity.setItemStatus(platformOrderItemInfo.getItemStatus());
        itemEntity.setCreateBy("自动抓单");
        itemEntity.setUpdateBy("自动抓单");
        return itemEntity;
    }

    /**
     * 保存(本地仓发货)订单收件人信息
     */
    protected void saveOrderReceiver(PlatformOrderInfo platformOrderInfo, SaleOrderEntity saleOrderEntity) {
        SaleOrderReceiverEntity receiverEntity = buildOrderReceiverEntity(platformOrderInfo, saleOrderEntity);
        SaleOrderReceiverEntity oldReceiverEntity = saleOrderReceiverService.getByOrderId(saleOrderEntity.getOrderId());
        if (ObjectUtil.isNotNull(oldReceiverEntity)) {
            receiverEntity.setReceiverId(oldReceiverEntity.getReceiverId());
        }
        saleOrderReceiverService.saveOrUpdate(receiverEntity);
    }

    /**
     * 封装(本地仓发货)订单收件人信息
     */
    protected SaleOrderReceiverEntity buildOrderReceiverEntity(PlatformOrderInfo platformOrderInfo, SaleOrderEntity saleOrderEntity) {
        if (ObjectUtil.isNull(platformOrderInfo) || ObjectUtil.isNull(saleOrderEntity)) {
            throw new IllegalArgumentException("platformOrderInfo or saleOrderEntity cannot be null");
        }
        PlatformOrderReceiverInfo receiverInfo = platformOrderInfo.getPlatformOrderReceiverInfo();
        if (ObjectUtil.isNull(receiverInfo)) {
            throw new IllegalArgumentException("receiverInfo cannot be null");
        }
        SaleOrderReceiverEntity receiverEntity = new SaleOrderReceiverEntity();
        receiverEntity.setOrderId(saleOrderEntity.getOrderId());
        receiverEntity.setLocation(saleOrderEntity.getLocation());
        receiverEntity.setReceiverName(receiverInfo.getReceiverName());
        receiverEntity.setCountry(receiverInfo.getReceiverCountry());
        receiverEntity.setProvince(receiverInfo.getReceiverProvince());
        receiverEntity.setCity(receiverInfo.getReceiverCity());
        receiverEntity.setArea(receiverInfo.getReceiverDistrict());
        receiverEntity.setAddress(receiverInfo.getReceiverAddress());
        receiverEntity.setHouseNumber(receiverInfo.getReceiverHouseNumber());
        receiverEntity.setMobile(receiverInfo.getReceiverMobile());
        receiverEntity.setPhone(receiverInfo.getReceiverPhone());
        receiverEntity.setPostCode(receiverInfo.getReceiverPostCode());
        receiverEntity.setBuyerNick(platformOrderInfo.getBuyerNick());
        receiverEntity.setBuyerEmail(platformOrderInfo.getBuyerEmail());
        receiverEntity.setCreateBy("自动抓单");
        receiverEntity.setUpdateBy("自动抓单");
        return receiverEntity;
    }

    /**
     * 保存(平台仓发货)订单收件人信息
     */
    protected void savePlatformOrderReceiver(PlatformOrderInfo platformOrderInfo, PlatformOrderEntity platformOrderEntity) {
        PlatformReceiverEntity receiverEntity = buildPlatformReceiverEntity(platformOrderInfo, platformOrderEntity);
        PlatformReceiverEntity oldReceiverEntity = platformReceiverService.getByPlatformOrderId(platformOrderEntity.getPlatformOrderId());
        if (ObjectUtil.isNotNull(oldReceiverEntity)) {
            receiverEntity.setReceiverId(oldReceiverEntity.getReceiverId());
        }
        platformReceiverService.saveOrUpdate(receiverEntity);
    }

    /**
     * 封装(平台仓发货)订单收件人信息
     */
    protected PlatformReceiverEntity buildPlatformReceiverEntity(PlatformOrderInfo platformOrderInfo, PlatformOrderEntity platformOrderEntity) {
        if (ObjectUtil.isNull(platformOrderInfo) || ObjectUtil.isNull(platformOrderEntity)) {
            throw new IllegalArgumentException("platformOrderInfo or platformOrderEntity cannot be null");
        }
        PlatformOrderReceiverInfo receiverInfo = platformOrderInfo.getPlatformOrderReceiverInfo();
        if (ObjectUtil.isNull(receiverInfo)) {
            throw new IllegalArgumentException("receiverInfo cannot be null");
        }
        PlatformReceiverEntity receiverEntity = new PlatformReceiverEntity();
        receiverEntity.setPlatformOrderId(platformOrderEntity.getPlatformOrderId());
        receiverEntity.setLocation(platformOrderEntity.getLocation());
        receiverEntity.setReceiverName(receiverInfo.getReceiverName());
        receiverEntity.setCountry(receiverInfo.getReceiverCountry());
        receiverEntity.setProvince(receiverInfo.getReceiverProvince());
        receiverEntity.setCity(receiverInfo.getReceiverCity());
        receiverEntity.setArea(receiverInfo.getReceiverDistrict());
        receiverEntity.setAddress(receiverInfo.getReceiverAddress());
        receiverEntity.setMobile(receiverInfo.getReceiverMobile());
        receiverEntity.setPhone(receiverInfo.getReceiverPhone());
        receiverEntity.setPostCode(receiverInfo.getReceiverPostCode());
        receiverEntity.setBuyerNick(platformOrderInfo.getBuyerNick());
        receiverEntity.setBuyerEmail(platformOrderInfo.getBuyerEmail());
        receiverEntity.setCreateBy("自动抓单");
        receiverEntity.setUpdateBy("自动抓单");
        return receiverEntity;
    }

    /**
     * 根据订单是否取消而忽略队列
     */
    private List<OrderItemGrabQueueEntity> ignoreOrderItemGrabQueue(List<OrderItemGrabQueueEntity> orderItemGrabQueueEntityList) {
        if (NsyCollUtil.isEmpty(orderItemGrabQueueEntityList)) {
            return orderItemGrabQueueEntityList;
        }
        List<OrderItemGrabQueueEntity> validOrderItemGrabQueueEntityList = new ArrayList<>();
        orderItemGrabQueueEntityList.forEach(orderItemGrabQueueEntity -> {
            boolean isCancel = isCancelOrder(orderItemGrabQueueEntity.getStoreId(), orderItemGrabQueueEntity.getOrderNo(), orderItemGrabQueueEntity.getOrderType());
            if (isCancel) {
                orderItemGrabQueueEntity.setQueueStatus(QueueStatusEnum.IGNORE.getCode());
                orderItemGrabQueueEntity.setRemark("订单状态已取消，无需抓单订单明细");
                orderItemGrabQueueEntity.setUpdateBy("自动抓单");
                orderItemGrabQueueService.updateById(orderItemGrabQueueEntity);

                orderGrabStatusService.updateOrderGrabStatusForIsGrabItem(orderItemGrabQueueEntity.getStoreId(), orderItemGrabQueueEntity.getOrderId(), orderItemGrabQueueEntity.getOrderNo(), IsEnum.IS.getCode(), "自动抓单"); //更新 订单状态表是否抓取详情
            } else {
                validOrderItemGrabQueueEntityList.add(orderItemGrabQueueEntity);
            }
        });
        return validOrderItemGrabQueueEntityList;
    }

    /**
     * 判断订单是否取消
     */
    private boolean isCancelOrder(Integer storeId, String orderNo, Integer orderType) {
        // 订单分流： FBA 存平台订单表
        if (OrderTypeEnum.FBA.getCode().equals(orderType)) {
            return platformOrderService.isCancelOrder(storeId, orderNo);
        } else {
            // 订单分流： FBM 存订单表
            return saleOrderService.isCancelOrder(storeId, orderNo);
        }
    }
}