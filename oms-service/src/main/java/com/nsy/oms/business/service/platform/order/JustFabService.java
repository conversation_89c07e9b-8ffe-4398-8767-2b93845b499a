package com.nsy.oms.business.service.platform.order;

import com.nsy.api.transfer.enums.PlatformTypeEnum;
import com.nsy.oms.business.service.base.SkuInfoService;
import com.nsy.oms.business.service.platform.order.base.BaseCompleteOrderInfoService;
import com.nsy.oms.repository.entity.order.SaleOrderItemEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 销售平台抓单重构--Shopify
 */
@Service
public class JustFabService extends BaseCompleteOrderInfoService {

    @Autowired
    private SkuInfoService skuInfoService;

    /**
     * 平台
     */
    @Override
    public PlatformTypeEnum platform() {
        return PlatformTypeEnum.JUSTFAB;
    }

    @Override
    public void convertOrderItemSku(List<SaleOrderItemEntity> orderItemEntities, Integer storeId) {
        skuInfoService.getSkuInfo(storeId, orderItemEntities, 0);
    }
}
