package com.nsy.oms.business.manage.amazon.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
public class StaWaitingApplyShipmentPlanCreateResponse {
    @ApiModelProperty(name = "failList", value = "失败list")
    List<StaShipmentPlanDTO> failList;
    @ApiModelProperty(name = "successList", value = "成功list")
    List<StaShipmentPlanDTO> successList;

    @NoArgsConstructor
    @Data
    public static class StaShipmentPlanDTO {
        @JsonProperty("storeId")
        private Integer storeId;
        @JsonProperty("storeName")
        private String storeName;
        @JsonProperty("colorSku")
        private String colorSku;
    }
}
