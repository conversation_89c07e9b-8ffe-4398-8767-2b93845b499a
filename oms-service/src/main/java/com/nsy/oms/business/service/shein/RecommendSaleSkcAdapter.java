package com.nsy.oms.business.service.shein;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.privilege.LoginInfoService;
import com.nsy.oms.business.domain.request.shein.SheinRecommendSaleSkcCheckRequest;
import com.nsy.oms.business.domain.request.shein.SheinRecommendSaleSkcDeleteRequest;
import com.nsy.oms.business.domain.request.shein.SheinRecommendSaleSkcIdListRequest;
import com.nsy.oms.business.domain.request.shein.SheinRecommendSaleSkcIgnoreRequest;
import com.nsy.oms.business.domain.request.shein.SheinRecommendSaleSkcListRequest;
import com.nsy.oms.business.domain.request.shein.SheinRecommendSaleSkcManualRequest;
import com.nsy.oms.business.domain.request.shein.SheinRecommendSaleSkcPushRequest;
import com.nsy.oms.business.domain.request.shein.SheinRecommendSaleSkcRequest;
import com.nsy.oms.business.domain.request.shein.SyncSheinRecommendSaleSkcStatusRequest;
import com.nsy.oms.business.manage.omspublish.OmsPublishApiService;
import com.nsy.oms.business.manage.omspublish.response.SelectWebsiteModel;
import com.nsy.oms.business.manage.search.SearchApiService;
import com.nsy.oms.business.manage.user.UserApiService;
import com.nsy.oms.business.manage.user.response.SysUserInfo;
import com.nsy.oms.enums.shein.SheinRecommendPlatformEnum;
import com.nsy.oms.enums.shein.SheinRecommendSaleSkcStatusEnum;
import com.nsy.oms.enums.shein.SheinRecommendTypeEnum;
import com.nsy.oms.mq.producer.PublishSkcMessageSendService;
import com.nsy.oms.repository.dao.sa.SaStoreDao;
import com.nsy.oms.repository.dao.sa.SaStoreWebsiteDao;
import com.nsy.oms.repository.dao.shein.SheinRecommendRecordDao;
import com.nsy.oms.repository.dao.shein.SheinRecommendSaleSkcDao;
import com.nsy.oms.repository.entity.sa.SaStoreEntity;
import com.nsy.oms.repository.entity.sa.SaStoreWebsiteEntity;
import com.nsy.oms.repository.entity.shein.SheinRecommendOperationLogEntity;
import com.nsy.oms.repository.entity.shein.SheinRecommendRecordEntity;
import com.nsy.oms.repository.entity.shein.SheinRecommendSaleSkcEntity;
import com.nsy.oms.utils.Validator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 *
 */
@Service
public class RecommendSaleSkcAdapter implements InitializingBean {
    private static final Logger LOGGER = LoggerFactory.getLogger(RecommendSaleSkcAdapter.class);
    private static final Map<String, RecommendSaleSkcPlatformService> RECOMMEND_PLATFORM_MAP = new HashMap<>(2);
    public static final String SYSTEM_OPERATOR = "系统建议";

    @Autowired
    private SearchApiService searchApiService;
    @Autowired
    private SheinRecommendRecordDao sheinRecommendRecordDao;
    @Autowired
    private SheinRecommendSaleSkcDao sheinRecommendSaleSkcDao;
    @Autowired
    private SaStoreWebsiteDao saStoreWebsiteDao;
    @Autowired
    private PublishSkcMessageSendService publishSkcMessageSendService;
    @Autowired
    private SaStoreDao saStoreDao;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private OmsPublishApiService omsPublishApiService;
    @Autowired
    private SheinRecommendOperationLogService sheinRecommendOperationLogService;
    @Qualifier("sheinRecommendSaleSkcServiceImpl")
    @Autowired
    private RecommendSaleSkcPlatformService sheinRecommendSaleSkcService;
    @Qualifier("temuRecommendSaleSkcServiceImpl")
    @Autowired
    private RecommendSaleSkcPlatformService temuRecommendSaleSkcService;
    @Qualifier("tiktokShopRecommendSaleSkcServiceImpl")
    @Autowired
    private RecommendSaleSkcPlatformService tiktokShopRecommendSaleSkcService;
    @Qualifier("tiktokRecommendSaleSkcServiceImpl")
    @Autowired
    private RecommendSaleSkcPlatformService tiktokRecommendSaleSkcServiceImpl;
    @Qualifier("amazonRecommendSaleSkcServiceImpl")
    @Autowired
    private RecommendSaleSkcPlatformService amazonRecommendSaleSkcServiceImpl;
    @Autowired
    private UserApiService userApiService;

    @Override
    public void afterPropertiesSet() {
        RECOMMEND_PLATFORM_MAP.put(SheinRecommendPlatformEnum.SHEIN.getPlatform(), sheinRecommendSaleSkcService);
        RECOMMEND_PLATFORM_MAP.put(SheinRecommendPlatformEnum.TEMU.getPlatform(), temuRecommendSaleSkcService);
        RECOMMEND_PLATFORM_MAP.put(SheinRecommendPlatformEnum.TIKTOK_SHOP.getPlatform(), tiktokShopRecommendSaleSkcService);
        RECOMMEND_PLATFORM_MAP.put(SheinRecommendPlatformEnum.TIKTOK.getPlatform(), tiktokRecommendSaleSkcServiceImpl);
        RECOMMEND_PLATFORM_MAP.put(SheinRecommendPlatformEnum.AMAZON.getPlatform(), amazonRecommendSaleSkcServiceImpl);
    }

    /**
     * 系统推荐
     * @param request
     */
    public void systemAdd(SheinRecommendSaleSkcListRequest request) {
        List<SheinRecommendSaleSkcRequest> recommendSaleSkcRequests = request.getSheinRecommendSaleSkcRequests();
        //数据为空,数据已经跑完,对不满足今天规则确是待上架状态的昨天的数据删除
        if (CollectionUtils.isEmpty(recommendSaleSkcRequests)) {
            sheinRecommendSaleSkcDao.deleteBeforeToday();
            LOGGER.warn("recommendSaleSkcRequests is empty");
            return;
        }
        for (SheinRecommendSaleSkcRequest r : recommendSaleSkcRequests) {
            List<Integer> recommendStoreIds = StringUtils.hasText(r.getRecommendStoreIds()) ? new ArrayList<>(Arrays.asList(r.getRecommendStoreIds().split(","))).stream().map(Integer::valueOf).collect(Collectors.toList()) : Lists.newArrayList();
            if (CollectionUtils.isEmpty(recommendStoreIds)) continue;
            for (Integer storeId : recommendStoreIds) {
                SaStoreEntity saStoreEntity = saStoreDao.getById(storeId);
                if (Objects.isNull(saStoreEntity)) continue;
                RecommendSaleSkcPlatformService platformService = getPlatformServiceByPlatform(saStoreEntity);
                if (Objects.isNull(platformService)) continue;
                Optional<SheinRecommendSaleSkcEntity> saleSkcEntityOptional = platformService.filterAndBuildSystemAddSaleSkc(r, saStoreEntity);
                if (!saleSkcEntityOptional.isPresent()) continue;
                SheinRecommendSaleSkcEntity sheinRecommendSaleSkcEntity = saleSkcEntityOptional.get();
                boolean isNew = Objects.isNull(sheinRecommendSaleSkcEntity.getSheinRecommendSaleSkcId());
                sheinRecommendSaleSkcDao.saveOrUpdate(sheinRecommendSaleSkcEntity);
                if (!isNew) continue;
                String content = String.format("商品id:%s;skc:%s;推荐店铺:%s;推荐类型:%s;", r.getProductId(), r.getSkc(), sheinRecommendSaleSkcEntity.getRecommendStoreName(), SheinRecommendTypeEnum.valueOf(sheinRecommendSaleSkcEntity.getRecommendType()).getDesc());
                SheinRecommendOperationLogEntity sheinRecommendOperationLogEntity = sheinRecommendOperationLogService.buildSheinRecommendOperationLog(sheinRecommendSaleSkcEntity.getSheinRecommendSaleSkcId(), SYSTEM_OPERATOR, content, "", SYSTEM_OPERATOR, SYSTEM_OPERATOR, sheinRecommendSaleSkcEntity.getLocation());
                sheinRecommendOperationLogService.saveBatch(Collections.singletonList(sheinRecommendOperationLogEntity));
            }
        }
    }

    public void syncSheinRecommendSaleSkcStatus(SyncSheinRecommendSaleSkcStatusRequest request) {
        sheinRecommendSaleSkcDao.delSheinRecommendSaleSkc(request.getPublishStoreId(), request.getSkcs());
    }

    public String getCreateDate() {
        return sheinRecommendSaleSkcDao.getCreateDate();
    }

    public void push(SheinRecommendSaleSkcPushRequest request) {
        if (!Optional.ofNullable(request.getIsForcedPush()).isPresent()) {
            request.setIsForcedPush(false);
        }
        List<Integer> websiteIds = request.getWebsiteIds();
        // 获取站点关联店铺
        List<SaStoreWebsiteEntity> saStoreWebsiteEntities = saStoreWebsiteDao.getListByWebsiteIds(websiteIds);
        Validator.valid(saStoreWebsiteEntities, CollectionUtils::isEmpty, "站点没有关联店铺");
        Map<Integer, SaStoreWebsiteEntity> saStoreWebsiteMap = saStoreWebsiteEntities.stream().collect(Collectors.toMap(SaStoreWebsiteEntity::getStoreId, a -> a, (k1, k2) -> k1));
        List<Integer> storeIds = saStoreWebsiteEntities.stream().map(SaStoreWebsiteEntity::getStoreId).distinct().collect(Collectors.toList());
        List<SaStoreEntity> saStoreEntities = saStoreDao.listByIds(storeIds);
        Validator.valid(saStoreEntities, CollectionUtils::isEmpty, "站点没有关联店铺");
        RecommendSaleSkcValidator.validMultiPlatform(saStoreEntities);
        RecommendSaleSkcPlatformService platformService = getPlatformServiceByPlatform(saStoreEntities.get(0));
        Validator.valid(platformService, Objects::isNull, "该平台不支持推荐请确认");
        platformService.validPush(request, storeIds);

        List<SheinRecommendSaleSkcEntity> sheinRecommendSaleSkcEntityList = Lists.newArrayList();
        List<SheinRecommendRecordEntity> sheinRecommendRecordEntityList = Lists.newArrayList();
        for (String skc : request.getColorList()) {
            for (SaStoreEntity saStoreEntity : saStoreEntities) {
                SheinRecommendSaleSkcEntity entity = findExistOrElseBuildNew(saStoreEntity, skc, request.getProductId());
                sheinRecommendSaleSkcEntityList.add(entity);
                SaStoreWebsiteEntity saStoreWebsiteEntity = saStoreWebsiteMap.get(saStoreEntity.getId());
                buildSheinRecommendRecord(request.getProductId(), skc, saStoreWebsiteEntity.getWebsiteId(), saStoreWebsiteEntity.getWebsiteName(),
                        saStoreEntity.getDepartment(), platformService.platform(), sheinRecommendRecordEntityList);
            }
        }
        if (!CollectionUtils.isEmpty(sheinRecommendSaleSkcEntityList)) {
            sheinRecommendSaleSkcDao.saveOrUpdateBatch(sheinRecommendSaleSkcEntityList);
            sheinRecommendRecordDao.saveBatch(sheinRecommendRecordEntityList);
            List<SheinRecommendOperationLogEntity> sheinRecommendOperationLogEntities = new ArrayList<>(sheinRecommendSaleSkcEntityList.size());
            String content = String.format("商品id:%s;skc:%s;站点名称:%s", request.getProductId(), String.join(",", request.getColorList()), saStoreWebsiteEntities.stream().map(SaStoreWebsiteEntity::getWebsiteName).collect(Collectors.joining(";")));
            sheinRecommendSaleSkcEntityList.forEach(entity -> {
                SheinRecommendOperationLogEntity sheinRecommendOperationLogEntity = sheinRecommendOperationLogService.buildSheinRecommendOperationLog(entity.getSheinRecommendSaleSkcId(), "商品推送", content, loginInfoService.getIpAddress(), loginInfoService.getUserName(), loginInfoService.getName(), loginInfoService.getLocation());
                sheinRecommendOperationLogEntities.add(sheinRecommendOperationLogEntity);
            });
            sheinRecommendOperationLogService.saveBatch(sheinRecommendOperationLogEntities);
            //更新es
            searchApiService.syncSheinRecommendByIds(sheinRecommendSaleSkcEntityList.stream().map(SheinRecommendSaleSkcEntity::getSheinRecommendSaleSkcId).collect(Collectors.toList()));
            messageSendSheinRecommendRecord(sheinRecommendRecordEntityList);
        }
    }

    private void buildSheinRecommendRecord(Integer productId, String skc, Integer recommendWebsiteId, String recommendWebsiteName,
                                           String recommendDepartment, String recommendPlatform, List<SheinRecommendRecordEntity> sheinRecommendRecordEntityList) {
        SheinRecommendRecordEntity sheinRecommendRecordEntity = new SheinRecommendRecordEntity();
        sheinRecommendRecordEntity.setProductId(productId);
        sheinRecommendRecordEntity.setSpu(skc.split("-")[0]);
        sheinRecommendRecordEntity.setSkc(skc);
        sheinRecommendRecordEntity.setRecommendUserAccount(loginInfoService.getUserName());
        sheinRecommendRecordEntity.setRecommendUserId(loginInfoService.getUserId());
        sheinRecommendRecordEntity.setRecommendUserName(loginInfoService.getName());
        sheinRecommendRecordEntity.setRecommendDepartment(recommendDepartment);
        sheinRecommendRecordEntity.setRecommendPlatform(recommendPlatform);
        sheinRecommendRecordEntity.setRecommendWebsiteName(recommendWebsiteName);
        sheinRecommendRecordEntity.setRecommendWebsiteId(recommendWebsiteId);
        sheinRecommendRecordEntity.setRecommendDate(new Date());
        sheinRecommendRecordEntity.setCreateBy(loginInfoService.getUserName());
        sheinRecommendRecordEntity.setLocation(loginInfoService.getLocation());
        sheinRecommendRecordEntityList.add(sheinRecommendRecordEntity);
    }

    public void omsPush(SheinRecommendSaleSkcPushRequest request) {
        List<SaStoreWebsiteEntity> saStoreWebsiteEntities = saStoreWebsiteDao.getListByWebsiteIds(request.getWebsiteIds());
        Validator.valid(saStoreWebsiteEntities, CollectionUtils::isEmpty, "站点没有关联店铺");
        List<SaStoreEntity> saStoreEntities = saStoreDao.listByIds(saStoreWebsiteEntities.stream().map(SaStoreWebsiteEntity::getStoreId).distinct().collect(Collectors.toList()));
        SheinRecommendPlatformEnum platformEnum = SheinRecommendPlatformEnum.of(saStoreEntities.get(0).allPlatform());
        List<SheinRecommendSaleSkcEntity> sheinRecommendSaleSkcEntities = sheinRecommendSaleSkcDao.getBySkcInAndPlatform(request.getColorList(), platformEnum.getOmsPublishPlatform());
        List<SheinRecommendSaleSkcEntity> sheinRecommendSaleSkcEntityList = Lists.newArrayList();
        for (String skc : request.getColorList()) {
            //已存在上架中的,非自主推荐的跳过
            SaStoreEntity saStoreEntity = saStoreEntities.get(0);
            if (canPushOtherPlatform(skc, platformEnum, sheinRecommendSaleSkcEntities, saStoreEntity) || canPushShein(skc, platformEnum, sheinRecommendSaleSkcEntities)) {
                SheinRecommendSaleSkcEntity entity = sheinRecommendSaleSkcEntities.stream().filter(sr -> {
                    if (SheinRecommendPlatformEnum.SHEIN.equals(platformEnum)) {
                        return Objects.equals(skc, sr.getSkc());
                    }
                    return Objects.equals(skc, sr.getSkc()) && Objects.equals(saStoreEntity.getId(), sr.getRecommendStoreId());
                }).findFirst().orElse(new SheinRecommendSaleSkcEntity());
                entity.setProductId(request.getProductId());
                entity.setStatus(SheinRecommendSaleSkcStatusEnum.SHELFING.getCode());
                entity.setMarkDate(new Date());
                entity.setPublishStoreId(saStoreEntity.getId());
                entity.setPublishStoreName(saStoreEntity.getErpStoreName());
                entity.setRecommendType(SheinRecommendTypeEnum.RECOMMEND.name());
                entity.setSkc(skc);
                entity.setPlatform(SheinRecommendPlatformEnum.of(saStoreEntity.allPlatform()).getOmsPublishPlatform());
                entity.setCreateBy(StringUtils.hasText(request.getCreateBy()) ? request.getCreateBy() : loginInfoService.getUserName());
                entity.setRecommendStoreId(saStoreEntity.getId());
                entity.setRecommendStoreName(saStoreEntity.getErpStoreName());
                entity.setLocation(saStoreEntity.getLocation());
                sheinRecommendSaleSkcEntityList.add(entity);
            }
        }
        if (!CollectionUtils.isEmpty(sheinRecommendSaleSkcEntityList)) {
            sheinRecommendSaleSkcDao.saveOrUpdateBatch(sheinRecommendSaleSkcEntityList);
            List<SheinRecommendOperationLogEntity> sheinRecommendOperationLogEntities = new ArrayList<>(sheinRecommendSaleSkcEntityList.size());
            String content = String.format("商品id:%s;skc:%s;站点名称:%s", request.getProductId(), String.join(",", request.getColorList()), saStoreWebsiteEntities.stream().map(SaStoreWebsiteEntity::getWebsiteName).collect(Collectors.joining(";")));
            sheinRecommendSaleSkcEntityList.forEach(entity -> {
                SheinRecommendOperationLogEntity sheinRecommendOperationLogEntity = sheinRecommendOperationLogService.buildSheinRecommendOperationLog(entity.getSheinRecommendSaleSkcId(), "刊登推送", content, loginInfoService.getIpAddress(), loginInfoService.getUserName(), loginInfoService.getName(), loginInfoService.getLocation());
                sheinRecommendOperationLogEntities.add(sheinRecommendOperationLogEntity);
            });
            sheinRecommendOperationLogService.saveBatch(sheinRecommendOperationLogEntities);
            //更新es
            searchApiService.syncSheinRecommendByIds(sheinRecommendSaleSkcEntityList.stream().map(SheinRecommendSaleSkcEntity::getSheinRecommendSaleSkcId).collect(Collectors.toList()));
        }

    }

    private static boolean canPushOtherPlatform(String skc, SheinRecommendPlatformEnum platformEnum, List<SheinRecommendSaleSkcEntity> sheinRecommendSaleSkcEntities, SaStoreEntity saStoreEntity) {
        return !SheinRecommendPlatformEnum.SHEIN.equals(platformEnum)
                && !sheinRecommendSaleSkcEntities.stream().anyMatch(sr -> Objects.equals(sr.getSkc(), skc)
                && Objects.equals(sr.getRecommendStoreId(), saStoreEntity.getId())
                && Objects.equals(SheinRecommendSaleSkcStatusEnum.SHELFING.getCode(), sr.getStatus()));
    }

    private static boolean canPushShein(String skc, SheinRecommendPlatformEnum platformEnum, List<SheinRecommendSaleSkcEntity> sheinRecommendSaleSkcEntities) {
        return SheinRecommendPlatformEnum.SHEIN.equals(platformEnum)
                && !sheinRecommendSaleSkcEntities.stream().anyMatch(sr -> Objects.equals(sr.getSkc(), skc)
                && Objects.equals(SheinRecommendSaleSkcStatusEnum.SHELFING.getCode(), sr.getStatus()));
    }

    public void ignore(SheinRecommendSaleSkcIgnoreRequest request) {
        request.getIds().forEach(id -> {
            SheinRecommendSaleSkcEntity sheinRecommendSaleSkcEntity = sheinRecommendSaleSkcDao.getById(id);
            Integer status = sheinRecommendSaleSkcEntity.getStatus();
            sheinRecommendSaleSkcEntity.setReason(request.getReason());
            sheinRecommendSaleSkcEntity.setStatus(SheinRecommendSaleSkcStatusEnum.IGNORE.getCode());
            sheinRecommendSaleSkcDao.updateById(sheinRecommendSaleSkcEntity);
            String content = String.format("skc:%s;状态:%s->%s;原因:%s", sheinRecommendSaleSkcEntity.getSkc(), SheinRecommendSaleSkcStatusEnum.getDescByCode(status), SheinRecommendSaleSkcStatusEnum.IGNORE.getDesc(), request.getReason());
            SheinRecommendOperationLogEntity sheinRecommendOperationLogEntity = sheinRecommendOperationLogService.buildSheinRecommendOperationLog(sheinRecommendSaleSkcEntity.getSheinRecommendSaleSkcId(), "忽略推荐", content, loginInfoService.getIpAddress(), loginInfoService.getUserName(), loginInfoService.getName(), loginInfoService.getLocation());
            sheinRecommendOperationLogService.saveBatch(Collections.singletonList(sheinRecommendOperationLogEntity));
            //更新es
            searchApiService.syncSheinRecommendByIds(Lists.newArrayList(sheinRecommendSaleSkcEntity.getSheinRecommendSaleSkcId()));
        });
    }

    public void cancelIgnore(SheinRecommendSaleSkcIgnoreRequest request) {
        request.getIds().forEach(id -> {
            SheinRecommendSaleSkcEntity sheinRecommendSaleSkcEntity = sheinRecommendSaleSkcDao.getById(id);
            Integer status = sheinRecommendSaleSkcEntity.getStatus();
            sheinRecommendSaleSkcEntity.setReason(0);
            sheinRecommendSaleSkcEntity.setStatus(SheinRecommendSaleSkcStatusEnum.UN_SHELF.getCode());
            sheinRecommendSaleSkcDao.updateById(sheinRecommendSaleSkcEntity);
            String content = String.format("skc:%s;状态:%s->%s", sheinRecommendSaleSkcEntity.getSkc(), SheinRecommendSaleSkcStatusEnum.getDescByCode(status), SheinRecommendSaleSkcStatusEnum.UN_SHELF.getDesc());
            SheinRecommendOperationLogEntity sheinRecommendOperationLogEntity = sheinRecommendOperationLogService.buildSheinRecommendOperationLog(sheinRecommendSaleSkcEntity.getSheinRecommendSaleSkcId(), "忽略推荐", content, loginInfoService.getIpAddress(), loginInfoService.getUserName(), loginInfoService.getName(), loginInfoService.getLocation());
            sheinRecommendOperationLogService.saveBatch(Collections.singletonList(sheinRecommendOperationLogEntity));
            //更新es
            searchApiService.syncSheinRecommendByIds(Lists.newArrayList(sheinRecommendSaleSkcEntity.getSheinRecommendSaleSkcId()));
        });
    }

    
    public void delete(SheinRecommendSaleSkcDeleteRequest request) {
        List<SheinRecommendSaleSkcEntity> sheinRecommendSaleSkcEntities = sheinRecommendSaleSkcDao.listByIds(request.getSheinRecommendSaleSkcIds());
        if (CollectionUtils.isEmpty(sheinRecommendSaleSkcEntities)) return;
        boolean flag = sheinRecommendSaleSkcEntities.stream().anyMatch(item -> !SheinRecommendTypeEnum.RECOMMEND.name().equals(item.getRecommendType()));
        if (flag) throw new BusinessServiceException("只能删除自主推送类型的内容,请确认");
        sheinRecommendSaleSkcEntities.forEach(sheinRecommendSaleSkcEntity -> {
            sheinRecommendSaleSkcEntity.setStatus(SheinRecommendSaleSkcStatusEnum.SHELFED.getCode());
        });
        sheinRecommendSaleSkcDao.updateBatchById(sheinRecommendSaleSkcEntities);
        List<SheinRecommendOperationLogEntity> sheinRecommendOperationLogEntities = new ArrayList<>(sheinRecommendSaleSkcEntities.size());
        sheinRecommendSaleSkcEntities.forEach(entity -> {
            String content = String.format("skc:%s", entity.getSkc());
            SheinRecommendOperationLogEntity sheinRecommendOperationLogEntity = sheinRecommendOperationLogService.buildSheinRecommendOperationLog(entity.getSheinRecommendSaleSkcId(), "删除推荐", content, loginInfoService.getIpAddress(), loginInfoService.getUserName(), loginInfoService.getName(), loginInfoService.getLocation());
            sheinRecommendOperationLogEntities.add(sheinRecommendOperationLogEntity);
        });
        sheinRecommendOperationLogService.saveBatch(sheinRecommendOperationLogEntities);
        //更新es
        searchApiService.deleteSheinRecommendByIds(sheinRecommendSaleSkcEntities.stream().map(SheinRecommendSaleSkcEntity::getSheinRecommendSaleSkcId).collect(Collectors.toList()));
    }

    /**
     * 标识上架
     * @param request
     */
    public void manual(SheinRecommendSaleSkcManualRequest request) {
        List<SheinRecommendSaleSkcEntity> sheinRecommendSaleSkcEntities = sheinRecommendSaleSkcDao.listByIds(request.getSheinRecommendSaleSkcIdList());
        boolean existShelfing = sheinRecommendSaleSkcEntities.stream().anyMatch(s -> Objects.equals(s.getStatus(), SheinRecommendSaleSkcStatusEnum.SHELFING.getCode()));
        if (existShelfing) {
            throw new BusinessServiceException("上架中的不允许标识上架!");
        }
        SaStoreEntity saStoreEntity = saStoreDao.getById(request.getStoreId());
        List<String> skcList = sheinRecommendSaleSkcEntities.stream().map(SheinRecommendSaleSkcEntity::getSkc).distinct().collect(Collectors.toList());
        Map<String, List<SheinRecommendSaleSkcEntity>> map = sheinRecommendSaleSkcDao.getBySkcInAndPlatform(skcList, SheinRecommendPlatformEnum.of(saStoreEntity.allPlatform()).getOmsPublishPlatform()).stream().collect(Collectors.groupingBy(SheinRecommendSaleSkcEntity::getSkc));
        map.forEach((s, value) -> {
            boolean existStore = value.stream().anyMatch(v -> !Objects.equals(v.getPublishStoreId(), request.getStoreId()) && Objects.equals(v.getStatus(), SheinRecommendSaleSkcStatusEnum.SHELFING.getCode()));
            if (existStore) {
                throw new BusinessServiceException("上架中已经存在该店铺!");
            }
        });
        markAsPublish(sheinRecommendSaleSkcEntities, saStoreEntity, loginInfoService.getUserName());
    }

    /**
     * 标识上架
     * @param request
     */
    public void changeSheinRecommendSaleSkcStatus(SheinRecommendSaleSkcIdListRequest request) {
        List<SheinRecommendSaleSkcEntity> recommendSaleSkcEntities = sheinRecommendSaleSkcDao.getSheinRecommendSaleSkcListByIds(request.getSheinRecommendSaleSkcIdList());
        if (recommendSaleSkcEntities.stream().anyMatch(t -> !t.getStatus().equals(SheinRecommendSaleSkcStatusEnum.UN_SHELF.getCode()))) {
            throw new BusinessServiceException("有状态不是未上架的skc，请重新选择");
        }
        for (SheinRecommendSaleSkcEntity entity : recommendSaleSkcEntities) {
            entity.setStatus(SheinRecommendSaleSkcStatusEnum.SHELFING.getCode());
            entity.setUpdateBy(request.getOperatorUserName());
            entity.setMarkDate(new Date());
        }
        sheinRecommendSaleSkcDao.saveOrUpdateBatch(recommendSaleSkcEntities);
        List<SheinRecommendOperationLogEntity> sheinRecommendOperationLogEntities = new ArrayList<>(recommendSaleSkcEntities.size());
        recommendSaleSkcEntities.forEach(entity -> {
            SheinRecommendOperationLogEntity sheinRecommendOperationLogEntity = sheinRecommendOperationLogService.buildSheinRecommendOperationLog(entity.getSheinRecommendSaleSkcId(), "未上架更新为上架中", "", "", request.getOperatorUserName(), request.getOperatorUserName(), entity.getLocation());
            sheinRecommendOperationLogEntities.add(sheinRecommendOperationLogEntity);
        });
        sheinRecommendOperationLogService.saveBatch(sheinRecommendOperationLogEntities);
        searchApiService.syncSheinRecommendByIds(recommendSaleSkcEntities.stream().map(SheinRecommendSaleSkcEntity::getSheinRecommendSaleSkcId).collect(Collectors.toList()));
    }

    /**
     * 标识上架
     * 1.同店铺skc更新为上架
     * 2.其他店铺同skc忽略 shein同skc
     * 3.同店铺Spu其他skc如果是新品更新为复色
     * @param targetStore
     * @param sourceEntities
     */
    public void markAsPublish(List<SheinRecommendSaleSkcEntity> sourceEntities, SaStoreEntity targetStore, String operatorBy) {
        RecommendSaleSkcValidator.validMarkAsPublish(sourceEntities, targetStore);
        List<SheinRecommendOperationLogEntity> sheinRecommendOperationLogEntities = new ArrayList<>(sourceEntities.size());
        RecommendSaleSkcPlatformService platformService = getPlatformServiceByPlatform(targetStore);
        Validator.valid(platformService, Objects::isNull, "该店铺平台不支持,请确认");
        SysUserInfo sysUserInfo = userApiService.getUserInfoByUserAccount(operatorBy);
        for (SheinRecommendSaleSkcEntity sheinRecommendSaleSkcEntity : sourceEntities) {
            String content = String.format("skc:%s;状态:%s->%s;上架店铺名称:%s", sheinRecommendSaleSkcEntity.getSkc(), SheinRecommendSaleSkcStatusEnum.UN_SHELF.getDesc(), SheinRecommendSaleSkcStatusEnum.SHELFING.getDesc(), targetStore.getErpStoreName());
            sheinRecommendSaleSkcEntity.setStatus(SheinRecommendSaleSkcStatusEnum.SHELFING.getCode());
            sheinRecommendSaleSkcEntity.setPublishStoreId(targetStore.getId());
            sheinRecommendSaleSkcEntity.setPublishStoreName(targetStore.getErpStoreName());
            sheinRecommendSaleSkcEntity.setMarkDate(new Date());
            sheinRecommendSaleSkcDao.saveOrUpdate(sheinRecommendSaleSkcEntity);
            SheinRecommendOperationLogEntity sheinRecommendOperationLogEntity = sheinRecommendOperationLogService.buildSheinRecommendOperationLog(sheinRecommendSaleSkcEntity.getSheinRecommendSaleSkcId(), "手动上架", content, loginInfoService.getIpAddress(), sysUserInfo.getUserAccount(), sysUserInfo.getUserName(), loginInfoService.getLocation());
            sheinRecommendOperationLogEntities.add(sheinRecommendOperationLogEntity);
            platformService.modifySameSpuOrSkc(sheinRecommendSaleSkcEntity, sysUserInfo);
        }
        sheinRecommendOperationLogService.saveBatch(sheinRecommendOperationLogEntities);
        searchApiService.syncSheinRecommendByIds(sourceEntities.stream().map(SheinRecommendSaleSkcEntity::getSheinRecommendSaleSkcId).collect(Collectors.toList()));
    }

    public void pending(Integer sheinRecommendSaleSkcId) {
        SheinRecommendSaleSkcEntity sheinRecommendSaleSkcEntity = sheinRecommendSaleSkcDao.getById(sheinRecommendSaleSkcId);
        Validator.valid(sheinRecommendSaleSkcEntity, Objects::isNull, "Shein推荐不存在");
        Validator.valid(sheinRecommendSaleSkcEntity.getStatus(), item -> !SheinRecommendSaleSkcStatusEnum.SHELFING.getCode().equals(item), "Shein推荐状态不为上架中");
        SaStoreEntity saStoreEntity = saStoreDao.getById(sheinRecommendSaleSkcEntity.getPublishStoreId());
        Validator.valid(sheinRecommendSaleSkcEntity, Objects::isNull, "上架店铺不存在");
        RecommendSaleSkcPlatformService platformService = getPlatformServiceByPlatform(saStoreEntity);
        Validator.valid(sheinRecommendSaleSkcEntity, Objects::isNull, "该店铺平台不支持,请确认");

        Integer status = sheinRecommendSaleSkcEntity.getStatus();
        platformService.pending(sheinRecommendSaleSkcEntity);
        sheinRecommendSaleSkcDao.updateById(sheinRecommendSaleSkcEntity);
        //日志
        String content = String.format("skc:%s;状态:%s->%s", sheinRecommendSaleSkcEntity.getSkc(), SheinRecommendSaleSkcStatusEnum.getDescByCode(status), SheinRecommendSaleSkcStatusEnum.getDescByCode(sheinRecommendSaleSkcEntity.getStatus()));
        SheinRecommendOperationLogEntity sheinRecommendOperationLogEntity = sheinRecommendOperationLogService.buildSheinRecommendOperationLog(sheinRecommendSaleSkcEntity.getSheinRecommendSaleSkcId(), "取消上架", content, loginInfoService.getIpAddress(), loginInfoService.getUserName(), loginInfoService.getName(), loginInfoService.getLocation());
        sheinRecommendOperationLogService.saveBatch(Collections.singletonList(sheinRecommendOperationLogEntity));
        //同步ES
        searchApiService.syncSheinRecommendByIds(Collections.singletonList(sheinRecommendSaleSkcEntity.getSheinRecommendSaleSkcId()));
    }


    /**
     * 修改建议
     * @param request
     */
    public void modifyRecommendStore(SheinRecommendSaleSkcCheckRequest request) {
        List<SheinRecommendSaleSkcEntity> sheinRecommendSaleSkcEntities = sheinRecommendSaleSkcDao.listByIds(request.getSheinRecommendSaleSkcIdList());
        SaStoreEntity saStoreEntity = saStoreDao.getById(request.getRecommendStoreIds());
        RecommendSaleSkcPlatformService platformService = Objects.isNull(saStoreEntity) ? null : getPlatformServiceByPlatform(saStoreEntity);
        RecommendSaleSkcValidator.validModifyRecommendStore(sheinRecommendSaleSkcEntities, loginInfoService.getUserName(), saStoreEntity, platformService);

        List<SaStoreWebsiteEntity> saStoreWebsiteEntities = saStoreWebsiteDao.getList(Integer.valueOf(request.getRecommendStoreIds()));
        if (CollectionUtils.isEmpty(saStoreWebsiteEntities)) {
            throw new BusinessServiceException("店铺不存在站点");
        }
        List<Integer> websiteIds = saStoreWebsiteEntities.stream().map(SaStoreWebsiteEntity::getWebsiteId).collect(Collectors.toList());
        List<SelectWebsiteModel> selectWebsiteModels = omsPublishApiService.getErpWebsiteConfigByConfigWebsiteIds(websiteIds);
        Map<String, SelectWebsiteModel> websiteMap = selectWebsiteModels.stream().collect(Collectors.toMap(SelectWebsiteModel::getId, a -> a, (k1, k2) -> k1));

        List<SheinRecommendRecordEntity> sheinRecommendRecordEntityList = Lists.newArrayList();
        List<SheinRecommendOperationLogEntity> sheinRecommendOperationLogEntities = new ArrayList<>(sheinRecommendSaleSkcEntities.size());
        sheinRecommendSaleSkcEntities.forEach(sheinRecommendSaleSkcEntity -> {
            //日志
            String content = String.format("skc:%s;推荐类型:%s->%s;推荐店铺:%s", sheinRecommendSaleSkcEntity.getSkc(), SheinRecommendTypeEnum.valueOf(sheinRecommendSaleSkcEntity.getRecommendType()).getDesc(), SheinRecommendTypeEnum.RECOMMEND.getDesc(), saStoreEntity.getErpStoreName());
            SheinRecommendOperationLogEntity sheinRecommendOperationLogEntity = sheinRecommendOperationLogService.buildSheinRecommendOperationLog(sheinRecommendSaleSkcEntity.getSheinRecommendSaleSkcId(), "修改建议", content, loginInfoService.getIpAddress(), loginInfoService.getUserName(), loginInfoService.getName(), loginInfoService.getLocation());
            sheinRecommendOperationLogEntities.add(sheinRecommendOperationLogEntity);
            sheinRecommendSaleSkcEntity.setRecommendType(SheinRecommendTypeEnum.RECOMMEND.name());
            sheinRecommendSaleSkcEntity.setCreateBy(loginInfoService.getUserName());
            sheinRecommendSaleSkcEntity.setCreateDate(new Date());
            sheinRecommendSaleSkcEntity.setRecommendStoreId(saStoreEntity.getId());
            sheinRecommendSaleSkcEntity.setRecommendStoreName(saStoreEntity.getErpStoreName());
            buildSheinRecommendRecord(sheinRecommendSaleSkcEntity.getProductId(),
                    sheinRecommendSaleSkcEntity.getSkc(),
                    saStoreWebsiteEntities.get(0).getWebsiteId(),
                    saStoreWebsiteEntities.get(0).getWebsiteName(),
                    saStoreEntity.getDepartment(),
                    Optional.ofNullable(websiteMap.get(String.valueOf(saStoreWebsiteEntities.get(0).getWebsiteId()))).isPresent() ? websiteMap.get(String.valueOf(saStoreWebsiteEntities.get(0).getWebsiteId())).getPlatform() : "",
                    sheinRecommendRecordEntityList);
        });
        sheinRecommendOperationLogService.saveBatch(sheinRecommendOperationLogEntities);
        sheinRecommendRecordDao.saveBatch(sheinRecommendRecordEntityList);
        sheinRecommendSaleSkcDao.saveOrUpdateBatch(sheinRecommendSaleSkcEntities);
        searchApiService.syncSheinRecommendByIds(sheinRecommendSaleSkcEntities.stream().map(SheinRecommendSaleSkcEntity::getSheinRecommendSaleSkcId).collect(Collectors.toList()));
        messageSendSheinRecommendRecord(sheinRecommendRecordEntityList);
    }

    public void messageSendSheinRecommendRecord(List<SheinRecommendRecordEntity> sheinRecommendRecordEntityList) {
        sheinRecommendRecordEntityList.forEach(sheinRecommendRecordEntity -> {
            publishSkcMessageSendService.sendUpdateMessage(String.format("PS_%s_%s", sheinRecommendRecordEntity.getRecommendWebsiteId(), sheinRecommendRecordEntity.getSkc()));
        });
    }

    private SheinRecommendSaleSkcEntity findExistOrElseBuildNew(SaStoreEntity saStoreEntity, String skc, Integer productId) {
        SheinRecommendSaleSkcEntity entity = sheinRecommendSaleSkcDao.getOneIncludeDeleteBySkcAndRecommendStore(skc, saStoreEntity.getId());
        if (Objects.isNull(entity)) {
            entity = new SheinRecommendSaleSkcEntity();
            entity.setCreateDate(new Date());
            entity.setLocation(saStoreEntity.getLocation());
            entity.setCreateBy(loginInfoService.getUserName());
        }
        entity.setProductId(productId);
        entity.setPlatform(SheinRecommendPlatformEnum.of(saStoreEntity.allPlatform()).getOmsPublishPlatform());
        entity.setRecommendStoreId(saStoreEntity.getId());
        entity.setRecommendStoreName(saStoreEntity.getErpStoreName());
        entity.setSkc(skc);
        entity.setStatus(SheinRecommendSaleSkcStatusEnum.UN_SHELF.getCode());
        entity.setRecommendType(SheinRecommendTypeEnum.RECOMMEND.name());
        entity.setUpdateDate(new Date());
        entity.setUpdateBy(loginInfoService.getUserName());
        return entity;
    }


    private RecommendSaleSkcPlatformService getPlatformServiceByPlatform(SaStoreEntity saStoreEntity) {
        if (StrUtil.isNotEmpty(saStoreEntity.getPlatformName()) && saStoreEntity.getPlatformName().startsWith(SheinRecommendPlatformEnum.AMAZON.getPlatform()) || StrUtil.isNotEmpty(saStoreEntity.getSecondPlatformName()) && saStoreEntity.getSecondPlatformName().startsWith(SheinRecommendPlatformEnum.AMAZON.getPlatform())) {
            return amazonRecommendSaleSkcServiceImpl;
        }
        if ("Tiktok直邮".equals(saStoreEntity.getPlatformName()) || "Tiktok直邮".equals(saStoreEntity.getSecondPlatformName())) {
            return tiktokRecommendSaleSkcServiceImpl;
        }
        RecommendSaleSkcPlatformService platformService = RECOMMEND_PLATFORM_MAP.get(saStoreEntity.getPlatformName());
        if (Objects.isNull(platformService)) {
            platformService = RECOMMEND_PLATFORM_MAP.get(saStoreEntity.getSecondPlatformName());
        }
        return platformService;
    }



}
