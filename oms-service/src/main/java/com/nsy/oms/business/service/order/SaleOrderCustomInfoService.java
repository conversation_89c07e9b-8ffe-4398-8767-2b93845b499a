package com.nsy.oms.business.service.order;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nsy.oms.repository.entity.order.SaleOrderCustomInfoEntity;

import java.util.List;

/**
 * 订单自定义信息表 服务接口
 *
 * <AUTHOR>
 */
public interface SaleOrderCustomInfoService extends IService<SaleOrderCustomInfoEntity> {

    /**
     * 根据订单ID查询自定义信息
     *
     * @param orderId 订单ID
     * @return 自定义信息列表
     */
    List<SaleOrderCustomInfoEntity> getByOrderId(Integer orderId);

    /**
     * 根据订单号查询自定义信息
     *
     * @param orderNo 订单号
     * @return 自定义信息列表
     */
    List<SaleOrderCustomInfoEntity> getByOrderNo(String orderNo);

    /**
     * 根据订单号和自定义字段查询自定义信息
     *
     * @param orderId     订单号
     * @param customField 自定义字段
     * @return 自定义信息
     */
    SaleOrderCustomInfoEntity getByOrderNoAndCustomField(Integer orderId, String customField);

    /**
     * 根据订单号自定义信息列表
     *
     * @param orderNo 订单号
     * @return 自定义信息
     */
    List<SaleOrderCustomInfoEntity> getByOrderNoList(String orderNo);
} 