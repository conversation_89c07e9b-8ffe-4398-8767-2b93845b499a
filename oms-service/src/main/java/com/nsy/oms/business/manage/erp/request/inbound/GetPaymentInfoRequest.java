package com.nsy.oms.business.manage.erp.request.inbound;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetPaymentInfoRequest {

    @JsonProperty("RefundRequestId")
    private Integer refundRequestId;

    @JsonProperty("StoreId")
    private Integer storeId;

    @JsonProperty("RefundMoney")
    private BigDecimal refundMoney;

    @JsonProperty("BuyerEmail")
    private String buyerEmail;

}


