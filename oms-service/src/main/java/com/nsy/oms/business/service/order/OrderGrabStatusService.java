package com.nsy.oms.business.service.order;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nsy.oms.enums.order.OrderTypeEnum;
import com.nsy.oms.repository.entity.order.OrderGrabStatusEntity;
import com.nsy.oms.repository.entity.sa.SaStoreEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【order_grab_status(订单抓单状态表)】的数据库操作Service
 * @createDate 2024-06-13 14:52:05
 */
public interface OrderGrabStatusService extends IService<OrderGrabStatusEntity> {

    /**
     * 保存或更新订单抓单状态实体
     * @param grabStatusEntity
     */
    void saveOrUpdateEntity(OrderGrabStatusEntity grabStatusEntity);

    List<OrderGrabStatusEntity> getListBystoreIdAndOrderNos(Integer storeId, List<String> orderNos);

    void updateStatus(Integer storeId, String orderNo, Integer itemStatus);

    List<OrderGrabStatusEntity> existOrders(Integer storeId, List<String> orderNoList);

    /**
     * 抓单后，保存或更新订单状态信息
     * @param storeEntity 店铺信息
     * @param orderId 订单ID
     * @param platformOrderNo 平台订单号
     * @param orderType 订单类型
     * @param updateBy 更新人，记录最后一次更新操作的执行人
     * @param isGrabItem 是否抓取详情：0-否，1-是
     * @param isGrabAddress 是否抓取地址：0-否，1-是
     */
    void saveOrUpdateOrderGrabStatus(SaStoreEntity storeEntity, Integer orderId, String platformOrderNo, OrderTypeEnum orderType, String updateBy, Integer isGrabItem, Integer isGrabAddress);

    /**
     * 更新订单状态信息的是否抓取详情
     * @param storeId 店铺Id
     * @param orderId 订单ID
     * @param platformOrderNo 平台订单号
     * @param updateBy 更新人，记录最后一次更新操作的执行人
     * @param isGrabItem 是否抓取详情：0-否，1-是
     */
    void updateOrderGrabStatusForIsGrabItem(Integer storeId, Integer orderId, String platformOrderNo, Integer isGrabItem, String updateBy);

    /**
     * 更新订单状态信息的是否抓取地址
     * @param storeId 店铺Id
     * @param orderId 订单ID
     * @param platformOrderNo 平台订单号
     * @param updateBy 更新人，记录最后一次更新操作的执行人
     * @param isGrabAddress 是否抓取地址：0-否，1-是
     */
    void updateOrderGrabStatusForIsGrabAddress(Integer storeId, Integer orderId, String platformOrderNo, Integer isGrabAddress, String updateBy);
}
