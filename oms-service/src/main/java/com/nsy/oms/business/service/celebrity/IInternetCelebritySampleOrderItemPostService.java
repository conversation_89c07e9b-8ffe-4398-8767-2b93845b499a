package com.nsy.oms.business.service.celebrity;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nsy.oms.business.domain.request.celebrity.SaveInternetCelebritySampleOrderItemPostRequest;
import com.nsy.oms.business.domain.response.celebrity.SyncInternetCelebritySampleOrderItemPost;
import com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderItemPostEntity;

import java.util.List;

/**
 * <p>
 * 网红样衣订单明细发帖信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-29
 */
public interface IInternetCelebritySampleOrderItemPostService extends IService<InternetCelebritySampleOrderItemPostEntity> {

    List<InternetCelebritySampleOrderItemPostEntity> getByInternetCelebritySampleOrderItemIds(List<Integer> internetCelebritySampleOrderItemIds);

    void saveOrUpdate(SaveInternetCelebritySampleOrderItemPostRequest request);

    void deleteById(Integer internetCelebritySampleOrderItemPostId);

    void syncInternetCelebritySampleOrderItemPostByImport(Integer internetCelebritySampleOrderItemId, List<SyncInternetCelebritySampleOrderItemPost> syncInternetCelebritySampleOrderItemPosts);

    List<InternetCelebritySampleOrderItemPostEntity> getListByVideoUrl(String videoUrl);

    List<InternetCelebritySampleOrderItemPostEntity> listByIdList(List<Integer> postIds);

    InternetCelebritySampleOrderItemPostEntity findTopByStoreIdAndVideoCode(Integer storeId, String videoCode);

}
