package com.nsy.oms.business.domain.request.sa;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/13 14:34
 */
public class SaMediaAccountRequest {
    @ApiModelProperty("id集合")
    private List<Integer> idList;
    @ApiModelProperty("账号主体id")
    private Integer accountSubjectId;
    @ApiModelProperty("分配人员")
    private String allocateUser;

    @ApiModelProperty("申请说明")
    private String remark;
    @ApiModelProperty("申请说明")
    private String applyRemark;
    @ApiModelProperty("ip")
    private String ip;
    @ApiModelProperty("ip归属")
    private String ipLocation;
    @ApiModelProperty("品牌名")
    private String brand;
    @ApiModelProperty("是否关联护照或绿卡 0 否 1是")
    private Integer isAssociatedGreenCardOrPassport;
    @ApiModelProperty("申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date applyDate;
    @ApiModelProperty("正式运营时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date officialOperationDate;
    @ApiModelProperty("社媒账号")
    List<SaLiveMediaAccountMappingRequest> mediaList;

    /**
     * 商品通知配置人员
     */
    @ApiModelProperty("新商品通知配置人员")
    private String storeStaffings;

    /**
     * 商品通知配置人员
     */
    @ApiModelProperty("老商品通知配置人员前")
    private String oldStoreStaffings;

    @ApiModelProperty("变更前开户行id")
    private String oldBankId;

    @ApiModelProperty("开户行id")
    private String bankId;

    @ApiModelProperty("变更前卡号id")
    private Integer oldCardId;

    @ApiModelProperty("卡号id")
    private Integer cardId;

    public String getOldBankId() {
        return oldBankId;
    }

    public void setOldBankId(String oldBankId) {
        this.oldBankId = oldBankId;
    }

    public String getBankId() {
        return bankId;
    }

    public void setBankId(String bankId) {
        this.bankId = bankId;
    }

    public Integer getOldCardId() {
        return oldCardId;
    }

    public void setOldCardId(Integer oldCardId) {
        this.oldCardId = oldCardId;
    }

    public Integer getCardId() {
        return cardId;
    }

    public void setCardId(Integer cardId) {
        this.cardId = cardId;
    }

    public String getStoreStaffings() {
        return storeStaffings;
    }

    public String getApplyRemark() {
        return applyRemark;
    }

    public void setApplyRemark(String applyRemark) {
        this.applyRemark = applyRemark;
    }

    public void setStoreStaffings(String storeStaffings) {
        this.storeStaffings = storeStaffings;
    }

    public String getOldStoreStaffings() {
        return oldStoreStaffings;
    }

    public void setOldStoreStaffings(String oldStoreStaffings) {
        this.oldStoreStaffings = oldStoreStaffings;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public Integer getIsAssociatedGreenCardOrPassport() {
        return isAssociatedGreenCardOrPassport;
    }

    public void setIsAssociatedGreenCardOrPassport(Integer isAssociatedGreenCardOrPassport) {
        this.isAssociatedGreenCardOrPassport = isAssociatedGreenCardOrPassport;
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public Date getOfficialOperationDate() {
        return officialOperationDate;
    }

    public void setOfficialOperationDate(Date officialOperationDate) {
        this.officialOperationDate = officialOperationDate;
    }

    public List<SaLiveMediaAccountMappingRequest> getMediaList() {
        return mediaList;
    }

    public void setMediaList(List<SaLiveMediaAccountMappingRequest> mediaList) {
        this.mediaList = mediaList;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getIpLocation() {
        return ipLocation;
    }

    public void setIpLocation(String ipLocation) {
        this.ipLocation = ipLocation;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getAllocateUser() {
        return allocateUser;
    }

    public void setAllocateUser(String allocateUser) {
        this.allocateUser = allocateUser;
    }

    public List<Integer> getIdList() {
        return idList;
    }

    public void setIdList(List<Integer> idList) {
        this.idList = idList;
    }

    public Integer getAccountSubjectId() {
        return accountSubjectId;
    }

    public void setAccountSubjectId(Integer accountSubjectId) {
        this.accountSubjectId = accountSubjectId;
    }
}
