package com.nsy.oms.business.service.download.stockout;

import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.api.core.apicore.response.CustomExcelResponse;
import com.nsy.api.core.apicore.util.NsyCollUtil;
import com.nsy.api.core.apicore.util.NsyExcelUtil;
import com.nsy.api.core.apicore.util.NsyJacksonUtils;
import com.nsy.api.oms.enums.QuartzDownloadQueueTypeEnum;
import com.nsy.oms.business.domain.download.stockout.OverseaSkcExport;
import com.nsy.oms.business.domain.request.download.DownloadRequest;
import com.nsy.oms.business.domain.request.stockout.OverseaSkcPageRequest;
import com.nsy.oms.business.domain.response.base.DownloadResponse;
import com.nsy.oms.business.domain.response.stockout.OverseaSkcPageResponse;
import com.nsy.oms.business.service.download.IDownloadService;
import com.nsy.oms.business.service.stockout.OverseaSkcService;
import com.nsy.oms.utils.JsonMapper;
import com.nsy.oms.utils.mp.LocationContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/11/1714:11
 */
@Slf4j
@Service
public class OverseaSkcDownloadServiceImpl implements IDownloadService {
    @Autowired
    private OverseaSkcService overseaSkcService;

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.OVERSEA_SKC_LIST;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        log.info("OverseaSkcDownloadServiceImpl-接收到的下载请求:{}", JsonMapper.toJson(request));
        OverseaSkcPageRequest searchRequest = NsyJacksonUtils.toObj(request.getRequestContent(), OverseaSkcPageRequest.class);
        //请求页码
        searchRequest.setPageIndex(request.getPageIndex());
        searchRequest.setPageSize(request.getPageSize());
        LocationContext.setLocation(request.getLocation());

        PageResponse<OverseaSkcPageResponse> page = overseaSkcService.page(searchRequest);
        if (NsyCollUtil.isEmpty(page.getContent())) {
            return DownloadResponse.of(null, 0L);
        }
        DownloadResponse response = new DownloadResponse();
        List<List<Object>> data = new ArrayList<>();
        page.getContent().forEach(skuPage -> {
            skuPage.getSpaceItemList().forEach(spaceItem -> {
                OverseaSkcExport skcExport = new OverseaSkcExport();

                skcExport.setSkc(skuPage.getSkc());
                skcExport.setLabel(skuPage.getLabel());
                skcExport.setImageUrl(skuPage.getImageUrl());
                skcExport.setTitle(skuPage.getTitle());
                skcExport.setSku(skuPage.getSku());
                skcExport.setSizeCode(skuPage.getSizeCode());

                skcExport.setTotalAvailableStock(skuPage.getAvailableStock());
                skcExport.setTotalOnTheWayStock(skuPage.getOnTheWayStock());
                skcExport.setTotalStockOutQtyIn7(skuPage.getStockOutQtyIn7());
                skcExport.setTotalStockOutQtyIn14(skuPage.getStockOutQtyIn14());
                skcExport.setTotalStockOutQtyIn30(skuPage.getStockOutQtyIn30());
                skcExport.setTotalDailyStockOutQtyIn7(skuPage.getDailyStockOutQtyIn7());
                skcExport.setTotalDailyStockOutQtyIn14(skuPage.getDailyStockOutQtyIn14());
                skcExport.setTotalDailyStockOutQtyIn30(skuPage.getDailyStockOutQtyIn30());
                skcExport.setTotalAvailableDays(skuPage.getAvailableDays());

                skcExport.setSpaceName(spaceItem.getSpaceName());
                skcExport.setAvailableStock(spaceItem.getAvailableStock());
                skcExport.setOnTheWayStock(spaceItem.getOnTheWayStock());
                skcExport.setStockOutQtyIn7(spaceItem.getStockOutQtyIn7());
                skcExport.setStockOutQtyIn14(spaceItem.getStockOutQtyIn14());
                skcExport.setStockOutQtyIn30(spaceItem.getStockOutQtyIn30());
                skcExport.setDailyStockOutQtyIn7(spaceItem.getDailyStockOutQtyIn7());
                skcExport.setDailyStockOutQtyIn14(spaceItem.getDailyStockOutQtyIn14());
                skcExport.setDailyStockOutQtyIn30(spaceItem.getDailyStockOutQtyIn30());
                skcExport.setAvailableDays(spaceItem.getAvailableDays());

                data.add(NsyExcelUtil.getData(OverseaSkcExport.class, skcExport));
            });
        });
        CustomExcelResponse excelResponse = new CustomExcelResponse();
        excelResponse.setHeaders(NsyExcelUtil.getCommonHeads(OverseaSkcExport.class));
        excelResponse.setData(data);
        response.setTotalCount(page.getTotalCount());
        response.setDataJsonStr(JsonMapper.toJson(excelResponse));
        return response;
    }


}
