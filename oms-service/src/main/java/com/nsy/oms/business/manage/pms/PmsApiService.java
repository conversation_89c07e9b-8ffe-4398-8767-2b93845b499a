package com.nsy.oms.business.manage.pms;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.pms.dto.product.ProductSpecDTO;
import com.nsy.oms.business.domain.request.celebrity.SyncCooperationNumRequest;
import com.nsy.oms.business.domain.request.sa.GetProductCategoryInfoRequest;
import com.nsy.oms.business.manage.pms.domain.ProductSkcRelationshipDTO;
import com.nsy.oms.business.manage.pms.request.ConfigInternetCelebrityRequest;
import com.nsy.oms.business.manage.pms.request.GetProductInfoRequest;
import com.nsy.oms.business.manage.pms.request.SimpleProductSpecRequest;
import com.nsy.oms.business.manage.pms.response.CategoryResponse;
import com.nsy.oms.business.manage.pms.response.ConfigInternetCelebrityResponse;
import com.nsy.oms.business.manage.pms.response.GetProductCategoryInfoResponse;
import com.nsy.oms.business.manage.pms.response.ProductInfoBySkuResponse;
import com.nsy.oms.business.manage.pms.response.ProductSeasonInfo;
import com.nsy.oms.business.manage.pms.response.ProductSpecResponse;
import com.nsy.oms.business.manage.pms.response.SimpleProductSpecResponse;
import com.nsy.oms.business.manage.user.request.shared.SelectModel;
import com.nsy.oms.utils.JsonMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.client.RestTemplate;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <AUTHOR>
 */
@Service
public class PmsApiService {
    private static final Logger LOGGER = LoggerFactory.getLogger(PmsApiService.class);
    @Inject
    private RestTemplate restTemplate;
    @Value("${nsy.service.url.pms}")
    private String pmsServiceUrl;

    /**
     * 获取主营品类数据
     */
    public List<CategoryResponse> getList() {
        List<CategoryResponse> categoryResponse = new ArrayList<>();
        String uri = String.format("%s/config/category/list", pmsServiceUrl);
        try {
            ResponseEntity<String> respEntity = this.restTemplate.getForEntity(uri, String.class);
            if (StringUtils.hasText(respEntity.getBody())) {
                categoryResponse = JsonMapper.jsonStringToObjectArray(respEntity.getBody(), CategoryResponse.class);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return categoryResponse;
    }

    public ProductSpecResponse getProductSpecBySpecSku(String specSku) {
        String uri = StrUtil.format("{}/product/specSku/{}", pmsServiceUrl, specSku);
        ProductSpecResponse productSpecResponse = null;
        try {
            ResponseEntity<String> respEntity = this.restTemplate.getForEntity(uri, String.class);
            if (StringUtils.hasText(respEntity.getBody())) {
                productSpecResponse = JSONUtil.toBean(respEntity.getBody(), ProductSpecResponse.class);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return productSpecResponse;
    }

    public ProductInfoBySkuResponse getProductInfo(String skc, String sku) {
        String uri = StrUtil.format("{}/product/product-info-by-sku", pmsServiceUrl);
        GetProductInfoRequest request = new GetProductInfoRequest();
        request.setSkc(skc);
        request.setSku(sku);
        ProductInfoBySkuResponse response = null;
        try {
            ResponseEntity<String> respEntity = this.restTemplate.postForEntity(uri, request, String.class);
            if (StringUtils.hasText(respEntity.getBody())) {
                response = JSONUtil.toBean(respEntity.getBody(), ProductInfoBySkuResponse.class);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return response;
    }


    public List<GetProductCategoryInfoResponse> getProductCategoryInfo(List<String> sellerSkus) {
        GetProductCategoryInfoRequest request = new GetProductCategoryInfoRequest();
        request.setErpSkus(sellerSkus);
        List<GetProductCategoryInfoResponse> responses = new ArrayList<>();
        String uri = String.format("%s/table-info/product-category-info", pmsServiceUrl);
        try {
            ResponseEntity<String> respEntity = this.restTemplate.postForEntity(uri, request, String.class);
            if (StringUtils.hasText(respEntity.getBody())) {
                responses = JsonMapper.jsonStringToObjectArray(respEntity.getBody(), GetProductCategoryInfoResponse.class);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return responses;
    }

    public ProductSkcRelationshipDTO listSkcRelations(List<String> skcList) {
        String uri = StrUtil.format("{}/product-skc-relationship/list-skc", pmsServiceUrl);
        ProductSkcRelationshipDTO response = null;
        try {
            ResponseEntity<String> respEntity = this.restTemplate.postForEntity(uri, skcList, String.class);
            if (StringUtils.hasText(respEntity.getBody())) {
                response = JSONUtil.toBean(respEntity.getBody(), ProductSkcRelationshipDTO.class);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return response;
    }

    public List<ProductSpecDTO> getSkcInfo(List<String> skcList) {
        if (CollectionUtils.isEmpty(skcList)) {
            return Collections.emptyList();
        }
        String uri = StrUtil.format("{}/product-spec/skc/get-skc-info", pmsServiceUrl);
        List<ProductSpecDTO> response = Collections.emptyList();
        try {
            ResponseEntity<String> respEntity = this.restTemplate.postForEntity(uri, skcList, String.class);
            if (StringUtils.hasText(respEntity.getBody())) {
                response = JsonMapper.jsonStringToObjectArray(respEntity.getBody(), ProductSpecDTO.class);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return response;
    }

    public List<ProductSpecDTO> specInfo(@RequestBody List<String> skuList) {
        String uri = String.format("%s/product-spec/spec-info", pmsServiceUrl);
        List<ProductSpecDTO> response = Collections.emptyList();
        try {
            ResponseEntity<String> respEntity = this.restTemplate.postForEntity(uri, skuList, String.class);
            if (StringUtils.hasText(respEntity.getBody())) {
                response = JsonMapper.jsonStringToObjectArray(respEntity.getBody(), ProductSpecDTO.class);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return response;
    }


    public List<ConfigInternetCelebrityResponse> getConfigInternetCelebrityList(ConfigInternetCelebrityRequest request) {
        List<ConfigInternetCelebrityResponse> responses = new ArrayList<>();
        String uri = String.format("%s/bd/internet-celebrity/info", pmsServiceUrl);
        try {
            ResponseEntity<String> respEntity = this.restTemplate.postForEntity(uri, request, String.class);
            if (StringUtils.hasText(respEntity.getBody())) {
                responses = JsonMapper.jsonStringToObjectArray(respEntity.getBody(), ConfigInternetCelebrityResponse.class);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return responses;
    }


    public void syncCooperationNum(SyncCooperationNumRequest request) {
        String uri = String.format("%s/bd/internet-celebrity/sync-cooperation-num", pmsServiceUrl);
        try {
            this.restTemplate.postForEntity(uri, request, String.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    public List<SimpleProductSpecResponse> productSkcSimpleInfos(List<String> skcList) {
        String uri = String.format("%s/product-skc-simple-infos", pmsServiceUrl);
        SimpleProductSpecRequest simpleProductSpecRequest = new SimpleProductSpecRequest();
        simpleProductSpecRequest.setSkcs(skcList);
        ResponseEntity<String> responseEntity = this.restTemplate.postForEntity(uri, simpleProductSpecRequest, String.class);
        return JsonMapper.jsonStringToObjectArray(responseEntity.getBody(), SimpleProductSpecResponse.class);
    }

    public List<ProductSeasonInfo> getProductSeasonLabelInfoList(List<String> spuList) {
        if (CollectionUtils.isEmpty(spuList)) {
            return Collections.emptyList();
        }
        String uri = String.format("%s/product/product-season-label-info-list", pmsServiceUrl);
        try {
            ResponseEntity<String> responseEntity = this.restTemplate.postForEntity(uri, spuList, String.class);
            return JSONUtil.toList(responseEntity.getBody(), ProductSeasonInfo.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public List<SelectModel> getDevelopSeasonSelect() {
        String uri = String.format("%s/config-style/develop-season-select", pmsServiceUrl);
        try {
            ResponseEntity<String> responseEntity = this.restTemplate.getForEntity(uri, String.class);
            return JSONUtil.toList(responseEntity.getBody(), SelectModel.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return Collections.emptyList();
    }
}
