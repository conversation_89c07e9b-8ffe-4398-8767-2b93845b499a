package com.nsy.oms.business.domain.stock;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 库存表
 *
 * <AUTHOR>
 * @Date 2025-04-02 14:40
 */
public class StockInfo {


    /**
     * StockId
     */
    private Integer stockId;
    /**
     * 仓库id
     */
    private Integer spaceId;
    /**
     * 仓库名称
     */
    private String spaceName;
    /**
     * 商品Id
     */
    private Integer productId;
    /**
     * 规格编码Id
     */
    private Integer specId;
    /**
     * SKU
     */
    private String sku;
    /**
     * 库存
     */
    private Integer stock;

    // 已预配库存
    private Integer prematchQty;

    // 可用库存
    private Integer availableStock;

    /**
     * 商通规格编码Id
     */
    private Integer erpSpecId;
    /**
     * 库存级别：店铺、部门、公司
     */
    private String stockType;
    /**
     * 店铺id 库存取店铺，部门，公司
     */
    private Integer storeId;
    /**
     * 店铺名称
     */
    private String storeName;
    /**
     * 共享库存
     */
    private Integer shareStock;
    /**
     * 前置贴标库存 ---- 包含融合标和吊牌
     */
    private Integer preLableStock;
    /**
     * 部门
     */
    private String businessType;
    /**
     * 是否锁定 1-是，0-否
     */
    private Integer isLock;

    /**
     * 定制款SpecIdList, 用于预配
     */
    private List<Integer> designSpecIdList;

    public Integer getPrematchQty() {
        return prematchQty;
    }

    public void setPrematchQty(Integer prematchQty) {
        this.prematchQty = prematchQty;
    }

    public Integer getAvailableStock() {
        return availableStock;
    }

    public void setAvailableStock(Integer availableStock) {
        this.availableStock = availableStock;
    }

    public Integer getStockId() {
        return stockId;
    }

    public void setStockId(Integer stockId) {
        this.stockId = stockId;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Integer getSpecId() {
        return specId;
    }

    public void setSpecId(Integer specId) {
        this.specId = specId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getStock() {
        return stock;
    }

    public void setStock(Integer stock) {
        this.stock = stock;
    }

    public Integer getErpSpecId() {
        return erpSpecId;
    }

    public void setErpSpecId(Integer erpSpecId) {
        this.erpSpecId = erpSpecId;
    }

    public String getStockType() {
        return stockType;
    }

    public void setStockType(String stockType) {
        this.stockType = stockType;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public Integer getShareStock() {
        return shareStock;
    }

    public void setShareStock(Integer shareStock) {
        this.shareStock = shareStock;
    }

    public Integer getPreLableStock() {
        return preLableStock;
    }

    public void setPreLableStock(Integer preLableStock) {
        this.preLableStock = preLableStock;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public Integer getIsLock() {
        return isLock;
    }

    public void setIsLock(Integer isLock) {
        this.isLock = isLock;
    }

    public List<Integer> getDesignSpecIdList() {
        return designSpecIdList;
    }

    public void setDesignSpecIdList(List<Integer> designSpecIdList) {
        this.designSpecIdList = designSpecIdList;
    }

    /**
     * 包含本身款和定制款, 用于预配
     */
    public List<Integer> getUsableSpecIdList() {
        if (designSpecIdList != null && !designSpecIdList.isEmpty()) {
            List<Integer> result = new ArrayList<>(designSpecIdList);
            if (specId != null && !result.contains(specId)) {
                result.add(specId);
            }
            return result;
        }

        return Collections.singletonList(specId);
    }
}
