package com.nsy.oms.business.service.platform.order.base;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.nsy.api.core.apicore.constant.enums.IsEnum;
import com.nsy.api.core.apicore.constant.enums.QueueStatusEnum;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.MD5Util;
import com.nsy.api.core.apicore.util.NsyCollUtil;
import com.nsy.api.core.apicore.util.NsyJacksonUtils;
import com.nsy.api.transfer.domain.request.platform.PlatformOrderRequest;
import com.nsy.api.transfer.domain.response.platform.PlatformOrderInfo;
import com.nsy.api.transfer.domain.response.platform.PlatformOrderInfoResponse;
import com.nsy.api.transfer.enums.PlatformOrderStatusEnum;
import com.nsy.api.transfer.enums.PlatformTypeEnum;
import com.nsy.oms.business.manage.transfer.TransferApiService;
import com.nsy.oms.business.service.order.OrderGrabStatusService;
import com.nsy.oms.business.service.order.OrderIdempotentService;
import com.nsy.oms.business.service.order.OrderMissedQueueService;
import com.nsy.oms.business.service.order.SaleOrderService;
import com.nsy.oms.business.service.platform.impl.PlatformOrderServiceImpl;
import com.nsy.oms.enums.order.OrderTypeEnum;
import com.nsy.oms.enums.platform.PlatformShippingTypeEnum;
import com.nsy.oms.repository.dao.sa.SaStoreConfigDao;
import com.nsy.oms.repository.entity.order.SaleOrderEntity;
import com.nsy.oms.repository.entity.order.OrderIdempotentEntity;
import com.nsy.oms.repository.entity.order.OrderMissedQueueEntity;
import com.nsy.oms.repository.entity.platform.PlatformOrderEntity;
import com.nsy.oms.repository.entity.sa.SaStoreEntity;
import com.nsy.oms.utils.SpringContextUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 平台抓单--返回完整订单信息
 */
public abstract class BaseCompleteOrderInfoService extends BasePlatformOrderService {
    private static final Logger LOGGER = LoggerFactory.getLogger(BaseCompleteOrderInfoService.class);
    @Autowired
    private TransferApiService transferApiService;
    @Autowired
    private SaStoreConfigDao saStoreConfigDao;
    @Autowired
    private SaleOrderService saleOrderService;
    @Autowired
    private PlatformOrderServiceImpl platformOrderService;
    @Autowired
    private OrderGrabStatusService orderGrabStatusService;
    @Autowired
    private OrderIdempotentService orderIdempotentService;
    @Autowired
    private OrderMissedQueueService orderMissedQueueService;

    @Value("${spring.profiles.active}")
    private String env;

    /**
     * 按时间抓单
     */
    @Override
    protected void getPlatformOrderByDate(SaStoreEntity storeEntity, PlatformOrderRequest orderRequest) {
        PlatformTypeEnum platformTypeEnum = PlatformTypeEnum.getPlatform(storeEntity.getPlatformId());
        if (platformTypeEnum == null) {
            throw new BusinessServiceException("根据ID获取平台失败！");
        }
        PlatformOrderInfoResponse response = transferApiService.getOrderListByDate(platformTypeEnum.getUrl(), orderRequest);
        if (ObjectUtil.isNull(response) || NsyCollUtil.isEmpty(response.getPlatformOrderInfoList())) {
            saStoreConfigDao.saveCurrentOrderCatchDate(storeEntity, orderRequest.getEndOrderTime(), getSubtractionOrderCatchDate(), "自动抓单"); //更新店铺最晚抓单时间
            return;
        }
        saStoreConfigDao.saveCurrentOrderCatchDate(storeEntity, calculateLatestOrderGrabDate(response.getPlatformOrderInfoList(), orderRequest.getEndOrderTime(), response.getHasNextPage()), getSubtractionOrderCatchDate(), "自动抓单"); //更新店铺最晚抓单时间
        doSaveOrderInfoByDate(storeEntity, response); //更新订单信息
        boolean hasNextPage = ObjectUtil.isNotNull(response.getHasNextPage()) && response.getHasNextPage();
        if (hasNextPage) {
            orderRequest.setPageIndex(orderRequest.getPageIndex() + 1);
            orderRequest.setNextPageInfo(response.getNextPageInfo());
            getPlatformOrderByDate(storeEntity, orderRequest);
        }
    }

    /**
     * 按订单号抓单
     */
    @Override
    protected void getPlatformOrderListByIds(SaStoreEntity storeEntity, PlatformOrderRequest orderRequest, List<OrderMissedQueueEntity> orderMissedQueueEntityList) {
        PlatformTypeEnum platformTypeEnum = PlatformTypeEnum.getPlatform(storeEntity.getPlatformId());
        if (platformTypeEnum == null) {
            throw new BusinessServiceException("根据ID获取平台失败！");
        }
        PlatformOrderInfoResponse response = transferApiService.getOrderListByIds(platformTypeEnum.getUrl(), orderRequest);
        if (ObjectUtil.isNull(response) || NsyCollUtil.isEmpty(response.getPlatformOrderInfoList())) {
            orderMissedQueueService.updateOrderMissedQueue(orderMissedQueueEntityList, QueueStatusEnum.EXECUTE_FAIL, "订单为空", "自动抓单");
            return;
        }
        doSaveOrderInfoByOrderNo(storeEntity, response); //更新订单信息
        orderMissedQueueService.updateOrderMissedQueue(orderMissedQueueEntityList, QueueStatusEnum.EXECUTE_SUCCESS, "", "自动抓单");
    }

    /**
     * 处理根据时间区间抓单返回信息--完整返回
     */
    protected void doSaveOrderInfoByDate(SaStoreEntity storeEntity, PlatformOrderInfoResponse response) {
        response.getPlatformOrderInfoList().forEach(platformOrderInfo -> {
            convertInfo(platformOrderInfo);
            SpringContextUtils.getBean(this.getClass()).handleOrderInfo(platformOrderInfo, storeEntity); //处理抓单返回订单信息
        });
    }

    /**
     * 处理根据订单号抓单返回信息--完整返回
     */
    protected void doSaveOrderInfoByOrderNo(SaStoreEntity storeEntity, PlatformOrderInfoResponse response) {
        response.getPlatformOrderInfoList().forEach(platformOrderInfo -> {
            SpringContextUtils.getBean(this.getClass()).handleOrderInfo(platformOrderInfo, storeEntity); //处理抓单返回订单信息
        });
    }

    /**
     * 处理抓单返回订单信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleOrderInfo(PlatformOrderInfo platformOrderInfo, SaStoreEntity storeEntity) {

        if ("production".equalsIgnoreCase(env) && ObjectUtil.isNotNull(platformOrderInfo.getOrderPaymentDate()) && platformOrderInfo.getOrderPaymentDate().before(DateUtil.offset(DateUtil.beginOfDay(new Date()), DateField.YEAR, -1))) {
            return; //付款时间一年前不处理
        }
        OrderIdempotentEntity orderIdempotentEntity = orderIdempotentService.getByStoreIdAndOrderNo(storeEntity.getId(), platformOrderInfo.getPlatformOrderNo()); //检验订单幂等
        String orderCrypt = MD5Util.crypt(NsyJacksonUtils.toJson(platformOrderInfo));
        if (ObjectUtil.isNotNull(orderIdempotentEntity)) {
            LOGGER.info("店铺ID_订单号_幂等表加密_单据加密:{}", String.format("%s_%s_%s_%s", storeEntity.getId(), platformOrderInfo.getPlatformOrderNo(), orderIdempotentEntity.getContent(), orderCrypt));
            if (orderIdempotentEntity.getContent().equals(orderCrypt)) {
                return; //返回信息和上一次没有变化，无须处理
            }
        }
        Integer orderId;
        String platformOrderNo;
        OrderTypeEnum orderType;
        boolean flag = Objects.equals(PlatformTypeEnum.Amazon.getCode(), storeEntity.getPlatformId()) || Objects.equals(PlatformTypeEnum.TikTokLocalShipping.getCode(), storeEntity.getPlatformId()) || Objects.equals(PlatformTypeEnum.TikTok.getCode(), storeEntity.getPlatformId());
        if (flag && PlatformShippingTypeEnum.isPlatformOrder(platformOrderInfo.getShippingType())) {
            if (PlatformOrderStatusEnum.UNPAID.getCode().equalsIgnoreCase(platformOrderInfo.getOrderStatus())) {
                LOGGER.info("订单号:{},平台是未付款状态，不允许抓到系统中", platformOrderInfo.getPlatformOrderNo());
                return;
            }
            PlatformOrderEntity oldPlatformOrderEntity = platformOrderService.getOrderByStoreIdAndOrderNo(storeEntity.getId(), platformOrderInfo.getPlatformOrderNo());
            if (ObjectUtil.isNotNull(oldPlatformOrderEntity) && checkOrderStatusIsDelivery(oldPlatformOrderEntity.getPlatformOriginalOrderNo(), oldPlatformOrderEntity.getOrderStatus())) {
                return; //订单状态已发货，无须处理
            }
            PlatformOrderEntity platformOrderEntity = savePlatformOrder(platformOrderInfo, oldPlatformOrderEntity, storeEntity); //保存(平台仓发货)订单表头
            savePlatformOrderReceiver(platformOrderInfo, platformOrderEntity); //保存(平台仓发货)订单收件人信息
            savePlatformOrderItem(platformOrderInfo, platformOrderEntity); //保存(平台仓发货)订单明细信息
            orderId = platformOrderEntity.getPlatformOrderId();
            platformOrderNo = platformOrderEntity.getPlatformOriginalOrderNo();
            orderType = OrderTypeEnum.FBA;
        } else {
            SaleOrderEntity oldSaleOrderEntity = saleOrderService.getByPlatformOrderNo(storeEntity.getId(), platformOrderInfo.getPlatformOrderNo());
            if (ObjectUtil.isNotNull(oldSaleOrderEntity) && checkOrderStatusIsDelivery(oldSaleOrderEntity.getPlatformOrderNo(), oldSaleOrderEntity.getOrderStatus())) {
                return; //订单状态已发货，无须处理
            }
            SaleOrderEntity saleOrderEntity = saveOrder(platformOrderInfo, oldSaleOrderEntity, storeEntity); //保存(本地仓发货)订单表头
            saveOrderReceiver(platformOrderInfo, saleOrderEntity); //保存(本地仓发货)订单收件人信息
            saveOrderItem(platformOrderInfo, saleOrderEntity); //保存(本地仓发货)订单明细信息
            orderId = saleOrderEntity.getOrderId();
            platformOrderNo = saleOrderEntity.getPlatformOrderNo();
            orderType = OrderTypeEnum.FBM;
        }
        orderIdempotentService.saveOrUpdateOrderIdempotent(orderCrypt, storeEntity, platformOrderNo, "自动抓单"); //保存或更新 订单幂等
        orderGrabStatusService.saveOrUpdateOrderGrabStatus(storeEntity, orderId, platformOrderNo, orderType, "自动抓单", IsEnum.IS.getCode(), IsEnum.IS.getCode()); //保存或更新 订单状态表
    }

    /**
     * 计算订单最新抓单时间
     */
    protected Date calculateLatestOrderGrabDate(List<PlatformOrderInfo> list, Date endOrderTime, boolean hasNextPage) {
        if (hasNextPage) {
            return list.stream()
                    .map(PlatformOrderInfo::getUpdateDate)
                    .filter(updateDate -> Optional.ofNullable(updateDate).isPresent())
                    .max(Date::compareTo)
                    .orElse(endOrderTime);
        }
        return endOrderTime;
    }
}
