package com.nsy.oms.business.service.celebrity;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nsy.api.oms.dto.request.order.SampleOrderRequest;
import com.nsy.oms.business.domain.request.celebrity.InternetCelebrityBindOrderRequest;
import com.nsy.oms.business.domain.request.celebrity.InternetCelebrityOrderRemarkRequest;
import com.nsy.oms.business.domain.request.celebrity.InternetCelebritySampleOrderRequest;
import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.oms.business.domain.request.celebrity.InternetCelebrityOrderRepairRequest;
import com.nsy.oms.business.domain.request.celebrity.SyncChangeInternetCelebrityOwnerRequest;
import com.nsy.oms.business.domain.response.celebrity.InternetCelebritySampleOrder;
import com.nsy.oms.business.domain.response.celebrity.SyncInternetCelebritySampleOrder;
import com.nsy.oms.business.domain.response.order.ResponseTokenInfo;
import com.nsy.oms.business.domain.response.sa.SaStoreDetailByErpResponse;
import com.nsy.oms.business.manage.erp.request.GetSampleOrderRequest;
import com.nsy.oms.business.manage.thirdparty.auth.TiktokAuth;
import com.nsy.oms.business.manage.thirdparty.response.Tracking;
import com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderEntity;

import java.util.List;

/**
 * <p>
 * 网红样衣订单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-29
 */
public interface IInternetCelebritySampleOrderService extends IService<InternetCelebritySampleOrderEntity> {

    List<InternetCelebritySampleOrder> getInternetCelebritySampleOrderList(InternetCelebritySampleOrderRequest request);

    PageResponse<InternetCelebritySampleOrder> getInternetCelebritySampleOrderPage(InternetCelebritySampleOrderRequest request);

    PageResponse<InternetCelebritySampleOrder> getCelebrityPerformanceRatePage(InternetCelebritySampleOrderRequest request);

    List<Tracking> tracking(Integer internetCelebritySampleOrderItemId);

    void repair(InternetCelebrityOrderRepairRequest request);

    SaStoreDetailByErpResponse setRequestTokenInfo(TiktokAuth tiktokAuth, Integer deliveryStoreId);

    void saveRefreshToken(ResponseTokenInfo tiktokAuth, SaStoreDetailByErpResponse saStoreDetailByErpResponse);

    void syncInternetCelebritySampleOrder(List<SyncInternetCelebritySampleOrder> syncInternetCelebritySampleOrderList, Integer isInit);

    void syncInternetCelebritySampleOrderByImport(List<SyncInternetCelebritySampleOrder> syncInternetCelebritySampleOrderList);

    void setPackageInfo(SyncInternetCelebritySampleOrder syncInternetCelebritySampleOrder);

    List<InternetCelebritySampleOrderEntity> getListByPlatformOrderNos(List<String> platformOrderNos);

    InternetCelebritySampleOrderEntity getByPlatformOrderNo(String platformOrderNo);

    void getDataByErp(List<SyncInternetCelebritySampleOrder> syncInternetCelebritySampleOrders, GetSampleOrderRequest getSampleOrderRequest);

    void getDataByOms(List<SyncInternetCelebritySampleOrder> syncInternetCelebritySampleOrders, SampleOrderRequest sampleOrderRequest);

    void bind(InternetCelebrityBindOrderRequest internetCelebrityBindOrderRequest);

    void sync(List<SyncInternetCelebritySampleOrder> syncInternetCelebritySampleOrderList, Integer isInit);

    void syncUnknownOrder();

    void remark(InternetCelebrityOrderRemarkRequest request);

    void syncChangeInternetCelebrityOwner(SyncChangeInternetCelebrityOwnerRequest syncChangeInternetCelebrityOwnerRequest);

    List<InternetCelebritySampleOrderEntity> listByIdList(List<Integer> oderIds);

    SyncInternetCelebritySampleOrder erpOrder(String platformOrderNo, Integer storeRelationId);

    void saveSampleOrderDate(InternetCelebritySampleOrderEntity orderEntity);
}
