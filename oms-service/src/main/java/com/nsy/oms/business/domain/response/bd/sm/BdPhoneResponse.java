package com.nsy.oms.business.domain.response.bd.sm;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class BdPhoneResponse {
    /**
     *
     */
    @ApiModelProperty("id")
    private Integer phoneId;

    /**
     * 电话
     */
    @ApiModelProperty("电话号码")
    private String phone;

    /**
     * 运营商
     */
    @ApiModelProperty("运营商")
    private String operatorName;

    /**
     * 运营商
     */
    @ApiModelProperty("运营商")
    private String operator;
    /**
     * 状态: 0:停用 1:启用
     */
    @ApiModelProperty("状态: 0:停用 1:启用")
    private String statusName;

    /**
     * 卡管理者
     */
    @ApiModelProperty("卡管理者")
    private String cardManager;

    /**
     * 部门
     */
    @ApiModelProperty("部门")
    private String department;

    /**
     * 持卡人
     */
    @ApiModelProperty("持卡人")
    private String cardHolder;

    @ApiModelProperty("公司主体")
    private String company;

    @ApiModelProperty("主体区域")
    private String mainArea;

    /**
     * 社媒号
     */
    @ApiModelProperty("社媒号")
    private List<BdMediaBaseResponse> mediaAccount;
    /**
     * 直播号
     */
    @ApiModelProperty("直播号")
    private List<BdLiveBaseResponse> liveAccount;

    /**
     * 店铺账号
     */
    @ApiModelProperty("店铺账号")
    private List<BdStoreBaseResponse> storeAccount;

    /**
     * 广告账号
     */
    @ApiModelProperty("广告账号")
    private List<BdAdBaseResponse> adAccount;

    /**
     * 状态: 0:停用 1:启用
     */
    private Integer status;

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getMainArea() {
        return mainArea;
    }

    public void setMainArea(String mainArea) {
        this.mainArea = mainArea;
    }

    public Integer getPhoneId() {
        return phoneId;
    }

    public void setPhoneId(Integer phoneId) {
        this.phoneId = phoneId;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }


    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getCardManager() {
        return cardManager;
    }

    public void setCardManager(String cardManager) {
        this.cardManager = cardManager;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getCardHolder() {
        return cardHolder;
    }

    public void setCardHolder(String cardHolder) {
        this.cardHolder = cardHolder;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public List<BdMediaBaseResponse> getMediaAccount() {
        return mediaAccount;
    }

    public void setMediaAccount(List<BdMediaBaseResponse> mediaAccount) {
        this.mediaAccount = mediaAccount;
    }

    public List<BdLiveBaseResponse> getLiveAccount() {
        return liveAccount;
    }

    public void setLiveAccount(List<BdLiveBaseResponse> liveAccount) {
        this.liveAccount = liveAccount;
    }

    public List<BdStoreBaseResponse> getStoreAccount() {
        return storeAccount;
    }

    public void setStoreAccount(List<BdStoreBaseResponse> storeAccount) {
        this.storeAccount = storeAccount;
    }

    public List<BdAdBaseResponse> getAdAccount() {
        return adAccount;
    }

    public void setAdAccount(List<BdAdBaseResponse> adAccount) {
        this.adAccount = adAccount;
    }
}
