package com.nsy.oms.business.domain.request.celebrity;

import com.nsy.oms.business.domain.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * @Author: lhh
 * @Date: 2025-04-18
 * @Description: 网红查询请求类
 */
@ApiModel("达人列表查询req")
public class InternetCelebrityPageRequest extends PageRequest {
    @ApiModelProperty("ids")
    private List<Integer> ids;

    @ApiModelProperty("达人账号")
    private String internetCelebrityName;

    @ApiModelProperty("达人账号")
    private List<String> internetCelebrityNameList;

    @ApiModelProperty("达人Email")
    private String email;

    @ApiModelProperty("卖家备注")
    private String remark;

    @ApiModelProperty("客服邮箱")
    private List<String> bdEmailList;

    @ApiModelProperty("客服")
    private List<Integer> bdIdList;

    @ApiModelProperty("店铺")
    private List<Integer> relateStoreIdList;

    @ApiModelProperty("建联结果")
    private List<String> relationStatus;

    @ApiModelProperty("达人等级")
    private List<String> levelList;

    @ApiModelProperty("适配度")
    private List<String> shiyingFitnessList;

    @ApiModelProperty("14天视频数")
    private Integer videoNumIn14;

    @ApiModelProperty("达人画像")
    private String internetCelebrityInfo;

    @ApiModelProperty("Instagram账号")
    private String instagram;

    @ApiModelProperty("Facebook账号")
    private String facebook;

    @ApiModelProperty("WhatsApp账号")
    private String whatsapp;

    @ApiModelProperty("手机号码")
    private String phoneNumber;

    @ApiModelProperty("最小粉丝数")
    private Integer minFollowers;

    @ApiModelProperty("最大粉丝数")
    private Integer maxFollowers;

    @ApiModelProperty("GMV统计开始日期")
    private Date gmvStartDate;

    @ApiModelProperty("GMV统计结束日期")
    private Date gmvEndDate;

    @ApiModelProperty("创建开始日期")
    private Date createStartDate;

    @ApiModelProperty("创建结束日期")
    private Date createEndDate;

    @ApiModelProperty("未关联达人店铺")
    private List<Integer> unRelateStoreIdList;

    @ApiModelProperty("公海未归属")
    private Integer bdNotCare;

    @ApiModelProperty("bdId")
    private Integer bdId;

    public List<Integer> getBdIdList() {
        return bdIdList;
    }

    public void setBdIdList(List<Integer> bdIdList) {
        this.bdIdList = bdIdList;
    }

    @ApiModelProperty("cooperateTimes 合作次数")
    private Integer cooperateTimes;

    @ApiModelProperty("cooperateTimes 合作次数 条件")
    private String cooperateTimesCondition;

    public Integer getCooperateTimes() {
        return cooperateTimes;
    }

    public void setCooperateTimes(Integer cooperateTimes) {
        this.cooperateTimes = cooperateTimes;
    }

    public String getCooperateTimesCondition() {
        return cooperateTimesCondition;
    }

    public void setCooperateTimesCondition(String cooperateTimesCondition) {
        this.cooperateTimesCondition = cooperateTimesCondition;
    }

    public Integer getBdNotCare() {
        return bdNotCare;
    }

    public void setBdNotCare(Integer bdNotCare) {
        this.bdNotCare = bdNotCare;
    }

    public Integer getBdId() {
        return bdId;
    }

    public void setBdId(Integer bdId) {
        this.bdId = bdId;
    }

    public Integer getVideoNumIn14() {
        return videoNumIn14;
    }

    public void setVideoNumIn14(Integer videoNumIn14) {
        this.videoNumIn14 = videoNumIn14;
    }

    public List<Integer> getIds() {
        return ids;
    }

    public void setIds(List<Integer> ids) {
        this.ids = ids;
    }

    public String getInternetCelebrityName() {
        return internetCelebrityName;
    }

    public void setInternetCelebrityName(String internetCelebrityName) {
        this.internetCelebrityName = internetCelebrityName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<String> getBdEmailList() {
        return bdEmailList;
    }

    public void setBdEmailList(List<String> bdEmailList) {
        this.bdEmailList = bdEmailList;
    }

    public List<Integer> getRelateStoreIdList() {
        return relateStoreIdList;
    }

    public void setRelateStoreIdList(List<Integer> relateStoreIdList) {
        this.relateStoreIdList = relateStoreIdList;
    }

    public List<String> getRelationStatus() {
        return relationStatus;
    }

    public void setRelationStatus(List<String> relationStatus) {
        this.relationStatus = relationStatus;
    }

    public List<String> getLevelList() {
        return levelList;
    }

    public void setLevelList(List<String> levelList) {
        this.levelList = levelList;
    }

    public List<String> getShiyingFitnessList() {
        return shiyingFitnessList;
    }

    public void setShiyingFitnessList(List<String> shiyingFitnessList) {
        this.shiyingFitnessList = shiyingFitnessList;
    }

    public String getInternetCelebrityInfo() {
        return internetCelebrityInfo;
    }

    public void setInternetCelebrityInfo(String internetCelebrityInfo) {
        this.internetCelebrityInfo = internetCelebrityInfo;
    }

    public String getInstagram() {
        return instagram;
    }

    public void setInstagram(String instagram) {
        this.instagram = instagram;
    }

    public String getFacebook() {
        return facebook;
    }

    public void setFacebook(String facebook) {
        this.facebook = facebook;
    }

    public String getWhatsapp() {
        return whatsapp;
    }

    public void setWhatsapp(String whatsapp) {
        this.whatsapp = whatsapp;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public Integer getMinFollowers() {
        return minFollowers;
    }

    public void setMinFollowers(Integer minFollowers) {
        this.minFollowers = minFollowers;
    }

    public Integer getMaxFollowers() {
        return maxFollowers;
    }

    public void setMaxFollowers(Integer maxFollowers) {
        this.maxFollowers = maxFollowers;
    }

    public Date getGmvStartDate() {
        return gmvStartDate;
    }

    public void setGmvStartDate(Date gmvStartDate) {
        this.gmvStartDate = gmvStartDate;
    }

    public Date getGmvEndDate() {
        return gmvEndDate;
    }

    public void setGmvEndDate(Date gmvEndDate) {
        this.gmvEndDate = gmvEndDate;
    }

    public Date getCreateStartDate() {
        return createStartDate;
    }

    public void setCreateStartDate(Date createStartDate) {
        this.createStartDate = createStartDate;
    }

    public Date getCreateEndDate() {
        return createEndDate;
    }

    public void setCreateEndDate(Date createEndDate) {
        this.createEndDate = createEndDate;
    }

    public List<Integer> getUnRelateStoreIdList() {
        return unRelateStoreIdList;
    }

    public void setUnRelateStoreIdList(List<Integer> unRelateStoreIdList) {
        this.unRelateStoreIdList = unRelateStoreIdList;
    }

    public List<String> getInternetCelebrityNameList() {
        return internetCelebrityNameList;
    }

    public void setInternetCelebrityNameList(List<String> internetCelebrityNameList) {
        this.internetCelebrityNameList = internetCelebrityNameList;
    }
}
