package com.nsy.oms.business.service.download;

import com.nsy.api.oms.enums.QuartzDownloadQueueTypeEnum;
import com.nsy.oms.business.domain.request.download.DownloadRequest;
import com.nsy.oms.business.domain.response.base.DownloadResponse;

public interface IDownloadService {

    /**
     * 下载队列类型
     */
    QuartzDownloadQueueTypeEnum type();

    /**
     * 分页查询下载数据
     */
    DownloadResponse queryExportData(DownloadRequest request);
}
