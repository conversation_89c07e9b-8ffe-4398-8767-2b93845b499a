package com.nsy.oms.business.service.platform.impl;

import com.nsy.api.core.apicore.constant.ServiceNameConstants;
import com.nsy.business.base.constant.KafkaTopicConstant;
import com.nsy.business.base.enums.etl.BusinessTypeEnum;
import com.nsy.business.base.mq.EtlCommonMessage;
import com.nsy.oms.business.service.platform.PlatformOrderPushKingDeeService;
import com.nsy.oms.business.service.platform.PlatformOrderPushKingdeeQueueService;
import com.nsy.oms.constants.CacheKeyConstant;
import com.nsy.oms.constants.EtlCallbackConstant;
import com.nsy.oms.constants.KafkaBusinessMarkConstant;
import com.nsy.oms.enums.PushKingDeeTypeEnum;
import com.nsy.oms.mq.producer.MessageProducer;
import com.nsy.oms.repository.entity.platform.PlatformOrderEntity;
import com.nsy.oms.repository.entity.platform.PlatformOrderPushKingdeeQueueEntity;
import com.nsy.oms.utils.RedisClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class FbaPushOverseaStockoutOrderServiceImpl implements PlatformOrderPushKingDeeService {
    private static final String FBA_DATE = "2025-01-01";
    @Autowired
    private RedisClient redisClient;
    @Autowired
    private PlatformOrderPushKingdeeQueueService pushKingdeeQueueService;
    @Autowired
    private MessageProducer messageProducer;

    @Override
    public PushKingDeeTypeEnum pushKingDeeTypeEnum() {
        return PushKingDeeTypeEnum.FBA_OVERSEA_STOCK_OUT_ORDER;
    }

    @Override
    public String getCacheDate() {
        return (String) redisClient.get(CacheKeyConstant.FBA_PUSH_ORDER_TO_KING_DEE_DATE);
    }

    @Override
    public String getControlTime() {
        return FBA_DATE;
    }

    @Override
    public List<PlatformOrderPushKingdeeQueueEntity> listData(Date businessStarDate, Date businessEndDate, Integer fetchCount) {
        return pushKingdeeQueueService.getFbaPushOrder(businessStarDate, businessEndDate, fetchCount);
    }

    @Override
    public void setCacheDate(String cacheDate) {
        redisClient.set(CacheKeyConstant.FBA_PUSH_ORDER_TO_KING_DEE_DATE, cacheDate);
    }

    @Override
    @SuppressWarnings("unchecked")
    public void pushOrderToKingDee(PlatformOrderEntity orderEntity) {
        EtlCommonMessage message = new EtlCommonMessage();
        message.setBusinessNo(orderEntity.getPlatformOrderNo());
        message.setSource(ServiceNameConstants.API_OMS);
        message.setLocation(orderEntity.getLocation());
        message.setBusinessType(BusinessTypeEnum.OVERSEA_STOCK_OUT_ORDER.getCode());
        message.setCallbackInterfacePath(EtlCallbackConstant.PLATFORM_ORDER_CALLBACK_INTERFACE);
        messageProducer.sendMessage(KafkaBusinessMarkConstant.FBA_ORDER_PUSH, KafkaTopicConstant.OMS_OVERSEA_STOCK_OUT_ORDER_SHIPMENTS_TOPIC, message);
    }
}
