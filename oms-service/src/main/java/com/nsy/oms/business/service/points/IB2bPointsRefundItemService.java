package com.nsy.oms.business.service.points;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nsy.oms.business.manage.erp.response.inbound.TradePaymentInfo;
import com.nsy.oms.repository.entity.points.B2bPointsRefundItemEntity;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * B2B积分退款明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
public interface IB2bPointsRefundItemService extends IService<B2bPointsRefundItemEntity> {

    List<B2bPointsRefundItemEntity> getListByRefundIds(List<Integer> refundIds);

    void syncItems(Integer b2bPointsRefundId, List<TradePaymentInfo> tradePaymentInfoList);

    void affirmOrCancelRefundItem(Integer b2bPointsRefundItemId, String remark, String refundStatus, Integer storeId);

    void cancelPushMall(Integer storeId, Integer refundId, BigDecimal refundAmount);
}
