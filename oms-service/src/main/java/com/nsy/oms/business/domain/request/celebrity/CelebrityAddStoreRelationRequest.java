package com.nsy.oms.business.domain.request.celebrity;

import com.nsy.oms.business.domain.request.base.IdsRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Author: lhh
 * @Date: 2025-04-21
 * @Description:
 */
@ApiModel("添加关联店铺")
public class CelebrityAddStoreRelationRequest extends IdsRequest {
    @ApiModelProperty("店铺id")
    private Integer storeId;
    @ApiModelProperty("店铺名称")
    private String storeName;
    @ApiModelProperty("客服邮箱")
    private String bdEmail;

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getBdEmail() {
        return bdEmail;
    }

    public void setBdEmail(String bdEmail) {
        this.bdEmail = bdEmail;
    }
}
