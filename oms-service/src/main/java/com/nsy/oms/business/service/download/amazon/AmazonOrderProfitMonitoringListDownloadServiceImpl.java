package com.nsy.oms.business.service.download.amazon;

import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.base.BaseListResponse;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.response.CustomExcelResponse;
import com.nsy.api.core.apicore.util.NsyExcelUtil;
import com.nsy.api.oms.enums.QuartzDownloadQueueTypeEnum;
import com.nsy.oms.business.domain.download.amazon.AmazonOrderProfitMonitoringSkcExport;
import com.nsy.oms.business.domain.request.amazon.AmazonOrderProfitMonitoringPageRequest;
import com.nsy.oms.business.domain.request.download.DownloadRequest;
import com.nsy.oms.business.domain.response.amazon.AmazonOrderProfitMonitoringSkcResponse;
import com.nsy.oms.business.domain.response.amazon.AmazonOrderProfitMonitoringSpuResponse;
import com.nsy.oms.business.domain.response.base.DownloadResponse;
import com.nsy.oms.business.manage.erp.ErpApiService;
import com.nsy.oms.business.manage.erp.domain.UserStore;
import com.nsy.oms.business.manage.user.UserApiService;
import com.nsy.oms.business.manage.user.response.BdDictionaryItem;
import com.nsy.oms.business.manage.user.response.SysUserInfo;
import com.nsy.oms.business.service.amazon.AmazonOrderProfitMonitoringSearchService;
import com.nsy.oms.business.service.download.IDownloadService;
import com.nsy.oms.constants.StringConstant;
import com.nsy.oms.enums.bd.DictionaryKeySelectEnum;
import com.nsy.oms.utils.BigDecimalUtil;
import com.nsy.oms.utils.JsonMapper;
import com.nsy.oms.utils.mp.LocationContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
@Slf4j
public class AmazonOrderProfitMonitoringListDownloadServiceImpl implements IDownloadService {
    @Autowired
    private AmazonOrderProfitMonitoringSearchService amazonOrderProfitMonitoringSearchService;
    @Autowired
    private ErpApiService erpApiService;
    @Autowired
    private UserApiService userApiService;

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.OMS_AMAZON_ORDER_PROFIT_MONITORING_LIST;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        AmazonOrderProfitMonitoringPageRequest downloadRequest = JsonMapper.fromJson(request.getRequestContent(), AmazonOrderProfitMonitoringPageRequest.class);
        downloadRequest.setPageSize(request.getPageSize());
        downloadRequest.setPageIndex(request.getPageIndex());
        SysUserInfo sysUserInfo = userApiService.getUserInfoByUserAccount(request.getRequestedBy());
        if (Objects.isNull(sysUserInfo)) {
            throw new BusinessServiceException("该用户不存在,请确认后重试");
        }
        LocationContext.setLocation(sysUserInfo.getLocation());
        if (!Objects.equals(1, sysUserInfo.getIsSupperAccount()) && !isConfigAdmin(sysUserInfo.getUserAccount())) {
            UserStore userStore = erpApiService.getErpUserStoreResponse(sysUserInfo.getUserAccount());
            downloadRequest.setPermissionStoreIds(Optional.ofNullable(userStore.getStoreIdList()).filter(CollectionUtils::isNotEmpty).orElse(Lists.newArrayList(0)));
        }
        BaseListResponse<AmazonOrderProfitMonitoringSpuResponse> baseListResponse = amazonOrderProfitMonitoringSearchService.page(downloadRequest);

        CustomExcelResponse customExcelResponse = new CustomExcelResponse();
        customExcelResponse.setHeaders(NsyExcelUtil.getCommonHeads(AmazonOrderProfitMonitoringSkcExport.class));
        List<List<Object>> excelData = new ArrayList<>();
        getExcelList(baseListResponse.getContent()).forEach(export -> excelData.add(NsyExcelUtil.getData(AmazonOrderProfitMonitoringSkcExport.class, export)));
        customExcelResponse.setData(excelData);
        DownloadResponse downloadResponse = new DownloadResponse();
        downloadResponse.setTotalCount(baseListResponse.getTotalCount());
        downloadResponse.setDataJsonStr(JsonMapper.toJson(customExcelResponse));
        return downloadResponse;
    }

    private boolean isConfigAdmin(String userAccount) {
        List<BdDictionaryItem> fbaReplenishAdmins = userApiService.getDictionaryValues(DictionaryKeySelectEnum.FBA_REPLENISH_ADMIN.getKey());
        return CollectionUtils.isNotEmpty(fbaReplenishAdmins) && fbaReplenishAdmins.stream().anyMatch(configAdmin -> userAccount.equals(configAdmin.getValue()));
    }

    public List<AmazonOrderProfitMonitoringSkcExport> getExcelList(List<AmazonOrderProfitMonitoringSpuResponse> list) {
        List<AmazonOrderProfitMonitoringSkcExport> excelData = new ArrayList<>();
        for (AmazonOrderProfitMonitoringSpuResponse response : list) {
            if (CollectionUtils.isNotEmpty(response.getSkcList())) {
                for (AmazonOrderProfitMonitoringSkcResponse skc : response.getSkcList()) {
                    AmazonOrderProfitMonitoringSkcExport export = new AmazonOrderProfitMonitoringSkcExport();
                    export.setSpu(response.getSpu());
                    export.setImageUrl(response.getImageUrl());
                    export.setCategoryName(response.getCategoryName());
                    export.setParentAsin(response.getParentAsin());
                    export.setDepartment(response.getDepartment());
                    export.setStoreName(response.getStoreName());
                    export.setSeason(response.getSeason());
                    export.setDevelopSeason(response.getDevelopSeason());
                    export.setSkc(skc.getSkc());
                    export.setSaleQty(String.format("%s|%s|%s", skc.getLast7DateSaleQty(), skc.getLast14DateSaleQty(), skc.getLast30DateSaleQty()));
                    export.setInv(String.format("%s(%s|%s|%s)", skc.getTotalInv(), skc.getQuanzhouInv(), skc.getOverseasInv(), skc.getPurchaseInTransitInv()));
                    export.setProfitRate(String.format("%.2f|%.2f|%.2f", skc.getProfitRateIn7Days(), skc.getProfitRateIn14Days(), skc.getProfitRateIn30Days()));
                    export.setAdCostRate(skc.getAdCostRate().toString());
                    export.setReturnRate(String.format("%.2f", skc.getReturnRate()));
                    export.setPrice(String.format("%.2f(%.2f)", skc.getSalePrice(), skc.getYourPrice()));
                    export.setOrderPriceIn24Hours(String.format("%.2f~%.2f", skc.getMinOrderPriceIn24Hours(), skc.getMaxOrderPriceIn24Hours()));
                    export.setProfitRateIn24Hours(String.format("%.2f~%.2f", skc.getMinProfitRateIn24Hours(), skc.getMaxProfitRateIn24Hours()));
                    export.setCurrentLevel(String.format("%.2f%%|$%.2f", skc.getCurrentLevelCommissionRate(), skc.getCurrentLevelBalancePrice()));
                    export.setLevelFourProfitRate(buildProfitRate(skc.getLevelFourProfitRate(), skc.getLevelFourDownshiftIncreaseProfit()));
                    export.setLevelThreeProfitRate(buildProfitRate(skc.getLevelThreeProfitRate(), skc.getLevelThreeDownshiftIncreaseProfit()));
                    export.setLevelTwoProfitRate(buildProfitRate(skc.getLevelTwoProfitRate(), skc.getLevelTwoDownshiftIncreaseProfit()));
                    export.setLevelOneProfitRate(buildProfitRate(skc.getLevelOneProfitRate(), skc.getLevelOneDownshiftIncreaseProfit()));
                    excelData.add(export);
                }
            }
        }
        return excelData;
    }

    private String buildProfitRate(BigDecimal profitRate, BigDecimal downshiftIncreaseProfit) {
        if (!BigDecimalUtil.isValid(profitRate) && !BigDecimalUtil.isValid(downshiftIncreaseProfit)) {
            return StringConstant.EMPTY;
        }
        return String.format("%.2f%%|$%.2f", profitRate, downshiftIncreaseProfit);
    }
}
