package com.nsy.oms.business.manage.thirdparty.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

/**
 * User: lvjianda
 * Date: 2020/1/15
 */
public class OrderItem {
    @JsonProperty("OrderItemId")
    private String orderItemId;
    @JsonProperty("Title")
    private String title;
    /// SKU
    @JsonProperty("Sku")
    private String sku;
    /// 平台商品Id
    @JsonProperty("ProductId")
    private String productId; // 这个我们返回不了
    @JsonProperty("Qty")
    private int qty;
    /// 成交价,指的没有
    @JsonProperty("Price")
    private BigDecimal price; // 这个是对应船长的ItemPrice_Amount ？？
    @JsonProperty("SalePrice")
    private BigDecimal salePrice; // 和上面的成交价怎么区分
    /// 总优惠
    @JsonProperty("TotalDiscount")
    private BigDecimal totalDiscount; //优惠金额，是该商品单件优惠*数量。如果有平台折扣，需要按金额比例折算进来
    @JsonProperty("DiscountMoney")
    private BigDecimal discountMoney; //订单优惠 （满减，打折，抵用券等）
    @JsonProperty("ASIN")
    private String asin;
    @JsonProperty("Status")
    private String status;
    @JsonProperty("OuterIid")
    private String outerIid;
    @JsonProperty("outerSkuId")
    private String outerSkuId;
    @JsonProperty("OuterProductId")
    private String outerProductId;
    @JsonProperty("RefundId")
    private String refundId;
    @JsonProperty("RefundStatus")
    private String refundStatus;
    @JsonProperty("ShippingPriceAmount")
    private BigDecimal shippingPriceAmount;
    @JsonProperty("ShippingDiscountAmount")
    private BigDecimal shippingDiscountAmount;
    @JsonIgnore
    private BigDecimal promotionDiscountAmount;
    @JsonProperty("IossNumber")
    private String iossNumber;
    @JsonProperty("CustomerProductionUrl")
    private String customerProductionUrl; //shopify轻定制图片素材
    @JsonProperty("CustomerPreviewUrl")
    private String customerPreviewUrl;
    @JsonProperty("CustomerSkc")
    private String customerSkc;

    @JsonProperty("CustomerSku")
    private String customerSku;
    @JsonProperty("SkuCode")
    private String skuCode;
    @JsonProperty("Size")
    private String size;
    @JsonProperty("Transparency")
    private Boolean transparency;

    public Boolean getTransparency() {
        return transparency;
    }

    public void setTransparency(Boolean transparency) {
        this.transparency = transparency;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getSkuCode() {
        return skuCode;
    }

    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    public String getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(String orderItemId) {
        this.orderItemId = orderItemId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public int getQty() {
        return qty;
    }

    public void setQty(int qty) {
        this.qty = qty;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getOuterIid() {
        return outerIid;
    }

    public void setOuterIid(String outerIid) {
        this.outerIid = outerIid;
    }

    public String getOuterSkuId() {
        return outerSkuId;
    }

    public void setOuterSkuId(String outerSkuId) {
        this.outerSkuId = outerSkuId;
    }

    public String getOuterProductId() {
        return outerProductId;
    }

    public void setOuterProductId(String outerProductId) {
        this.outerProductId = outerProductId;
    }

    public BigDecimal getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(BigDecimal salePrice) {
        this.salePrice = salePrice;
    }

    public BigDecimal getTotalDiscount() {
        return totalDiscount;
    }

    public void setTotalDiscount(BigDecimal totalDiscount) {
        this.totalDiscount = totalDiscount;
    }

    public String getAsin() {
        return asin;
    }

    public void setAsin(String asin) {
        this.asin = asin;
    }

    public BigDecimal getShippingDiscountAmount() {
        return shippingDiscountAmount;
    }

    public void setShippingDiscountAmount(BigDecimal shippingDiscountAmount) {
        this.shippingDiscountAmount = shippingDiscountAmount;
    }

    public BigDecimal getShippingPriceAmount() {
        return shippingPriceAmount;
    }

    public void setShippingPriceAmount(BigDecimal shippingPriceAmount) {
        this.shippingPriceAmount = shippingPriceAmount;
    }

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public String getRefundStatus() {
        return refundStatus;
    }

    public void setRefundStatus(String refundStatus) {
        this.refundStatus = refundStatus;
    }

    public BigDecimal getPromotionDiscountAmount() {
        return promotionDiscountAmount;
    }

    public void setPromotionDiscountAmount(BigDecimal promotionDiscountAmount) {
        this.promotionDiscountAmount = promotionDiscountAmount;
    }

    public String getIossNumber() {
        return iossNumber;
    }

    public void setIossNumber(String iossNumber) {
        this.iossNumber = iossNumber;
    }

    public BigDecimal getDiscountMoney() {
        return discountMoney;
    }

    public void setDiscountMoney(BigDecimal discountMoney) {
        this.discountMoney = discountMoney;
    }

    public String getCustomerProductionUrl() {
        return customerProductionUrl;
    }

    public void setCustomerProductionUrl(String customerProductionUrl) {
        this.customerProductionUrl = customerProductionUrl;
    }

    public String getCustomerPreviewUrl() {
        return customerPreviewUrl;
    }

    public void setCustomerPreviewUrl(String customerPreviewUrl) {
        this.customerPreviewUrl = customerPreviewUrl;
    }

    public String getCustomerSkc() {
        return customerSkc;
    }

    public void setCustomerSkc(String customerSkc) {
        this.customerSkc = customerSkc;
    }

    public String getCustomerSku() {
        return customerSku;
    }

    public void setCustomerSku(String customerSku) {
        this.customerSku = customerSku;
    }
}
