package com.nsy.oms.business.domain.response.inbound;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PlanUploadResponse {
    @ApiModelProperty("补货计划单汇总数据")
    InboundShipmentPlanSummary planSummary;

    @ApiModelProperty("原单列表")
    private List<FbaInboundShipmentPlanItem> fbaInboundShipmentPlanItemList;

    @ApiModelProperty(name = "message", value = "信息")
    private String message;

}