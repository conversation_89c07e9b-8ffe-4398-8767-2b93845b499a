package com.nsy.oms.business.domain.request.sa;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/1 17:46
 */
public class SaAccountInfoRequest {
    /**
     * 商品通知配置人员
     */
    @ApiModelProperty("商品通知配置人员")
    private List<StoreStaffingRequest> staffings;
    @ApiModelProperty("erp店铺名称")
    @NotNull(message = "erp店铺名称不能为空")
    private String erpStoreName;
    @ApiModelProperty("市场数据")
    private List<StoreMarketRequest> marketLists;
    @ApiModelProperty("业绩归属")
    private String achievementAttribution;

    public List<StoreStaffingRequest> getStaffings() {
        return staffings;
    }

    public void setStaffings(List<StoreStaffingRequest> staffings) {
        this.staffings = staffings;
    }

    public String getErpStoreName() {
        return erpStoreName;
    }

    public void setErpStoreName(String erpStoreName) {
        this.erpStoreName = erpStoreName;
    }

    public List<StoreMarketRequest> getMarketLists() {
        return marketLists;
    }

    public void setMarketLists(List<StoreMarketRequest> marketLists) {
        this.marketLists = marketLists;
    }

    public String getAchievementAttribution() {
        return achievementAttribution;
    }

    public void setAchievementAttribution(String achievementAttribution) {
        this.achievementAttribution = achievementAttribution;
    }
}
