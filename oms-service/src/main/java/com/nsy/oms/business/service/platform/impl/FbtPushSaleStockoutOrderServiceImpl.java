package com.nsy.oms.business.service.platform.impl;

import com.nsy.api.core.apicore.constant.ServiceNameConstants;
import com.nsy.business.base.constant.KafkaTopicConstant;
import com.nsy.business.base.enums.etl.BusinessTypeEnum;
import com.nsy.business.base.mq.EtlCommonMessage;
import com.nsy.oms.business.service.platform.PlatformOrderItemService;
import com.nsy.oms.business.service.platform.PlatformOrderPushKingDeeService;
import com.nsy.oms.business.service.platform.PlatformOrderPushKingdeeQueueService;
import com.nsy.oms.constants.CacheKeyConstant;
import com.nsy.oms.constants.EtlCallbackConstant;
import com.nsy.oms.constants.KafkaBusinessMarkConstant;
import com.nsy.oms.enums.PushKingDeeTypeEnum;
import com.nsy.oms.mq.producer.MessageProducer;
import com.nsy.oms.repository.entity.platform.PlatformOrderEntity;
import com.nsy.oms.repository.entity.platform.PlatformOrderItemEntity;
import com.nsy.oms.repository.entity.platform.PlatformOrderPushKingdeeQueueEntity;
import com.nsy.oms.utils.RedisClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
public class FbtPushSaleStockoutOrderServiceImpl implements PlatformOrderPushKingDeeService {
    private static final String FBT_DATE = "2025-01-01";
    @Autowired
    private RedisClient redisClient;
    @Autowired
    private PlatformOrderItemService platformOrderItemService;
    @Autowired
    private MessageProducer messageProducer;
    @Autowired
    private PlatformOrderPushKingdeeQueueService pushKingdeeQueueService;

    @Override
    public PushKingDeeTypeEnum pushKingDeeTypeEnum() {
        return PushKingDeeTypeEnum.FBT_SALE_STOCK_OUT_ORDER;
    }


    @Override
    public String getCacheDate() {
        return (String) redisClient.get(CacheKeyConstant.FBT_PUSH_ORDER_TO_KING_DEE_DATE);
    }

    @Override
    public String getControlTime() {
        return FBT_DATE;
    }

    @Override
    public List<PlatformOrderPushKingdeeQueueEntity> listData(Date businessStarDate, Date businessEndDate, Integer fetchCount) {
        return pushKingdeeQueueService.getFbtPushOrder(businessStarDate, businessEndDate, fetchCount);
    }

    @Override
    public void setCacheDate(String cacheDate) {
        redisClient.set(CacheKeyConstant.FBT_PUSH_ORDER_TO_KING_DEE_DATE, cacheDate);
    }

    @Override
    @SuppressWarnings("unchecked")
    public void pushOrderToKingDee(PlatformOrderEntity orderEntity) {
        List<PlatformOrderItemEntity> itemEntityList = platformOrderItemService.listByPlatformOrderId(orderEntity.getPlatformOrderId());
        // 有一个等于零
        boolean anyMatchZero = itemEntityList.stream().anyMatch(itemEntity -> Objects.nonNull(itemEntity.getUnitPrice()) && BigDecimal.ZERO.compareTo(itemEntity.getUnitPrice()) == 0);
        if (anyMatchZero) {
            EtlCommonMessage message = new EtlCommonMessage();
            message.setBusinessNo(orderEntity.getPlatformOrderNo());
            message.setSource(ServiceNameConstants.API_OMS);
            message.setLocation(orderEntity.getLocation());
            message.setBusinessType(BusinessTypeEnum.OMS_STOCK_OTHER_SALE_OUT_ORDER.getCode());
            message.setCallbackInterfacePath(EtlCallbackConstant.OTHER_STOCK_OUT_ORDER_CALLBACK_INTERFACE);
            messageProducer.sendMessage(KafkaBusinessMarkConstant.OTHER_STOCK_OUT_ORDER_PUSH, KafkaTopicConstant.OMS_STOCK_OTHER_SALE_OUT_ORDER_TOPIC, message);
        }
        // 有一个大于零
        boolean anyMatchGtZero = itemEntityList.stream().anyMatch(itemEntity -> Objects.nonNull(itemEntity.getUnitPrice()) && BigDecimal.ZERO.compareTo(itemEntity.getUnitPrice()) < 0);
        if (anyMatchGtZero) {
            EtlCommonMessage message = new EtlCommonMessage();
            message.setBusinessNo(orderEntity.getPlatformOrderNo());
            message.setSource(ServiceNameConstants.API_OMS);
            message.setLocation(orderEntity.getLocation());
            message.setBusinessType(BusinessTypeEnum.OMS_SALE_STOCK_OUT_ORDER.getCode());
            message.setCallbackInterfacePath(EtlCallbackConstant.PLATFORM_ORDER_CALLBACK_INTERFACE);
            messageProducer.sendMessage(KafkaBusinessMarkConstant.FBT_ORDER_PUSH, KafkaTopicConstant.OMS_SALE_STOCK_OUT_ORDER_TOPIC, message);
        }
    }
}
