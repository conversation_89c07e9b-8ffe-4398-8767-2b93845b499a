package com.nsy.oms.business.manage.wms.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 查询仓库在途明细请求类
 */
@ApiModel(value = "StockTransferTrackingDetailRequest", description = "查询仓库在途明细请求")
public class StockTransferTrackingDetailRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "SKU列表", required = true)
    @NotEmpty(message = "SKU列表不能为空")
    private List<String> skuList;

    public List<String> getSkuList() {
        return skuList;
    }

    public void setSkuList(List<String> skuList) {
        this.skuList = skuList;
    }
}
