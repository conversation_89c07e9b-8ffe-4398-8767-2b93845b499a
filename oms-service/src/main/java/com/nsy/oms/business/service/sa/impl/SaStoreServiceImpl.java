package com.nsy.oms.business.service.sa.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.base.BaseListResponse;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.privilege.LoginInfoService;
import com.nsy.api.core.apicore.util.NsyJacksonUtils;
import com.nsy.api.core.apicore.util.NsyListUtil;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.oms.dto.request.bd.CreateWebsiteRequest;
import com.nsy.api.oms.dto.request.bd.WebSiteInfo;
import com.nsy.api.oms.dto.response.bd.CreateWebsiteResponse;
import com.nsy.api.oms.dto.response.linxing.LxMappingResponse;
import com.nsy.api.oms.dto.response.store.StoreStaffingResponse;
import com.nsy.api.oms.feign.EtlFeignClient;
import com.nsy.api.oms.feign.WebsiteConfigFeignClient;
import com.nsy.api.transfer.enums.PlatformTypeEnum;
import com.nsy.business.base.dto.request.lingxing.LxMappingQueryRequest;
import com.nsy.business.base.enums.LocationEnum;
import com.nsy.oms.business.domain.dto.StoreDTO;
import com.nsy.oms.business.domain.dto.StoreExceptionDTO;
import com.nsy.oms.business.domain.request.activiti.BatchStartProcessRequest;
import com.nsy.oms.business.domain.request.activiti.BusinessInfo;
import com.nsy.oms.business.domain.request.sa.B2BStoreSearchRequest;
import com.nsy.oms.business.domain.request.sa.DistributorsStorePageRequest;
import com.nsy.oms.business.domain.request.sa.DistributorsStoreRequest;
import com.nsy.oms.business.domain.request.sa.EtlStoreSearchRequest;
import com.nsy.oms.business.domain.request.sa.SaAccountInfoRequest;
import com.nsy.oms.business.domain.request.sa.SaMediaAccountRequest;
import com.nsy.oms.business.domain.request.sa.SaStorePageRequest;
import com.nsy.oms.business.domain.request.sa.SaStoreRequest;
import com.nsy.oms.business.domain.request.sa.SaStoreSearchRequest;
import com.nsy.oms.business.domain.request.sa.SalesmanStoreRequest;
import com.nsy.oms.business.domain.request.sa.StoreConfigRequest;
import com.nsy.oms.business.domain.request.sa.StoreInfoRequest;
import com.nsy.oms.business.domain.request.sa.StoreMarketRequest;
import com.nsy.oms.business.domain.request.sa.StorePermissionSelectRequest;
import com.nsy.oms.business.domain.request.sa.StoreSelectRequest;
import com.nsy.oms.business.domain.response.activit.TaskResponse;
import com.nsy.oms.business.domain.response.base.PageResponse;
import com.nsy.oms.business.domain.response.sa.DistributorsStoreResponse;
import com.nsy.oms.business.domain.response.sa.SaSaleAccountResponse;
import com.nsy.oms.business.domain.response.sa.SaStoreDetailByErpResponse;
import com.nsy.oms.business.domain.response.sa.SaStoreDetailResponse;
import com.nsy.oms.business.domain.response.sa.SaStoreListResponse;
import com.nsy.oms.business.domain.response.sa.SaStoreResponse;
import com.nsy.oms.business.domain.response.sa.SaStoreWebsiteResponse;
import com.nsy.oms.business.domain.response.sa.SalesmanStoreResponse;
import com.nsy.oms.business.domain.response.sa.StoreConfigResponse;
import com.nsy.oms.business.domain.response.sa.StoreFinanceAccountResponse;
import com.nsy.oms.business.domain.response.sa.StoreFinancePaymentAccountResponse;
import com.nsy.oms.business.domain.response.sa.StorePrincipalResponse;
import com.nsy.oms.business.domain.response.sa.StoreResponse;
import com.nsy.oms.business.external.ErpExternalService;
import com.nsy.oms.business.factory.ErpPlatformServiceFactory;
import com.nsy.oms.business.manage.activity.ActivitiApiService;
import com.nsy.oms.business.manage.amazon.AmazonApiService;
import com.nsy.oms.business.manage.amazon.response.SkuPrefixDto;
import com.nsy.oms.business.manage.erp.ErpApiService;
import com.nsy.oms.business.manage.erp.domain.UserStore;
import com.nsy.oms.business.manage.erp.response.StoreData;
import com.nsy.oms.business.manage.notify.NotifyApiService;
import com.nsy.oms.business.manage.user.UserApiService;
import com.nsy.oms.business.manage.user.UserDictionaryApiService;
import com.nsy.oms.business.manage.user.request.shared.SelectModel;
import com.nsy.oms.business.manage.user.response.SysUserInfo;
import com.nsy.oms.business.manage.user.response.SysUserPageResponse;
import com.nsy.oms.business.service.auth.SauAliInterConfigService;
import com.nsy.oms.business.service.auth.SauAmazonConfigService;
import com.nsy.oms.business.service.auth.SauPddConfigService;
import com.nsy.oms.business.service.auth.SauPlatformAuthConfigService;
import com.nsy.oms.business.service.auth.SauShopifyConfigService;
import com.nsy.oms.business.service.auth.SauTaobaoConfigService;
import com.nsy.oms.business.service.auth.SauWholesaleConfigService;
import com.nsy.oms.business.service.auth.SauWmtConfigService;
import com.nsy.oms.business.service.base.BaseService;
import com.nsy.oms.business.service.bd.BdMarketplaceService;
import com.nsy.oms.business.service.bd.BdOperateLogService;
import com.nsy.oms.business.service.bd.BdPlatformService;
import com.nsy.oms.business.service.bd.BdStoreCreateTemplateConfigService;
import com.nsy.oms.business.service.bd.BdWebsiteCreateRuleService;
import com.nsy.oms.business.service.privilege.AccessControlService;
import com.nsy.oms.business.service.replenishment.FbaReplenishmentSkcService;
import com.nsy.oms.business.service.sa.SaAccountApplyService;
import com.nsy.oms.business.service.sa.SaAdAccountService;
import com.nsy.oms.business.service.sa.SaLiveAccountService;
import com.nsy.oms.business.service.sa.SaMediaAccountService;
import com.nsy.oms.business.service.sa.SaSaleAccountMarketMappingService;
import com.nsy.oms.business.service.sa.SaSaleAccountService;
import com.nsy.oms.business.service.sa.SaStoreAdAccountMappingService;
import com.nsy.oms.business.service.sa.SaStoreConfigService;
import com.nsy.oms.business.service.sa.SaStoreFinanceAccountService;
import com.nsy.oms.business.service.sa.SaStoreFinancePaymentAccountService;
import com.nsy.oms.business.service.sa.SaStoreGoodOperatorService;
import com.nsy.oms.business.service.sa.SaStoreMarketSetService;
import com.nsy.oms.business.service.sa.SaStorePrincipalService;
import com.nsy.oms.business.service.sa.SaStoreSalesmanService;
import com.nsy.oms.business.service.sa.SaStoreService;
import com.nsy.oms.business.service.sa.SaStoreStaffingService;
import com.nsy.oms.business.service.sa.SaStoreWebsiteService;
import com.nsy.oms.constants.NumberConstant;
import com.nsy.oms.constants.PatternConstant;
import com.nsy.oms.constants.PlatformConstant;
import com.nsy.oms.constants.StringConstant;
import com.nsy.oms.enums.BdDictionaryItem;
import com.nsy.oms.enums.StatusEnum;
import com.nsy.oms.enums.activiti.ActivitiProcessDefinitionKeyEnum;
import com.nsy.oms.enums.bd.BusinessTypeEnum;
import com.nsy.oms.enums.bd.DictionaryKeySelectEnum;
import com.nsy.oms.enums.bd.MediaAccountNoticeUserEnum;
import com.nsy.oms.enums.bd.StoreCardNoticeUserEnum;
import com.nsy.oms.enums.order.OrderGrabPlatformEnum;
import com.nsy.oms.enums.sa.AccountApplyStatusEnum;
import com.nsy.oms.enums.sa.AccountTypeEnum;
import com.nsy.oms.enums.sa.ApplyTypeEnum;
import com.nsy.oms.enums.sa.FincalAccountStatusEnum;
import com.nsy.oms.enums.sa.OperateMethodEnum;
import com.nsy.oms.enums.sa.PaymentCardStatusEnum;
import com.nsy.oms.enums.sa.PlatformEnum;
import com.nsy.oms.enums.sa.SaStoreDistributionStoreFlagEnum;
import com.nsy.oms.enums.sa.StoreAuthStatusEnum;
import com.nsy.oms.enums.sa.StoreGrantStatusEnum;
import com.nsy.oms.enums.sa.StoreSelectModeEnum;
import com.nsy.oms.enums.sa.StoreSkuPrefixEnum;
import com.nsy.oms.enums.sa.StoreStatusEnum;
import com.nsy.oms.enums.sa.StoreTypeEnum;
import com.nsy.oms.repository.dao.auth.SauPddConfigDao;
import com.nsy.oms.repository.dao.sa.SaStoreDao;
import com.nsy.oms.repository.dao.sa.SaStoreFinancePaymentAccountDao;
import com.nsy.oms.repository.dao.sa.SaStoreWebsiteDao;
import com.nsy.oms.repository.entity.auth.SauPddConfigEntity;
import com.nsy.oms.repository.entity.base.BaseMpEntity;
import com.nsy.oms.repository.entity.bd.BdMarketplaceEntity;
import com.nsy.oms.repository.entity.bd.BdPlatformEntity;
import com.nsy.oms.repository.entity.bd.BdStoreCreateTemplateConfigEntity;
import com.nsy.oms.repository.entity.bd.BdWebsiteCreateRuleEntity;
import com.nsy.oms.repository.entity.sa.SaAccountApplyEntity;
import com.nsy.oms.repository.entity.sa.SaAdAccountEntity;
import com.nsy.oms.repository.entity.sa.SaLiveAccountEntity;
import com.nsy.oms.repository.entity.sa.SaMediaAccountEntity;
import com.nsy.oms.repository.entity.sa.SaSaleAccountEntity;
import com.nsy.oms.repository.entity.sa.SaSaleAccountMarketMappingEntity;
import com.nsy.oms.repository.entity.sa.SaStoreConfigEntity;
import com.nsy.oms.repository.entity.sa.SaStoreEntity;
import com.nsy.oms.repository.entity.sa.SaStoreFinanceAccountEntity;
import com.nsy.oms.repository.entity.sa.SaStoreFinancePaymentAccountEntity;
import com.nsy.oms.repository.entity.sa.SaStoreGoodOperatorEntity;
import com.nsy.oms.repository.entity.sa.SaStoreStaffingEntity;
import com.nsy.oms.repository.entity.sa.SaStoreWebsiteEntity;
import com.nsy.oms.utils.JsonMapper;
import com.nsy.oms.utils.ListUtil;
import com.nsy.oms.utils.mp.LocationContext;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2022/11/29 18:18
 * sa 是 店铺账号缩写(store_account)
 */
@Service
@Slf4j
public class SaStoreServiceImpl implements SaStoreService {
    private static final Logger LOGGER = LoggerFactory.getLogger(SaStoreServiceImpl.class);
    @Autowired
    private SaStoreDao saStoreDao;
    @Autowired
    private SaStoreConfigService saStoreConfigService;
    @Autowired
    private SaStoreFinanceAccountService saStoreFinanceAccountService;
    @Autowired
    private SaStoreFinancePaymentAccountService saStoreFinancePaymentAccountService;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private BdOperateLogService bdOperateLogService;
    @Autowired
    private BaseService baseService;
    @Autowired
    private SaStoreMarketSetService saStoreMarketSetService;
    @Autowired
    private SaStoreStaffingService saStoreStaffingService;
    @Autowired
    private SaStoreSalesmanService saStoreSalesmanService;
    @Autowired
    private SaStorePrincipalService saStorePrincipalService;
    @Autowired
    private UserApiService userApiService;
    @Autowired
    private ErpExternalService erpExternalService;
    @Autowired
    private SaStoreWebsiteService saStoreWebsiteService;
    @Autowired
    private SauAliInterConfigService sauAliInterConfigService;
    @Autowired
    private SauAmazonConfigService sauAmazonConfigService;
    @Autowired
    private SauWmtConfigService sauWmtConfigService;
    @Autowired
    private SauWholesaleConfigService sauWholesaleConfigService;
    @Autowired
    private SauPddConfigService sauPddConfigService;
    @Autowired
    private SauShopifyConfigService sauShopifyConfigService;
    @Autowired
    private SauTaobaoConfigService sauTaobaoConfigService;
    @Autowired
    private ErpPlatformServiceFactory erpPlatformServiceFactory;
    @Autowired
    private SauPlatformAuthConfigService sauPlatformAuthConfigService;
    @Autowired
    private SauPddConfigDao pddConfigDao;
    @Autowired
    private SaStoreWebsiteDao saStoreWebsiteDao;
    @Autowired
    private UserDictionaryApiService userDictionaryApiService;
    @Autowired
    private EtlFeignClient etlFeignClient;
    @Autowired
    private ErpApiService erpApiService;
    @Autowired
    private AmazonApiService amazonApiService;
    @Autowired
    private BdPlatformService bdPlatformService;
    @Autowired
    private BdWebsiteCreateRuleService bdWebsiteCreateRuleService;
    @Autowired
    private WebsiteConfigFeignClient websiteConfigFeignClient;
    @Autowired
    private SaSaleAccountService saSaleAccountService;
    @Autowired
    private SaSaleAccountMarketMappingService saSaleAccountMarketMappingService;
    @Autowired
    private BdMarketplaceService bdMarketplaceService;
    @Autowired
    private BdStoreCreateTemplateConfigService bdStoreCreateTemplateConfigService;
    @Autowired
    private SaAccountApplyService saAccountApplyService;
    @Autowired
    private ActivitiApiService activitiApiService;
    @Autowired
    private SaMediaAccountService saMediaAccountService;
    @Autowired
    private SaLiveAccountService saLiveAccountService;
    @Autowired
    private SaAdAccountService saAdAccountService;
    @Autowired
    private NotifyApiService notifyApiService;
    @Autowired
    private AccessControlService accessControlService;
    @Autowired
    private SaStoreFinancePaymentAccountDao saStoreFinancePaymentAccountDao;
    @Autowired
    private SaStoreGoodOperatorService saStoreGoodOperatorService;
    @Autowired
    private SaStoreAdAccountMappingService saStoreAdAccountMappingService;


    @Override
    public List<SaStoreEntity> getStoreList(Integer platformId) {
        return saStoreDao.getStoreList(platformId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveData(SaStoreRequest saStoreRequest) {
        SaStoreEntity saStoreEntity = new SaStoreEntity();
        BeanUtils.copyProperties(saStoreRequest, saStoreEntity);
        saStoreEntity.setShippingChannel(JSONUtil.toJsonStr(saStoreRequest.getShippingChannel()));
        saStoreEntity.setCreateBy(loginInfoService.getName());
        saStoreEntity.setDistributionStoreFlag(saStoreRequest.getDistributionStoreFlag() == 1 ? SaStoreDistributionStoreFlagEnum.NO.getCode() : SaStoreDistributionStoreFlagEnum.YES.getCode());
        //开店时间默认不填就是创建时间
        saStoreRequest.setOpenShopDate(ObjectUtils.isEmpty(saStoreRequest.getOpenShopDate()) ? DateUtil.date() : saStoreRequest.getOpenShopDate());
        validateParam(saStoreRequest);
        saStoreEntity.setSkuRule(StringUtil.EMPTY);
        //一个店铺只能被关联一次
        validateStoreAssociated(saStoreRequest.getAssociatedStoreId());
        saStoreEntity.setSecondDepartment(saStoreRequest.getSecondDepartmentName());
        BdPlatformEntity bdPlatformEntity = bdPlatformService.getInfo(saStoreEntity.getPlatformId());
        saStoreEntity.setIsAuth(bdPlatformEntity.getIsAuth() == 1 ? 0 : 2);
        saStoreEntity.setCanUseInboundApi(String.join(",", saStoreRequest.getCanUseInboundApiList()));
        saStoreDao.save(saStoreEntity);
        BdWebsiteCreateRuleEntity bdWebsiteCreateRuleEntity = null;
        if (!ObjectUtils.isEmpty(saStoreEntity.getSecondPlatformId())) {
            bdWebsiteCreateRuleEntity = bdWebsiteCreateRuleService.getWebsiteRule(saStoreEntity.getSecondPlatformId());
        }
        if (ObjectUtils.isEmpty(bdWebsiteCreateRuleEntity)) {
            bdWebsiteCreateRuleEntity = bdWebsiteCreateRuleService.getWebsiteRule(saStoreEntity.getPlatformId());
        }
        //存在站点生成规则就需要进行创建站点
        if (!ObjectUtils.isEmpty(bdWebsiteCreateRuleEntity) && loginInfoService.getLocation().toUpperCase(Locale.ROOT).equals(LocationEnum.QUANZHOU.name())) {
            try {
                createWebSiteInfo(saStoreRequest, saStoreEntity, bdWebsiteCreateRuleEntity, bdPlatformEntity, 0);
            } catch (Exception e) {
                throw new BusinessServiceException(e.getMessage(), e);
            }
        }

        Integer fbsStoreId = isNeedCreateFbaStore(saStoreRequest.getPlatformName(), saStoreRequest.getAchievementAttribution()) ? createAssociatedStore(saStoreEntity) : null;
        if (isNeedSaveOrUpdateStoreMarketSet(saStoreRequest.getPlatformId())) {
            saStoreMarketSetService.saveStoreMarket(saStoreRequest, saStoreEntity.getId());
        }
        saStorePrincipalService.saveStorePrincipal(saStoreRequest, saStoreEntity.getId());
        saStoreSalesmanService.saveStoreSalesman(saStoreRequest, saStoreEntity.getId());
        saStoreStaffingService.saveStoreStaffing(saStoreRequest, saStoreEntity.getId());
        saStoreGoodOperatorService.saveGoodOpeartor(saStoreRequest, saStoreEntity.getId());
        saStoreConfigService.saveSaStoreConfig(saStoreRequest, saStoreEntity.getId(), fbsStoreId);
        saStoreFinanceAccountService.saveSaStoreFinanceAccount(saStoreRequest, saStoreEntity.getId());
        saStoreFinancePaymentAccountService.saveSaStoreFinancePaymentAccount(saStoreRequest, saStoreEntity.getId(), fbsStoreId);
        saStoreAdAccountMappingService.saveAdAccount(saStoreRequest, saStoreEntity.getId());
        baseService.sendQueue(saStoreEntity.getId(), StringUtil.EMPTY);
        if (!ObjectUtils.isEmpty(fbsStoreId)) {
            baseService.sendQueue(fbsStoreId, StringUtil.EMPTY);
        }

        bdOperateLogService.addLog(BusinessTypeEnum.STORE.getCode(), saStoreEntity.getId());
    }

    /**
     * 如果是亚马逊平台是通过店铺名_平台名称
     *
     * @param saStoreRequest
     * @param saStoreEntity
     * @param bdWebsiteCreateRuleEntity
     * @param bdPlatformEntity
     */
    private void createWebSiteInfo(SaStoreRequest saStoreRequest, SaStoreEntity saStoreEntity, BdWebsiteCreateRuleEntity bdWebsiteCreateRuleEntity, BdPlatformEntity bdPlatformEntity, Integer isAdd) {
        CreateWebsiteRequest request = new CreateWebsiteRequest();
        request.setStoreId(saStoreEntity.getId());
        request.setStoreName(saStoreRequest.getErpStoreName());
        request.setPlatformId(saStoreRequest.getPlatformId());
        request.setPlatformName(bdPlatformEntity.getPlatform());
        request.setDepartment(saStoreRequest.getDepartment());
        List<WebSiteInfo> websiteList = new ArrayList<>();
        if (bdWebsiteCreateRuleEntity.getSpecialType() == 1) {
            if (isAdd > 0) {
                modifyWebsite(saStoreRequest, saStoreEntity, bdPlatformEntity);
            }
            WebSiteInfo webSiteInfo = new WebSiteInfo();
            webSiteInfo.setWebsiteName(bdWebsiteCreateRuleEntity.getSpecialValue());
            webSiteInfo.setIsAdd(1);
            websiteList.add(webSiteInfo);
        } else if (bdWebsiteCreateRuleEntity.getSpecialType() == 0) {
            if (isAdd > 0) {
                modifyWebsite(saStoreRequest, saStoreEntity, bdPlatformEntity);
            }
            List<SaStoreWebsiteResponse> saStoreWebsiteResponses = saStoreWebsiteService.getWebsiteList(saStoreEntity.getId());
            if (CollectionUtils.isEmpty(saStoreWebsiteResponses)) {
                WebSiteInfo webSiteInfo = new WebSiteInfo();
                webSiteInfo.setWebsiteName(saStoreRequest.getErpStoreName());
                webSiteInfo.setIsAdd(1);
                websiteList.add(webSiteInfo);
            }
        } else {
            //亚马逊比较特殊
            List<StoreMarketRequest> marketSetups = saStoreRequest.getMarketLists();
            if (CollectionUtils.isEmpty(marketSetups)) {
                throw new BusinessServiceException("该销售账号的市场已经全部创建站点,店铺创建失败");
            }
            if (isAdd > 0) {
                modifyWebsite(saStoreRequest, saStoreEntity, bdPlatformEntity);
            }
            marketSetups.forEach(item -> {
                makeWebsiteData(saStoreRequest, saStoreEntity, websiteList, item);
            });
        }

        request.setWebsiteList(Optional.of(websiteList).orElse(new ArrayList<>()).stream().distinct().collect(Collectors.toList()));
        if (!CollectionUtils.isEmpty(request.getWebsiteList())) {
            log.info("调用刊登系统创建站点,request 为 {}", JsonMapper.toJson(request));
            CreateWebsiteResponse response = websiteConfigFeignClient.save(request);
            log.info("调用刊登系统创建站点,response 为 {}", JsonMapper.toJson(response));
            if (!CollectionUtils.isEmpty(response.getWebsiteList())) {
                response.getWebsiteList().forEach(item -> saStoreWebsiteService.saveData(response, item));
            }
        }
    }

    private void makeWebsiteData(SaStoreRequest saStoreRequest, SaStoreEntity saStoreEntity, List<WebSiteInfo> websiteList, StoreMarketRequest item) {
        if (saStoreWebsiteService.isNeedSave(saStoreEntity.getId(), item.getMarketListName())) {
            if (item.getStatus() == 0) {
                WebSiteInfo webSiteInfo = new WebSiteInfo();
                String countryCode = item.getMarketListName().split("\\(")[0];
                webSiteInfo.setWebsiteName(saStoreRequest.getErpStoreName() + "_" + countryCode);
                webSiteInfo.setIsAdd(item.getStatus());
                webSiteInfo.setWebsiteId(Integer.valueOf(item.getMarketListId()));
                webSiteInfo.setMarketName(item.getMarketListName());
                String marketplaceId = Optional.ofNullable(bdMarketplaceService.getByCountryCode(countryCode)).orElse(new BdMarketplaceEntity()).getMarketplaceId();
                webSiteInfo.setMarketCode(marketplaceId);
                websiteList.add(webSiteInfo);
            }
        } else {
            WebSiteInfo webSiteInfo = new WebSiteInfo();
            String countryCode = item.getMarketListName().split("\\(")[0];
            webSiteInfo.setWebsiteName(saStoreRequest.getErpStoreName() + "_" + countryCode);
            webSiteInfo.setWebsiteId(Integer.valueOf(item.getMarketListId()));
            webSiteInfo.setIsAdd(item.getStatus());
            webSiteInfo.setMarketName(item.getMarketListName());
            String marketplaceId = Optional.ofNullable(bdMarketplaceService.getByCountryCode(countryCode)).orElse(new BdMarketplaceEntity()).getMarketplaceId();
            webSiteInfo.setMarketCode(marketplaceId);
            websiteList.add(webSiteInfo);
        }
    }

    /**
     * 场景一:
     * 修改前: 一级平台  二级平台有  修后 一级平台  二级平台无  这时候需要判断旧二级平台是否有规则，有需要删除旧站点
     * 修改前: 一级平台  二级平台有  修后 一级平台  二级平台有  这时候需要判断旧二级平台是否有规则，有需要删除旧站点，判断一级平台是否有规则，有需要删除
     * <p>
     * 场景二:
     * 修改前: 一级平台  二级平台无  修后 一级平台  二级平台有  这时候需要判断新二级平台是否有规则，有需要删除旧站点
     *
     * @param saStoreRequest
     * @param saStoreEntity
     * @param bdPlatformEntity
     */
    private void modifyWebsite(SaStoreRequest saStoreRequest, SaStoreEntity saStoreEntity, BdPlatformEntity bdPlatformEntity) {
        CreateWebsiteRequest request = new CreateWebsiteRequest();
        request.setStoreId(saStoreEntity.getId());
        request.setStoreName(saStoreRequest.getErpStoreName());
        request.setPlatformId(saStoreRequest.getPlatformId());
        request.setPlatformName(bdPlatformEntity.getPlatform());
        request.setDepartment(saStoreRequest.getDepartment());
        List<WebSiteInfo> websiteList = new ArrayList<>();
        if (saStoreRequest.getSecondPlatformName().equals(saStoreEntity.getSecondPlatformName())) {
            return;
        }
        if (StringUtil.isNotBlank(saStoreEntity.getSecondPlatformName()) && StringUtil.isBlank(saStoreRequest.getSecondPlatformName())) {
            log.info("店铺ID {} 原来有二级平台,现在没有", saStoreEntity.getId());
            BdWebsiteCreateRuleEntity bdWebsiteCreateRuleEntity = bdWebsiteCreateRuleService.getWebsiteRule(saStoreEntity.getSecondPlatformId());
            createWebsiteListByDelete(saStoreEntity, websiteList, bdWebsiteCreateRuleEntity);
        }
        if (StringUtil.isNotBlank(saStoreEntity.getSecondPlatformName()) && StringUtil.isNotBlank(saStoreRequest.getSecondPlatformName())) {
            log.info("店铺ID {} 原来有二级平台,现在换平台", saStoreEntity.getId());
            BdWebsiteCreateRuleEntity bdWebsiteCreateRuleEntity = bdWebsiteCreateRuleService.getWebsiteRule(saStoreEntity.getSecondPlatformId());
            createWebsiteListByDelete(saStoreEntity, websiteList, bdWebsiteCreateRuleEntity);
            bdWebsiteCreateRuleEntity = bdWebsiteCreateRuleService.getWebsiteRule(saStoreEntity.getPlatformId());
            createWebsiteListByDelete(saStoreEntity, websiteList, bdWebsiteCreateRuleEntity);
        }
        if (StringUtil.isBlank(saStoreEntity.getSecondPlatformName()) && StringUtil.isNotBlank(saStoreRequest.getSecondPlatformName())) {
            log.info("店铺ID {} 原来没有二级平台,现在有二级平台", saStoreEntity.getId());
            BdWebsiteCreateRuleEntity bdWebsiteCreateRuleEntity = bdWebsiteCreateRuleService.getWebsiteRule(saStoreRequest.getSecondPlatformId());
            if (!ObjectUtils.isEmpty(bdWebsiteCreateRuleEntity)) {
                bdWebsiteCreateRuleEntity = bdWebsiteCreateRuleService.getWebsiteRule(saStoreEntity.getPlatformId());
                createWebsiteListByDelete(saStoreEntity, websiteList, bdWebsiteCreateRuleEntity);
            }

        }
        websiteList = Optional.of(websiteList).orElse(new ArrayList<>()).stream().distinct().collect(Collectors.toList());
        request.setWebsiteList(websiteList);
        if (!CollectionUtils.isEmpty(websiteList)) {
            log.info("调用刊登系统创建站点,request 为 {}", JsonMapper.toJson(request));
            CreateWebsiteResponse response = websiteConfigFeignClient.save(request);
            log.info("调用刊登系统创建站点,response 为 {}", JsonMapper.toJson(response));
            if (!CollectionUtils.isEmpty(response.getWebsiteList())) {
                response.getWebsiteList().forEach(item -> saStoreWebsiteService.saveData(response, item));
            }
        }
    }

    private void createWebsiteListByDelete(SaStoreEntity saStoreEntity, List<WebSiteInfo> websiteList, BdWebsiteCreateRuleEntity bdWebsiteCreateRuleEntity1) {
        if (!ObjectUtils.isEmpty(bdWebsiteCreateRuleEntity1)) {
            if (bdWebsiteCreateRuleEntity1.getSpecialType() == 1) {
                saStoreWebsiteService.removeWebsite(saStoreEntity.getId());
            } else {
                List<SaStoreWebsiteResponse> saStoreWebsiteResponses = saStoreWebsiteService.getWebsiteList(saStoreEntity.getId());
                Optional.of(saStoreWebsiteResponses).orElse(new ArrayList<>()).forEach(item -> {
                    WebSiteInfo webSiteInfo = new WebSiteInfo();
                    webSiteInfo.setWebsiteName(item.getWebsiteName());
                    webSiteInfo.setIsAdd(0);
                    webSiteInfo.setMarketName(item.getMarketName());
                    webSiteInfo.setWebsiteId(Integer.valueOf(item.getWebsiteId()));
                    websiteList.add(webSiteInfo);
                });
            }
        }
    }

    private Integer createAssociatedStore(SaStoreEntity saStoreEntity) {
        Integer fbsStoreId;
        SaStoreEntity fbaSaStoreEntity = new SaStoreEntity();
        BeanUtils.copyProperties(saStoreEntity, fbaSaStoreEntity, "id");
        String suffix = saStoreEntity.getPlatformName().contains(StringConstant.AMAZON) ? StringConstant.AMAZON_STORE_SUFFIX : StringConstant.WMT_STORE_SUFFIX;
        String associatedErpStoreName = saStoreEntity.getErpStoreName() + suffix;
        fbaSaStoreEntity.setErpStoreName(associatedErpStoreName);
        String achievementAttribution = saStoreEntity.getPlatformName().contains(StringConstant.AMAZON) ? StringConstant.AMAZON_OVERSEA : StringConstant.WMT_WFS;
        fbaSaStoreEntity.setAchievementAttribution(achievementAttribution);
        fbaSaStoreEntity.setCreateBy(StringConstant.SYSTEM);
        fbaSaStoreEntity.setAssociatedStoreId(saStoreEntity.getId());
        saStoreDao.save(fbaSaStoreEntity);
        fbsStoreId = fbaSaStoreEntity.getId();
        saStoreEntity.setAssociatedStoreId(fbsStoreId);
        saStoreDao.updateById(saStoreEntity);
        return fbsStoreId;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveDistributionStore(DistributorsStoreRequest saStoreRequest) {
        log.info("saveDistributionStore request: {}", JsonMapper.toJson(saStoreRequest));
        SaStoreEntity saStoreEntity = new SaStoreEntity();
        BeanUtils.copyProperties(saStoreRequest, saStoreEntity);
        saStoreEntity.setCreateBy(loginInfoService.getName());
        saStoreEntity.setDistributionStoreFlag(SaStoreDistributionStoreFlagEnum.YES.getCode());
        if (StringUtil.isNotBlank(saStoreRequest.getDepartment())) {
            List<BdDictionaryItem> list = userDictionaryApiService.getDictionaryValues(DictionaryKeySelectEnum.DEPARTMENT.getKey());
            Map<String, String> departmentMap = list.stream().collect(Collectors.toMap(v -> String.valueOf(v.getLabel()), v -> v.getValue()));
            saStoreEntity.setDepartmentId(Integer.valueOf(departmentMap.get(saStoreRequest.getDepartment())));
        }
        if (StringUtil.isBlank(saStoreRequest.getStoreName())) {
            saStoreEntity.setErpStoreName(saStoreRequest.getErpStoreName());
        }
        //开店时间默认不填就是创建时间
        saStoreRequest.setOpenShopDate(ObjectUtils.isEmpty(saStoreRequest.getOpenShopDate()) ? DateUtil.date() : saStoreRequest.getOpenShopDate());
        //一个店铺只能被关联一次
        validateStoreAssociated(saStoreRequest.getAssociatedStoreId());
        saStoreDao.save(saStoreEntity);
        saStoreConfigService.saveSaStore(saStoreEntity.getId());
        Integer fbsStoreId = isNeedCreateFbaStore(saStoreRequest.getPlatformName(), saStoreRequest.getAchievementAttribution()) ? createAssociatedStore(saStoreEntity) : null;
        saStoreRequest.setStoreName(saStoreEntity.getErpStoreName());
        erpExternalService.saveData(saStoreRequest, saStoreEntity.getId());
        baseService.sendQueue(saStoreEntity.getId(), getTypeByPlatformId(saStoreRequest.getPlatformId()));
        if (!ObjectUtils.isEmpty(fbsStoreId)) {
            baseService.sendQueue(fbsStoreId, getTypeByPlatformId(saStoreRequest.getPlatformId()));
        }
        bdOperateLogService.addLog(BusinessTypeEnum.STORE.getCode(), saStoreEntity.getId());
    }

    private String getTypeByPlatformId(Integer platformId) {
        Map<Integer, String> platformMap = new HashMap<>();
        platformMap.put(PlatformEnum.AMAZON_AMERICA.getCode(), BusinessTypeEnum.AMAZON.name());
        platformMap.put(PlatformEnum.AMAZON_BRITAIN.getCode(), BusinessTypeEnum.AMAZON.name());
        platformMap.put(PlatformEnum.AMAZON_JAPAN.getCode(), BusinessTypeEnum.AMAZON.name());
        platformMap.put(PlatformEnum.AMAZON_CANADA.getCode(), BusinessTypeEnum.AMAZON.name());
        platformMap.put(PlatformEnum.AMAZON_GERMANY.getCode(), BusinessTypeEnum.AMAZON.name());
        platformMap.put(PlatformEnum.AMAZON_SPAIN.getCode(), BusinessTypeEnum.AMAZON.name());
        platformMap.put(PlatformEnum.AMAZON_FRANCE.getCode(), BusinessTypeEnum.AMAZON.name());
        platformMap.put(PlatformEnum.AMAZON_INDIA.getCode(), BusinessTypeEnum.AMAZON.name());
        platformMap.put(PlatformEnum.AMAZON_ITALY.getCode(), BusinessTypeEnum.AMAZON.name());
        platformMap.put(PlatformEnum.AMAZON_MEXICO.getCode(), BusinessTypeEnum.AMAZON.name());
        platformMap.put(PlatformEnum.AMAZON_AUSTRALIA.getCode(), BusinessTypeEnum.AMAZON.name());
        platformMap.put(PlatformEnum.AMAZON_EUROPE.getCode(), BusinessTypeEnum.AMAZON.name());
        platformMap.put(PlatformEnum.AMAZON_NORTHAMERICA.getCode(), BusinessTypeEnum.AMAZON.name());
        platformMap.put(PlatformEnum.ALIBABA_INTERNATION.getCode(), BusinessTypeEnum.ALI_INTER.name());
        platformMap.put(PlatformEnum.PIN_DUO_DUO.getCode(), BusinessTypeEnum.PDD.name());
        platformMap.put(PlatformEnum.SHOPIFY.getCode(), BusinessTypeEnum.SHOPIFY.name());
        platformMap.put(PlatformEnum.ALIBABA_INTERNATION.getCode(), BusinessTypeEnum.WHOLESALE.name());
        platformMap.put(PlatformEnum.TAO_BAO.getCode(), BusinessTypeEnum.TAO_BAO.name());
        return platformMap.getOrDefault(platformId, BusinessTypeEnum.COMMON.name());
    }

    /**
     * 亚马逊和沃尔玛这俩类平台新增店铺，
     * 会自动创建一条补货的店铺，并且自动关联
     *
     * @param platFormName
     * @param achievementAttribution
     * @return
     */
    private boolean isNeedCreateFbaStore(String platFormName, String achievementAttribution) {
        boolean platFormIsMatch = platFormName.contains(StringConstant.AMAZON) || platFormName.contains(StringConstant.WMT);
        boolean achievementAttributionIsMatch = StringConstant.AMAZON_OVERSEA.equals(achievementAttribution) || StringConstant.WMT_WFS.equals(achievementAttribution);
        return platFormIsMatch && !achievementAttributionIsMatch;
    }

    private void validateStoreAssociated(Integer associatedStoreId) {
        if (ObjectUtil.isNotNull(associatedStoreId) && !NumberConstant.ZERO.equals(associatedStoreId) && saStoreDao.storeIsAssociated(associatedStoreId)) {
            throw new BusinessServiceException("该店铺已经被关联");
        }
    }

    @Override
    public boolean checkSku(String sku) {
        //SKU规则只能输入英文
        if (!sku.matches(PatternConstant.STRING_PATTERN)) {
            throw new BusinessServiceException("SKU规则只能输入英文");
        }
        if (StoreSkuPrefixEnum.isContainPrefix(sku)) {
            throw new BusinessServiceException("sku前缀字母不符合要求");
        }
        return saStoreDao.skuIsExists(sku);
    }

    private void validateParam(SaStoreRequest saStoreRequest) {
        //ERP店铺名称不能重复
        if (saStoreDao.isExists(saStoreRequest.getErpStoreName())) {
            throw new BusinessServiceException("ERP店铺名称已经存在");
        }
        //市场是否被关联过
        if (!CollectionUtils.isEmpty(saStoreRequest.getStoreFinancePaymentAccountList())) {
            saStoreRequest.getStoreFinancePaymentAccountList().forEach(item -> {
                if (saStoreFinancePaymentAccountService.isExists(item.getCardId())) {
                    throw new BusinessServiceException("该卡已经被关联店铺,请选择其他付款卡");
                }
                if (saAccountApplyService.isExist(item.getCardId())) {
                    throw new BusinessServiceException("该卡已经被关联店铺,请选择其他付款卡");
                }
            });
        }
        if (!CollectionUtils.isEmpty(saStoreRequest.getStoreFinanceAccountList())) {
            saStoreRequest.getStoreFinanceAccountList().forEach(item -> {
                if (saStoreFinanceAccountService.isExists(saStoreRequest.getPlatformId(), item.getAccountId())) {
                    throw new BusinessServiceException("账号已经被同个平台使用");
                }
            });
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateData(SaStoreRequest saStoreRequest) {
        log.info("saStoreRequest 的值为 {}", JsonMapper.toJson(saStoreRequest));
        //市场是否被关联过
        //一个店铺只能被关联一次
        validateStoreAssociatedInUpdateScene(saStoreRequest.getAssociatedStoreId(), saStoreRequest.getId());
        StringBuffer description = new StringBuffer();
        SaStoreEntity oldStoreEntity = Optional.ofNullable(saStoreDao.getById(saStoreRequest.getId())).orElseThrow(() -> new BusinessServiceException("店铺不存在"));
        SaStoreEntity saStoreEntity = new SaStoreEntity();
        BeanUtils.copyProperties(saStoreRequest, saStoreEntity);
        saStoreEntity.setShippingChannel(JSONUtil.toJsonStr(saStoreRequest.getShippingChannel()));
        selectDataSetDefaultValue(saStoreRequest, saStoreEntity);
        saStoreEntity.setUpdateBy(loginInfoService.getName());
        saStoreEntity.setCanUseInboundApi(String.join(",", saStoreRequest.getCanUseInboundApiList()));
        BdPlatformEntity bdPlatformEntity = bdPlatformService.getInfo(saStoreEntity.getPlatformId());
        saStoreEntity.setIsAuth(bdPlatformEntity.getIsAuth() == 1 ? 0 : 2);
        if (checkSaleAccount(oldStoreEntity, saStoreRequest)) {
            throw new BusinessServiceException("账号所属的平台、部门与店铺所属平台、部门、市场不一致");
        }
        saStoreDao.updateById(saStoreEntity);
        if (isNeedCreateFbaStore(saStoreRequest.getPlatformName(), saStoreRequest.getAchievementAttribution()) && loginInfoService.getLocation().toUpperCase(Locale.ROOT).equals(LocationEnum.QUANZHOU.name())) {
            createWebsite(saStoreRequest, oldStoreEntity, saStoreEntity, bdPlatformEntity);
        }
        description.append(bdOperateLogService.getLogDescription(oldStoreEntity, saStoreEntity, SaStoreEntity.class));

        Integer fbaStoreId = saStoreDao.getAssociatedStoreId(saStoreRequest.getId());
        description = saStoreConfigService.updateSaStoreConfig(saStoreRequest, description);

        if (isNeedSaveOrUpdateStoreMarketSet(saStoreRequest.getPlatformId())) {
            description = saStoreMarketSetService.updateStoreMarket(saStoreRequest, description);
        }
        description = saStorePrincipalService.updateStorePrincipal(saStoreRequest, description);
        description = saStoreSalesmanService.updateStoreSalesman(saStoreRequest, description);
        description = saStoreStaffingService.updateStoreStaffing(saStoreRequest, description);
        description = saStoreGoodOperatorService.updateGoodOpeartor(saStoreRequest, description);
        description = saStoreAdAccountMappingService.updateAccounts(saStoreRequest, description);
        saStoreWebsiteService.updateStoreWebsite(saStoreRequest, description);
        bdOperateLogService.updateLog(BusinessTypeEnum.STORE.getCode(), saStoreEntity.getId(), description.toString());
        if (!ObjectUtils.isEmpty(fbaStoreId)) {
            baseService.sendQueue(fbaStoreId, StringUtil.EMPTY);
        }
        baseService.sendQueue(saStoreEntity.getId(), StringUtil.EMPTY);
    }

    private boolean checkSaleAccount(SaStoreEntity oldStoreEntity, SaStoreRequest saStoreRequest) {
        if (ObjectUtils.isEmpty(saStoreRequest.getSaleAccountId()) || saStoreRequest.getSaleAccountId() < 1) {
            return false;
        }
        if (oldStoreEntity.getSaleAccountId().equals(saStoreRequest.getSaleAccountId())) {
            return false;
        }
        SaSaleAccountResponse saSaleAccountResponse = saSaleAccountService.getInfo(saStoreRequest.getSaleAccountId());
        Integer secondDepartmentId = StringUtil.isNotBlank(saSaleAccountResponse.getSecondDepartmentId()) ? Integer.valueOf(saSaleAccountResponse.getSecondDepartmentId()) : 0;
        if (!(saSaleAccountResponse.getPlatformId().equals(oldStoreEntity.getPlatformId())
                && saSaleAccountResponse.getDepartmentId().equals(oldStoreEntity.getDepartmentId())
                && secondDepartmentId.equals(oldStoreEntity.getSecondDepartmentId()))) {
            return true;
        }
        if (oldStoreEntity.getPlatformId() >= 801 && oldStoreEntity.getPlatformId() <= 813) {
            //销售账号的市场数据
            List<SaSaleAccountMarketMappingEntity> saleAccountMarketMappingEntities = saSaleAccountMarketMappingService.getListBySaleAccountId(saStoreRequest.getSaleAccountId());
            //店铺站点的数据
            List<SaStoreWebsiteResponse> saStoreWebsiteEntities = saStoreWebsiteService.getWebsiteList(saStoreRequest.getId());
            if (saleAccountMarketMappingEntities.size() < saStoreWebsiteEntities.size()) {
                return true;
            }
            List<String> saleAccountMarketNames = Optional.of(saleAccountMarketMappingEntities).orElse(new ArrayList<>()).stream().map(SaSaleAccountMarketMappingEntity::getMarketName).distinct().collect(Collectors.toList());
            List<String> saStoreMarketNames = Optional.of(saStoreWebsiteEntities).orElse(new ArrayList<>()).stream().map(SaStoreWebsiteResponse::getMarketName).distinct().collect(Collectors.toList());
            return !saleAccountMarketNames.containsAll(saStoreMarketNames);
        } else {
            return false;
        }
    }


    private void createWebsite(SaStoreRequest saStoreRequest, SaStoreEntity oldStoreEntity, SaStoreEntity saStoreEntity, BdPlatformEntity bdPlatformEntity) {
        List<Integer> storeIds = cn.hutool.core.collection.ListUtil.toList(9083, 7757, 9129, 9130, 9128, 8495, 8525, 8760, 8936, 9071, 9072, 9073, 9074, 9075, 9076, 7304, 7784, 7343);
        if (!storeIds.contains(saStoreRequest.getId())) {
            BdWebsiteCreateRuleEntity bdWebsiteCreateRuleEntity = bdWebsiteCreateRuleService.getWebsiteRule(saStoreEntity.getPlatformId());
            if (!ObjectUtils.isEmpty(saStoreEntity.getSecondPlatformId())) {
                bdWebsiteCreateRuleEntity = bdWebsiteCreateRuleService.getWebsiteRule(saStoreEntity.getSecondPlatformId());
            }
            if (ObjectUtils.isEmpty(bdWebsiteCreateRuleEntity)) {
                bdWebsiteCreateRuleEntity = bdWebsiteCreateRuleService.getWebsiteRule(saStoreEntity.getPlatformId());
            }
            //存在站点生成规则就需要进行创建站点
            if (!ObjectUtils.isEmpty(bdWebsiteCreateRuleEntity)) {
                try {
                    createWebSiteInfo(saStoreRequest, oldStoreEntity, bdWebsiteCreateRuleEntity, bdPlatformEntity, 1);
                } catch (Exception e) {
                    throw new BusinessServiceException(e.getMessage());
                }
            } else {
                modifyWebsite(saStoreRequest, oldStoreEntity, bdPlatformEntity);
            }
        }
    }


    private void validateStoreAssociatedInUpdateScene(Integer associatedStoreId, Integer storeId) {
        if (ObjectUtil.isNotNull(associatedStoreId) && !NumberConstant.ZERO.equals(associatedStoreId)) {
            Integer originStoreId = saStoreDao.getAssociatedStoreId(associatedStoreId);
            if (ObjectUtil.isNotNull(originStoreId) && !originStoreId.equals(storeId)) {
                throw new RuntimeException("该店铺已经被关联");
            }
        }
    }

    private void selectDataSetDefaultValue(SaStoreRequest saStoreRequest, SaStoreEntity saStoreEntity) {
        if (ObjectUtil.isEmpty(saStoreRequest.getAssociatedStoreId())) {
            saStoreEntity.setAssociatedStoreId(NumberConstant.ZERO);
        }
        if (ObjectUtil.isEmpty(saStoreRequest.getSecondPlatformId())) {
            saStoreEntity.setSecondPlatformId(NumberConstant.ZERO);
        }
        if (ObjectUtil.isEmpty(saStoreRequest.getCategoryId())) {
            saStoreEntity.setCategoryId(NumberConstant.ZERO);
        }
        if (ObjectUtil.isEmpty(saStoreRequest.getNature())) {
            saStoreEntity.setNature(NumberConstant.ZERO);
        }
        if (ObjectUtil.isEmpty(saStoreRequest.getNature())) {
            saStoreEntity.setNature(NumberConstant.ZERO);
        }
        if (ObjectUtil.isEmpty(saStoreRequest.getDefaultShippingAddressId())) {
            saStoreEntity.setDefaultShippingAddressId(NumberConstant.ZERO);
        }
        if (ObjectUtil.isEmpty(saStoreRequest.getStoreType())) {
            saStoreEntity.setStoreType(NumberConstant.ZERO);
        }
        if (ObjectUtil.isEmpty(saStoreRequest.getDepartmentId())) {
            saStoreEntity.setDepartmentId(NumberConstant.ZERO);
        }
        if (ObjectUtil.isEmpty(saStoreRequest.getTeamId())) {
            saStoreEntity.setTeamId(NumberConstant.ZERO);
        }
        if (ObjectUtil.isEmpty(saStoreRequest.getSaleAccountId())) {
            saStoreEntity.setSaleAccountId(NumberConstant.ZERO);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDistributionStore(DistributorsStoreRequest saStoreRequest) {
        log.info("updateDistributionStore request: {}", JsonMapper.toJson(saStoreRequest));
        StringBuffer description = new StringBuffer();
        SaStoreEntity oldStoreEntity = Optional.ofNullable(saStoreDao.getById(saStoreRequest.getId())).orElseThrow(() -> new BusinessServiceException("店铺不存在"));
        SaStoreEntity saStoreEntity = new SaStoreEntity();
        BeanUtils.copyProperties(saStoreRequest, saStoreEntity);
        saStoreEntity.setUpdateBy(loginInfoService.getName());
        saStoreDao.updateById(saStoreEntity);
        description.append(bdOperateLogService.getLogDescription(oldStoreEntity, saStoreEntity, SaStoreEntity.class));

        Integer fbaStoreId = saStoreDao.getAssociatedStoreId(saStoreRequest.getId());
        description = saStoreConfigService.updateDistributionSaStoreConfig(saStoreRequest, description);
        if (StringUtil.isBlank(saStoreRequest.getStoreName())) {
            saStoreEntity.setErpStoreName(saStoreRequest.getErpStoreName());
        }
        if (isNeedSaveOrUpdateStoreMarketSet(saStoreRequest.getPlatformId())) {
            description = saStoreMarketSetService.updateDistributionStoreMarket(saStoreRequest, description);
        }
        description = saStorePrincipalService.updateDistributionStorePrincipal(saStoreRequest, description);
        description = saStoreStaffingService.updateDistributionStoreStaffing(saStoreRequest, description);
        erpExternalService.saveData(saStoreRequest, saStoreEntity.getId());
        bdOperateLogService.updateLog(BusinessTypeEnum.STORE.getCode(), saStoreEntity.getId(), description.toString());
        if (!ObjectUtils.isEmpty(fbaStoreId)) {
            baseService.sendQueue(fbaStoreId, StringUtil.EMPTY);
        }
        baseService.sendQueue(saStoreEntity.getId(), StringUtil.EMPTY);
    }

    /*
     * 亚马逊欧洲，亚马逊北美才有市场设置，根据平台id:812/813
     */
    private boolean isNeedSaveOrUpdateStoreMarketSet(Integer platformId) {
        return platformId.equals(PlatformEnum.AMAZON_EUROPE.getCode()) || platformId.equals(PlatformEnum.AMAZON_NORTHAMERICA.getCode());
    }

    @Override
    public PageResponse<SaStoreResponse> list(SaStorePageRequest saStorePageRequest) {
        PageResponse<SaStoreResponse> pageResponse = PageResponse.of(0L, 1L);
        List<Integer> storeList = null;
        if (ObjectUtil.isNotNull(saStorePageRequest.getMarketId())) {
            storeList = saStoreWebsiteService.getStoreList(saStorePageRequest.getMarketId());
        }
        if (ObjectUtil.isNotNull(saStorePageRequest.getStoreId())) {
            storeList = new ArrayList<>();
            storeList.add(saStorePageRequest.getStoreId());
        }
        // 0 直播号 1 社媒号 2 广告账号 3. 邮箱 4. 电话
        if (ObjectUtil.isNotNull(saStorePageRequest.getSearchType()) && saStorePageRequest.getSearchType() == 5) {
            SaLiveAccountEntity saLiveAccountEntity = saLiveAccountService.getInfoByAccount(saStorePageRequest.getSearchValue());
            saStorePageRequest.setLiveAccountId(ObjectUtil.isEmpty(saLiveAccountEntity) ? 0 : saLiveAccountEntity.getLiveAccountId());
        }
        if (ObjectUtil.isNotNull(saStorePageRequest.getSearchType()) && saStorePageRequest.getSearchType() == 1) {
            SaMediaAccountEntity saMediaAccountEntity = saMediaAccountService.getInfoByAccount(saStorePageRequest.getSearchValue());
            saStorePageRequest.setMediaAccountId(ObjectUtil.isEmpty(saMediaAccountEntity) ? 0 : saMediaAccountEntity.getMediaAccountId());
        }
        if (ObjectUtil.isNotNull(saStorePageRequest.getSearchType()) && saStorePageRequest.getSearchType() == 2) {
            SaAdAccountEntity saAdAccountEntity = saAdAccountService.getInfoByAccount(saStorePageRequest.getSearchValue());
            saStorePageRequest.setAdAccountId(ObjectUtil.isEmpty(saAdAccountEntity) ? 0 : saAdAccountEntity.getAdAccountId());
        }
        if (ObjectUtil.isNotNull(saStorePageRequest.getSearchType()) && saStorePageRequest.getSearchType() == 3) {
            List<Integer> saSaleAccountList = saSaleAccountService.getListByEmail(saStorePageRequest.getSearchValue());
            if (CollectionUtil.isEmpty(saSaleAccountList)) {
                return pageResponse;
            }
            saStorePageRequest.setSaSaleAccountList(saSaleAccountList);
        }
        if (ObjectUtil.isNotNull(saStorePageRequest.getSearchType()) && saStorePageRequest.getSearchType() == 4) {
            List<Integer> saSaleAccountList = saSaleAccountService.getListByPhone(saStorePageRequest.getSearchValue());
            if (CollectionUtil.isEmpty(saSaleAccountList)) {
                return pageResponse;
            }
            saStorePageRequest.setSaSaleAccountList(saSaleAccountList);
        }
        Page<StoreResponse> saStoreEntityPage = saStoreDao.getList(saStorePageRequest, storeList);
        pageResponse = PageResponse.of(saStoreEntityPage.getTotal(), saStoreEntityPage.getPages());
        pageResponse.setContent(buildResponse(saStoreEntityPage.getRecords()));
        return pageResponse;
    }


    @Override
    public List<SaStoreResponse> getSaStoreList(SaStorePageRequest saStorePageRequest) {
        List<SaStoreEntity> saStoreList;
        if (ObjectUtils.isEmpty(saStorePageRequest.getIsCem())) {
            saStoreList = saStoreDao.getSaStoreList(saStorePageRequest);
        } else {
            saStorePageRequest.setPlatformIds(NsyListUtil.toList(PlatformEnum.AMAZON_AMERICA.getCode(), PlatformEnum.AMAZON_BRITAIN.getCode(),
                    PlatformEnum.AMAZON_BRITAIN.getCode(), PlatformEnum.AMAZON_JAPAN.getCode(),
                    PlatformEnum.AMAZON_CANADA.getCode(), PlatformEnum.AMAZON_GERMANY.getCode(), PlatformEnum.AMAZON_SPAIN.getCode(),
                    PlatformEnum.AMAZON_FRANCE.getCode(), PlatformEnum.AMAZON_AUSTRALIA.getCode(), PlatformEnum.AMAZON_EUROPE.getCode(),
                    PlatformEnum.AMAZON_NORTHAMERICA.getCode()
            ));
            saStoreList = saStoreDao.getStoreList(saStorePageRequest);
        }
        if (CollectionUtils.isEmpty(saStoreList)) {
            return Collections.emptyList();
        }
        return BeanUtil.copyToList(saStoreList, SaStoreResponse.class);
    }

    private List<SaStoreResponse> buildResponse(List<StoreResponse> saStoreEntities) {
        return saStoreEntities.stream().map(item -> {
            SaStoreResponse saStoreResponse = new SaStoreResponse();
            BeanUtils.copyProperties(item, saStoreResponse);
            Integer grantStatus = erpPlatformServiceFactory.getErpPlatformService(item.getPlatformId()).getAuthStatus(item.getId());
            saStoreResponse.setGrantStatusName(StoreGrantStatusEnum.getNameByCode(grantStatus));
            saStoreResponse.setStatusName(StoreStatusEnum.getNameByCode(item.getStatus()));
            saStoreResponse.setMarketName(saStoreWebsiteService.getWebsiteName(item.getId()));
            saStoreResponse.setSecondDepartment(item.getSecondDepartment());
            saStoreResponse.setAuthName(StoreAuthStatusEnum.getNameByCode(item.getIsAuth()));
            saStoreResponse.setFincalAccountStatusName(FincalAccountStatusEnum.getNameByCode(item.getFincalAccountStatus()));
            saStoreResponse.setPaymentCardStatusName(PaymentCardStatusEnum.getNameByCode(item.getPaymentCardStatus()));
            saStoreResponse.setOperateMethodName(OperateMethodEnum.getNameByCode(item.getOperateMethod()));
            return saStoreResponse;
        }).collect(Collectors.toList());
    }

    @Override
    public SaStoreDetailResponse getInfo(Integer storeId) {
        SaStoreDetailResponse saStoreDetailResponse = new SaStoreDetailResponse();
        SaStoreEntity saStoreEntity = Optional.ofNullable(saStoreDao.getById(storeId)).orElseThrow(() -> new BusinessServiceException("店铺不存在"));
        BeanUtils.copyProperties(saStoreEntity, saStoreDetailResponse);
        saStoreDetailResponse.setScale(saStoreEntity.getScale() == 0 ? StringUtil.EMPTY : String.valueOf(saStoreEntity.getScale()));
        StoreConfigResponse storeConfigResponse = new StoreConfigResponse();
        saStoreDetailResponse.setDepartment(saStoreEntity.getDepartment());
        saStoreDetailResponse.setDepartmentId(saStoreEntity.getDepartmentId() == 0 ? -1 : saStoreEntity.getDepartmentId());
        saStoreDetailResponse.setSaleAccountId(saStoreEntity.getSaleAccountId() == 0 ? -1 : saStoreEntity.getSaleAccountId());
        saStoreDetailResponse.setWebsites(saStoreWebsiteService.getWebsiteList(storeId));
        saStoreDetailResponse.setAssociatedStoreId(saStoreEntity.getAssociatedStoreId() == 0 ? -1 : saStoreEntity.getAssociatedStoreId());
        saStoreDetailResponse.setSecondPlatformId(saStoreEntity.getSecondPlatformId() == 0 ? -1 : saStoreEntity.getSecondPlatformId());
        saStoreDetailResponse.setTeamId(saStoreEntity.getTeamId() == 0 ? -1 : saStoreEntity.getTeamId());
        saStoreDetailResponse.setDefaultShippingAddressId(saStoreEntity.getDefaultShippingAddressId() == 0 ? -1 : saStoreEntity.getDefaultShippingAddressId());
        saStoreDetailResponse.setStoreType(saStoreEntity.getStoreType() == 0 ? -1 : saStoreEntity.getStoreType());
        saStoreDetailResponse.setNature(saStoreEntity.getNature() == 0 ? -1 : saStoreEntity.getNature());
        saStoreDetailResponse.setGrantStatus(erpPlatformServiceFactory.getErpPlatformService(saStoreEntity.getPlatformId()).getAuthStatus(saStoreEntity.getId()));
        SaStoreConfigEntity saStoreConfigEntity = saStoreConfigService.getInfo(storeId);
        if (!ObjectUtils.isEmpty(saStoreConfigEntity)) {
            BeanUtils.copyProperties(saStoreConfigEntity, storeConfigResponse);
            storeConfigResponse.setSynchronizeOrder(saStoreConfigEntity.getProhibitSynchronizedOrder());
        }
        if (Objects.nonNull(saStoreEntity.getOpenShopDate())) {
            saStoreDetailResponse.setShopAge((int) DateUtil.between(saStoreEntity.getOpenShopDate(), new Date(), DateUnit.DAY));
        } else {
            saStoreDetailResponse.setShopAge(BigDecimal.ZERO.intValue());
        }
        saStoreDetailResponse.setCategoryId(saStoreEntity.getCategoryId() == 0 ? "" : saStoreEntity.getCategoryId().toString());
        saStoreDetailResponse.setConfig(storeConfigResponse);
        List<SaStoreFinanceAccountEntity> paymentCreditCardResponses = saStoreFinanceAccountService.getList(storeId);
        saStoreDetailResponse.setStoreFinanceAccountList(buildStoreFinanceAccountResponses(paymentCreditCardResponses));
        List<SaStoreFinancePaymentAccountEntity> saStoreFinancePaymentAccountEntities = saStoreFinancePaymentAccountService.getList(storeId);
        saStoreDetailResponse.setStoreFinancePaymentAccountList(buildStoreFinancePaymentAccountResponses(saStoreFinancePaymentAccountEntities));

        saStoreDetailResponse.setMarketSetups(saStoreMarketSetService.getList(storeId));
        saStoreDetailResponse.setMarketLists(saStoreWebsiteService.getMarketList(storeId));
        saStoreDetailResponse.setStorePrincipals(saStorePrincipalService.getList(storeId));
        saStoreDetailResponse.setStaffings(saStoreStaffingService.getList(storeId));
        saStoreDetailResponse.setSalesmans(saStoreSalesmanService.getList(storeId));
        saStoreDetailResponse.setUsers(saStoreGoodOperatorService.getList(storeId));
        saStoreDetailResponse.setAdAccounts(saStoreAdAccountMappingService.getList(storeId));
        String canUseInboundApi = saStoreEntity.getCanUseInboundApi();
        List<String> canUseInboundApiList = new ArrayList<>();
        if (StrUtil.isNotEmpty(canUseInboundApi)) {
            canUseInboundApiList = Arrays.stream(canUseInboundApi.split(",")).collect(Collectors.toList());
        }
        saStoreDetailResponse.setCanUseInboundApiList(canUseInboundApiList);
        String shippingChannel = saStoreEntity.getShippingChannel();
        if (StrUtil.isNotEmpty(shippingChannel)) {
            saStoreDetailResponse.setShippingChannel(JSONUtil.toList(shippingChannel, String.class));
            saStoreDetailResponse.setShippingChannelName(String.join(",", saStoreDetailResponse.getShippingChannel()));
        }
        return saStoreDetailResponse;
    }

    private void buildResponseByUserCode(Integer storeId, SaStoreDetailByErpResponse saStoreDetailResponse) {
        List<SysUserPageResponse> sysUserPageResponses = userApiService.getUserList().getContent();
        Map<Integer, String> userCodeMap = sysUserPageResponses.stream().collect(Collectors.toMap(SysUserPageResponse::getUserId, SysUserPageResponse::getUserAccount));
        List<StoreStaffingResponse> storeStaffingResponses = saStoreStaffingService.getList(storeId);
        for (StoreStaffingResponse storeStaffingResponse : storeStaffingResponses) {
            storeStaffingResponse.setUserCode(userCodeMap.get(Integer.valueOf(storeStaffingResponse.getStaffingId())));
        }
        saStoreDetailResponse.setNotifyUserCodeList(storeStaffingResponses);
        List<StorePrincipalResponse> storePrincipalResponses = saStorePrincipalService.getList(storeId);
        storePrincipalResponses.forEach(item -> item.setUserCode(userCodeMap.get(Integer.valueOf(item.getStorePrincipalId()))));
        saStoreDetailResponse.setPrincipals(storePrincipalResponses);
    }

    private List<StoreFinancePaymentAccountResponse> buildStoreFinancePaymentAccountResponses(List<SaStoreFinancePaymentAccountEntity> paymentCreditCardResponses) {
        return paymentCreditCardResponses.stream().map(item -> {
            StoreFinancePaymentAccountResponse storeFinanceAccountResponse = new StoreFinancePaymentAccountResponse();
            storeFinanceAccountResponse.setStoreId(item.getStoreId());
            storeFinanceAccountResponse.setBankId(item.getBankId());
            storeFinanceAccountResponse.setBankName(item.getBankName());
            storeFinanceAccountResponse.setCardNum(item.getCardNum());
            storeFinanceAccountResponse.setCardId(item.getId());
            storeFinanceAccountResponse.setHolderName(item.getHolderName());
            return storeFinanceAccountResponse;
        }).collect(Collectors.toList());
    }

    @NotNull
    private List<StoreFinanceAccountResponse> buildStoreFinanceAccountResponses(List<SaStoreFinanceAccountEntity> saStoreFinanceAccountEntities) {
        return saStoreFinanceAccountEntities.stream().map(item -> {
            StoreFinanceAccountResponse storeFinanceAccountResponse = new StoreFinanceAccountResponse();
            storeFinanceAccountResponse.setAccountId(item.getAccountId());
            storeFinanceAccountResponse.setAccountNum(item.getAccountNum());
            storeFinanceAccountResponse.setPaymentPlatformId(item.getPaymentPlatformId());
            storeFinanceAccountResponse.setPaymentPlatformName(item.getPaymentPlatformName());
            return storeFinanceAccountResponse;
        }).collect(Collectors.toList());
    }


    @Override
    public SaStoreDetailByErpResponse info(StoreInfoRequest storeInfoRequest) {
        log.info("storeInfoRequest 的请求数据为 {}", JsonMapper.toJson(storeInfoRequest));
        SaStoreDetailByErpResponse saStoreDetailResponse = new SaStoreDetailByErpResponse();
        SaStoreEntity saStoreEntity = Optional.ofNullable(saStoreDao.getById(storeInfoRequest.getStoreId())).orElseThrow(() -> new BusinessServiceException("店铺不存在"));
        BeanUtils.copyProperties(saStoreEntity, saStoreDetailResponse, "canUseInboundApi");
        saStoreDetailResponse.setOperator(saStoreEntity.getCreateBy());
        StoreConfigResponse storeConfigResponse = new StoreConfigResponse();
        SaStoreConfigEntity saStoreConfigEntity = saStoreConfigService.getInfo(storeInfoRequest.getStoreId());
        if (!ObjectUtils.isEmpty(saStoreConfigEntity)) {
            BeanUtils.copyProperties(saStoreConfigEntity, storeConfigResponse);
            storeConfigResponse.setSynchronizeOrder(saStoreConfigEntity.getProhibitSynchronizedOrder());
        }
        saStoreDetailResponse.setConfig(storeConfigResponse);
        saStoreDetailResponse.setStoreType(StoreTypeEnum.getNameByCode(saStoreEntity.getStoreType()));
        saStoreDetailResponse.setMarketPlaceList(saStoreMarketSetService.getResponseList(storeInfoRequest.getStoreId()));
        saStoreDetailResponse.setWebsiteIdList(saStoreWebsiteService.getWebsiteIdList(storeInfoRequest.getStoreId()));
        saStoreDetailResponse.setSecondDepartmentName(saStoreEntity.getSecondDepartment());
        String canUseInboundApi = saStoreEntity.getCanUseInboundApi();
        List<String> canUseInboundApiList = new ArrayList<>();
        if (StrUtil.isNotEmpty(canUseInboundApi)) {
            canUseInboundApiList = Arrays.stream(canUseInboundApi.split(",")).collect(Collectors.toList());
        }
        String shippingChannel = saStoreEntity.getShippingChannel();
        if (StrUtil.isNotEmpty(shippingChannel)) {
            saStoreDetailResponse.setShippingChannel(JSONUtil.toList(saStoreEntity.getShippingChannel(), String.class));
            saStoreDetailResponse.setShippingChannelName(String.join(",", saStoreDetailResponse.getShippingChannel()));
        }
        saStoreDetailResponse.setCanUseInboundApiList(canUseInboundApiList);
        buildResponseByUserCode(storeInfoRequest.getStoreId(), saStoreDetailResponse);
        buildStoreAuthResponse(storeInfoRequest, saStoreDetailResponse);
        log.info("SaStoreDetailByErpResponse 店铺id {} 返回的数据为 {}", storeInfoRequest.getStoreId(), NsyJacksonUtils.toJson(saStoreDetailResponse));
        return saStoreDetailResponse;
    }

    @Override
    public BaseListResponse<SaStoreWebsiteResponse> getWebsiteInfo(List<Integer> websiteIds) {
        log.info("getWebsiteInfo 的请求数据为 {}", JsonMapper.toJson(websiteIds));
        List<SaStoreWebsiteEntity> storeWebsiteEntities = saStoreWebsiteDao.getListByWebsiteIds(websiteIds);
        if (CollectionUtils.isEmpty(storeWebsiteEntities)) return BaseListResponse.of(new ArrayList(), 0L);
        Map<Integer, SaStoreEntity> storeEntityMap = saStoreDao.listByIds(storeWebsiteEntities.stream().map(SaStoreWebsiteEntity::getStoreId).collect(Collectors.toList())).stream().collect(Collectors.toMap(SaStoreEntity::getId, Function.identity(), (k1, k2) -> k1));
        List<SaStoreWebsiteResponse> collect = storeWebsiteEntities.stream().map(item -> {
            SaStoreWebsiteResponse response = new SaStoreWebsiteResponse();
            response.setId(item.getId());
            response.setWebsiteId(String.valueOf(item.getWebsiteId()));
            response.setWebsiteName(item.getWebsiteName());
            response.setStoreId(item.getStoreId());
            SaStoreEntity saStoreEntity = storeEntityMap.getOrDefault(item.getStoreId(), new SaStoreEntity());
            response.setDepartment(saStoreEntity.getDepartment());
            response.setStatus(item.getStatus());
            response.setLocation(item.getLocation());
            response.setErpStoreName(saStoreEntity.getErpStoreName());
            return response;
        }).collect(Collectors.toList());
        log.info("getWebsiteInfo 返回的数据为 {}", NsyJacksonUtils.toJson(collect));
        return BaseListResponse.of(collect, (long) collect.size());
    }

    /**
     * AMAZON:亚马逊授权 SHOPIFY:shopify授权  ALI_INTER:阿里国际授权 WMT:沃尔玛授权 TAO_BAO 淘宝授权 WHOLESALE:1688授权 pdd:拼多多授权
     *
     * @param storeInfoRequest
     * @param saStoreDetailResponse
     */
    private void buildStoreAuthResponse(StoreInfoRequest storeInfoRequest, SaStoreDetailByErpResponse saStoreDetailResponse) {
        //todo 暂时没办法只根据平台id进行识别返回相应授权信息
        switch (storeInfoRequest.getType()) {
            case PlatformConstant.AMAZON:
                saStoreDetailResponse.setAmazonConfigResponse(sauAmazonConfigService.getInfoResponse(storeInfoRequest.getStoreId()));
                break;
            case PlatformConstant.ALI_INTER:
                saStoreDetailResponse.setAliInterConfigResponse(sauAliInterConfigService.getInfoResponse(storeInfoRequest.getStoreId()));
                break;
            case PlatformConstant.WMT:
                saStoreDetailResponse.setSauWmtConfigResponse(sauWmtConfigService.getInfoResponse(storeInfoRequest.getStoreId()));
                break;
            case PlatformConstant.TAO_BAO:
                saStoreDetailResponse.setTaobaoConfigResponse(sauTaobaoConfigService.getInfoResponse(storeInfoRequest.getStoreId()));
                break;
            case PlatformConstant.WHOLESALE:
                saStoreDetailResponse.setWholesaleConfigResponse(sauWholesaleConfigService.getInfoResponse(storeInfoRequest.getStoreId()));
                break;
            case PlatformConstant.PDD:
                saStoreDetailResponse.setPddConfigResponse(sauPddConfigService.getInfoResponse(storeInfoRequest.getStoreId()));
                break;
            case PlatformConstant.SHOPIFY:
                saStoreDetailResponse.setShopifyConfigResponse(sauShopifyConfigService.getInfoResponse(storeInfoRequest.getStoreId()));
                break;
            default:
                saStoreDetailResponse.setSauPlatformAuthConfigResponse(sauPlatformAuthConfigService.getInfoResponse(storeInfoRequest.getStoreId()));
                break;
        }
    }

    @Override
    public PageResponse<DistributorsStoreResponse> getDistributorsStoreList(DistributorsStorePageRequest distributorsStoreRequest) {
        Page<SaStoreEntity> saStoreEntityPage = saStoreDao.getDistributorsStoreList(distributorsStoreRequest);
        PageResponse<DistributorsStoreResponse> responsePageResponse = PageResponse.of(saStoreEntityPage.getTotal(), saStoreEntityPage.getPages());
        responsePageResponse.setContent(getDistributorsStoreResponses(saStoreEntityPage));
        return responsePageResponse;
    }

    private List<DistributorsStoreResponse> getDistributorsStoreResponses(Page<SaStoreEntity> saStoreEntityPage) {
        return Optional.ofNullable(saStoreEntityPage.getRecords()).orElse(new ArrayList<>()).stream().map(item -> {
            DistributorsStoreResponse storeResponse = erpExternalService.getStoreResponse(item.getId(), item.getPlatformId());
            storeResponse.setDisAInfoID(item.getId());
            storeResponse.setStoreName(item.getErpStoreName());
            storeResponse.setDisPlatformName(item.getPlatformName());
            storeResponse.setDisALock(item.getStatus());
            storeResponse.setAccountName(item.getErpStoreName());
            return storeResponse;
        }).collect(Collectors.toList());
    }

    @Override
    public List<SaStoreEntity> getByStatusAndPlatformName(Integer status, String platformName) {
        return saStoreDao.getByStatusAndPlatformName(status, platformName);
    }

    /**
     * 获取需要预警的
     */
    @Override
    public List<SaStoreEntity> getNeedWarnPddStore() {
        LambdaQueryWrapper<SaStoreEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SaStoreEntity::getStatus, StatusEnum.NORMAL.getCode());
        List<SauPddConfigEntity> allValidPddCon = pddConfigDao.getByGrantAuthStatus(StatusEnum.NORMAL.getCode());
        if (allValidPddCon.isEmpty()) {
            return new ArrayList<>();
        }
        List<Integer> storeIds = allValidPddCon.stream().map(SauPddConfigEntity::getStoreId).collect(Collectors.toList());
        lambdaQueryWrapper.in(SaStoreEntity::getId, storeIds);
        return saStoreDao.list(lambdaQueryWrapper);
    }

    @Override
    public SaStoreEntity getById(Integer id) {
        return saStoreDao.getById(id);
    }

    @Override
    public SaStoreEntity getByStoreId(Integer storeId) {
        if (Objects.isNull(storeId)) {
            throw new BusinessServiceException("店铺ID为空！");
        }

        SaStoreEntity storeEntity = saStoreDao.getById(storeId);

        if (Objects.isNull(storeEntity)) {
            throw new BusinessServiceException(String.format("查询不到店铺，店铺ID：%s", storeId));
        }
        return storeEntity;
    }

    @Override
    public List<SaStoreResponse> storeList() {
        return Optional.ofNullable(saStoreDao.list()).orElse(new ArrayList<>()).stream().map(item -> {
            SaStoreResponse saStoreResponse = new SaStoreResponse();
            BeanUtils.copyProperties(item, saStoreResponse);
            return saStoreResponse;
        }).collect(Collectors.toList());
    }

    @Override
    public List<Integer> getStoreIds(List<String> storeNames) {
        return Optional.ofNullable(saStoreDao.getListByStoreNames(storeNames)).orElse(new ArrayList<>()).stream().map(SaStoreEntity::getId).collect(Collectors.toList());
    }

    @Override
    public SaStoreListResponse getByPlatformName(Integer status, String platformName) {
        List<SaStoreEntity> saStoreEntities = this.getByStatusAndPlatformName(status, platformName);
        //二级平台
        List<SaStoreEntity> saStoreEntityList = saStoreDao.getByStatusAndSecondPlatformName(status, platformName);
        saStoreEntities.addAll(saStoreEntityList);
        if (CollectionUtils.isEmpty(saStoreEntities)) {
            return new SaStoreListResponse();
        }
        List<SaStoreWebsiteEntity> saStoreWebsiteEntities = saStoreWebsiteDao.getListByStoreIds(saStoreEntities.stream().map(SaStoreEntity::getId).collect(Collectors.toList()));
        List<SaStoreResponse> saStoreResponses = saStoreEntities.stream()
                .filter(ListUtil.distinctByKey(SaStoreEntity::getId))
                .map(s -> {
                    SaStoreResponse saStoreResponse = new SaStoreResponse();
                    saStoreResponse.setStoreName(s.getErpStoreName());
                    saStoreResponse.setId(s.getId());
                    saStoreResponse.setPlatformName(s.getPlatformName());
                    saStoreResponse.setSecondPlatformName(s.getSecondPlatformName());
                    saStoreResponse.setLocation(s.getLocation());
                    SaStoreWebsiteEntity saStoreWebsiteEntity = saStoreWebsiteEntities.stream().filter(sa -> Objects.equals(s.getId(), sa.getStoreId())).findFirst().orElse(new SaStoreWebsiteEntity());
                    saStoreResponse.setWebsiteId(Objects.isNull(saStoreWebsiteEntity.getWebsiteId()) ? NumberConstant.ZERO : saStoreWebsiteEntity.getWebsiteId());
                    return saStoreResponse;
                }).collect(Collectors.toList());
        SaStoreListResponse saStoreListResponse = new SaStoreListResponse();
        saStoreListResponse.setList(saStoreResponses);
        return saStoreListResponse;
    }

    @Override
    public SaStoreListResponse queryStoreByPlatformList(Integer storeStatus, List<String> platformList) {
        SaStoreListResponse response = new SaStoreListResponse();
        response.setList(new ArrayList<>());
        platformList.forEach(platform -> {
            SaStoreListResponse platformResponse = getByPlatformName(storeStatus, platform);
            if (!CollectionUtils.isEmpty(platformResponse.getList())) {
                response.getList().addAll(platformResponse.getList());
            }
        });
        response.setList(response.getList().stream().filter(ListUtil.distinctByKey(SaStoreResponse::getId)).collect(Collectors.toList()));
        return response;
    }

    @Override
    public List<SaStoreEntity> getStoreByStoreNames(List<String> storeNames) {
        return saStoreDao.getListByStoreNames(storeNames);
    }

    @Override
    public List<SelectModel> getOnSaleStoreSelect() {
        String location = loginInfoService.getLocation();
        return saStoreDao.getOnSaleStoreList().stream()
                .filter(e -> location.equalsIgnoreCase(e.getLocation()))
                .map(e -> new SelectModel(e.getId().toString(), e.getErpStoreName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<SelectModel> getSaleStoreByPermission(String platformName) {
        String location = loginInfoService.getLocation();
        List<Integer> permissionStoreIds = accessControlService.isAdmin() ? Collections.emptyList() : accessControlService.doPrivileged(new FbaReplenishmentSkcService.FbaReplenishmentSkcPrivilegeAction(userApiService, loginInfoService.getUserName()));
        return saStoreDao.getOnSaleStoreList().stream()
                .filter(e -> location.equalsIgnoreCase(e.getLocation()))
                .filter(e -> platformName.equalsIgnoreCase(e.getPlatformName()))
                .filter(e -> accessControlService.isAdmin() || permissionStoreIds.contains(e.getId()))
                .map(e -> new SelectModel(e.getId().toString(), e.getErpStoreName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<SaStoreEntity> getSaleStore(String platformName) {
        return saStoreDao.getOnSaleStoreList().stream()
                .filter(e -> platformName.equalsIgnoreCase(e.getPlatformName()))
                .collect(Collectors.toList());
    }


    @Override
    public List<SelectModel> getSelectByLocationAndDepart(String location, String dept) {
        List<SaStoreEntity> locationStore = saStoreDao.getOnSaleStoreList().stream()
                .filter(e -> location.equalsIgnoreCase(e.getLocation())).collect(Collectors.toList());
        //过滤部门
        if (StrUtil.isNotEmpty(dept)) {
            locationStore = locationStore.stream().filter(e -> e.getDepartment().equalsIgnoreCase(dept)).collect(Collectors.toList());
        }
        return locationStore.stream().map(e -> new SelectModel(e.getId().toString(), e.getErpStoreName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<SaStoreResponse> storeListByRequest(SaStoreSearchRequest request) {
        List<SaStoreWebsiteEntity> storeWebsiteEntities = CollectionUtils.isEmpty(request.getWebsiteIdList())
                ? Lists.newArrayList() : saStoreWebsiteDao.getListByWebsiteIds(request.getWebsiteIdList());
        if (CollectionUtils.isEmpty(request.getErpStoreNameList()) && CollectionUtils.isEmpty(storeWebsiteEntities) && CollectionUtils.isEmpty(request.getStoreIdList())) {
            return Collections.emptyList();
        }
        Map<Integer, Integer> websiteMap = storeWebsiteEntities.stream().collect(Collectors.toMap(SaStoreWebsiteEntity::getStoreId, SaStoreWebsiteEntity::getWebsiteId, (a, b) -> a));
        LambdaQueryWrapper<SaStoreEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (!CollectionUtils.isEmpty(request.getErpStoreNameList())) {
            queryWrapper.in(SaStoreEntity::getErpStoreName, request.getErpStoreNameList());
        }
        if (!CollectionUtils.isEmpty(request.getStoreIdList())) {
            queryWrapper.in(SaStoreEntity::getId, request.getStoreIdList());
        }
        if (!CollectionUtils.isEmpty(storeWebsiteEntities)) {
            queryWrapper.in(SaStoreEntity::getId, storeWebsiteEntities.stream().map(SaStoreWebsiteEntity::getStoreId).distinct().collect(Collectors.toList()));
        }
        return Optional.ofNullable(saStoreDao.list(queryWrapper)).orElse(new ArrayList<>()).stream().map(item -> {
            SaStoreResponse saStoreResponse = new SaStoreResponse();
            BeanUtils.copyProperties(item, saStoreResponse);
            saStoreResponse.setWebsiteId(websiteMap.getOrDefault(item.getId(), 0));
            return saStoreResponse;
        }).collect(Collectors.toList());
    }

    @Override
    public List<LxMappingResponse> getStoreListByLocation(String location, String storeId) {
        List<SaStoreEntity> saStoreEntities = saStoreDao.getOnSaleStoreList();
        List<String> storeIds = saStoreEntities.stream()
                .filter(e -> StringUtils.hasText(storeId) ? storeId.equals(String.valueOf(e.getId())) && location.equals(e.getLocation()) : location.equals(e.getLocation()))
                .map(item -> item.getId().toString())
                .collect(Collectors.toList());
        Map<Integer, SaStoreEntity> storeMap = saStoreEntities.stream()
                .filter(e -> location.equals(e.getLocation())).collect(Collectors.toMap(SaStoreEntity::getId, Function.identity()));
        LxMappingQueryRequest lxMappingQueryRequest = new LxMappingQueryRequest();
        lxMappingQueryRequest.setBusinessType(com.nsy.business.base.enums.etl.BusinessTypeEnum.OMS_STORE_LX_INFO_MAPPING.getCode());
        lxMappingQueryRequest.setBusinessNoList(storeIds);
        LOGGER.info("getStoreListByLocation location:{}. request:{}", LocationContext.getLocation(), JsonMapper.toJson(lxMappingQueryRequest));
        List<LxMappingResponse> responses = etlFeignClient.getExternalNoList(lxMappingQueryRequest);
        return Optional.ofNullable(responses).orElse(new ArrayList<>()).stream().filter(item -> item.getLocation().equals(location)).peek(item -> item.setStoreName(storeMap.containsKey(Integer.valueOf(item.getBusinessNo())) ? storeMap.get(Integer.valueOf(item.getBusinessNo())).getErpStoreName() : StringUtil.EMPTY)).collect(Collectors.toList());
    }

    @Override
    public List<SaStoreResponse> getRequireStoreSelect() {
        LambdaQueryWrapper<SaStoreEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SaStoreEntity::getStatus, 1);
        lambdaQueryWrapper.ne(SaStoreEntity::getAchievementAttribution, StringConstant.AMAZON_OVERSEA);
        // 过滤用户订单权限
        if (!loginInfoService.isAdmin()) {
            List<StoreData> storeDataList = erpApiService.getPermissionStoreList(loginInfoService.getUserName());
            if (CollectionUtils.isEmpty(storeDataList)) {
                return Collections.emptyList();
            }
            lambdaQueryWrapper.in(SaStoreEntity::getId, storeDataList.stream().map(StoreData::getStoreId).collect(Collectors.toList()));
        }
        List<SaStoreEntity> list = saStoreDao.list(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<Integer> storeIds = list.stream().map(SaStoreEntity::getId).collect(Collectors.toList());
        List<SaStoreWebsiteEntity> storeWebSiteList = saStoreWebsiteDao.getListByStoreIds(storeIds);
        return list.stream().map(s -> {
            SaStoreResponse response = new SaStoreResponse();
            response.setId(s.getId());
            response.setErpStoreName(s.getErpStoreName());
            response.setDepartment(s.getDepartment());
            response.setPlatformName(s.getPlatformName());
            response.setDepartmentId(s.getDepartmentId());
            response.setWebsiteIdList(storeWebSiteList.stream().filter(w -> w.getStoreId().equals(s.getId())).map(SaStoreWebsiteEntity::getWebsiteId).collect(Collectors.toList()));
            return response;
        }).collect(Collectors.toList());
    }

    @Override
    public List<SalesmanStoreResponse> getSalesmanStoreSelect(SalesmanStoreRequest request) {
        Integer departmentId;
        if (StringUtil.isEmpty(request.getDevelopDepartment())) {
            departmentId = null;
        } else {
            // 特殊case：开款部门的【内贸】对应到这边的部门是：内贸部
            String departmentName = "内贸".equals(request.getDevelopDepartment()) ? "内贸部" : request.getDevelopDepartment();
            // 去用户系统根据部门名称获取部门id
            List<BdDictionaryItem> list = userDictionaryApiService.getDictionaryValues(DictionaryKeySelectEnum.DEPARTMENT.getKey());
            departmentId = list.stream().filter(e -> departmentName.equals(e.getLabel())).findAny().map(e -> Integer.valueOf(e.getValue())).orElse(null);
        }
        // 业绩部门不等于亚马逊海外仓,海外代发,沃尔玛WFS,张年栋,赠品
        List<String> achievementAttributionList = Arrays.asList("亚马逊海外仓", "海外代发", "沃尔玛WFS", "张年栋", "赠品");
        // 平台id不等于48, 41, 56
        List<Integer> platformIds = Arrays.asList(48, 41, 56);
        LambdaQueryWrapper<SaStoreEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(BaseMpEntity::getLocation, loginInfoService.getLocation())
                .eq(SaStoreEntity::getStatus, 1).eq(departmentId != null, SaStoreEntity::getDepartmentId, departmentId)
                .notIn(SaStoreEntity::getAchievementAttribution, achievementAttributionList)
                .notIn(SaStoreEntity::getPlatformId, platformIds);
        // 过滤用户订单权限
        if (!loginInfoService.isAdmin() && request.isFilterUserPermission()) {
            List<StoreData> storeDataList = erpApiService.getPermissionStoreList(loginInfoService.getUserName());
            if (CollectionUtils.isEmpty(storeDataList)) {
                return Collections.emptyList();
            }
            lambdaQueryWrapper.in(SaStoreEntity::getId, storeDataList.stream().map(StoreData::getStoreId).collect(Collectors.toList()));
        }
        List<SaStoreEntity> storeEntities = saStoreDao.list(lambdaQueryWrapper);
        if (storeEntities.isEmpty()) {
            return Collections.emptyList();
        }
        Map<Integer, List<SkuPrefixDto>> skuPrefixDtoMap = amazonApiService
                .getSkuPrefixListByStoreIds(storeEntities.stream().map(SaStoreEntity::getId).collect(Collectors.toList()))
                .stream().collect(Collectors.groupingBy(SkuPrefixDto::getAmazonStoreId));
        List<SalesmanStoreResponse> result = new ArrayList<>();
        for (SaStoreEntity storeEntity : storeEntities) {
            if (skuPrefixDtoMap.containsKey(storeEntity.getId())) {
                skuPrefixDtoMap.get(storeEntity.getId()).forEach(skuPrefixDto -> result.add(buildSalesmanStoreResponse(storeEntity, skuPrefixDto)));
            } else {
                result.add(buildSalesmanStoreResponse(storeEntity, null));
            }
        }
        return result.stream().filter(ListUtil.distinctByKey(SalesmanStoreResponse::getLabel)).collect(Collectors.toList());
    }

    private SalesmanStoreResponse buildSalesmanStoreResponse(SaStoreEntity storeEntity, SkuPrefixDto skuPrefixDto) {
        SalesmanStoreResponse response = new SalesmanStoreResponse();
        response.setStoreId(storeEntity.getId());
        response.setStoreName(storeEntity.getErpStoreName());
        if (skuPrefixDto != null) {
            response.setSalesmanUserName(skuPrefixDto.getUserAccount());
            response.setSalesmanName(skuPrefixDto.getUserName());
            response.setLabel(String.format("%s-%s", storeEntity.getErpStoreName(), skuPrefixDto.getUserName()));
            response.setValue(String.format("%s-%s", storeEntity.getId().toString(), skuPrefixDto.getUserAccount()));
        } else {
            response.setSalesmanUserName(StringConstant.EMPTY);
            response.setSalesmanName(StringConstant.EMPTY);
            response.setLabel(storeEntity.getErpStoreName());
            response.setValue(storeEntity.getId().toString());
        }
        return response;
    }

    @Override
    public void updateStoreIsAuthStatus(Integer storeId) {
        SaStoreEntity saStoreEntity = saStoreDao.getById(storeId);
        BdPlatformEntity bdPlatformEntity = bdPlatformService.getInfo(saStoreEntity.getPlatformId());
        saStoreEntity.setIsAuth(bdPlatformEntity.getIsAuth() == 1 ? 1 : 2);
        saStoreDao.updateById(saStoreEntity);
    }

    @Override
    public List<StoreDTO> getOrderGrabStoreIds(String platform, Integer fetchCount, String location) {
        return saStoreDao.getOrderGrabStoreIds(OrderGrabPlatformEnum.getCodes(platform), fetchCount, location);
    }

    /**
     * 根据平台Id或者店铺Id 区域查询符合条件的抓单店铺
     */
    @Override
    public List<StoreDTO> getOrderGrabStoreIdList(String platform, Integer storeId, String location, List<Integer> notInStoreIdList) {
        return saStoreDao.getOrderGrabStoreIdList(PlatformTypeEnum.getPlatformIdList(platform), storeId, location, notInStoreIdList);
    }

    @Override
    public List<SelectModel> getB2bWebsiteSelect(B2BStoreSearchRequest request) {
        List<SelectModel> selectModelList = getB2bStoreSelect(request);
        if (CollectionUtils.isEmpty(selectModelList)) {
            return Collections.emptyList();
        }
        List<Integer> storeIds = selectModelList.stream().map(s -> Integer.valueOf(s.getValue())).collect(Collectors.toList());
        List<SaStoreWebsiteEntity> storeWebsiteEntityList = saStoreWebsiteDao.getListByStoreIds(storeIds);
        Map<Integer, String> map = new HashMap<>();
        List<SelectModel> selectModels = new ArrayList<>();
        for (SaStoreWebsiteEntity websiteEntity : storeWebsiteEntityList) {
            if (!map.containsKey(websiteEntity.getWebsiteId())) {
                map.put(websiteEntity.getWebsiteId(), websiteEntity.getWebsiteName());
                SelectModel selectModel = new SelectModel();
                selectModel.setLabel(websiteEntity.getWebsiteName());
                selectModel.setValue(websiteEntity.getWebsiteId().toString());
                selectModel.setAttribute(websiteEntity.getStoreId().toString());
                selectModels.add(selectModel);
            }
        }
        return selectModels;
    }

    @Override
    public List<SelectModel> getEtlStoreSelect(EtlStoreSearchRequest request) {
        List<SelectModel> list = new ArrayList<>();
        List<SaStoreEntity> saStoreEntities = saStoreDao.list(new LambdaQueryWrapper<SaStoreEntity>()
                .eq(StringUtil.isNotBlank(request.getDepartment()), SaStoreEntity::getDepartment, request.getDepartment())
                .eq(StringUtil.isNotBlank(request.getSecondDepartment()), SaStoreEntity::getSecondDepartment, request.getSecondDepartment())
                .eq(Objects.nonNull(request.getPlatformId()), SaStoreEntity::getPlatformId, request.getPlatformId())
        );
        if (!CollectionUtils.isEmpty(saStoreEntities)) {
            saStoreEntities.forEach(item -> {
                SelectModel selectModel = new SelectModel();
                selectModel.setId(item.getId().toString());
                selectModel.setValue(item.getId().toString());
                selectModel.setLabel(item.getErpStoreName());
                list.add(selectModel);
            });
        }
        return list;
    }

    @Override
    public List<SelectModel> getB2bStoreSelect(B2BStoreSearchRequest request) {
        LambdaQueryWrapper<SaStoreEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(BaseMpEntity::getLocation, loginInfoService.getLocation())
                .eq(SaStoreEntity::getStatus, 1);
        // 过滤用户订单权限
        if (!loginInfoService.isAdmin() && request.isFilterUserPermission()) {
            List<StoreData> storeDataList = erpApiService.getPermissionStoreList(loginInfoService.getUserName());
            if (CollectionUtils.isEmpty(storeDataList)) {
                return Collections.emptyList();
            }
            lambdaQueryWrapper.in(SaStoreEntity::getId, storeDataList.stream().map(StoreData::getStoreId).collect(Collectors.toList()));
        }
        lambdaQueryWrapper.and(wrapper -> wrapper
                .eq(SaStoreEntity::getDepartment, "B2B")
                .or()
                .eq(SaStoreEntity::getSecondPlatformName, "B2B")
        );
        List<SaStoreEntity> storeEntityList = saStoreDao.list(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(storeEntityList)) {
            return Collections.emptyList();
        }
        List<Integer> storeIds = storeEntityList.stream().map(SaStoreEntity::getId).collect(Collectors.toList());
        List<SaStoreWebsiteEntity> storeWebsiteEntityList = saStoreWebsiteDao.getListByStoreIds(storeIds);
        return storeEntityList.stream().map(s -> {
            SelectModel selectModel = new SelectModel();
            selectModel.setLabel(s.getErpStoreName());
            selectModel.setValue(s.getId().toString());
            storeWebsiteEntityList.stream().filter(w -> w.getStoreId().equals(s.getId())).findAny().ifPresent(w -> {
                selectModel.setAttribute(w.getWebsiteId().toString());
            });
            return selectModel;
        }).collect(Collectors.toList());
    }

    @Override
    public List<SaStoreEntity> listByIds(List<Integer> storeIds) {
        return saStoreDao.listByIds(storeIds);
    }

    @Transactional(rollbackFor = BusinessServiceException.class)
    @Override
    public void addAccount(SaAccountApplyEntity saAccountApplyEntity) {
        List<BdDictionaryItem> bdDictionaryItems = userDictionaryApiService.getDictionaryValues("oms_second_department_" + saAccountApplyEntity.getDepartmentId());
        Map<String, String> secondDepartmentMap = Optional.of(bdDictionaryItems).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(v -> String.valueOf(v.getLabel()), v -> v.getValue()));
        BdPlatformEntity bdPlatformEntity = bdPlatformService.getInfo(saAccountApplyEntity.getPlatformId());
        saAccountApplyEntity.setPlatformName(bdPlatformEntity.getName());
        LocationContext.setLocation(loginInfoService.getLocation());
        try {
            Integer saleAccountId = saSaleAccountService.addSaleAccount(saAccountApplyEntity, secondDepartmentMap);
            if (StringUtil.isEmpty(saAccountApplyEntity.getStoreBase())) {
                return;
            }
            List<SaAccountInfoRequest> requests = JSONUtil.toList(saAccountApplyEntity.getStoreBase(), SaAccountInfoRequest.class);
            if (CollectionUtils.isEmpty(requests)) {
                return;
            }
            BdStoreCreateTemplateConfigEntity bdStoreCreateTemplateConfigEntity = bdStoreCreateTemplateConfigService.getConfigByPlatformId(saAccountApplyEntity.getPlatformId());
            List<BdDictionaryItem> list = userDictionaryApiService.getDictionaryValues(DictionaryKeySelectEnum.DEPARTMENT.getKey());
            Map<String, String> departmentMap = list.stream().collect(Collectors.toMap(v -> String.valueOf(v.getLabel()), v -> v.getValue()));
            if (hasDuplicateMarketListIds(requests)) {
                throw new BusinessServiceException("一个市场只能绑定一个店铺");
            }
            Optional.of(requests).orElse(new ArrayList<>()).forEach(item -> {
                if (saStoreDao.isExists(item.getErpStoreName())) {
                    throw new BusinessServiceException("ERP店铺名称已经存在");
                }
                SaStoreEntity saStoreEntity = buildSaStoreEntity(saAccountApplyEntity, saleAccountId, item);
                saStoreEntity.setPlatformName(bdPlatformEntity.getName());
                log.info("addAccount {} {}", saAccountApplyEntity.getSecondDepartment(), Integer.valueOf(secondDepartmentMap.getOrDefault(saAccountApplyEntity.getSecondDepartment(), "0")));
                saStoreEntity.setSecondPlatformId(Integer.valueOf(secondDepartmentMap.getOrDefault(saAccountApplyEntity.getPlatformId(), "0")));
                saStoreEntity.setIsAuth(bdPlatformEntity.getIsAuth() == 1 ? 0 : 2);
                saStoreEntity.setCanUseInboundApi(String.join(",", bdStoreCreateTemplateConfigEntity.getCanUseInboundApi()));
                saStoreDao.save(saStoreEntity);
                BdWebsiteCreateRuleEntity bdWebsiteCreateRuleEntity = null;
                SaStoreRequest saStoreRequest = getSaStoreRequest(saAccountApplyEntity, bdStoreCreateTemplateConfigEntity, departmentMap);
                saStoreRequest.setErpStoreName(item.getErpStoreName());
                if (!ObjectUtils.isEmpty(saStoreEntity.getSecondPlatformId())) {
                    bdWebsiteCreateRuleEntity = bdWebsiteCreateRuleService.getWebsiteRule(saStoreEntity.getSecondPlatformId());
                }
                if (ObjectUtils.isEmpty(bdWebsiteCreateRuleEntity)) {
                    bdWebsiteCreateRuleEntity = bdWebsiteCreateRuleService.getWebsiteRule(saStoreEntity.getPlatformId());
                }
                //存在站点生成规则就需要进行创建站点
                if (!ObjectUtils.isEmpty(bdWebsiteCreateRuleEntity) && loginInfoService.getLocation().toUpperCase(Locale.ROOT).equals(LocationEnum.QUANZHOU.name())) {
                    markMarkPlaceList(item, saStoreEntity, saStoreRequest);
                    createWebSiteInfo(saStoreRequest, saStoreEntity, bdWebsiteCreateRuleEntity, bdPlatformEntity, 0);
                }

                Integer fbsStoreId = isNeedCreateFbaStore(bdPlatformEntity.getName(), item.getAchievementAttribution()) ? createAssociatedStore(saStoreEntity) : null;
                if (isNeedSaveOrUpdateStoreMarketSet(saAccountApplyEntity.getPlatformId())) {
                    saStoreMarketSetService.saveStoreMarket(saStoreRequest, saStoreEntity.getId());
                }
                makeStaffing(item, saStoreRequest);
                saStoreStaffingService.saveStoreStaffing(saStoreRequest, saStoreEntity.getId());
                StoreConfigRequest storeConfigRequest = new StoreConfigRequest();
                BeanUtils.copyProperties(bdStoreCreateTemplateConfigEntity, storeConfigRequest);
                saStoreRequest.setConfig(storeConfigRequest);
                saStoreConfigService.saveSaStoreConfig(saStoreRequest, saStoreEntity.getId(), fbsStoreId);
                baseService.sendQueue(saStoreEntity.getId(), StringUtil.EMPTY);
                if (!ObjectUtils.isEmpty(fbsStoreId)) {
                    baseService.sendQueue(fbsStoreId, StringUtil.EMPTY);
                }
                bdOperateLogService.addLog(BusinessTypeEnum.STORE.getCode(), saStoreEntity.getId());
            });
        } catch (Exception e) {
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    @NotNull
    private SaStoreEntity buildSaStoreEntity(SaAccountApplyEntity saAccountApplyEntity, Integer saleAccountId, SaAccountInfoRequest item) {
        SaStoreEntity saStoreEntity = new SaStoreEntity();
        BeanUtils.copyProperties(saAccountApplyEntity, saStoreEntity);
        saStoreEntity.setAchievementAttribution(item.getAchievementAttribution());
        saStoreEntity.setErpStoreName(item.getErpStoreName());
        saStoreEntity.setBrand(saAccountApplyEntity.getBrand());
        saStoreEntity.setCreateBy(loginInfoService.getName());
        saStoreEntity.setDepartmentId(Integer.valueOf(saAccountApplyEntity.getDepartmentId()));
        saStoreEntity.setDistributionStoreFlag(SaStoreDistributionStoreFlagEnum.NO.getCode());
        //开店时间默认不填就是创建时间
        saStoreEntity.setOpenShopDate(DateUtil.date());
        saStoreEntity.setSaleAccountId(saleAccountId);
        saStoreEntity.setSkuRule(StringUtil.EMPTY);
        saStoreEntity.setStatus(StoreStatusEnum.OPEN.getCode());
        saStoreEntity.setPlatformId(saAccountApplyEntity.getPlatformId());
        //一个店铺只能被关联一次
        saStoreEntity.setSecondDepartment(saAccountApplyEntity.getSecondDepartment());
        return saStoreEntity;
    }

    private void makeStaffing(SaAccountInfoRequest item, SaStoreRequest saStoreRequest) {
        Optional.of(item.getStaffings()).orElse(new ArrayList<>()).forEach(item2 -> {
            item2.setStatus(1);
            SysUserInfo sysUserInfo = userApiService.getUserByUserId(item2.getStaffingId());
            item2.setStaffingName(sysUserInfo.getUserName());
            item2.setUserCode(sysUserInfo.getUserName());
        });
        saStoreRequest.setStaffings(item.getStaffings());
    }

    private void markMarkPlaceList(SaAccountInfoRequest item, SaStoreEntity saStoreEntity, SaStoreRequest saStoreRequest) {
        saStoreRequest.setMarketLists(Optional.of(item.getMarketLists()).orElse(new ArrayList<>()).stream().map(item2 -> {
            StoreMarketRequest storeMarketRequest = new StoreMarketRequest();
            storeMarketRequest.setStoreId(saStoreEntity.getId());
            storeMarketRequest.setMarketListId(item2.getMarketListId());
            storeMarketRequest.setMarketListName(item2.getMarketListName());
            storeMarketRequest.setStatus(1);
            return storeMarketRequest;
        }).collect(Collectors.toList()));
    }

    public static boolean hasDuplicateMarketListIds(List<SaAccountInfoRequest> storeList) {
        Set<String> marketListIds = new HashSet<>();
        return storeList.stream()
                .flatMap(store -> store.getMarketLists().stream())
                .anyMatch(marketList -> !marketListIds.add(marketList.getMarketListId()));
    }

    @NotNull
    private SaStoreRequest getSaStoreRequest(SaAccountApplyEntity saAccountApplyEntity, BdStoreCreateTemplateConfigEntity bdStoreCreateTemplateConfigEntity, Map<String, String> departmentMap) {
        SaStoreRequest saStoreRequest = new SaStoreRequest();
        BeanUtils.copyProperties(bdStoreCreateTemplateConfigEntity, saStoreRequest);
        saStoreRequest.setPlatformId(saAccountApplyEntity.getPlatformId());
        saStoreRequest.setPlatformName(saAccountApplyEntity.getPlatformName());
        saStoreRequest.setDepartment(saAccountApplyEntity.getDepartment());
        saStoreRequest.setDepartmentId(Integer.valueOf(departmentMap.get(saAccountApplyEntity.getDepartment())));
        saStoreRequest.setSecondDepartmentName(saAccountApplyEntity.getSecondDepartment());
        return saStoreRequest;
    }

    @Override
    public List<SaStoreEntity> getListBySaMediaAccountId(Integer mediaAccountId) {
        return saStoreDao.list(Wrappers.<SaStoreEntity>lambdaQuery().eq(SaStoreEntity::getMediaAccountId, mediaAccountId));
    }

    @Override
    public List<SaStoreEntity> getListBySaLiveAccountId(Integer liveAccountId) {
        return saStoreDao.list(Wrappers.<SaStoreEntity>lambdaQuery().eq(SaStoreEntity::getLiveAccountId, liveAccountId));
    }

    @Override
    public List<SaStoreEntity> getListBySaAdAccountId(Integer adAccountId) {
        return saStoreDao.list(Wrappers.<SaStoreEntity>lambdaQuery().eq(SaStoreEntity::getAdAccountId, adAccountId));
    }


    @Override
    public void changeAllocateUser(SaMediaAccountRequest request) {
        LocationContext.setLocation(loginInfoService.getLocation());
        List<SaStoreEntity> saStoreEntities = saStoreDao.list(Wrappers.<SaStoreEntity>lambdaQuery().in(SaStoreEntity::getId, request.getIdList()));
        Optional.of(saStoreEntities).orElse(new ArrayList<>()).forEach(item -> {
            try {
                SaAccountApplyEntity saAccountApplyEntity = new SaAccountApplyEntity();
                saAccountApplyEntity.setApplyType(ApplyTypeEnum.CHANGE.getCode());
                saAccountApplyEntity.setAccountType(AccountTypeEnum.STORE_ACCOUNT.getCode());
                saAccountApplyEntity.setAccount(item.getErpStoreName());
                saAccountApplyEntity.setDepartmentId(item.getDepartmentId().toString());
                saAccountApplyEntity.setAllocateUser(String.valueOf(loginInfoService.getUserId()));
                saAccountApplyEntity.setDepartment(item.getDepartment());
                saAccountApplyEntity.setRemark(request.getRemark());
                saAccountApplyEntity.setSecondDepartment(item.getSecondDepartment());
                saAccountApplyEntity.setOldStoreStaffings(request.getOldStoreStaffings());
                saAccountApplyEntity.setStoreStaffings(request.getStoreStaffings());
                saAccountApplyEntity.setCreateBy(loginInfoService.getUserName());
                saAccountApplyEntity.setLocation(loginInfoService.getLocation());
                saAccountApplyService.save(saAccountApplyEntity);
                bdOperateLogService.updateLog(BusinessTypeEnum.SM_MEDIA_ACCOUNT.name(), saAccountApplyEntity.getApplyId(), "发起变更请求" + saAccountApplyEntity.getApplyId());
                BusinessInfo businessInfo = makeBusinessInfo(saAccountApplyEntity);
                List<TaskResponse> responses = activitiApiService.tasksByBusinessKey(businessInfo.getBusinessKey());
                if (CollectionUtil.isNotEmpty(responses)) {
                    saAccountApplyEntity.setStatus(AccountApplyStatusEnum.BUSINESS_CHECK.getCode());
                    saAccountApplyService.commonOperaActivitiAndUpdateSaAccountApplyEntity(saAccountApplyEntity, responses, new HashMap<>(), ActivitiProcessDefinitionKeyEnum.medic_account_change.name(), true);
                    bdOperateLogService.updateLog(BusinessTypeEnum.SM_ACCOUNT_APPLY.name(), saAccountApplyEntity.getApplyId(), "状态变为待事业部审核");
                }
            } catch (Exception e) {
                throw new BusinessServiceException(e.getMessage(), e);
            }
        });
    }

    @NotNull
    private BusinessInfo makeBusinessInfo(SaAccountApplyEntity saAccountApplyEntity) {
        BatchStartProcessRequest batchStartProcessRequest = new BatchStartProcessRequest();
        BusinessInfo businessInfo = new BusinessInfo();
        batchStartProcessRequest.setProcessDefinitionKey(ActivitiProcessDefinitionKeyEnum.medic_account_change.name());
        batchStartProcessRequest.setCompanyId("1");
        batchStartProcessRequest.setCreateBy(loginInfoService.getUserName());
        batchStartProcessRequest.setCreateName(loginInfoService.getName());
        businessInfo.setSearchCode(String.format("任务ID：%s，账号：%s，社媒号变更审批", saAccountApplyEntity.getApplyId(), saAccountApplyEntity.getAccount()));
        businessInfo.setBusinessKey(String.format("%s:%s", ActivitiProcessDefinitionKeyEnum.medic_account_change.getPrefix(), saAccountApplyEntity.getApplyId().toString()));
        batchStartProcessRequest.setBusinessInfo(Collections.singletonList(businessInfo));
        activitiApiService.startProcessInstanceByKey(batchStartProcessRequest);
        return businessInfo;
    }

    @Override
    public void updateAllocateUser(SaAccountApplyEntity saAccountApplyEntity) {
        LocationContext.setLocation(saAccountApplyEntity.getLocation());
        SaStoreEntity saStoreEntity = saStoreDao.getOne(Wrappers.<SaStoreEntity>lambdaQuery()
                .eq(SaStoreEntity::getErpStoreName, saAccountApplyEntity.getAccount()));
        saStoreStaffingService.updateStoreStaffingStatus(saStoreEntity.getId(), saAccountApplyEntity.getOldStoreStaffings());
        saStoreStaffingService.addStoreStaffing(saStoreEntity.getId(), saAccountApplyEntity.getStoreStaffings(), loginInfoService.getLocation());
        bdOperateLogService.updateLog(BusinessTypeEnum.STORE.getCode(), saStoreEntity.getId(), "通过变更流程进行变更，申请id" + saAccountApplyEntity.getApplyId());
        SysUserInfo sysUserInfo = userApiService.getUserByUserId(Integer.valueOf(saAccountApplyEntity.getStoreStaffings()));
        String content = "账号类型：店铺类型，账号：" + saAccountApplyEntity.getAccount() + "，于" + DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN) + "商品通知配置人员 变更后:" + sysUserInfo.getUserName() + "，审批通过，请悉知！";
        notifyApiService.sendMarkdownDingTalkMessage("变更通知", content, Arrays.stream(MediaAccountNoticeUserEnum.values()).map(MediaAccountNoticeUserEnum::getCode).collect(Collectors.toList()));
    }

    @Override
    public void closeApply(SaMediaAccountRequest request) {
        LocationContext.setLocation(loginInfoService.getLocation());
        List<SaStoreEntity> saStoreEntities = saStoreDao.list(Wrappers.<SaStoreEntity>lambdaQuery().in(SaStoreEntity::getId, request.getIdList()));
        Optional.of(saStoreEntities).orElse(new ArrayList<>()).forEach(item -> {
            try {
                SaAccountApplyEntity saAccountApplyEntity = new SaAccountApplyEntity();
                saAccountApplyEntity.setApplyType(ApplyTypeEnum.CLOSE_ACCOUNT_APPLY.getCode());
                saAccountApplyEntity.setAccountType(AccountTypeEnum.STORE_ACCOUNT.getCode());
                saAccountApplyEntity.setAccount(item.getErpStoreName());
                saAccountApplyEntity.setRemark(request.getRemark());
                saAccountApplyEntity.setApplyRemark(request.getApplyRemark());
                saAccountApplyEntity.setPlatformId(item.getPlatformId());
                saAccountApplyEntity.setPlatformName(item.getPlatformName());
                saAccountApplyEntity.setDepartmentId(item.getDepartmentId().toString());
                saAccountApplyEntity.setDepartment(item.getDepartment());
                saAccountApplyEntity.setSecondDepartment(item.getSecondDepartment());
                saAccountApplyEntity.setOldStoreStaffings(request.getOldStoreStaffings());
                saAccountApplyEntity.setStoreStaffings(request.getStoreStaffings());
                saAccountApplyEntity.setCreateBy(loginInfoService.getUserName());
                saAccountApplyEntity.setAllocateUser(String.valueOf(loginInfoService.getUserId()));
                saAccountApplyEntity.setLocation(loginInfoService.getLocation());
                saAccountApplyService.save(saAccountApplyEntity);
                bdOperateLogService.updateLog(BusinessTypeEnum.STORE.name(), saAccountApplyEntity.getApplyId(), "发起关店请求" + saAccountApplyEntity.getApplyId());
                BusinessInfo businessInfo = makeBusinessInfoByClose(saAccountApplyEntity);
                List<TaskResponse> responses = activitiApiService.tasksByBusinessKey(businessInfo.getBusinessKey());
                if (CollectionUtil.isNotEmpty(responses)) {
                    saAccountApplyEntity.setStatus(AccountApplyStatusEnum.BUSINESS_CHECK.getCode());
                    saAccountApplyService.commonOperaActivitiAndUpdateSaAccountApplyEntity(saAccountApplyEntity, responses, new HashMap<>(), ActivitiProcessDefinitionKeyEnum.medic_account_close_apply.name(), true);
                    bdOperateLogService.updateLog(BusinessTypeEnum.SM_ACCOUNT_APPLY.name(), saAccountApplyEntity.getApplyId(), "状态变为待事业部审核");
                }
            } catch (Exception e) {
                throw new BusinessServiceException(e.getMessage(), e);
            }
        });
    }

    @NotNull
    private BusinessInfo makeBusinessInfoByClose(SaAccountApplyEntity saAccountApplyEntity) {
        BatchStartProcessRequest batchStartProcessRequest = new BatchStartProcessRequest();
        BusinessInfo businessInfo = new BusinessInfo();
        batchStartProcessRequest.setProcessDefinitionKey(ActivitiProcessDefinitionKeyEnum.medic_account_close_apply.name());
        batchStartProcessRequest.setCompanyId("1");
        batchStartProcessRequest.setCreateBy(loginInfoService.getUserName());
        batchStartProcessRequest.setCreateName(loginInfoService.getName());
        businessInfo.setSearchCode(String.format("任务ID：%s，账号：%s,社媒号关店审批", saAccountApplyEntity.getApplyId(), saAccountApplyEntity.getAccount()));
        businessInfo.setBusinessKey(String.format("%s:%s", ActivitiProcessDefinitionKeyEnum.medic_account_close_apply.getPrefix(), saAccountApplyEntity.getApplyId().toString()));
        batchStartProcessRequest.setBusinessInfo(Collections.singletonList(businessInfo));
        activitiApiService.startProcessInstanceByKey(batchStartProcessRequest);
        return businessInfo;
    }

    @NotNull
    private BusinessInfo makeBusinessInfoByCard(SaAccountApplyEntity saAccountApplyEntity) {
        BatchStartProcessRequest batchStartProcessRequest = new BatchStartProcessRequest();
        BusinessInfo businessInfo = new BusinessInfo();
        batchStartProcessRequest.setProcessDefinitionKey(ActivitiProcessDefinitionKeyEnum.medic_store_card_change.name());
        batchStartProcessRequest.setCompanyId("1");
        batchStartProcessRequest.setCreateBy(loginInfoService.getUserName());
        batchStartProcessRequest.setCreateName(loginInfoService.getName());
        businessInfo.setSearchCode(String.format("任务ID：%s，账号：%s，付款信用卡变更", saAccountApplyEntity.getApplyId(), saAccountApplyEntity.getAccount()));
        businessInfo.setBusinessKey(String.format("%s:%s", ActivitiProcessDefinitionKeyEnum.medic_store_card_change.getPrefix(), saAccountApplyEntity.getApplyId().toString()));
        batchStartProcessRequest.setBusinessInfo(Collections.singletonList(businessInfo));
        activitiApiService.startProcessInstanceByKey(batchStartProcessRequest);
        return businessInfo;
    }

    @Override
    public void updateStatus(SaAccountApplyEntity saAccountApplyEntity) {
        LocationContext.setLocation(saAccountApplyEntity.getLocation());
        SaStoreEntity saStoreEntity = saStoreDao.getOne(Wrappers.<SaStoreEntity>lambdaQuery()
                .eq(SaStoreEntity::getErpStoreName, saAccountApplyEntity.getAccount()));
        saStoreEntity.setStatus(StoreStatusEnum.CLOSE.getCode());
        saStoreDao.updateById(saStoreEntity);
        baseService.sendQueue(saStoreEntity.getId(), StringUtil.EMPTY);

        if (isNeedCreateFbaStore(saStoreEntity.getPlatformName(), saStoreEntity.getAchievementAttribution()) && saStoreEntity.getAssociatedStoreId() > 0) {
            SaStoreEntity fbaSaStoreEntity = saStoreDao.getById(saStoreEntity.getAssociatedStoreId());
            fbaSaStoreEntity.setStatus(StoreStatusEnum.CLOSE.getCode());
            saStoreDao.updateById(fbaSaStoreEntity);
            baseService.sendQueue(fbaSaStoreEntity.getId(), StringUtil.EMPTY);
        }
        bdOperateLogService.updateLog(BusinessTypeEnum.STORE.getCode(), saStoreEntity.getId(), "通过申请关店流程进行关店，申请id" + saAccountApplyEntity.getApplyId());
        if (saStoreDao.count(Wrappers.<SaStoreEntity>lambdaQuery()
                .ne(SaStoreEntity::getId, saStoreEntity.getId())
                .eq(SaStoreEntity::getSaleAccountId, saStoreEntity.getSaleAccountId())) < 1) {
            SaSaleAccountEntity saSaleAccountEntity = saSaleAccountService.getSaleAccountId(saStoreEntity.getSaleAccountId());
            saSaleAccountEntity.setEnable(0);
            saSaleAccountService.updateSaleAccountById(saSaleAccountEntity);
            bdOperateLogService.updateLog(BusinessTypeEnum.SALE_ACCOUNT.getCode(), saSaleAccountEntity.getId(), "通过申请关店流程进行关店，申请id" + saAccountApplyEntity.getApplyId());
        }
        String content = "账号类型：店铺号，账号：" + saAccountApplyEntity.getAccount() + "，于" + DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN) + "申请关店，审批通过，请悉知";
        notifyApiService.sendMarkdownDingTalkMessage("关店通知", content, Arrays.stream(MediaAccountNoticeUserEnum.values()).map(MediaAccountNoticeUserEnum::getCode).collect(Collectors.toList()));
        List<SaStoreGoodOperatorEntity> saStoreGoodOperatorEntities = saStoreGoodOperatorService.getListByStoreId(saStoreEntity.getId());
        if (CollectionUtil.isNotEmpty(saStoreGoodOperatorEntities)) {
            notifyApiService.sendMarkdownDingTalkMessage("关店通知", content, Optional.of(saStoreGoodOperatorEntities).orElse(new ArrayList<>()).stream().map(item -> {
                SysUserInfo sysUserInfo = userApiService.getUserByUserId(item.getUserId());
                return sysUserInfo.getUserAccount();
            }).collect(Collectors.toList()));
        }
    }

    @Override
    public List<SaStoreEntity> getListByAccountSubjectId(Integer id) {
        return saStoreDao.getListByAccountSubjectId(id);
    }

    @Override
    public List<SaStoreStaffingEntity> getStoreStaffingList(Integer storeId) {
        return saStoreStaffingService.getStoreStaffingList(storeId);
    }

    @Override
    public List<StoreExceptionDTO> getStoreException() {
        return saStoreDao.getStoreException();
    }

    @Override
    public BaseListResponse<SelectModel> storePermissionSelectList(StorePermissionSelectRequest request) {
        List<SaStoreEntity> list;
        List<Integer> statusList = StoreSelectModeEnum.getStatusList(request.getStatusNameList());
        if (accessControlService.isAdmin()) {
            list = saStoreDao.getListByStatusList(statusList);
        } else {
            UserStore userStore = erpApiService.getErpUserStoreResponse(accessControlService.getUserName());
            if (userStore.getIsAllStores()) {
                list = saStoreDao.getListByStatusList(statusList);
            } else {
                List<Integer> storeIdList = userStore.getStoreIdList();
                list = saStoreDao.getListByStatusListAndStoreIdList(statusList, storeIdList);
            }
        }
        if (CollUtil.isEmpty(list)) return BaseListResponse.of(0L);

        List<SelectModel> selectModels = list.stream()
                .map(saStoreEntity -> new SelectModel(saStoreEntity.getId().toString(), saStoreEntity.getErpStoreName(), saStoreEntity.getErpStoreName(), saStoreEntity.getErpStoreName()))
                .collect(Collectors.toList());

        return BaseListResponse.of(selectModels, (long) selectModels.size());
    }

    @Override
    public void changerCardInfo(SaMediaAccountRequest request) {
        LocationContext.setLocation(loginInfoService.getLocation());
        List<SaStoreEntity> saStoreEntities = saStoreDao.list(Wrappers.<SaStoreEntity>lambdaQuery().in(SaStoreEntity::getId, request.getIdList()));
        Optional.of(saStoreEntities).orElse(new ArrayList<>()).forEach(item -> {
            try {
                if (saStoreFinancePaymentAccountDao.isExists(request.getCardId())) {
                    throw new BusinessServiceException("该卡已经被关联店铺,请选择其他付款卡");
                }
                if (saAccountApplyService.isExist(request.getCardId())) {
                    throw new BusinessServiceException("该卡已经被关联店铺,请选择其他付款卡");
                }
                List<SaStoreFinancePaymentAccountEntity> paymentAccountEntities = saStoreFinancePaymentAccountService.getList(item.getId());
                SaAccountApplyEntity saAccountApplyEntity = new SaAccountApplyEntity();
                saAccountApplyEntity.setApplyType(ApplyTypeEnum.CARD_CHANGE.getCode());
                saAccountApplyEntity.setAccountType(AccountTypeEnum.STORE_ACCOUNT.getCode());
                saAccountApplyEntity.setAccount(item.getErpStoreName());
                saAccountApplyEntity.setRemark(request.getRemark());
                saAccountApplyEntity.setApplyRemark(request.getApplyRemark());
                saAccountApplyEntity.setPlatformId(item.getPlatformId());
                saAccountApplyEntity.setPlatformName(item.getPlatformName());
                saAccountApplyEntity.setDepartmentId(item.getDepartmentId().toString());
                saAccountApplyEntity.setDepartment(item.getDepartment());
                saAccountApplyEntity.setSecondDepartment(item.getSecondDepartment());
                saAccountApplyEntity.setOldStoreStaffings(request.getOldStoreStaffings());
                saAccountApplyEntity.setStoreStaffings(request.getStoreStaffings());
                if (CollectionUtil.isNotEmpty(paymentAccountEntities)) {
                    saAccountApplyEntity.setOldCardId(paymentAccountEntities.get(0).getCardId());
                    saAccountApplyEntity.setOldBankId(paymentAccountEntities.get(0).getBankId().toString());
                }
                saAccountApplyEntity.setCardId(request.getCardId());
                saAccountApplyEntity.setBankId(request.getBankId());
                saAccountApplyEntity.setCreateBy(loginInfoService.getUserName());
                saAccountApplyEntity.setAllocateUser(String.valueOf(loginInfoService.getUserId()));
                saAccountApplyEntity.setLocation(loginInfoService.getLocation());
                saAccountApplyService.save(saAccountApplyEntity);
                bdOperateLogService.updateLog(BusinessTypeEnum.STORE.name(), saAccountApplyEntity.getApplyId(), "发起信用卡变更" + saAccountApplyEntity.getApplyId());
                BusinessInfo businessInfo = makeBusinessInfoByCard(saAccountApplyEntity);
                List<TaskResponse> responses = activitiApiService.tasksByBusinessKey(businessInfo.getBusinessKey());
                if (CollectionUtil.isNotEmpty(responses)) {
                    saAccountApplyEntity.setStatus(AccountApplyStatusEnum.BUSINESS_CHECK.getCode());
                    saAccountApplyService.commonOperaActivitiAndUpdateSaAccountApplyEntity(saAccountApplyEntity, responses, new HashMap<>(), ActivitiProcessDefinitionKeyEnum.medic_store_card_change.name(), true);
                    bdOperateLogService.updateLog(BusinessTypeEnum.SM_ACCOUNT_APPLY.name(), saAccountApplyEntity.getApplyId(), "状态变为待事业部审核");
                }
            } catch (Exception e) {
                throw new BusinessServiceException(e.getMessage(), e);
            }
        });
    }

    @Override
    public void updateStoreCardInfo(SaAccountApplyEntity saAccountApplyEntity) {
        LocationContext.setLocation(saAccountApplyEntity.getLocation());
        SaStoreEntity saStoreEntity = saStoreDao.getOne(Wrappers.<SaStoreEntity>lambdaQuery()
                .eq(SaStoreEntity::getErpStoreName, saAccountApplyEntity.getAccount()));
        saStoreFinancePaymentAccountService.updateStoreCardInfo(saAccountApplyEntity, saStoreEntity.getId());
        String content = "账号类型：店铺号，账号：" + saAccountApplyEntity.getAccount() + "，于" + DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN) + "发起信用卡变更，审批通过，请悉知";
        notifyApiService.sendMarkdownDingTalkMessage("信用卡变更通知", content, Arrays.stream(StoreCardNoticeUserEnum.values()).map(StoreCardNoticeUserEnum::getCode).collect(Collectors.toList()));

    }

    @Override
    public List<SelectModel> storeSelectByRequest(StoreSelectRequest request) {
        LambdaQueryWrapper<SaStoreEntity> wrapper = new LambdaQueryWrapper<>();
        if (!CollectionUtils.isEmpty(request.getPlatformNameList())) {
            wrapper.in(SaStoreEntity::getPlatformName, request.getPlatformNameList())
                    .or().in(SaStoreEntity::getSecondPlatformName, request.getPlatformNameList());
        }
        return saStoreDao.list(wrapper).stream().map(s -> {
            SelectModel selectModel = new SelectModel();
            selectModel.setValue(s.getId().toString());
            selectModel.setLabel(s.getErpStoreName());
            return selectModel;
        }).collect(Collectors.toList());
    }

    @Override
    public boolean isExists(List<Integer> ids, Integer platformId) {
        return saStoreDao.count(Wrappers.<SaStoreEntity>lambdaQuery()
                .eq(SaStoreEntity::getPlatformId, platformId)
                .in(SaStoreEntity::getSaleAccountId, ids)) > 0;
    }
}
