package com.nsy.oms.business.domain.response.lingxing;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 * @date 2023/11/21 16:25
 */
public class ErpScDataSellerResponse {
    private Integer sid;
    private Integer mid;
    private String name;
    @JsonProperty("seller_id")
    private String sellerId;
    @JsonProperty("account_name")
    private String accountName;
    @JsonProperty("seller_account_id")
    private String sellerAccountId;
    private String region;
    private String country;
    @JsonProperty("has_ads_setting")
    private Integer hasAdsSetting;
    private String marketplaceId;
    private Integer status;

    public Integer getSid() {
        return sid;
    }

    public void setSid(Integer sid) {
        this.sid = sid;
    }

    public Integer getMid() {
        return mid;
    }

    public void setMid(Integer mid) {
        this.mid = mid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSellerId() {
        return sellerId;
    }

    public void setSellerId(String sellerId) {
        this.sellerId = sellerId;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getSellerAccountId() {
        return sellerAccountId;
    }

    public void setSellerAccountId(String sellerAccountId) {
        this.sellerAccountId = sellerAccountId;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public Integer getHasAdsSetting() {
        return hasAdsSetting;
    }

    public void setHasAdsSetting(Integer hasAdsSetting) {
        this.hasAdsSetting = hasAdsSetting;
    }

    public String getMarketplaceId() {
        return marketplaceId;
    }

    public void setMarketplaceId(String marketplaceId) {
        this.marketplaceId = marketplaceId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
