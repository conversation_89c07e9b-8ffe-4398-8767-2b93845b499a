package com.nsy.oms.business.domain.request.points;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description:
 * @author: linCheng
 * @create: 2025-05-09 17:59
 **/
@Data
public class AffirmRefundItemRequest {

    @ApiModelProperty("退款请求ID")
    @NotNull(message = "退款请求ID不能为空")
    private Integer refundId;

    @ApiModelProperty("退款请求明细id")
    private Integer b2bPointsRefundItemId;

    @ApiModelProperty("备注")
    private String remark;
}
