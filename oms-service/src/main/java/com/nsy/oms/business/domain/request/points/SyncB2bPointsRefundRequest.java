package com.nsy.oms.business.domain.request.points;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @description:
 * @author: linCheng
 * @create: 2025-05-09 09:35
 **/
@Data
public class SyncB2bPointsRefundRequest {

    @ApiModelProperty("退款数据")
    @NotEmpty(message = "退款数据不能为空")
    @Valid
    private List<SyncB2bPointsRefund> syncB2bPointsRefunds;

}
