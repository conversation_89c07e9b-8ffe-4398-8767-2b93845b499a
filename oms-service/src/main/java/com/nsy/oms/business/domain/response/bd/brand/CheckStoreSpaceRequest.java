package com.nsy.oms.business.domain.response.bd.brand;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-09-25
 */
public class CheckStoreSpaceRequest {
    @NotEmpty(message = "请求数据不能为空")
    private List<StoreSpace> storeSpaceList;

    public List<StoreSpace> getStoreSpaceList() {
        return storeSpaceList;
    }

    public void setStoreSpaceList(List<StoreSpace> storeSpaceList) {
        this.storeSpaceList = storeSpaceList;
    }

    public static class StoreSpace {
        @NotNull(message = "店铺id不能为空")
        private Integer storeId;
        @NotNull(message = "仓库id不能为空")
        private Integer spaceId;
        // 该店铺有设置品牌，该品牌有对应的仓库：1是0否
        private int result = 0;

        public Integer getStoreId() {
            return storeId;
        }

        public void setStoreId(Integer storeId) {
            this.storeId = storeId;
        }

        public Integer getSpaceId() {
            return spaceId;
        }

        public void setSpaceId(Integer spaceId) {
            this.spaceId = spaceId;
        }

        public int getResult() {
            return result;
        }

        public void setResult(int result) {
            this.result = result;
        }
    }
}
