package com.nsy.oms.business.domain.response.bd.expert;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * 店铺达人信息
 */
public class BdStoreExpertPageResponse {
    @ApiModelProperty("主键id")
    private Integer id;
    @ApiModelProperty("头像图片地址")
    private String avatar;
    @ApiModelProperty("昵称")
    private String nickname;
    @ApiModelProperty("邮箱")
    private String email;
    @ApiModelProperty("粉丝数")
    private String fansNum;
    @ApiModelProperty("发送邮件状态：1成功2失败")
    private Integer contactStatus;
    @ApiModelProperty("发送邮件状态描述")
    private String contactStatusDesc;
    @ApiModelProperty("粉丝性别比例-男")
    private BigDecimal fansMaleRatio;
    @ApiModelProperty("粉丝性别比例-女")
    private BigDecimal fansFemaleRatio;
    @ApiModelProperty("粉丝年龄分布比例-1")
    private BigDecimal fansAgeRatio1;
    @ApiModelProperty("粉丝年龄分布比例-2")
    private BigDecimal fansAgeRatio2;
    @ApiModelProperty("粉丝年龄分布比例-3")
    private BigDecimal fansAgeRatio3;
    @ApiModelProperty("备注")
    private String remark;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getFansNum() {
        return fansNum;
    }

    public void setFansNum(String fansNum) {
        this.fansNum = fansNum;
    }

    public Integer getContactStatus() {
        return contactStatus;
    }

    public void setContactStatus(Integer contactStatus) {
        this.contactStatus = contactStatus;
    }

    public String getContactStatusDesc() {
        return contactStatusDesc;
    }

    public void setContactStatusDesc(String contactStatusDesc) {
        this.contactStatusDesc = contactStatusDesc;
    }

    public BigDecimal getFansMaleRatio() {
        return fansMaleRatio;
    }

    public void setFansMaleRatio(BigDecimal fansMaleRatio) {
        this.fansMaleRatio = fansMaleRatio;
    }

    public BigDecimal getFansFemaleRatio() {
        return fansFemaleRatio;
    }

    public void setFansFemaleRatio(BigDecimal fansFemaleRatio) {
        this.fansFemaleRatio = fansFemaleRatio;
    }

    public BigDecimal getFansAgeRatio1() {
        return fansAgeRatio1;
    }

    public void setFansAgeRatio1(BigDecimal fansAgeRatio1) {
        this.fansAgeRatio1 = fansAgeRatio1;
    }

    public BigDecimal getFansAgeRatio2() {
        return fansAgeRatio2;
    }

    public void setFansAgeRatio2(BigDecimal fansAgeRatio2) {
        this.fansAgeRatio2 = fansAgeRatio2;
    }

    public BigDecimal getFansAgeRatio3() {
        return fansAgeRatio3;
    }

    public void setFansAgeRatio3(BigDecimal fansAgeRatio3) {
        this.fansAgeRatio3 = fansAgeRatio3;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}