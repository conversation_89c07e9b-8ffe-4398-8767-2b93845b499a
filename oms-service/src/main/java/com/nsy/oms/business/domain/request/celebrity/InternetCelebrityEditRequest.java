package com.nsy.oms.business.domain.request.celebrity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * @Author: lhh
 * @Date: 2025-04-24
 * @Description:
 */
@ApiModel("达人列表编辑req")
public class InternetCelebrityEditRequest {
    @ApiModelProperty("storeRelationId")
    private Integer storeRelationId;
    @ApiModelProperty("bdId")
    private Integer bdId;
    @ApiModelProperty("bdAccount")
    private String bdAccount;
    @ApiModelProperty("bdName")
    private String bdName;
    @ApiModelProperty("建联结果")
    private String relationStatus;
    @ApiModelProperty("达人适配度")
    private String shiyingFitness;
    @ApiModelProperty("佣金率")
    private BigDecimal commissionRate;
    @ApiModelProperty("bd备注")
    private String remark;

    @ApiModelProperty("达人邮箱")
    private String email;
    @ApiModelProperty("达人等级")
    private String level;
    @ApiModelProperty("达人主页")
    private String homePage;
    @ApiModelProperty("gmv")
    private BigDecimal gmv;
    @ApiModelProperty("粉丝数")
    private Integer followers;
    @ApiModelProperty("达人画像")
    private String internetCelebrityInfo;
    @ApiModelProperty("instagram")
    private String instagram;
    @ApiModelProperty("facebook")
    private String facebook;
    @ApiModelProperty("whatsapp")
    private String whatsapp;
    @ApiModelProperty("phoneNumber")
    private String phoneNumber;
    @ApiModelProperty("达人图片")
    private String imageUrl;

    public Integer getStoreRelationId() {
        return storeRelationId;
    }

    public void setStoreRelationId(Integer storeRelationId) {
        this.storeRelationId = storeRelationId;
    }

    public Integer getBdId() {
        return bdId;
    }

    public void setBdId(Integer bdId) {
        this.bdId = bdId;
    }

    public String getBdAccount() {
        return bdAccount;
    }

    public void setBdAccount(String bdAccount) {
        this.bdAccount = bdAccount;
    }

    public String getBdName() {
        return bdName;
    }

    public void setBdName(String bdName) {
        this.bdName = bdName;
    }

    public String getRelationStatus() {
        return relationStatus;
    }

    public void setRelationStatus(String relationStatus) {
        this.relationStatus = relationStatus;
    }

    public String getShiyingFitness() {
        return shiyingFitness;
    }

    public void setShiyingFitness(String shiyingFitness) {
        this.shiyingFitness = shiyingFitness;
    }

    public BigDecimal getCommissionRate() {
        return commissionRate;
    }

    public void setCommissionRate(BigDecimal commissionRate) {
        this.commissionRate = commissionRate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getHomePage() {
        return homePage;
    }

    public void setHomePage(String homePage) {
        this.homePage = homePage;
    }

    public BigDecimal getGmv() {
        return gmv;
    }

    public void setGmv(BigDecimal gmv) {
        this.gmv = gmv;
    }

    public Integer getFollowers() {
        return followers;
    }

    public void setFollowers(Integer followers) {
        this.followers = followers;
    }

    public String getInternetCelebrityInfo() {
        return internetCelebrityInfo;
    }

    public void setInternetCelebrityInfo(String internetCelebrityInfo) {
        this.internetCelebrityInfo = internetCelebrityInfo;
    }

    public String getInstagram() {
        return instagram;
    }

    public void setInstagram(String instagram) {
        this.instagram = instagram;
    }

    public String getFacebook() {
        return facebook;
    }

    public void setFacebook(String facebook) {
        this.facebook = facebook;
    }

    public String getWhatsapp() {
        return whatsapp;
    }

    public void setWhatsapp(String whatsapp) {
        this.whatsapp = whatsapp;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
}
