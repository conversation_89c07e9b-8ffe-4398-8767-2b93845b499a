package com.nsy.oms.business.domain.upload;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.oms.business.manage.pms.response.ConfigInternetCelebrityResponse;
import com.nsy.oms.business.manage.user.response.SysUserInfo;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.util.Date;


public class InternetCelebritySampleOrderImport extends BaseErrorTemplate {

    @ExcelProperty("订单店铺")
    private String storeName;

    @ExcelProperty("发货店铺")
    private String deliveryStoreName;

    @ExcelProperty("订单号")
    private String platformOrderNo;

    private String platformOriginalOrderNo;

    @ExcelProperty("网红")
    @NotBlank(message = "【网红】不允许为空")
    private String internetCelebrityNickname;

    @ExcelProperty("网红负责人")
    private String ownerName;

    @ExcelProperty("规格编码")
    private String sku;

    @ExcelProperty("视频code")
    private String videoCode;

    @ExcelProperty("视频链接")
    private String videoUrl;

    @ExcelProperty("发帖日期")
    private Date postDate;

    @ExcelProperty("订单类型")
    private String orderType;

    private Integer ownerDeptId;

    private Integer internetCelebrityDeptId;

    private String internetCelebrityDeptName;

    private SysUserInfo sysUserInfo;

    private Boolean flag;

    private Integer deliveryStoreId;

    private Integer storeId;

    private Integer qty;

    private String sellerSku;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date orderDeliveryDate;

    private ConfigInternetCelebrityResponse configInternetCelebrityResponse;

    public Boolean getFlag() {
        return flag;
    }

    public void setFlag(Boolean flag) {
        this.flag = flag;
    }

    public SysUserInfo getSysUserInfo() {
        return sysUserInfo;
    }

    public void setSysUserInfo(SysUserInfo sysUserInfo) {
        this.sysUserInfo = sysUserInfo;
    }

    public ConfigInternetCelebrityResponse getConfigInternetCelebrityResponse() {
        return configInternetCelebrityResponse;
    }

    public void setConfigInternetCelebrityResponse(ConfigInternetCelebrityResponse configInternetCelebrityResponse) {
        this.configInternetCelebrityResponse = configInternetCelebrityResponse;
    }

    public Integer getOwnerDeptId() {
        return ownerDeptId;
    }

    public void setOwnerDeptId(Integer ownerDeptId) {
        this.ownerDeptId = ownerDeptId;
    }

    public Integer getInternetCelebrityDeptId() {
        return internetCelebrityDeptId;
    }

    public void setInternetCelebrityDeptId(Integer internetCelebrityDeptId) {
        this.internetCelebrityDeptId = internetCelebrityDeptId;
    }

    public String getInternetCelebrityDeptName() {
        return internetCelebrityDeptName;
    }

    public void setInternetCelebrityDeptName(String internetCelebrityDeptName) {
        this.internetCelebrityDeptName = internetCelebrityDeptName;
    }

    public String getPlatformOrderNo() {
        return platformOrderNo;
    }

    public void setPlatformOrderNo(String platformOrderNo) {
        this.platformOrderNo = platformOrderNo;
    }

    public String getInternetCelebrityNickname() {
        return internetCelebrityNickname;
    }

    public void setInternetCelebrityNickname(String internetCelebrityNickname) {
        this.internetCelebrityNickname = internetCelebrityNickname;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public Integer getDeliveryStoreId() {
        return deliveryStoreId;
    }

    public void setDeliveryStoreId(Integer deliveryStoreId) {
        this.deliveryStoreId = deliveryStoreId;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getDeliveryStoreName() {
        return deliveryStoreName;
    }

    public void setDeliveryStoreName(String deliveryStoreName) {
        this.deliveryStoreName = deliveryStoreName;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getVideoCode() {
        return videoCode;
    }

    public void setVideoCode(String videoCode) {
        this.videoCode = videoCode;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public Date getPostDate() {
        return postDate;
    }

    public void setPostDate(Date postDate) {
        this.postDate = postDate;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getPlatformOriginalOrderNo() {
        return platformOriginalOrderNo;
    }

    public void setPlatformOriginalOrderNo(String platformOriginalOrderNo) {
        this.platformOriginalOrderNo = platformOriginalOrderNo;
    }

    public Integer getQty() {
        return qty;
    }

    public void setQty(Integer qty) {
        this.qty = qty;
    }

    public String getSellerSku() {
        return sellerSku;
    }

    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    public Date getOrderDeliveryDate() {
        return orderDeliveryDate;
    }

    public void setOrderDeliveryDate(Date orderDeliveryDate) {
        this.orderDeliveryDate = orderDeliveryDate;
    }
}
