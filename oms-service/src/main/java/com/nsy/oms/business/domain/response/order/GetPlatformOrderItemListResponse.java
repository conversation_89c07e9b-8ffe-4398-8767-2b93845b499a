package com.nsy.oms.business.domain.response.order;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
public class GetPlatformOrderItemListResponse extends BaseResponse implements Serializable {
    private static final long serialVersionUID = -7446703134028690261L;
    private Integer storeId;
    private String orderId;
    private BigDecimal postFee;
    private BigDecimal totalFee;
    private BigDecimal discountFee;
    private BigDecimal discountMoney;
    private String iossNumer;

    private List<GetPlatformOrderItemResponse> itemList;

    public List<GetPlatformOrderItemResponse> getItemList() {
        return itemList;
    }

    public void setItemList(List<GetPlatformOrderItemResponse> itemList) {
        this.itemList = itemList;
    }

    public BigDecimal getPostFee() {
        return postFee;
    }

    public void setPostFee(BigDecimal postFee) {
        this.postFee = postFee;
    }

    public BigDecimal getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(BigDecimal totalFee) {
        this.totalFee = totalFee;
    }

    public BigDecimal getDiscountFee() {
        return discountFee;
    }

    public void setDiscountFee(BigDecimal discountFee) {
        this.discountFee = discountFee;
    }

    public BigDecimal getDiscountMoney() {
        return discountMoney;
    }

    public void setDiscountMoney(BigDecimal discountMoney) {
        this.discountMoney = discountMoney;
    }

    public String getIossNumer() {
        return iossNumer;
    }

    public void setIossNumer(String iossNumer) {
        this.iossNumer = iossNumer;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }
}
