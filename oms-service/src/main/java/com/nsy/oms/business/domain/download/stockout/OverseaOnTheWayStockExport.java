package com.nsy.oms.business.domain.download.stockout;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.api.core.apicore.annotation.NsyExcelProperty;
import java.util.Date;

/**
 * 海外仓在途明细导出实体
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
public class OverseaOnTheWayStockExport {
    @NsyExcelProperty("sku")
    private String sku;

    @NsyExcelProperty("订单号")
    private String orderNo;

    @NsyExcelProperty("店铺")
    private String storeName;

    @NsyExcelProperty("发货数量")
    private Integer shipQty;

    @NsyExcelProperty("目的仓库")
    private String targetSpaceName;

    @NsyExcelProperty("物流公司")
    private String logisticsCompany;

    @NsyExcelProperty("仓库发货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date shipDate;

    @NsyExcelProperty("预计到货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date estimatedArrivalDate;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public Integer getShipQty() {
        return shipQty;
    }

    public void setShipQty(Integer shipQty) {
        this.shipQty = shipQty;
    }

    public String getTargetSpaceName() {
        return targetSpaceName;
    }

    public void setTargetSpaceName(String targetSpaceName) {
        this.targetSpaceName = targetSpaceName;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public Date getShipDate() {
        return shipDate;
    }

    public void setShipDate(Date shipDate) {
        this.shipDate = shipDate;
    }

    public Date getEstimatedArrivalDate() {
        return estimatedArrivalDate;
    }

    public void setEstimatedArrivalDate(Date estimatedArrivalDate) {
        this.estimatedArrivalDate = estimatedArrivalDate;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }
}