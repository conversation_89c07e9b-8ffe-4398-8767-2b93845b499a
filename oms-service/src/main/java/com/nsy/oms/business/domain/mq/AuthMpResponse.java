package com.nsy.oms.business.domain.mq;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/7 16:24
 */
public class AuthMpResponse {
    @ApiModelProperty("店铺ID")
    private Integer storeId;
    @ApiModelProperty("状态 0 关闭 1 启用")
    private Integer status;
    @ApiModelProperty("站点数据")
    private List<Integer> websiteIds;

    public List<Integer> getWebsiteIds() {
        return websiteIds;
    }

    public void setWebsiteIds(List<Integer> websiteIds) {
        this.websiteIds = websiteIds;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }


}
