package com.nsy.oms.business.domain.download.celebrity;

import com.nsy.api.core.apicore.annotation.NsyExcelProperty;

/**
 * @Author: lhh
 * @Date: 2025-04-24
 * @Description:
 */
public class InternetCelebrityRelationListExport {
    @NsyExcelProperty("店铺")
    private String storeName;
    @NsyExcelProperty("订单号")
    private String platformOrderNo;
    @NsyExcelProperty("订单下单时间")
    private String orderCreateDate;
    @NsyExcelProperty("建联日期")
    private String relationDate;
    @NsyExcelProperty("BD邮箱")
    private String bdEmail;
    @NsyExcelProperty("BD")
    private String bdName;
    @NsyExcelProperty("达人账号")
    private String internetCelebrityName;
    @NsyExcelProperty("达人邮箱")
    private String email;
    @NsyExcelProperty("寄样时间")
    private String orderDeliveryDate;
    @NsyExcelProperty("物流状态")
    private String packageStatus;
    @NsyExcelProperty("物流单号")
    private String trackingNumber;
    @NsyExcelProperty("妥投时间")
    private String orderCompromiseDate;
    @NsyExcelProperty("图片")
    private String imageUrl;
    @NsyExcelProperty("erp_sku")
    private String erpSku;
    @NsyExcelProperty("平台pid")
    private String sellerProductId;
    @NsyExcelProperty("视频id")
    private String videoCode;
    @NsyExcelProperty("视频链接")
    private String videoUrl;
    @NsyExcelProperty("视频发布时间")
    private String postDate;
    @NsyExcelProperty("广告意图")
    private String adIntention;
    @NsyExcelProperty("广告code")
    private String adCode;
    @NsyExcelProperty("广告名称")
    private String adName;

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getPlatformOrderNo() {
        return platformOrderNo;
    }

    public void setPlatformOrderNo(String platformOrderNo) {
        this.platformOrderNo = platformOrderNo;
    }

    public String getOrderCreateDate() {
        return orderCreateDate;
    }

    public void setOrderCreateDate(String orderCreateDate) {
        this.orderCreateDate = orderCreateDate;
    }

    public String getPackageStatus() {
        return packageStatus;
    }

    public void setPackageStatus(String packageStatus) {
        this.packageStatus = packageStatus;
    }

    public String getTrackingNumber() {
        return trackingNumber;
    }

    public void setTrackingNumber(String trackingNumber) {
        this.trackingNumber = trackingNumber;
    }

    public String getOrderCompromiseDate() {
        return orderCompromiseDate;
    }

    public void setOrderCompromiseDate(String orderCompromiseDate) {
        this.orderCompromiseDate = orderCompromiseDate;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public String getPostDate() {
        return postDate;
    }

    public void setPostDate(String postDate) {
        this.postDate = postDate;
    }

    public String getRelationDate() {
        return relationDate;
    }

    public void setRelationDate(String relationDate) {
        this.relationDate = relationDate;
    }

    public String getBdEmail() {
        return bdEmail;
    }

    public void setBdEmail(String bdEmail) {
        this.bdEmail = bdEmail;
    }

    public String getBdName() {
        return bdName;
    }

    public void setBdName(String bdName) {
        this.bdName = bdName;
    }

    public String getInternetCelebrityName() {
        return internetCelebrityName;
    }

    public void setInternetCelebrityName(String internetCelebrityName) {
        this.internetCelebrityName = internetCelebrityName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getOrderDeliveryDate() {
        return orderDeliveryDate;
    }

    public void setOrderDeliveryDate(String orderDeliveryDate) {
        this.orderDeliveryDate = orderDeliveryDate;
    }

    public String getErpSku() {
        return erpSku;
    }

    public void setErpSku(String erpSku) {
        this.erpSku = erpSku;
    }

    public String getSellerProductId() {
        return sellerProductId;
    }

    public void setSellerProductId(String sellerProductId) {
        this.sellerProductId = sellerProductId;
    }

    public String getVideoCode() {
        return videoCode;
    }

    public void setVideoCode(String videoCode) {
        this.videoCode = videoCode;
    }

    public String getAdIntention() {
        return adIntention;
    }

    public void setAdIntention(String adIntention) {
        this.adIntention = adIntention;
    }

    public String getAdCode() {
        return adCode;
    }

    public void setAdCode(String adCode) {
        this.adCode = adCode;
    }

    public String getAdName() {
        return adName;
    }

    public void setAdName(String adName) {
        this.adName = adName;
    }
}
