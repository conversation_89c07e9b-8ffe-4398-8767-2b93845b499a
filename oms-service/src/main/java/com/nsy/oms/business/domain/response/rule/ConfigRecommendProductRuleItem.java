package com.nsy.oms.business.domain.response.rule;
public class ConfigRecommendProductRuleItem {
    private Integer configRecommendProductRuleItemId;

    /**
     * 规则id
     */
    private Integer configRecommendProductRuleId;

    /**
     * 商品，标签，尺码库存，销量
     */
    private String ruleKey;

    /**
     * 值
     */
    private String ruleValue;

    /**
     * 默认
     */
    private Integer isDefault;


    public Integer getConfigRecommendProductRuleItemId() {
        return configRecommendProductRuleItemId;
    }

    public void setConfigRecommendProductRuleItemId(Integer configRecommendProductRuleItemId) {
        this.configRecommendProductRuleItemId = configRecommendProductRuleItemId;
    }

    public Integer getConfigRecommendProductRuleId() {
        return configRecommendProductRuleId;
    }

    public void setConfigRecommendProductRuleId(Integer configRecommendProductRuleId) {
        this.configRecommendProductRuleId = configRecommendProductRuleId;
    }

    public String getRuleKey() {
        return ruleKey;
    }

    public void setRuleKey(String ruleKey) {
        this.ruleKey = ruleKey;
    }

    public String getRuleValue() {
        return ruleValue;
    }

    public void setRuleValue(String ruleValue) {
        this.ruleValue = ruleValue;
    }


    public Integer getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }
}