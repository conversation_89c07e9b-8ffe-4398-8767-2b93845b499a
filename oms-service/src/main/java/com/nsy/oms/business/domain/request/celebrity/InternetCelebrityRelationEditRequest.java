package com.nsy.oms.business.domain.request.celebrity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author: lhh
 * @Date: 2025-04-24
 * @Description:
 */
@ApiModel("达人建联列表编辑req")
public class InternetCelebrityRelationEditRequest {
    @ApiModelProperty("id")
    private Integer id;
    @ApiModelProperty("达人账号")
    private String internetCelebrityName;
    @ApiModelProperty("建联结果")
    private String relationStatus;
    @ApiModelProperty("bd备注")
    private String remark;

    @ApiModelProperty("样衣订单号")
    private String platformOrderNo;
    @ApiModelProperty("发货店铺id")
    private Integer deliveryStoreId;
    @ApiModelProperty("平台原始订单号")
    private String platformOriginalOrderNo;
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("订单发货时间")
    private Date orderDeliveryDate;
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("订单创建时间")
    private Date orderCreateDate;
    @ApiModelProperty("订单买家昵称")
    private String buyerNick;
    @ApiModelProperty("地区")
    private String location;
    @ApiModelProperty("物流单号")
    private String trackingNumber;
    @ApiModelProperty("订单类型")
    private String orderType;
    @ApiModelProperty("样品SKU")
    private String sku;
    @ApiModelProperty("数量")
    private Integer qty;
    @ApiModelProperty("sku")
    private String sellerSku;
    @ApiModelProperty("平台sku id")
    private String sellerSkuId;
    @ApiModelProperty("平台PID")
    private String sellerProductId;
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("签收日期")
    private Date orderCompromiseDate;

    @ApiModelProperty("视频授权使用")
    private String videoAuthorization;
    @ApiModelProperty("视频ID")
    private String videoCode;
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("视频发布日期")
    private Date postDate;
    @ApiModelProperty("视频链接")
    private String videoUrl;

    @ApiModelProperty("广告意图")
    private String adIntention;
    @ApiModelProperty("广告CODE")
    private String adCode;
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("广告投放日期")
    private Date adDate;
    @ApiModelProperty("广告意见反馈")
    private String adFeedback;
    @ApiModelProperty("建联日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date relationDate;
    @ApiModelProperty("创建日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createDate;

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public Integer getQty() {
        return qty;
    }

    public void setQty(Integer qty) {
        this.qty = qty;
    }

    public String getSellerSku() {
        return sellerSku;
    }

    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    public String getSellerSkuId() {
        return sellerSkuId;
    }

    public void setSellerSkuId(String sellerSkuId) {
        this.sellerSkuId = sellerSkuId;
    }

    public Integer getDeliveryStoreId() {
        return deliveryStoreId;
    }

    public void setDeliveryStoreId(Integer deliveryStoreId) {
        this.deliveryStoreId = deliveryStoreId;
    }

    public String getPlatformOriginalOrderNo() {
        return platformOriginalOrderNo;
    }

    public void setPlatformOriginalOrderNo(String platformOriginalOrderNo) {
        this.platformOriginalOrderNo = platformOriginalOrderNo;
    }

    public Date getOrderDeliveryDate() {
        return orderDeliveryDate;
    }

    public void setOrderDeliveryDate(Date orderDeliveryDate) {
        this.orderDeliveryDate = orderDeliveryDate;
    }

    public Date getOrderCreateDate() {
        return orderCreateDate;
    }

    public void setOrderCreateDate(Date orderCreateDate) {
        this.orderCreateDate = orderCreateDate;
    }

    public String getBuyerNick() {
        return buyerNick;
    }

    public void setBuyerNick(String buyerNick) {
        this.buyerNick = buyerNick;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getTrackingNumber() {
        return trackingNumber;
    }

    public void setTrackingNumber(String trackingNumber) {
        this.trackingNumber = trackingNumber;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getInternetCelebrityName() {
        return internetCelebrityName;
    }

    public void setInternetCelebrityName(String internetCelebrityName) {
        this.internetCelebrityName = internetCelebrityName;
    }

    public String getRelationStatus() {
        return relationStatus;
    }

    public void setRelationStatus(String relationStatus) {
        this.relationStatus = relationStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getPlatformOrderNo() {
        return platformOrderNo;
    }

    public void setPlatformOrderNo(String platformOrderNo) {
        this.platformOrderNo = platformOrderNo;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getSellerProductId() {
        return sellerProductId;
    }

    public void setSellerProductId(String sellerProductId) {
        this.sellerProductId = sellerProductId;
    }

    public Date getOrderCompromiseDate() {
        return orderCompromiseDate;
    }

    public void setOrderCompromiseDate(Date orderCompromiseDate) {
        this.orderCompromiseDate = orderCompromiseDate;
    }

    public String getVideoAuthorization() {
        return videoAuthorization;
    }

    public void setVideoAuthorization(String videoAuthorization) {
        this.videoAuthorization = videoAuthorization;
    }

    public String getVideoCode() {
        return videoCode;
    }

    public void setVideoCode(String videoCode) {
        this.videoCode = videoCode;
    }

    public Date getPostDate() {
        return postDate;
    }

    public void setPostDate(Date postDate) {
        this.postDate = postDate;
    }

    public String getAdIntention() {
        return adIntention;
    }

    public void setAdIntention(String adIntention) {
        this.adIntention = adIntention;
    }

    public String getAdCode() {
        return adCode;
    }

    public void setAdCode(String adCode) {
        this.adCode = adCode;
    }

    public Date getAdDate() {
        return adDate;
    }

    public void setAdDate(Date adDate) {
        this.adDate = adDate;
    }

    public String getAdFeedback() {
        return adFeedback;
    }

    public void setAdFeedback(String adFeedback) {
        this.adFeedback = adFeedback;
    }

    public Date getRelationDate() {
        return relationDate;
    }

    public void setRelationDate(Date relationDate) {
        this.relationDate = relationDate;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
}
