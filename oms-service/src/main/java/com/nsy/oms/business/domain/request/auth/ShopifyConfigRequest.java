package com.nsy.oms.business.domain.request.auth;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.oms.business.domain.valid.InsertGroup;
import com.nsy.oms.business.domain.valid.UpdateGroup;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/11/25 13:53
 */

public class ShopifyConfigRequest {
    @ApiModelProperty("主键id")
    @NotNull(message = "【主键】不能为空!", groups = UpdateGroup.class)
    private Integer id;
    /**
     * 店铺名称
     */
    @ApiModelProperty("店铺名称")
    @NotBlank(message = "【店铺名称】不能为空!", groups = { InsertGroup.class, UpdateGroup.class })
    private String storeName;
    /**
     * 店铺id
     */
    @ApiModelProperty("店铺id")
    @NotNull(message = "【店铺id】不能为空!", groups = { InsertGroup.class, UpdateGroup.class })
    private Integer storeId;

    /**
     * 网址
     */
    @ApiModelProperty("网址")
    private String address;

    /**
     * shopifyKey
     */
    @ApiModelProperty("shopifyKey")
    @NotBlank(message = "【shopifyKey】不能为空!", groups = { InsertGroup.class, UpdateGroup.class })
    private String shopifyKey;

    /**
     * Secret Key
     */
    @ApiModelProperty("secretKey")
    @NotBlank(message = "【secretKey】不能为空!", groups = { InsertGroup.class, UpdateGroup.class })
    private String secretKey;

    /**
     * 授权日期
     */
    @ApiModelProperty("授权日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    private Date grantDate;

    @ApiModelProperty("状态 0 关闭 1开启")
    private Integer enable;
    /**
     * accessToken
     */
    @ApiModelProperty("access_token")
    private String accessToken;

    /**
     * refreshToken
     */
    @ApiModelProperty("refresh_token")
    private String refreshToken;

    /**
     * 有效期
     */
    @ApiModelProperty(value = "有效期", name = "validityDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    private Date validityDate;


    /**
     * 开始抓单时间
     */
    @ApiModelProperty(value = "开始抓单时间", name = "catchDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    private Date catchDate;


    /**
     * 当前抓单时间
     */
    @ApiModelProperty(value = "当前抓单时间", name = "currentCatchDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date currentCatchDate;

    /**
     * 代理地址
     */
    @ApiModelProperty(value = "代理地址", name = "proxyAddress")
    private String proxyAddress;


    /**
     * 地区名称
     */
    @ApiModelProperty(value = "地区名称", name = "locationName")
    private String locationName;


    /**
     * 账号ID
     */
    @ApiModelProperty(value = "账号ID", name = "accountId")
    private String accountId;


    /**
     * 特殊途径
     */
    @ApiModelProperty(value = "特殊途径", name = "specialGateWay")
    private String specialGateWay;


    /**
     * 途径
     */
    @ApiModelProperty(value = "途径", name = "gateway")
    private String gateway;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getShopifyKey() {
        return shopifyKey;
    }

    public void setShopifyKey(String shopifyKey) {
        this.shopifyKey = shopifyKey;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public Date getGrantDate() {
        return grantDate;
    }

    public void setGrantDate(Date grantDate) {
        this.grantDate = grantDate;
    }

    public Integer getEnable() {
        return enable;
    }

    public void setEnable(Integer enable) {
        this.enable = enable;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public Date getValidityDate() {
        return validityDate;
    }

    public void setValidityDate(Date validityDate) {
        this.validityDate = validityDate;
    }

    public Date getCatchDate() {
        return catchDate;
    }

    public void setCatchDate(Date catchDate) {
        this.catchDate = catchDate;
    }

    public Date getCurrentCatchDate() {
        return currentCatchDate;
    }

    public void setCurrentCatchDate(Date currentCatchDate) {
        this.currentCatchDate = currentCatchDate;
    }

    public String getProxyAddress() {
        return proxyAddress;
    }

    public void setProxyAddress(String proxyAddress) {
        this.proxyAddress = proxyAddress;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getSpecialGateWay() {
        return specialGateWay;
    }

    public void setSpecialGateWay(String specialGateWay) {
        this.specialGateWay = specialGateWay;
    }

    public String getGateway() {
        return gateway;
    }

    public void setGateway(String gateway) {
        this.gateway = gateway;
    }
}
