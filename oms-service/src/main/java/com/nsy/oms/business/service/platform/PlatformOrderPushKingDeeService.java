package com.nsy.oms.business.service.platform;

import com.nsy.oms.enums.PushKingDeeTypeEnum;
import com.nsy.oms.repository.entity.platform.PlatformOrderEntity;
import com.nsy.oms.repository.entity.platform.PlatformOrderPushKingdeeQueueEntity;

import java.util.Date;
import java.util.List;

public interface PlatformOrderPushKingDeeService {

    /**
     * 订单类型
     */
    PushKingDeeTypeEnum pushKingDeeTypeEnum();

    /**
     * 获取缓存时间
     */
    String getCacheDate();

    /**
     * 获取卡控时间
     */
    String getControlTime();

    /**
     * 查询已发货的平台订单
     *
     * @param businessStarDate
     * @param businessEndDate
     * @param fetchCount
     * @return
     */
    List<PlatformOrderPushKingdeeQueueEntity> listData(Date businessStarDate, Date businessEndDate, Integer fetchCount);

    /**
     * 设值缓存时间
     *
     * @param cacheDate
     */
    void setCacheDate(String cacheDate);

    /**
     * 推送平台订单
     *
     * @param orderEntity
     */
    void pushOrderToKingDee(PlatformOrderEntity orderEntity);

}
