package com.nsy.oms.business.domain.request.rule;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023年10月20日 0020 上午 10:01:31
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConfigRecommendProductRuleCheckBrandSpiltRequest {
    @NonNull
    Integer storeId;

    @NotEmpty
    List<Integer> spaceIdList;

}
