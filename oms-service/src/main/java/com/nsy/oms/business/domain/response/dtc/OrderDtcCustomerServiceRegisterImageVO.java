package com.nsy.oms.business.domain.response.dtc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: linCheng
 * @create: 2023-08-08 16:48
 **/
@Data
public class OrderDtcCustomerServiceRegisterImageVO {

    @ApiModelProperty("主键id")
    private Integer dtcCustomerServiceRegisterImageId;

    @ApiModelProperty("DTC客服ID")
    private Integer dtcCustomerServiceRegisterId;

    @ApiModelProperty("名称")
    private String originName;

    @ApiModelProperty("目录名称")
    private String attachmentName;

    @ApiModelProperty("url")
    private String attachmentUrl;

    @ApiModelProperty("创建时间")
    private Date createDate;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("更新时间")
    private Date updateDate;

    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("版本号")
    private Long version;

    @ApiModelProperty("地区")
    private String location;

}
