package com.nsy.oms.business.service.platform.order;

import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.NsyCollUtil;
import com.nsy.api.transfer.domain.response.platform.PlatformOrderInfo;
import com.nsy.api.transfer.domain.response.platform.PlatformOrderItemInfo;
import com.nsy.api.transfer.enums.PlatformTypeEnum;
import com.nsy.oms.business.service.base.SkuInfoService;
import com.nsy.oms.business.service.platform.order.base.BaseCompleteOrderInfoService;
import com.nsy.oms.repository.entity.order.SaleOrderItemEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import static com.nsy.oms.constants.platform.ShopifyConstants.PROTECTION_SKU;

/**
 * 销售平台抓单重构--Shopify
 */
@Service
public class ShopifyService extends BaseCompleteOrderInfoService {
    @Autowired
    private SkuInfoService skuInfoService;

    /**
     * 平台
     */
    @Override
    public PlatformTypeEnum platform() {
        return PlatformTypeEnum.Shopify;
    }

    /**
     * 更新订单信息，过滤掉包含PROTECTION_SKU的条目，返回不符合过滤条件的条目列表
     *
     * @param info 待处理的订单项
     * @return 不符合过滤条件的订单项列表
     */
    @Override
    public PlatformOrderInfo convertInfo(PlatformOrderInfo info) {
        if (NsyCollUtil.isEmpty(info.getOrderItemInfoList())) {
            throw new BusinessServiceException("item明细列表为空！");
        }

        // 创建不符合条件的项目列表
        List<PlatformOrderItemInfo> filteredList = new ArrayList<>();
        // 遍历列表，将符合条件的项目从列表中移除
        for (PlatformOrderItemInfo item : info.getOrderItemInfoList()) {
            if (item != null && item.getSellerSku() != null && !item.getSellerSku().toUpperCase(Locale.ROOT).contains(PROTECTION_SKU)) {
                // 不包含PROTECTION_SKU的项目，保留
                filteredList.add(item);
            }
        }
        info.setOrderItemInfoList(filteredList);
        return info;
    }

    @Override
    public void convertOrderItemSku(List<SaleOrderItemEntity> orderItemEntities, Integer storeId) {
        skuInfoService.getSkuInfo(storeId, orderItemEntities, 0);
    }
}
