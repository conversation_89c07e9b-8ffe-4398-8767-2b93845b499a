package com.nsy.oms.business.domain.request.auth;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.oms.business.domain.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;


@ApiModel("阿里国际授权")
public class SauAliInterConfigPageRequest extends PageRequest {
    @ApiModelProperty("店铺账号")
    private List<String> storeNames;

    @ApiModelProperty("创建日期开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createStartDate;

    @ApiModelProperty("创建日期结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createEndDate;
    private String location;

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public List<String> getStoreNames() {
        return storeNames;
    }

    public void setStoreNames(List<String> storeNames) {
        this.storeNames = storeNames;
    }

    public Date getCreateStartDate() {
        return createStartDate;
    }

    public void setCreateStartDate(Date createStartDate) {
        this.createStartDate = createStartDate;
    }

    public Date getCreateEndDate() {
        return createEndDate;
    }

    public void setCreateEndDate(Date createEndDate) {
        this.createEndDate = createEndDate;
    }
}
