package com.nsy.oms.business.manage.omspublish.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description:
 * @author: linCheng
 * @create: 2025-04-02 18:08
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PublishProductSpecInfo {

    private Integer storeId;

    private Integer websiteId;

    private String erpSku;

    private String websiteItemCode;

    private String websiteProductCode;

    private String websiteItemSku;

    private List<String> skuList;

}
