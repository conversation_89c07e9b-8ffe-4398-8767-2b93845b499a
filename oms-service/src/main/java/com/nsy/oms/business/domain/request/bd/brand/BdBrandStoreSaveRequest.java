package com.nsy.oms.business.domain.request.bd.brand;


import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

public class BdBrandStoreSaveRequest {
    @ApiModelProperty("主键id")
    private Integer brandStoreId;
    @ApiModelProperty("品牌id")
    @NotNull(message = "品牌不允许为空")
    private Integer brandId;
    @ApiModelProperty("店铺id")
    @NotNull(message = "店铺不允许为空")
    private Integer storeId;
    @ApiModelProperty("是否采购挂牌1是0否")
    @NotNull(message = "采购挂牌不允许为空")
    private Integer isOpenPurchaseBrand;

    public Integer getBrandStoreId() {
        return brandStoreId;
    }

    public void setBrandStoreId(Integer brandStoreId) {
        this.brandStoreId = brandStoreId;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public Integer getIsOpenPurchaseBrand() {
        return isOpenPurchaseBrand;
    }

    public void setIsOpenPurchaseBrand(Integer isOpenPurchaseBrand) {
        this.isOpenPurchaseBrand = isOpenPurchaseBrand;
    }
}
