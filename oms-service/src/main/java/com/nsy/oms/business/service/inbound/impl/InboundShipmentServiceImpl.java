package com.nsy.oms.business.service.inbound.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.oms.business.domain.response.inbound.InboundShipmentPlanTotalNum;
import com.nsy.oms.business.service.inbound.InboundShipmentService;
import com.nsy.oms.repository.sql.mapper.inbound.InboundShipmentEntityMapper;
import com.nsy.oms.repository.entity.inbound.InboundShipmentEntity;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【inbound_shipment(入库货件详情)】的数据库操作Service实现
 * @createDate 2025-03-28 10:34:20
 */
@Service
public class InboundShipmentServiceImpl extends ServiceImpl<InboundShipmentEntityMapper, InboundShipmentEntity> implements InboundShipmentService {

    @Override
    public List<InboundShipmentEntity> getByPlanIds(List<Integer> planIds) {
        return this.list(Wrappers.<InboundShipmentEntity>lambdaQuery().in(InboundShipmentEntity::getPlanId, planIds));
    }

    @Override
    public List<InboundShipmentPlanTotalNum> countQuantityShippedNumGroupUpPlanId(List<Integer> planIds) {
        if (CollectionUtils.isEmpty(planIds)) {
            return Collections.emptyList();
        }
        List<InboundShipmentPlanTotalNum> inboundShipmentPlanTotalNums = this.baseMapper.countQuantityShippedNumGroupUpPlanId(planIds);
        return CollectionUtils.isEmpty(inboundShipmentPlanTotalNums) ? Collections.emptyList() : inboundShipmentPlanTotalNums;
    }

    @Override
    public void deleteByFbaInboundShipmentPlanId(Integer planId) {
        LambdaQueryWrapper<InboundShipmentEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(InboundShipmentEntity::getPlanId, planId);
        this.remove(lambdaQueryWrapper);
    }

    @Override
    public List<InboundShipmentEntity> getListByStoreIdAndStatus(Integer storeId, List<String> statuses) {
        LambdaQueryWrapper<InboundShipmentEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(InboundShipmentEntity::getStoreId, storeId);
        lambdaQueryWrapper.in(InboundShipmentEntity::getStatus, statuses);
        return this.list(lambdaQueryWrapper);
    }

    @Override
    public InboundShipmentEntity getInboundShipment(String shipmentId) {
        LambdaQueryWrapper<InboundShipmentEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(InboundShipmentEntity::getShipmentId, shipmentId);
        return this.getOne(lambdaQueryWrapper);
    }

}




