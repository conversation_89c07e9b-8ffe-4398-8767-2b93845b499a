package com.nsy.oms.business.domain.request.sa;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @date 2022/12/13 10:30
 */
public class StoreInfoRequest {
    @ApiModelProperty("店铺id")
    private Integer storeId;
    @ApiModelProperty("授权类型 AMAZON:亚马逊授权 SHOPIFY:shopify授权  ALI_INTER:阿里国际授权 WMT:沃尔玛授权 TAO_BAO 淘宝授权 WHOLESALE:1688授权 PDD:拼多多授权 COMMON:通用")
    private String type;

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
