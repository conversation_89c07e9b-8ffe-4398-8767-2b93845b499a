package com.nsy.oms.business.domain.download.amazon;

import com.nsy.api.core.apicore.annotation.NsyExcelProperty;

/**
 * fba智能补货列表导出
 *
 * <AUTHOR>
 * @date 2023-03-03
 */
public class AmazonOrderProfitMonitoringSkcExport {
    @NsyExcelProperty("SPU")
    private String spu;
    @NsyExcelProperty("商品图片")
    private String imageUrl;
    @NsyExcelProperty("品类")
    private String categoryName;
    @NsyExcelProperty("父asin")
    private String parentAsin;
    @NsyExcelProperty("部门")
    private String department;
    @NsyExcelProperty("店铺名称")
    private String storeName;
    @NsyExcelProperty("季节")
    private String season;
    @NsyExcelProperty("开发季节")
    private String developSeason;
    @NsyExcelProperty("skc")
    private String skc;
    @NsyExcelProperty("7|14|30天销量")
    private String saleQty;

    @NsyExcelProperty("库存(泉州|海外|在途)")
    private String inv;

    @NsyExcelProperty("7|14|30天SKC利润率(%)")
    private String profitRate;

    @NsyExcelProperty("广告费率(%)")
    private String adCostRate;

    @NsyExcelProperty("退货率(%)")
    private String returnRate;

    @NsyExcelProperty("平均价格($)/活动价(原价)")
    private String price;

    @NsyExcelProperty("近24H订单成交价($)")
    private String orderPriceIn24Hours;

    @NsyExcelProperty("近24H利润率")
    private String profitRateIn24Hours;

    @NsyExcelProperty("当前档(佣金率|盈亏平衡价)")
    private String currentLevel;

    @NsyExcelProperty("第1档(<$9.99)\n佣金率5%\nFBA配送费：-$0.77")
    private String levelOneProfitRate;

    @NsyExcelProperty("第2档($10-$14.99)\n佣金率5%\n售价$:14.99")
    private String levelTwoProfitRate;

    @NsyExcelProperty("第3档($15-$19.99)\n佣金率10%\n售价$:19.99")
    private String levelThreeProfitRate;

    @NsyExcelProperty("第4档(>=$20)\n佣金率17%\n售价$:20.99")
    private String levelFourProfitRate;

    public String getSpu() {
        return spu;
    }

    public void setSpu(String spu) {
        this.spu = spu;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getParentAsin() {
        return parentAsin;
    }

    public void setParentAsin(String parentAsin) {
        this.parentAsin = parentAsin;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getSeason() {
        return season;
    }

    public void setSeason(String season) {
        this.season = season;
    }

    public String getDevelopSeason() {
        return developSeason;
    }

    public void setDevelopSeason(String developSeason) {
        this.developSeason = developSeason;
    }

    public String getSkc() {
        return skc;
    }

    public void setSkc(String skc) {
        this.skc = skc;
    }

    public String getSaleQty() {
        return saleQty;
    }

    public void setSaleQty(String saleQty) {
        this.saleQty = saleQty;
    }

    public String getInv() {
        return inv;
    }

    public void setInv(String inv) {
        this.inv = inv;
    }

    public String getProfitRate() {
        return profitRate;
    }

    public void setProfitRate(String profitRate) {
        this.profitRate = profitRate;
    }

    public String getAdCostRate() {
        return adCostRate;
    }

    public void setAdCostRate(String adCostRate) {
        this.adCostRate = adCostRate;
    }

    public String getReturnRate() {
        return returnRate;
    }

    public void setReturnRate(String returnRate) {
        this.returnRate = returnRate;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getOrderPriceIn24Hours() {
        return orderPriceIn24Hours;
    }

    public void setOrderPriceIn24Hours(String orderPriceIn24Hours) {
        this.orderPriceIn24Hours = orderPriceIn24Hours;
    }

    public String getProfitRateIn24Hours() {
        return profitRateIn24Hours;
    }

    public void setProfitRateIn24Hours(String profitRateIn24Hours) {
        this.profitRateIn24Hours = profitRateIn24Hours;
    }

    public String getCurrentLevel() {
        return currentLevel;
    }

    public void setCurrentLevel(String currentLevel) {
        this.currentLevel = currentLevel;
    }

    public String getLevelFourProfitRate() {
        return levelFourProfitRate;
    }

    public void setLevelFourProfitRate(String levelFourProfitRate) {
        this.levelFourProfitRate = levelFourProfitRate;
    }

    public String getLevelThreeProfitRate() {
        return levelThreeProfitRate;
    }

    public void setLevelThreeProfitRate(String levelThreeProfitRate) {
        this.levelThreeProfitRate = levelThreeProfitRate;
    }

    public String getLevelTwoProfitRate() {
        return levelTwoProfitRate;
    }

    public void setLevelTwoProfitRate(String levelTwoProfitRate) {
        this.levelTwoProfitRate = levelTwoProfitRate;
    }

    public String getLevelOneProfitRate() {
        return levelOneProfitRate;
    }

    public void setLevelOneProfitRate(String levelOneProfitRate) {
        this.levelOneProfitRate = levelOneProfitRate;
    }
}




