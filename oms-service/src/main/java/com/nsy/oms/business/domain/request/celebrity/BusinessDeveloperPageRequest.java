package com.nsy.oms.business.domain.request.celebrity;

import com.nsy.oms.business.domain.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * @Author: lhh
 * @Date: 2025-04-30
 * @Description:
 */
@ApiModel("bd列表查询")
public class BusinessDeveloperPageRequest extends PageRequest {
    @ApiModelProperty("ids")
    private List<Integer> ids;
    @ApiModelProperty("客服邮箱")
    private String bdEmail;
    @ApiModelProperty("客服邮箱")
    private List<String> bdEmailList;
    @ApiModelProperty("客服名称")
    private String bdName;
    @ApiModelProperty("状态")
    private Integer status;
    @ApiModelProperty("创建开始日期")
    private Date createStartDate;
    @ApiModelProperty("创建结束日期")
    private Date createEndDate;

    public List<Integer> getIds() {
        return ids;
    }

    public void setIds(List<Integer> ids) {
        this.ids = ids;
    }

    public String getBdEmail() {
        return bdEmail;
    }

    public void setBdEmail(String bdEmail) {
        this.bdEmail = bdEmail;
    }

    public List<String> getBdEmailList() {
        return bdEmailList;
    }

    public void setBdEmailList(List<String> bdEmailList) {
        this.bdEmailList = bdEmailList;
    }

    public String getBdName() {
        return bdName;
    }

    public void setBdName(String bdName) {
        this.bdName = bdName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreateStartDate() {
        return createStartDate;
    }

    public void setCreateStartDate(Date createStartDate) {
        this.createStartDate = createStartDate;
    }

    public Date getCreateEndDate() {
        return createEndDate;
    }

    public void setCreateEndDate(Date createEndDate) {
        this.createEndDate = createEndDate;
    }
}
