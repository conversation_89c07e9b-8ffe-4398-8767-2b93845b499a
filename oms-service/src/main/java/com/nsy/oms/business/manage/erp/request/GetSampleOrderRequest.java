package com.nsy.oms.business.manage.erp.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: linCheng
 * @create: 2024-09-09 09:31
 **/
public class GetSampleOrderRequest {
    @JsonProperty("TidList")
    private List<String> tidList;
    @JsonProperty("Tid")
    private String tid;
    @JsonProperty("Location")
    private String location;
    @JsonProperty("DeliveryDateBegin")
    private Date deliveryDateBegin;
    @JsonProperty("DeliveryDateEnd")
    private Date deliveryDateEnd;

    public List<String> getTidList() {
        return tidList;
    }

    public void setTidList(List<String> tidList) {
        this.tidList = tidList;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }


    public Date getDeliveryDateBegin() {
        return deliveryDateBegin;
    }

    public void setDeliveryDateBegin(Date deliveryDateBegin) {
        this.deliveryDateBegin = deliveryDateBegin;
    }

    public Date getDeliveryDateEnd() {
        return deliveryDateEnd;
    }

    public void setDeliveryDateEnd(Date deliveryDateEnd) {
        this.deliveryDateEnd = deliveryDateEnd;
    }


    public GetSampleOrderRequest() {
    }

    public GetSampleOrderRequest(String tid, String location) {
        this.tid = tid;
        this.location = location;
    }

    public GetSampleOrderRequest(String location, Date deliveryDateBegin, Date deliveryDateEnd) {
        this.location = location;
        this.deliveryDateBegin = deliveryDateBegin;
        this.deliveryDateEnd = deliveryDateEnd;
    }

    public GetSampleOrderRequest(String tid, String location, Date deliveryDateBegin, Date deliveryDateEnd) {
        this.tid = tid;
        this.location = location;
        this.deliveryDateBegin = deliveryDateBegin;
        this.deliveryDateEnd = deliveryDateEnd;
    }
}
