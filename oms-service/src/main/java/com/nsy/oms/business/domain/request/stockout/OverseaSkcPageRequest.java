package com.nsy.oms.business.domain.request.stockout;

import com.nsy.api.core.apicore.page.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * 海外仓skc表(分页用，主表)
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@ApiModel("海外仓skc表分页请求")
public class OverseaSkcPageRequest extends PageRequest {
    /**
     * 主键，无业务含义
     */
    @ApiModelProperty(value = "主键", name = "skcId")
    private List<Integer> skcIdList;

    /**
     * 部门
     */
    @ApiModelProperty(value = "部门", name = "department")
    private String department;

    /**
     * 补货组ID
     */
    @ApiModelProperty(value = "补货组ID", name = "replenishmentGroupId")
    private Integer replenishmentGroupId;

    /**
     * skc
     */
    @ApiModelProperty(value = "skc", name = "skc")
    private String skc;

    /**
     * 标签名称列表
     */
    @ApiModelProperty(value = "标签名称列表", name = "labelList")
    private List<String> labelList;

    /**
     * skcList
     */
    @ApiModelProperty(value = "skcList", name = "skcList")
    private List<String> skcList;

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getSkc() {
        return skc;
    }

    public void setSkc(String skc) {
        this.skc = skc;
    }

    public List<String> getLabelList() {
        return labelList;
    }

    public void setLabelList(List<String> labelList) {
        this.labelList = labelList;
    }

    public Integer getReplenishmentGroupId() {
        return replenishmentGroupId;
    }

    public void setReplenishmentGroupId(Integer replenishmentGroupId) {
        this.replenishmentGroupId = replenishmentGroupId;
    }

    public List<Integer> getSkcIdList() {
        return skcIdList;
    }

    public void setSkcIdList(List<Integer> skcIdList) {
        this.skcIdList = skcIdList;
    }

    public List<String> getSkcList() {
        return skcList;
    }

    public void setSkcList(List<String> skcList) {
        this.skcList = skcList;
    }
}