package com.nsy.oms.business.service.download.inbound;

import com.nsy.api.core.apicore.response.CustomExcelResponse;
import com.nsy.api.core.apicore.util.NsyExcelUtil;
import com.nsy.api.oms.enums.QuartzDownloadQueueTypeEnum;
import com.nsy.oms.business.domain.download.inbound.InboundShipmentExport;
import com.nsy.oms.business.domain.request.download.DownloadRequest;
import com.nsy.oms.business.domain.request.inbound.ShipmentPlanPageRequest;
import com.nsy.oms.business.domain.response.base.DownloadResponse;
import com.nsy.oms.business.domain.response.base.PageResponse;
import com.nsy.oms.business.domain.response.inbound.InboundPlan;
import com.nsy.oms.business.domain.response.inbound.InboundShipment;
import com.nsy.oms.business.service.download.IDownloadService;
import com.nsy.oms.business.service.inbound.InboundCreateService;
import com.nsy.oms.utils.BeanUtils;
import com.nsy.oms.utils.JsonMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * @description:
 * @author: linCheng
 * @create: 2025-04-03 10:06
 **/
@Slf4j
@Service
public class InboundShipmentDownloadServiceImpl implements IDownloadService {

    @Resource
    private InboundCreateService inboundCreateService;

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.OMS_FBT_SHIPMENT;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        log.info("InboundShipmentDownloadServiceImpl-DownloadRequest:{}", JsonMapper.toJson(request));
        DownloadResponse response = new DownloadResponse();
        ShipmentPlanPageRequest shipmentPlanPageRequest = JsonMapper.fromJson(request.getRequestContent(), ShipmentPlanPageRequest.class);
        shipmentPlanPageRequest.setPageIndex(request.getPageIndex());
        shipmentPlanPageRequest.setPageSize(request.getPageSize());
        PageResponse<InboundPlan> inboundPlanPageResponse = inboundCreateService.page(shipmentPlanPageRequest);
        Collection<InboundPlan> inboundPlans = inboundPlanPageResponse.getContent();
        if (CollectionUtils.isEmpty(inboundPlans)) {
            return DownloadResponse.of("[]", 0L);
        }

        List<List<Object>> data = new ArrayList<>();
        inboundPlans.forEach(inboundPlan -> {
            List<InboundShipment> inboundShipments = inboundPlan.getInboundShipments();
            if (CollectionUtils.isEmpty(inboundShipments)) {
                return;
            }
            if (CollectionUtils.isNotEmpty(shipmentPlanPageRequest.getIds())) {
                inboundShipments.forEach(inboundShipment -> {
                    if (shipmentPlanPageRequest.getIds().contains(inboundShipment.getId())) {
                        setData(inboundPlan, inboundShipment, data);
                    }
                });
            } else {
                inboundShipments.forEach(inboundShipment -> {
                    setData(inboundPlan, inboundShipment, data);
                });
            }
        });

        CustomExcelResponse excelResponse = new CustomExcelResponse();
        excelResponse.setHeaders(NsyExcelUtil.getCommonHeads(InboundShipmentExport.class));
        excelResponse.setData(data);
        response.setTotalCount(CollectionUtils.isEmpty(shipmentPlanPageRequest.getPlanIds()) ? inboundPlanPageResponse.getTotalCount() : shipmentPlanPageRequest.getIds().size());
        response.setDataJsonStr(JsonMapper.toJson(excelResponse));
        log.info("InboundShipmentDownloadServiceImpl-DownloadResponse:{}", JsonMapper.toJson(response));
        return response;
    }


    private void setData(InboundPlan inboundPlan,
                         InboundShipment inboundShipment,
                         List<List<Object>> data) {
        InboundShipmentExport inboundShipmentExport = new InboundShipmentExport();
        BeanUtils.copyPropertiesIgnoreNull(inboundShipment, inboundShipmentExport);
        inboundShipmentExport.setErpTid(inboundPlan.getErpTid());
        data.add(NsyExcelUtil.getData(InboundShipmentExport.class, inboundShipmentExport));
    }

}
