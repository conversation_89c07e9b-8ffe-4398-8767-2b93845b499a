package com.nsy.oms.business.domain.upload.sa;

import com.nsy.api.core.apicore.annotation.NsyExcelProperty;
import com.nsy.oms.business.domain.upload.BaseErrorTemplate;

import javax.validation.constraints.NotBlank;

public class BdEmailImport extends BaseErrorTemplate {


    /**
     * 邮箱
     */
    @NotBlank(message = "邮箱不允许为空")
    @NsyExcelProperty("邮箱")
    private String email;

    /**
     * 子账号JSON串
     */
    @NsyExcelProperty("子账号")
    private String childAccount;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getChildAccount() {
        return childAccount;
    }

    public void setChildAccount(String childAccount) {
        this.childAccount = childAccount;
    }
}
