package com.nsy.oms.business.service.report;


import com.baomidou.mybatisplus.extension.service.IService;
import com.nsy.api.transfer.domain.common.TiktokAuthResponse;
import com.nsy.oms.business.domain.dto.BaseParameterDTO;
import com.nsy.oms.business.domain.dto.TkStatementParameterDTO;
import com.nsy.oms.repository.entity.report.ReportTkStatementEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【report_tk_statement(TK账单表)】的数据库操作Service
 * @createDate 2025-05-08 15:02:13
 */
public interface ReportTkStatementService extends IService<ReportTkStatementEntity> {

    void saveRecords(List<TiktokAuthResponse> data, TkStatementParameterDTO tkStatementParameterDTO);

    void saveTransactionRecords(List<TiktokAuthResponse> data, BaseParameterDTO tkStatementParameterDTO);
}
