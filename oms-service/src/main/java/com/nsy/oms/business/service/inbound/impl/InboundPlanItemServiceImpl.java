package com.nsy.oms.business.service.inbound.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.oms.business.service.inbound.InboundPlanItemService;
import com.nsy.oms.repository.entity.inbound.InboundPlanItemEntity;
import com.nsy.oms.repository.sql.mapper.inbound.InboundPlanItemEntityMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【inbound_plan_item(fba补货单item)】的数据库操作Service实现
 * @createDate 2025-03-28 10:34:20
 */
@Service
public class InboundPlanItemServiceImpl extends ServiceImpl<InboundPlanItemEntityMapper, InboundPlanItemEntity> implements InboundPlanItemService {

    @Override
    public List<InboundPlanItemEntity> getByPlanIds(List<Integer> planIds) {
        return this.list(Wrappers.<InboundPlanItemEntity>lambdaQuery().in(InboundPlanItemEntity::getPlanId, planIds));
    }

    @Override
    public List<InboundPlanItemEntity> getByPlanId(Integer id) {
        LambdaQueryWrapper<InboundPlanItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InboundPlanItemEntity::getPlanId, id);
        return this.list(queryWrapper);
    }

}




