package com.nsy.oms.business.domain.request.sa;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.oms.business.domain.request.base.PageRequest;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/2 14:46
 */
public class SaAccountContactInfoApplyRequest extends PageRequest {
    @ApiModelProperty("修改使用")
    private Integer contactInfoApplyId;
    @ApiModelProperty("工作流统一用这个")
    private Integer taskId;
    /**
     * 账号类型 0 邮箱 1 手机
     */
    @ApiModelProperty("账号类型 0 邮箱 1 手机")
    private Integer accountType;
    /**
     * 附件管理
     */
    @ApiModelProperty("file_url")
    private List<FileRequest> fileUrlList;

    /**
     * 部门
     */
    @ApiModelProperty("部门")
    private String department;
    @ApiModelProperty("账号")
    private List<String> accountList;
    @ApiModelProperty("创建时间开始")
    private Date createDateStart;

    @ApiModelProperty("创建时间结束")
    private Date createDateEnd;
    /**
     * 二级部门
     */
    @ApiModelProperty("二级部门")
    private String secondDepartment;
    private Integer phoneId;
    /**
     * 邮件服务商
     */
    @ApiModelProperty("邮件服务商")
    private String serviceProvider;
    @ApiModelProperty("卡管理者")
    private String cardManager;

    /**
     * 主体区域 1:国内 2:香港 3:美国 4:其他境外
     */
    @ApiModelProperty("主体区域 1:国内 2:香港 3:美国 4:其他境外")
    private String mainArea;

    /**
     * 审核状态 0:待发起审批 1:待事业部负责人审批 2:待财务分配主体 3:待分配电话 4:待分配邮箱 5:待填写账号审批完成取消
     */
    @ApiModelProperty("审核状态 0:待发起审批 1:待事业部负责人审批 2:待财务分配主体 3:待分配电话 4:待分配邮箱 5:待填写账号审批完成取消")
    private Integer status;

    /**
     * 申请说明
     */
    @ApiModelProperty("申请说明")
    private String applyRemark;

    /**
     * 说明
     */
    @ApiModelProperty("说明")
    private String remark;


    /**
     * 账号
     */
    @ApiModelProperty("账号")
    private String account;

    /**
     * 续费日期
     */
    @ApiModelProperty("续费日期")
    @JsonFormat(pattern = "yyyy-mm-dd")
    private Date renewalDate;

    /**
     * 到期日期
     */
    @ApiModelProperty("到期日期")
    @JsonFormat(pattern = "yyyy-mm-dd")
    private Date expiryDate;

    /**
     * 账号主体id
     */
    @ApiModelProperty("账号主体id/公司主体")
    private Integer accountSubjectId;

    public String getCardManager() {
        return cardManager;
    }

    public void setCardManager(String cardManager) {
        this.cardManager = cardManager;
    }

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public List<FileRequest> getFileUrlList() {
        return fileUrlList;
    }

    public void setFileUrlList(List<FileRequest> fileUrlList) {
        this.fileUrlList = fileUrlList;
    }

    public Integer getContactInfoApplyId() {
        return contactInfoApplyId;
    }

    public void setContactInfoApplyId(Integer contactInfoApplyId) {
        this.contactInfoApplyId = contactInfoApplyId;
    }

    public List<String> getAccountList() {
        return accountList;
    }

    public void setAccountList(List<String> accountList) {
        this.accountList = accountList;
    }

    public Date getCreateDateStart() {
        return createDateStart;
    }

    public void setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
    }

    public Date getCreateDateEnd() {
        return createDateEnd;
    }

    public void setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
    }

    public Integer getAccountType() {
        return accountType;
    }

    public void setAccountType(Integer accountType) {
        this.accountType = accountType;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getSecondDepartment() {
        return secondDepartment;
    }

    public void setSecondDepartment(String secondDepartment) {
        this.secondDepartment = secondDepartment;
    }

    public String getServiceProvider() {
        return serviceProvider;
    }

    public void setServiceProvider(String serviceProvider) {
        this.serviceProvider = serviceProvider;
    }

    public String getMainArea() {
        return mainArea;
    }

    public void setMainArea(String mainArea) {
        this.mainArea = mainArea;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getApplyRemark() {
        return applyRemark;
    }

    public void setApplyRemark(String applyRemark) {
        this.applyRemark = applyRemark;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getPhoneId() {
        return phoneId;
    }

    public void setPhoneId(Integer phoneId) {
        this.phoneId = phoneId;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public Date getRenewalDate() {
        return renewalDate;
    }

    public void setRenewalDate(Date renewalDate) {
        this.renewalDate = renewalDate;
    }

    public Date getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(Date expiryDate) {
        this.expiryDate = expiryDate;
    }

    public Integer getAccountSubjectId() {
        return accountSubjectId;
    }

    public void setAccountSubjectId(Integer accountSubjectId) {
        this.accountSubjectId = accountSubjectId;
    }
}
