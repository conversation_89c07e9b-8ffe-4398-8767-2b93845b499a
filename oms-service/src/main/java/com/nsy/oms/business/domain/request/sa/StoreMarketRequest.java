package com.nsy.oms.business.domain.request.sa;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @date 2022/12/14 17:28
 */
public class StoreMarketRequest {

    @ApiModelProperty("id")
    private Integer id;

    /**
     * 店铺id
     */
    @ApiModelProperty("店铺id")
    private Integer storeId;

    /**
     * 市场id
     */
    @ApiModelProperty("市场id")
    private String marketListId;

    /**
     * 市场名称
     */
    @ApiModelProperty("市场名称")
    private String marketListName;


    @ApiModelProperty("状态 0 删除 1正常")
    private Integer status;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getMarketListId() {
        return marketListId;
    }

    public void setMarketListId(String marketListId) {
        this.marketListId = marketListId;
    }

    public String getMarketListName() {
        return marketListName;
    }

    public void setMarketListName(String marketListName) {
        this.marketListName = marketListName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
