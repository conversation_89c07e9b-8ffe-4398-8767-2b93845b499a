package com.nsy.oms.business.service.celebrity.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.oms.business.service.celebrity.InternetCelebrityNameRecordService;
import com.nsy.oms.constants.MybatisQueryConstant;
import com.nsy.oms.repository.entity.celebrity.InternetCelebrityNameRecordEntity;
import com.nsy.oms.repository.sql.mapper.celebrity.InternetCelebrityNameRecordMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
public class InternetCelebrityNameRecordServiceImpl
        extends ServiceImpl<InternetCelebrityNameRecordMapper, InternetCelebrityNameRecordEntity>
        implements InternetCelebrityNameRecordService {
    @Override
    public void updateNameRecord(Integer internetCelebrityId, String internetCelebrityNo, String internetCelebrityName) {
        if (StringUtils.hasText(internetCelebrityName)) {
            InternetCelebrityNameRecordEntity recordEntity = Optional.ofNullable(this.findLatestByInternetCelebrityId(internetCelebrityId)).orElseGet(InternetCelebrityNameRecordEntity::new);
            if (!Objects.equals(internetCelebrityName, recordEntity.getInternetCelebrityName())) {
                InternetCelebrityNameRecordEntity entity = new InternetCelebrityNameRecordEntity();
                entity.setInternetCelebrityId(internetCelebrityId);
                entity.setInternetCelebrityNo(internetCelebrityNo);
                entity.setInternetCelebrityName(internetCelebrityName);
                save(recordEntity);
            }
        }
    }

    private InternetCelebrityNameRecordEntity findLatestByInternetCelebrityId(Integer internetCelebrityId) {
        return this.getOne(new LambdaQueryWrapper<InternetCelebrityNameRecordEntity>()
                .eq(InternetCelebrityNameRecordEntity::getInternetCelebrityId, internetCelebrityId)
                .orderByDesc(InternetCelebrityNameRecordEntity::getId).last(MybatisQueryConstant.QUERY_FIRST));
    }

    @Override
    public List<InternetCelebrityNameRecordEntity> listByCelebrityIds(List<Integer> celebrityIds) {
        return this.list(new LambdaQueryWrapper<InternetCelebrityNameRecordEntity>()
                .in(InternetCelebrityNameRecordEntity::getInternetCelebrityId, celebrityIds));
    }

    @Override
    public InternetCelebrityNameRecordEntity findTopByCelebrityNameOrderByIdDesc(String celebrityName) {
        return this.getOne(new LambdaQueryWrapper<InternetCelebrityNameRecordEntity>()
                .eq(InternetCelebrityNameRecordEntity::getInternetCelebrityName, celebrityName)
                .orderByDesc(InternetCelebrityNameRecordEntity::getId)
                .last("limit 1")
        );
    }
}