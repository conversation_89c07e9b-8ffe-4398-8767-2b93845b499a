package com.nsy.oms.business.manage.tms;

import cn.hutool.core.util.StrUtil;
import com.nsy.api.core.apicore.util.NsyJacksonUtils;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.oms.business.manage.tms.request.OrderTrackingChannelRequest;
import com.nsy.oms.business.manage.tms.response.BdShipperAddressPageResponse;
import com.nsy.oms.business.manage.tms.response.BdShipperAddressResponse;
import com.nsy.oms.business.manage.tms.response.OrderTrackingChannelResponse;
import com.nsy.oms.business.manage.tms.response.PackageInfo;
import com.nsy.oms.business.manage.tms.response.TmsLogisticsChannelsConfig;
import com.nsy.oms.business.manage.tms.response.TmsPackageResponse;
import com.nsy.oms.business.manage.user.request.shared.SelectModel;
import com.nsy.oms.utils.JsonMapper;
import com.nsy.oms.utils.mp.LocationContext;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * <AUTHOR>
 */
@Service
public class TmsApiService {
    private static final Logger LOGGER = LoggerFactory.getLogger(TmsApiService.class);
    @Inject
    private RestTemplate restTemplate;
    @Value("${nsy.service.url.tms}")
    private String tmsServiceUrl;

    /**
     * 获取默认地址
     */
    public List<BdShipperAddressResponse> getList() {
        List<BdShipperAddressResponse> categoryResponse = new ArrayList<>();
        String uri = String.format("%s/bd-shipper-address/list ", tmsServiceUrl);
        try {
            // 1.请求头
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add("location", "QUANZHOU");
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            // 2.请求头 & 请求体
            Map<String, Integer> map = new HashMap<>();
            map.put("pageSize", 10000);
            HttpEntity<String> fromEntity = new HttpEntity<>(JsonMapper.toJson(map), httpHeaders);
            ResponseEntity<String> respEntity = this.restTemplate.postForEntity(uri, fromEntity, String.class);
            if (StringUtils.hasText(respEntity.getBody())) {
                BdShipperAddressPageResponse bdShipperAddressPageResponse = JsonMapper.fromJson(respEntity.getBody(), BdShipperAddressPageResponse.class);
                categoryResponse = bdShipperAddressPageResponse.getContent();
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return categoryResponse;
    }


    public List<PackageInfo> newTmsPackageList(List<String> logisticsNos) {
        if (CollectionUtils.isEmpty(logisticsNos)) {
            return Collections.emptyList();
        }
        List<PackageInfo> packageInfos = new ArrayList<>();
        String uri = String.format("%s/new/tms-package/list", tmsServiceUrl);
        try {
            // 1.请求头
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add("Location", "QUANZHOU");
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            // 2.请求头 & 请求体
            Map<String, Object> map = new HashMap<>();
            map.put("pageSize", 10000);
            map.put("location", "QUANZHOU");
            map.put("logisticsNos", logisticsNos);
            LOGGER.info("TmsApiService.newTmsPackageList.request:{}", JsonMapper.toJson(map));
            HttpEntity<String> fromEntity = new HttpEntity<>(JsonMapper.toJson(map), httpHeaders);
            ResponseEntity<String> respEntity = this.restTemplate.postForEntity(uri, fromEntity, String.class);
            LOGGER.info("TmsApiService.newTmsPackageList.response:{}", respEntity.getBody());
            if (StringUtils.hasText(respEntity.getBody())) {
                TmsPackageResponse tmsPackageResponse = JsonMapper.fromJson(respEntity.getBody(), TmsPackageResponse.class);
                packageInfos = tmsPackageResponse.getContent();
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return packageInfos;
    }

    /**
     * FBM：通过订单号获取物流信息
     */
    public List<OrderTrackingChannelResponse> getLogisticsInfo(OrderTrackingChannelRequest request) {
        List<OrderTrackingChannelResponse> categoryResponse = new ArrayList<>();
        String uri = String.format("%s/tracking/order-channel", tmsServiceUrl);
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add("Location", LocationContext.getLocation());
            HttpEntity<OrderTrackingChannelRequest> entity = new HttpEntity<>(request, headers);
            LOGGER.info("tms getLogisticsInfo request:{}", NsyJacksonUtils.toJson(request));
            ResponseEntity<String> respEntity = this.restTemplate.postForEntity(uri, entity, String.class);
            LOGGER.info("tms getLogisticsInfo response:{}", NsyJacksonUtils.toJson(respEntity));
            if (Objects.nonNull(respEntity.getBody())) {
                categoryResponse = JsonMapper.jsonStringToObjectArray(respEntity.getBody(), OrderTrackingChannelResponse.class);
            }
        } catch (Exception e) {
            LOGGER.error("tms getLogisticsInfo error: orderNo: {}, msg: {}", request.getOrderNoList(), e.getMessage());
        }
        return categoryResponse;
    }

    public List<SelectModel> getLogisticsCompanyByDeliverLocation(String location) {
        String uri = String.format("%s/logistics-company/fba-logistics/%s", tmsServiceUrl, location);
        ResponseEntity<String> forEntity = restTemplate.getForEntity(uri, String.class);
        return JsonMapper.jsonStringToObjectArray(forEntity.getBody(), SelectModel.class);
    }
    public TmsLogisticsChannelsConfig getByLogisticsChannelNames(String location, String logisticsChannelName) {
        LOGGER.debug("getByLogisticsChannelName,请求:{}", logisticsChannelName);
        String uri = StrUtil.format("{}/logistics-channel-config/logistics-channel-name/{}/{}", tmsServiceUrl, location, logisticsChannelName);
        TmsLogisticsChannelsConfig forObject = restTemplate.getForObject(uri, TmsLogisticsChannelsConfig.class);
        LOGGER.debug("getByLogisticsChannelName,响应:{}", JsonMapper.toJson(forObject));
        return forObject;
    }

}
