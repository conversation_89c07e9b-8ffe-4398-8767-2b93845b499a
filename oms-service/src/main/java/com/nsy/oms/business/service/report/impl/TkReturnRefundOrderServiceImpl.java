package com.nsy.oms.business.service.report.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.transfer.domain.common.TiktokAuthResponse;
import com.nsy.api.transfer.domain.request.platform.tiktok.TiktokReturnOrderQueryRequest;
import com.nsy.api.transfer.domain.response.platform.tiktok.refund.TiktokRefundDataResponse;
import com.nsy.api.transfer.enums.PlatformTypeEnum;
import com.nsy.oms.business.domain.dto.TkReturnRefundRecordsParameterDTO;
import com.nsy.oms.business.manage.transfer.TransferApiService;
import com.nsy.oms.business.service.report.TkReturnRefundOrderService;
import com.nsy.oms.repository.mongo.document.tiktok.ReportTkReturnRefundDocument;
import com.nsy.oms.repository.mongo.repository.tiktok.ReportTkReturnRefundDocumentRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class TkReturnRefundOrderServiceImpl implements TkReturnRefundOrderService {

    @Autowired
    private TransferApiService transferApiService;

    @Autowired
    private ReportTkReturnRefundDocumentRepository reportTkReturnRefundDocumentRepository;

    @Override
    public void saveReturnRefundOrderRecords(List<TiktokAuthResponse> data, TkReturnRefundRecordsParameterDTO parameterDTO) {
        if (!CollectionUtils.isEmpty(data)) {
            data.forEach(item -> {
                // 如果不为空就只执行一个店铺
                if (Objects.nonNull(parameterDTO.getStoreId()) && !item.getStoreId().equals(parameterDTO.getStoreId())) {
                    return;
                }
                doGetRecords(parameterDTO, item);
            });
        }
    }

    private void doGetRecords(TkReturnRefundRecordsParameterDTO parameterDTO, TiktokAuthResponse item) {
        if (StringUtils.isNotBlank(item.getAccessToken())) {
            PlatformTypeEnum platformTypeEnum = PlatformTypeEnum.getPlatform(item.getPlatformId());
            if (platformTypeEnum == null) {
                log.error("根据ID获取平台失败！");
                return;
            }
            TiktokReturnOrderQueryRequest request = new TiktokReturnOrderQueryRequest();
            request.setStoreId(item.getStoreId());
            request.setPageSize(parameterDTO.getFetchCount());
            request.setPlatform(platformTypeEnum);

            if (StringUtils.isNotBlank(parameterDTO.getStartDate()) || StringUtils.isNotBlank(parameterDTO.getEndDate())) {
                setDate(parameterDTO, request);
            } else {
                // 获取两天前的日期，并设置为当天的零点
                Date twoDaysAgo = DateUtil.offsetDay(new Date(), -2);
                String twoDaysAgoStr = DateUtil.format(DateUtil.beginOfDay(twoDaysAgo), "yyyy-MM-dd HH:mm:ss");
                request.setUpdateTimeGe(twoDaysAgoStr);
                String twoDaysAgoEndStr = DateUtil.format(DateUtil.endOfDay(twoDaysAgo), "yyyy-MM-dd HH:mm:ss");
                request.setUpdateTimeLt(twoDaysAgoEndStr);
            }
            try {
                getTiktokReturnRefundList(platformTypeEnum, request, item);
            } catch (InterruptedException e) {
                log.error(String.format("%s获取退货退款列表失败:%s", item.getStoreId(), e.getMessage()));
            }
        }
    }

    private static void setDate(TkReturnRefundRecordsParameterDTO parameterDTO, TiktokReturnOrderQueryRequest request) {
        if (StringUtils.isNotBlank(parameterDTO.getStartDate())) {
            request.setUpdateTimeGe(parameterDTO.getStartDate());
        }
        if (StringUtils.isNotBlank(parameterDTO.getEndDate())) {
            request.setUpdateTimeLt(parameterDTO.getEndDate());
        }
    }

    private void getTiktokReturnRefundList(PlatformTypeEnum platformTypeEnum, TiktokReturnOrderQueryRequest request, TiktokAuthResponse tiktokAuth) throws InterruptedException {
        TiktokRefundDataResponse returnRefundList = null;
        int maxRetries = 5;
        int currentRetry = 0;
        boolean success = false;

        while (!success && currentRetry < maxRetries) {
            try {
                returnRefundList = transferApiService.getTkReturnRefundList(platformTypeEnum.getUrl(), request, tiktokAuth);
                log.info("获取tk退货退款列表：{}", returnRefundList);
                success = true;
                currentRetry = 0; // 请求成功后重置重试次数
            } catch (Exception e) {
                currentRetry++;
                log.error("获取tk退货退款列表失败，当前重试次数：{}，错误信息：{}", currentRetry, e.getMessage());
                if (currentRetry < maxRetries) {
                    Thread.sleep(5000); // 等待5秒后重试
                } else {
                    log.error("获取tk退货退款列表失败，已达到最大重试次数：{}", maxRetries);
                    throw new BusinessServiceException("获取tk退货退款列表失败，已达到最大重试次数");
                }
            }
        }

        if (returnRefundList.getCode() != 0 || Objects.isNull(returnRefundList.getData()) || CollectionUtils.isEmpty(returnRefundList.getData().getReturnOrders())) {
            return;
        }

        List<ReportTkReturnRefundDocument> list = new ArrayList<>();
        returnRefundList.getData().getReturnOrders().forEach(item -> {
            ReportTkReturnRefundDocument document = new ReportTkReturnRefundDocument();
            ReportTkReturnRefundDocument result = reportTkReturnRefundDocumentRepository.findAllByStoreIdAndReturnIdAndOrderId(tiktokAuth.getStoreId(), item.getReturnId(), item.getOrderId());
            if (Objects.nonNull(result)) {
                // 删除重复数据
                reportTkReturnRefundDocumentRepository.delete(result);
            }
            document.setStoreId(tiktokAuth.getStoreId());
            document.setStoreName(tiktokAuth.getStoreName());
            document.setReturnId(item.getReturnId());
            document.setOrderId(item.getOrderId());
            document.setCreateTime(new Date(item.getCreateTime() * 1000));
            document.setUpdateTime(new Date(item.getUpdateTime() * 1000));
            document.setContent(JSONObject.toJSONString(item));
            document.setLocation(request.getLocation());

            // 设置AbstractDocument字段
            Date now = new Date();
            document.setCreateDate(now);
            document.setUpdateDate(now);
            document.setCreateBy("system");
            document.setUpdateBy("system");

            list.add(document);
        });
        reportTkReturnRefundDocumentRepository.saveAll(list);
        if (StringUtils.isNotBlank(returnRefundList.getData().getNextPageToken())) {
            if (checkToken(request, returnRefundList)) return;
            Thread.sleep(10000);
            request.setNextPageToken(returnRefundList.getData().getNextPageToken());
            getTiktokReturnRefundList(platformTypeEnum, request, tiktokAuth);
        }


    }

    private static boolean checkToken(TiktokReturnOrderQueryRequest request, TiktokRefundDataResponse tikTokResponse) {
        if (StringUtils.isNotBlank(request.getNextPageToken()) && request.getNextPageToken().equals(tikTokResponse.getData().getNextPageToken())) {
            return true;
        }
        return false;
    }
}
