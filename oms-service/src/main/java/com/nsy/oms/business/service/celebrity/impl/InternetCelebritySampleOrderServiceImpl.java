package com.nsy.oms.business.service.celebrity.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.api.core.apicore.privilege.LoginInfoService;
import com.nsy.api.oms.dto.request.order.SampleOrderRequest;
import com.nsy.api.oms.dto.response.order.SampleOrderResponse;
import com.nsy.api.pms.dto.product.ProductSpecDTO;
import com.nsy.api.transfer.enums.PlatformTypeEnum;
import com.nsy.oms.business.domain.request.auth.PlatformAuthConfigRequest;
import com.nsy.oms.business.domain.request.celebrity.InternetCelebrityBindOrderRequest;
import com.nsy.oms.business.domain.request.celebrity.InternetCelebrityOrderRemarkRequest;
import com.nsy.oms.business.domain.request.celebrity.InternetCelebrityOrderRepairRequest;
import com.nsy.oms.business.domain.request.celebrity.InternetCelebritySampleOrderRequest;
import com.nsy.oms.business.domain.request.celebrity.SyncChangeInternetCelebrityOwnerRequest;
import com.nsy.oms.business.domain.request.sa.StoreInfoRequest;
import com.nsy.oms.business.domain.response.auth.SauPlatformAuthConfigResponse;
import com.nsy.oms.business.domain.response.celebrity.CelebrityPerformanceRatePageResponse;
import com.nsy.oms.business.domain.response.celebrity.InternetCelebritySampleOrder;
import com.nsy.oms.business.domain.response.celebrity.InternetCelebritySampleOrderPostItem;
import com.nsy.oms.business.domain.response.celebrity.SyncInternetCelebritySampleOrder;
import com.nsy.oms.business.domain.response.celebrity.SyncInternetCelebritySampleOrderItem;
import com.nsy.oms.business.domain.response.order.ResponseTokenInfo;
import com.nsy.oms.business.domain.response.sa.SaStoreDetailByErpResponse;
import com.nsy.oms.business.manage.erp.ErpApiService;
import com.nsy.oms.business.manage.erp.domain.GetSampleOrderResponse;
import com.nsy.oms.business.manage.erp.domain.SampleOrder;
import com.nsy.oms.business.manage.erp.request.GetSampleOrderRequest;
import com.nsy.oms.business.manage.omspublish.OmsPublishApiService;
import com.nsy.oms.business.manage.omspublish.response.PublishProductSpecInfo;
import com.nsy.oms.business.manage.pms.PmsApiService;
import com.nsy.oms.business.manage.pms.request.ConfigInternetCelebrityRequest;
import com.nsy.oms.business.manage.pms.response.ConfigInternetCelebrityResponse;
import com.nsy.oms.business.manage.thirdparty.ThirdPartyApiService;
import com.nsy.oms.business.manage.thirdparty.auth.AmazonAuth;
import com.nsy.oms.business.manage.thirdparty.auth.TiktokAuth;
import com.nsy.oms.business.manage.thirdparty.request.GetOrderListByOrderIdsRequest;
import com.nsy.oms.business.manage.thirdparty.request.GetPackagesRequest;
import com.nsy.oms.business.manage.thirdparty.request.GetTrackingRequest;
import com.nsy.oms.business.manage.thirdparty.response.GetOrderListByDateResponse;
import com.nsy.oms.business.manage.thirdparty.response.GetPackagesResponse;
import com.nsy.oms.business.manage.thirdparty.response.GetTrackingResponse;
import com.nsy.oms.business.manage.thirdparty.response.Order;
import com.nsy.oms.business.manage.thirdparty.response.Tracking;
import com.nsy.oms.business.manage.user.UserApiService;
import com.nsy.oms.business.manage.user.response.BdDictionaryItem;
import com.nsy.oms.business.manage.user.response.SysDepartment;
import com.nsy.oms.business.service.auth.SauAmazonConfigService;
import com.nsy.oms.business.service.auth.SauPlatformAuthConfigService;
import com.nsy.oms.business.service.celebrity.IInternetCelebritySampleOrderItemPostService;
import com.nsy.oms.business.service.celebrity.IInternetCelebritySampleOrderItemService;
import com.nsy.oms.business.service.celebrity.IInternetCelebritySampleOrderPermissionService;
import com.nsy.oms.business.service.celebrity.IInternetCelebritySampleOrderService;
import com.nsy.oms.business.service.celebrity.InternetCelebrityStoreRelationService;
import com.nsy.oms.business.service.platform.PlatformOrderService;
import com.nsy.oms.business.service.sa.SaStoreService;
import com.nsy.oms.business.service.sa.SaStoreWebsiteService;
import com.nsy.oms.constants.MybatisQueryConstant;
import com.nsy.oms.constants.StringConstant;
import com.nsy.oms.enums.DepartmentTypeEnum;
import com.nsy.oms.enums.order.OrderGrabPlatformEnum;
import com.nsy.oms.enums.tkcreator.InternetCelebrityMessageType;
import com.nsy.oms.enumstable.InternetCelebritySampleOrderDeliveryTypeEnum;
import com.nsy.oms.enumstable.InternetCelebritySampleOrderPackageStatusEnum;
import com.nsy.oms.enumstable.InternetCelebritySampleOrderTrackingSyncStatusEnum;
import com.nsy.oms.mq.producer.InternetCelebrityMessageSendService;
import com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderItemEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderItemPostEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleStoreMappingEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebrityStoreRelationEntity;
import com.nsy.oms.repository.entity.sa.SaStoreEntity;
import com.nsy.oms.repository.sql.mapper.celebrity.InternetCelebritySampleOrderMapper;
import com.nsy.oms.utils.BeanUtils;
import com.nsy.oms.utils.mp.LocationContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 网红样衣订单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-29
 */
@Slf4j
@Service
public class InternetCelebritySampleOrderServiceImpl extends ServiceImpl<InternetCelebritySampleOrderMapper, InternetCelebritySampleOrderEntity> implements IInternetCelebritySampleOrderService {

    @Autowired
    private IInternetCelebritySampleOrderItemService internetCelebritySampleOrderItemService;
    @Autowired
    private IInternetCelebritySampleOrderItemPostService internetCelebritySampleOrderItemPostService;
    @Autowired
    private SauPlatformAuthConfigService sauPlatformAuthConfigService;
    @Autowired
    private SaStoreService saStoreService;
    @Autowired
    private ThirdPartyApiService thirdPartyApiService;
    @Autowired
    private IInternetCelebritySampleOrderPermissionService internetCelebritySampleOrderPermissionService;
    @Autowired
    private SauAmazonConfigService sauAmazonConfigService;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private ErpApiService erpApiService;
    @Autowired
    private PlatformOrderService platformOrderService;
    @Autowired
    private PmsApiService pmsApiService;
    @Inject
    UserApiService userApiService;
    @Autowired
    private InternetCelebrityMessageSendService internetCelebrityMessageSendService;
    @Autowired
    private OmsPublishApiService omsPublishApiService;
    @Autowired
    private SaStoreWebsiteService saStoreWebsiteService;
    @Autowired
    private InternetCelebrityStoreRelationService internetCelebrityStoreRelationService;
    @Autowired
    private InternetCelebritySampleStoreMappingServiceImpl internetCelebritySampleStoreMappingService;

    @Override
    public List<InternetCelebritySampleOrder> getInternetCelebritySampleOrderList(InternetCelebritySampleOrderRequest request) {
        return this.baseMapper.getInternetCelebritySampleOrderList(request);
    }


    @Override
    public PageResponse<InternetCelebritySampleOrder> getInternetCelebritySampleOrderPage(InternetCelebritySampleOrderRequest request) {

        PageResponse<InternetCelebritySampleOrder> pageResponse = new PageResponse<>();
        if (StringUtils.isNotEmpty(request.getVideoUrl())) {
            List<InternetCelebritySampleOrderItemPostEntity> internetCelebritySampleOrderItemPostEntities = internetCelebritySampleOrderItemPostService.getListByVideoUrl(request.getVideoUrl());
            if (CollectionUtils.isEmpty(internetCelebritySampleOrderItemPostEntities)) {
                pageResponse.setTotalCount(0);
                pageResponse.setContent(Collections.emptyList());
                return pageResponse;
            }
            List<Integer> internetCelebritySampleOrderItemIds = internetCelebritySampleOrderItemPostEntities.stream().map(InternetCelebritySampleOrderItemPostEntity::getInternetCelebritySampleOrderItemId).collect(Collectors.toList());
            request.setInternetCelebritySampleOrderItemIds(internetCelebritySampleOrderItemIds);
        }

        IPage<InternetCelebritySampleOrder> page = this.baseMapper.getInternetCelebritySampleOrderPage(new Page<>(request.getPageIndex(), request.getPageSize()), request);

        pageResponse.setTotalCount(page.getTotal());
        List<InternetCelebritySampleOrder> internetCelebritySampleOrders = page.getRecords();
        if (CollectionUtils.isEmpty(internetCelebritySampleOrders)) {
            pageResponse.setContent(Collections.emptyList());
            return pageResponse;
        }

        List<Integer> itemIds = internetCelebritySampleOrders.stream().map(InternetCelebritySampleOrder::getInternetCelebritySampleOrderItemId).collect(Collectors.toList());
        Map<Integer, List<InternetCelebritySampleOrderItemPostEntity>> itemPostMap = internetCelebritySampleOrderItemPostService.getByInternetCelebritySampleOrderItemIds(itemIds).stream().collect(Collectors.groupingBy(InternetCelebritySampleOrderItemPostEntity::getInternetCelebritySampleOrderItemId));
        internetCelebritySampleOrders.forEach(internetCelebritySampleOrder -> {
            internetCelebritySampleOrder.setSkuStr(internetCelebritySampleOrder.getSku());
            internetCelebritySampleOrder.setTrackingSyncStatusName(InternetCelebritySampleOrderTrackingSyncStatusEnum.getDesc(internetCelebritySampleOrder.getTrackingSyncStatus()));
            internetCelebritySampleOrder.setSku(String.format("%s (%s)", internetCelebritySampleOrder.getSku(), internetCelebritySampleOrder.getQty()));
            internetCelebritySampleOrder.setInternetCelebritySampleOrderPostItems(BeanUtil.copyToList(itemPostMap.getOrDefault(internetCelebritySampleOrder.getInternetCelebritySampleOrderItemId(), Collections.emptyList()), InternetCelebritySampleOrderPostItem.class));
        });

        pageResponse.setContent(internetCelebritySampleOrders);
        return pageResponse;
    }


    @Override
    public PageResponse<InternetCelebritySampleOrder> getCelebrityPerformanceRatePage(InternetCelebritySampleOrderRequest request) {
        //分页
        IPage<InternetCelebritySampleOrder> page = this.baseMapper.getCelebrityPerformanceRatePage(new Page<>(request.getPageIndex(), request.getPageSize()), request);
        //总
        InternetCelebritySampleOrder celebrityPerformanceTotalRate = this.baseMapper.getCelebrityPerformanceTotalRate(request);

        CelebrityPerformanceRatePageResponse<InternetCelebritySampleOrder> pageResponse = CelebrityPerformanceRatePageResponse.of(page.getTotal());
        pageResponse.setTotalRate(celebrityPerformanceTotalRate);
        pageResponse.setContent(page.getRecords());
        return pageResponse;
    }

    @Override
    public List<Tracking> tracking(Integer internetCelebritySampleOrderItemId) {
        InternetCelebritySampleOrderItemEntity sampleOrderItem = internetCelebritySampleOrderItemService.getById(internetCelebritySampleOrderItemId);
        InternetCelebritySampleOrderEntity internetCelebritySampleOrderEntity = this.getById(sampleOrderItem.getInternetCelebritySampleOrderId());
        List<InternetCelebritySampleOrderItemEntity> sampleOrderItemList = internetCelebritySampleOrderItemService.getList(sampleOrderItem.getInternetCelebritySampleOrderId());
        if (!Optional.ofNullable(internetCelebritySampleOrderEntity).isPresent()) {
            throw new BusinessServiceException("订单不存在");
        }
        if (!Optional.ofNullable(sampleOrderItem).isPresent()) {
            throw new BusinessServiceException("订单明细不存在");
        }
        List<Tracking> trackings = Lists.newArrayList();
        try {
            if (InternetCelebritySampleOrderDeliveryTypeEnum.FBA.getDesc().equals(sampleOrderItem.getOrderDeliveryType())) {
                List<InternetCelebritySampleOrderItemEntity> filterItemList = sampleOrderItemList.stream()
                        .filter(t -> t.getDeliveryStoreId().equals(sampleOrderItem.getDeliveryStoreId()) && t.getPlatformPackageId().equals(sampleOrderItem.getPlatformPackageId())).collect(Collectors.toList());
                trackings.addAll(getFbaTrackingInfo(filterItemList));
            } else if (InternetCelebritySampleOrderDeliveryTypeEnum.FBT.getDesc().equals(sampleOrderItem.getOrderDeliveryType())) {
                List<InternetCelebritySampleOrderItemEntity> filterItemList = sampleOrderItemList.stream()
                        .filter(t -> t.getDeliveryStoreId().equals(sampleOrderItem.getDeliveryStoreId()) && t.getPlatformOriginalOrderNo().equals(sampleOrderItem.getPlatformOriginalOrderNo())).collect(Collectors.toList());
                trackings.addAll(getFbtTrackingInfo(filterItemList));
            }
            saveSampleOrderDate(internetCelebritySampleOrderEntity);
        } catch (Exception e) {
            log.error("IInternetCelebritySampleOrderService.tracking.error:{}", e.getMessage(), e);
        }
        return trackings;
    }

    private List<Tracking> getFbaTrackingInfo(List<InternetCelebritySampleOrderItemEntity> internetCelebritySampleOrderItemList) {
        List<Tracking> trackings = Lists.newArrayList();
        if (CollectionUtils.isEmpty(internetCelebritySampleOrderItemList)) {
            return trackings;
        }
        String platformPackageId = internetCelebritySampleOrderItemList.get(0).getPlatformPackageId();
        Integer deliveryStoreId = internetCelebritySampleOrderItemList.get(0).getDeliveryStoreId();
        AmazonAuth amazonAuth = sauAmazonConfigService.getOrderGrabAuthInfo(deliveryStoreId);
        GetTrackingRequest getTrackingRequest = new GetTrackingRequest(platformPackageId);
        getTrackingRequest.setAmazonAuth(amazonAuth);
        getTrackingRequest.setPlatform(PlatformTypeEnum.Amazon);
        getTrackingRequest.setPackageNumber(Integer.valueOf(platformPackageId));
        try {
            GetTrackingResponse getTrackingResponse = thirdPartyApiService.getTracking(getTrackingRequest);
            List<Tracking> fbaTrackings = getTrackingResponse.getTracking();
            if (CollectionUtils.isEmpty(getTrackingResponse.getTracking())) {
                return trackings;
            }
            Tracking tracking = fbaTrackings.stream()
                    .filter(track -> Optional.ofNullable(track).isPresent() && StringUtils.isNotEmpty(track.getDescription())
                            && (track.getDescription().toUpperCase(Locale.ROOT).startsWith("Package delivered".toUpperCase(Locale.ROOT)) || track.getDescription().toUpperCase(Locale.ROOT).startsWith("Delivered in the mailroom".toUpperCase(Locale.ROOT))))
                    .findAny().orElseGet(Tracking::new);
            internetCelebritySampleOrderItemList.forEach(item -> {
                if (!Optional.ofNullable(item.getOrderCompromiseDate()).isPresent() && Optional.ofNullable(tracking.getUpdateTimeMillis()).isPresent()) {
                    item.setPackageStatus(InternetCelebritySampleOrderPackageStatusEnum.SIGN.getDesc());
                    item.setOrderCompromiseDate(tracking.getUpdateTimeMillis());
                    item.setUpdateDate(new Date());
                    item.setUpdateBy(loginInfoService.getName());
                    item.setTrackingSyncStatus(InternetCelebritySampleOrderTrackingSyncStatusEnum.SUCCESS.getCode());
                    internetCelebritySampleOrderItemService.saveOrUpdate(item);
                }
            });
            trackings.add(tracking);
        } catch (Exception e) {
            log.error("getFbaTrackingInfo.tracking.error:{}", e.getMessage(), e);
            internetCelebritySampleOrderItemList.forEach(item -> {
                if (StringUtils.isNotBlank(e.getMessage())) {
                    item.setTrackingSyncErrorMessage(StringUtils.substring(e.getMessage().replaceAll("\\s", ""), 0, 190));
                }
                item.setTrackingSyncStatus(InternetCelebritySampleOrderTrackingSyncStatusEnum.ABNORMAL.getCode());
            });
        }
        return trackings;
    }

    private List<Tracking> getFbtTrackingInfo(List<InternetCelebritySampleOrderItemEntity> internetCelebritySampleOrderItemList) {
        List<Tracking> trackings = Lists.newArrayList();
        if (CollectionUtils.isEmpty(internetCelebritySampleOrderItemList)) {
            return trackings;
        }
        String platformOriginalOrderNo = internetCelebritySampleOrderItemList.get(0).getPlatformOriginalOrderNo();
        Integer deliveryStoreId = internetCelebritySampleOrderItemList.get(0).getDeliveryStoreId();
        TiktokAuth tiktokAuth = new TiktokAuth();
        SaStoreDetailByErpResponse saStoreDetailByErpResponse = this.setRequestTokenInfo(tiktokAuth, deliveryStoreId);
        GetTrackingRequest getTrackingRequest = new GetTrackingRequest(platformOriginalOrderNo);
        getTrackingRequest.setPlatform(PlatformTypeEnum.TikTok);
        getTrackingRequest.setTiktokAuth(tiktokAuth);
        try {
            GetTrackingResponse getTrackingResponse = thirdPartyApiService.getTracking(getTrackingRequest);
            saveRefreshToken(getTrackingResponse.getResponseTokenInfo(), saStoreDetailByErpResponse);
            if (CollectionUtils.isEmpty(getTrackingResponse.getTracking())) {
                return trackings;
            }
            List<Tracking> fbtTrackings = getTrackingResponse.getTracking();
            Tracking tracking = fbtTrackings.stream()
                    .filter(track -> Optional.ofNullable(track).isPresent() && StringUtils.isNotEmpty(track.getDescription()) && track.getDescription().toUpperCase(Locale.ROOT).contains("delivered".toUpperCase(Locale.ROOT)))
                    .findAny().orElseGet(Tracking::new);
            internetCelebritySampleOrderItemList.forEach(item -> {
                if (!Optional.ofNullable(item.getOrderCompromiseDate()).isPresent() && Optional.ofNullable(tracking.getUpdateTimeMillis()).isPresent()) {
                    item.setPackageStatus(InternetCelebritySampleOrderPackageStatusEnum.SIGN.getDesc());
                    item.setTrackingSyncStatus(InternetCelebritySampleOrderTrackingSyncStatusEnum.SUCCESS.getCode());
                    item.setOrderCompromiseDate(tracking.getUpdateTimeMillis());
                    item.setUpdateDate(new Date());
                    item.setUpdateBy(loginInfoService.getName());
                    internetCelebritySampleOrderItemService.saveOrUpdate(item);
                }
            });
            trackings.add(tracking);
        } catch (Exception e) {
            log.error("getFbtTrackingInfo.tracking.error:{}", e.getMessage(), e);
            internetCelebritySampleOrderItemList.forEach(item -> {
                if (StringUtils.isNotBlank(e.getMessage())) {
                    item.setTrackingSyncErrorMessage(StringUtils.substring(e.getMessage().replaceAll("\\s", ""), 0, 190));
                }
                item.setTrackingSyncStatus(InternetCelebritySampleOrderTrackingSyncStatusEnum.ABNORMAL.getCode());
            });
        }
        return trackings;
    }

    @Override
    public void repair(InternetCelebrityOrderRepairRequest request) {
        List<SyncInternetCelebritySampleOrder> syncInternetCelebritySampleOrders = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(request.getFbaPlatformOrderNos())) {
            request.getFbaPlatformOrderNos().forEach(orderNo -> {
                getDataByErp(syncInternetCelebritySampleOrders, new GetSampleOrderRequest(orderNo, LocationContext.getLocation()));
            });
        }
        if (CollectionUtils.isNotEmpty(request.getFbtPlatformOrderNos())) {
            request.getFbtPlatformOrderNos().forEach(orderNo -> getDataByOms(syncInternetCelebritySampleOrders, new SampleOrderRequest(orderNo, 0, LocationContext.getLocation())));
        }
        List<SyncInternetCelebritySampleOrder> newAllSampleOrders = Lists.newArrayList();
        syncInternetCelebritySampleOrders.forEach(syncInternetCelebritySampleOrder -> {
            SyncInternetCelebritySampleOrder newSampleOrder = newAllSampleOrders.stream().filter(t -> t.getPlatformOrderNo().equals(syncInternetCelebritySampleOrder.getPlatformOrderNo())).findFirst().orElse(null);
            if (Objects.isNull(newSampleOrder)) {
                newSampleOrder = new SyncInternetCelebritySampleOrder();
                InternetCelebritySampleStoreMappingEntity sampleStoreMapping = internetCelebritySampleStoreMappingService.findTopBySampleStoreId(syncInternetCelebritySampleOrder.getStoreId());
                if (Objects.nonNull(sampleStoreMapping)) {
                    syncInternetCelebritySampleOrder.setStoreId(sampleStoreMapping.getStoreId());
                    syncInternetCelebritySampleOrder.setStoreName(sampleStoreMapping.getStoreName());
                    syncInternetCelebritySampleOrder.setSampleStoreId(sampleStoreMapping.getSampleStoreId());
                    syncInternetCelebritySampleOrder.setSampleStoreName(sampleStoreMapping.getSampleStoreName());
                } else {
                    syncInternetCelebritySampleOrder.setSampleStoreId(syncInternetCelebritySampleOrder.getStoreId());
                    syncInternetCelebritySampleOrder.setSampleStoreName(syncInternetCelebritySampleOrder.getStoreName());
                }
                BeanUtils.copyPropertiesIgnoreNull(syncInternetCelebritySampleOrder, newSampleOrder);
                newAllSampleOrders.add(newSampleOrder);
            } else {
                newSampleOrder.getSyncInternetCelebritySampleOrderItems().addAll(syncInternetCelebritySampleOrder.getSyncInternetCelebritySampleOrderItems());
            }
        });
        sync(syncInternetCelebritySampleOrders, 0);
    }


    @Override
    public SaStoreDetailByErpResponse setRequestTokenInfo(TiktokAuth tiktokAuth, Integer deliveryStoreId) {
        StoreInfoRequest storeInfoRequest = new StoreInfoRequest();
        storeInfoRequest.setStoreId(deliveryStoreId);
        storeInfoRequest.setType("COMMON");
        SaStoreDetailByErpResponse saStoreDetailByErpResponse = saStoreService.info(storeInfoRequest);
        SauPlatformAuthConfigResponse sauPlatformAuthConfigResponse = saStoreDetailByErpResponse.getSauPlatformAuthConfigResponse();

        tiktokAuth.setAccessToken(sauPlatformAuthConfigResponse.getAccountProperties1());
        tiktokAuth.setAccessTokenExpireIn(sauPlatformAuthConfigResponse.getAccountProperties3());
        tiktokAuth.setRefreshToken(sauPlatformAuthConfigResponse.getAccountProperties2());
        tiktokAuth.setAppKey(sauPlatformAuthConfigResponse.getAppKey());
        tiktokAuth.setAppSecret(sauPlatformAuthConfigResponse.getAppSecret());
        tiktokAuth.setOpenId(sauPlatformAuthConfigResponse.getAccountProperties4());
        tiktokAuth.setAppSecret(sauPlatformAuthConfigResponse.getAppSecret());
        tiktokAuth.setStoreId(deliveryStoreId);
        tiktokAuth.setShopCipher(sauPlatformAuthConfigResponse.getAccountProperties5());
        saStoreDetailByErpResponse.setSauPlatformAuthConfigResponse(sauPlatformAuthConfigResponse);
        return saStoreDetailByErpResponse;
    }


    @Override
    public void saveRefreshToken(ResponseTokenInfo tiktokAuth, SaStoreDetailByErpResponse saStoreDetailByErpResponse) {
        if (!Optional.ofNullable(tiktokAuth).isPresent()) {
            return;
        }

        Optional.of(tiktokAuth).ifPresent(tokenInfo -> {
            SauPlatformAuthConfigResponse sauPlatformAuthConfigResponse = saStoreDetailByErpResponse.getSauPlatformAuthConfigResponse();
            PlatformAuthConfigRequest erpUpdateStoreAuthInfoRequest = new PlatformAuthConfigRequest();
            BeanUtils.copyPropertiesIgnoreNull(sauPlatformAuthConfigResponse, erpUpdateStoreAuthInfoRequest);
            erpUpdateStoreAuthInfoRequest.setAccountProperties1(tokenInfo.getAccessToken());
            erpUpdateStoreAuthInfoRequest.setAccountProperties2(tokenInfo.getRefreshToken());
            erpUpdateStoreAuthInfoRequest.setAccountProperties3(tokenInfo.getAccessTokenTimeOut());
            sauPlatformAuthConfigService.updateStoreAuthInfo(erpUpdateStoreAuthInfoRequest);
        });
    }


    @Override
    public void syncInternetCelebritySampleOrder(List<SyncInternetCelebritySampleOrder> syncInternetCelebritySampleOrderList, Integer isInit) {
        log.info("syncInternetCelebritySampleOrder.syncInternetCelebritySampleOrderList:{}", JSON.toJSONString(syncInternetCelebritySampleOrderList));
        syncInternetCelebritySampleOrderList.forEach(syncInternetCelebritySampleOrder -> syncInternetCelebritySampleOrder.setStoreId(Optional.ofNullable(syncInternetCelebritySampleOrder.getStoreId()).isPresent() && 0 != syncInternetCelebritySampleOrder.getStoreId() ? syncInternetCelebritySampleOrder.getStoreId() : null));
        List<String> platformOrderNos = syncInternetCelebritySampleOrderList.stream().map(SyncInternetCelebritySampleOrder::getPlatformOrderNo).collect(Collectors.toList());
        List<InternetCelebritySampleOrderEntity> internetCelebritySampleOrderEntities = getListByPlatformOrderNos(platformOrderNos);
        Map<String, InternetCelebritySampleOrderEntity> internetCelebritySampleOrderMap = internetCelebritySampleOrderEntities.stream().collect(Collectors.toMap(InternetCelebritySampleOrderEntity::getPlatformOrderNo, a -> a, (k1, k2) -> k1));
        syncInternetCelebritySampleOrderList.forEach(syncInternetCelebritySampleOrder -> {
            InternetCelebritySampleOrderEntity internetCelebritySampleOrder = Optional.ofNullable(internetCelebritySampleOrderMap.get(syncInternetCelebritySampleOrder.getPlatformOrderNo())).map(entity -> {
                BeanUtils.copyPropertiesIgnoreNull(syncInternetCelebritySampleOrder, entity);
                entity.setUpdateDate(new Date());
                entity.setUpdateBy(StringUtils.isEmpty(loginInfoService.getName()) ? this.getClass().getSimpleName() : loginInfoService.getName());
                entity.setInternetCelebrityNo(syncInternetCelebritySampleOrder.getBuyerUid());
                entity.setInternetCelebrityNickname(syncInternetCelebritySampleOrder.getBuyerNick());
                entity.setInternetCelebrityId(0);
                return entity;
            }).orElseGet(() -> {
                InternetCelebritySampleOrderEntity entity = new InternetCelebritySampleOrderEntity();
                BeanUtils.copyPropertiesIgnoreNull(syncInternetCelebritySampleOrder, entity);
                entity.setCreateDate(new Date());
                entity.setCreateBy(this.getClass().getSimpleName());
                entity.setInternetCelebrityNo(syncInternetCelebritySampleOrder.getBuyerUid());
                entity.setInternetCelebrityNickname(syncInternetCelebritySampleOrder.getBuyerNick());
                entity.setInternetCelebrityId(0);
                return entity;
            });
            this.saveOrUpdate(internetCelebritySampleOrder);
            internetCelebritySampleOrderItemService.syncInternetCelebritySampleOrderItem(internetCelebritySampleOrder.getInternetCelebritySampleOrderId(), syncInternetCelebritySampleOrder.getSyncInternetCelebritySampleOrderItems());
            internetCelebritySampleOrderPermissionService.syncInternetCelebritySampleOrderPermission(internetCelebritySampleOrder.getInternetCelebritySampleOrderId(), syncInternetCelebritySampleOrder);
            this.saveSampleOrderDate(internetCelebritySampleOrder);
            if (isInit == 0) {
                internetCelebrityMessageSendService.sendMessage(internetCelebritySampleOrder.getInternetCelebritySampleOrderId(), InternetCelebrityMessageType.ORDER);
            }
        });
    }

    @Override
    public void syncInternetCelebritySampleOrderByImport(List<SyncInternetCelebritySampleOrder> syncInternetCelebritySampleOrderList) {
        log.info("syncInternetCelebritySampleOrder.syncInternetCelebritySampleOrderByImport:{}", JSON.toJSONString(syncInternetCelebritySampleOrderList));
        syncInternetCelebritySampleOrderList.forEach(syncInternetCelebritySampleOrder -> syncInternetCelebritySampleOrder.setStoreId(Optional.ofNullable(syncInternetCelebritySampleOrder.getStoreId()).isPresent() && 0 != syncInternetCelebritySampleOrder.getStoreId() ? syncInternetCelebritySampleOrder.getStoreId() : null));
        List<String> platformOrderNos = syncInternetCelebritySampleOrderList.stream().map(SyncInternetCelebritySampleOrder::getPlatformOrderNo).distinct().collect(Collectors.toList());
        List<InternetCelebritySampleOrderEntity> internetCelebritySampleOrderEntities = getListByPlatformOrderNos(platformOrderNos);
        Map<String, InternetCelebritySampleOrderEntity> internetCelebritySampleOrderMap = internetCelebritySampleOrderEntities.stream().collect(Collectors.toMap(InternetCelebritySampleOrderEntity::getPlatformOrderNo, a -> a, (k1, k2) -> k1));
        platformOrderNos.forEach(platformOrderNo -> {
            List<SyncInternetCelebritySampleOrder> sampleOrderList = syncInternetCelebritySampleOrderList.stream().filter(t -> t.getPlatformOrderNo().equals(platformOrderNo)).collect(Collectors.toList());
            SyncInternetCelebritySampleOrder syncInternetCelebritySampleOrder = sampleOrderList.get(0);
            List<SyncInternetCelebritySampleOrderItem> sampleOrderItemList = Lists.newArrayList();
            sampleOrderList.forEach(t -> {
                sampleOrderItemList.addAll(t.getSyncInternetCelebritySampleOrderItems());
            });
            InternetCelebritySampleOrderEntity internetCelebritySampleOrder = Optional.ofNullable(internetCelebritySampleOrderMap.get(platformOrderNo)).map(entity -> {
                BeanUtils.copyPropertiesIgnoreNull(syncInternetCelebritySampleOrder, entity);
                entity.setUpdateDate(new Date());
                entity.setUpdateBy(StringUtils.isEmpty(loginInfoService.getName()) ? this.getClass().getSimpleName() : loginInfoService.getName());
                return entity;
            }).orElseGet(() -> {
                InternetCelebritySampleOrderEntity entity = new InternetCelebritySampleOrderEntity();
                BeanUtils.copyPropertiesIgnoreNull(syncInternetCelebritySampleOrder, entity);
                entity.setCreateDate(new Date());
                entity.setCreateBy(this.getClass().getSimpleName());
                return entity;
            });
            this.saveOrUpdate(internetCelebritySampleOrder);
            internetCelebritySampleOrderItemService.syncInternetCelebritySampleOrderItemByImport(internetCelebritySampleOrder.getInternetCelebritySampleOrderId(), internetCelebritySampleOrder.getPlatformOrderNo(), sampleOrderItemList);
            internetCelebritySampleOrderPermissionService.syncInternetCelebritySampleOrderPermission(internetCelebritySampleOrder.getInternetCelebritySampleOrderId(), syncInternetCelebritySampleOrder);

            List<InternetCelebritySampleOrderItemEntity> internetCelebritySampleOrderItemEntities = internetCelebritySampleOrderItemService.getList(internetCelebritySampleOrder.getInternetCelebritySampleOrderId());
            List<InternetCelebritySampleOrderItemPostEntity> internetCelebritySampleOrderItemPostEntities = internetCelebritySampleOrderItemPostService.getByInternetCelebritySampleOrderItemIds(internetCelebritySampleOrderItemEntities.stream().map(InternetCelebritySampleOrderItemEntity::getInternetCelebritySampleOrderItemId).collect(Collectors.toList()));
            internetCelebritySampleOrder.setFirstPostDate(CollectionUtils.isEmpty(internetCelebritySampleOrderItemPostEntities) ? null : internetCelebritySampleOrderItemPostEntities.stream().map(InternetCelebritySampleOrderItemPostEntity::getPostDate).filter(Objects::nonNull).min(Date::compareTo).orElse(null));
            this.updateById(internetCelebritySampleOrder);
            this.saveSampleOrderDate(internetCelebritySampleOrder);
        });
    }


    @Override
    public InternetCelebritySampleOrderEntity getByPlatformOrderNo(String platformOrderNo) {
        return this.getOne(new LambdaQueryWrapper<InternetCelebritySampleOrderEntity>()
                .eq(InternetCelebritySampleOrderEntity::getPlatformOrderNo, platformOrderNo)
                .last(MybatisQueryConstant.QUERY_FIRST));
    }

    @Override
    public List<InternetCelebritySampleOrderEntity> getListByPlatformOrderNos(List<String> platformOrderNos) {
        LambdaQueryWrapper<InternetCelebritySampleOrderEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(InternetCelebritySampleOrderEntity::getPlatformOrderNo, platformOrderNos);
        return this.list(lambdaQueryWrapper);
    }

    @Override
    public void getDataByErp(List<SyncInternetCelebritySampleOrder> syncInternetCelebritySampleOrders, GetSampleOrderRequest getSampleOrderRequest) {
        GetSampleOrderResponse getSampleOrderResponse = erpApiService.getSampleOrder(getSampleOrderRequest);

        if (!Optional.ofNullable(getSampleOrderResponse).isPresent() || CollectionUtils.isEmpty(getSampleOrderResponse.getSampleOrderList())) {
            return;
        }
        getSampleOrderResponse.getSampleOrderList().forEach(sampleOrderResponse -> {
            SyncInternetCelebritySampleOrder syncInternetCelebritySampleOrder = getSyncInternetCelebritySampleOrder(sampleOrderResponse);
            syncInternetCelebritySampleOrders.add(syncInternetCelebritySampleOrder);
        });
    }

    private SyncInternetCelebritySampleOrder getSyncInternetCelebritySampleOrder(SampleOrder sampleOrderResponse) {
        SyncInternetCelebritySampleOrder syncInternetCelebritySampleOrder = new SyncInternetCelebritySampleOrder();
        syncInternetCelebritySampleOrder.setStoreId(sampleOrderResponse.getStoreId());
        syncInternetCelebritySampleOrder.setStoreName(sampleOrderResponse.getStoreName());
        syncInternetCelebritySampleOrder.setPlatformOrderNo(sampleOrderResponse.getPlatformOrderNo());
        syncInternetCelebritySampleOrder.setOrderCreateDate(sampleOrderResponse.getOrderCreateDate());
        syncInternetCelebritySampleOrder.setBuyerNick(sampleOrderResponse.getBuyerNick());
        syncInternetCelebritySampleOrder.setBuyerUid(sampleOrderResponse.getBuyerUid());
        syncInternetCelebritySampleOrder.setLocation(sampleOrderResponse.getLocation());

        List<SyncInternetCelebritySampleOrderItem> syncInternetCelebritySampleOrderItems = new ArrayList<>();

        List<PublishProductSpecInfo> specInfosReq = new ArrayList<>();
        sampleOrderResponse.getSampleOrderItemList().forEach(orderItem -> {

            List<Integer> websiteIdList = saStoreWebsiteService.getWebsiteIdList(sampleOrderResponse.getStoreId());
            if (CollUtil.isEmpty(websiteIdList)) {
                return;
            }
            PublishProductSpecInfo publishProductSpecInfo = new PublishProductSpecInfo();
            publishProductSpecInfo.setStoreId(sampleOrderResponse.getStoreId());
            publishProductSpecInfo.setWebsiteId(websiteIdList.get(0));
            publishProductSpecInfo.setErpSku(orderItem.getSku());
            specInfosReq.add(publishProductSpecInfo);
        });
        List<PublishProductSpecInfo> publishSpecInfoResp = omsPublishApiService.getPublishSpecInfos(specInfosReq);
        sampleOrderResponse.getSampleOrderItemList().forEach(orderItem -> {
            SyncInternetCelebritySampleOrderItem syncInternetCelebritySampleOrderItem = new SyncInternetCelebritySampleOrderItem();
            syncInternetCelebritySampleOrderItem.setSku(orderItem.getSku());
            syncInternetCelebritySampleOrderItem.setQty(orderItem.getQty());
            syncInternetCelebritySampleOrderItem.setSellerSku(orderItem.getSellerSku());
            syncInternetCelebritySampleOrderItem.setSellerSkuId(orderItem.getSellerSkuId());
            syncInternetCelebritySampleOrderItem.setSellerProductId(orderItem.getSellerProductId());
            syncInternetCelebritySampleOrderItem.setDeliveryStoreId(Optional.ofNullable(orderItem.getDeliveryStoreId()).isPresent() && orderItem.getDeliveryStoreId() != 0 ? orderItem.getDeliveryStoreId() : sampleOrderResponse.getStoreId());
            syncInternetCelebritySampleOrderItem.setPlatformOriginalOrderNo(orderItem.getPlatformOriginalOrderNo());
            syncInternetCelebritySampleOrderItem.setOrderDeliveryDate(orderItem.getOrderDeliveryDate());
            syncInternetCelebritySampleOrderItem.setTrackingNumber(orderItem.getLogisticsNo());
            syncInternetCelebritySampleOrderItem.setOrderDeliveryType(InternetCelebritySampleOrderDeliveryTypeEnum.FBA.getDesc().equals(orderItem.getDeliveryType()) ? InternetCelebritySampleOrderDeliveryTypeEnum.FBA.getDescription() : InternetCelebritySampleOrderDeliveryTypeEnum.OVERSEAS_WAREHOUSE.getDescription());
            Optional<PublishProductSpecInfo> publishProductSpecInfo = publishSpecInfoResp.stream().filter(info -> sampleOrderResponse.getStoreId().equals(info.getStoreId()) && orderItem.getSku().equals(info.getErpSku())).findAny();
            if (publishProductSpecInfo.isPresent()) {
                syncInternetCelebritySampleOrderItem.setSellerProductId(publishProductSpecInfo.get().getWebsiteProductCode());
                syncInternetCelebritySampleOrderItem.setSellerSkuId(publishProductSpecInfo.get().getWebsiteItemCode());
            }
            syncInternetCelebritySampleOrderItems.add(syncInternetCelebritySampleOrderItem);
        });
        syncInternetCelebritySampleOrder.setSyncInternetCelebritySampleOrderItems(syncInternetCelebritySampleOrderItems);
        return syncInternetCelebritySampleOrder;
    }


    @Override
    public void getDataByOms(List<SyncInternetCelebritySampleOrder> syncInternetCelebritySampleOrders, SampleOrderRequest sampleOrderRequest) {
        List<SampleOrderResponse> sampleOrderResponses = platformOrderService.getSampleOrders(sampleOrderRequest);
        log.info("getDataByOms.sampleOrderResponses:{}", JSON.toJSONString(sampleOrderResponses));
        if (CollectionUtils.isEmpty(sampleOrderResponses)) {
            return;
        }
        sampleOrderResponses.forEach(sampleOrderResponse -> {
            SyncInternetCelebritySampleOrder syncInternetCelebritySampleOrder = getSyncInternetCelebritySampleOrderOms(sampleOrderResponse);
            syncInternetCelebritySampleOrders.add(syncInternetCelebritySampleOrder);
        });
    }

    private SyncInternetCelebritySampleOrder getSyncInternetCelebritySampleOrderOms(SampleOrderResponse sampleOrderResponse) {
        SyncInternetCelebritySampleOrder syncInternetCelebritySampleOrder = new SyncInternetCelebritySampleOrder();
        syncInternetCelebritySampleOrder.setStoreId(sampleOrderResponse.getStoreId());
        syncInternetCelebritySampleOrder.setStoreName(sampleOrderResponse.getStoreName());
        syncInternetCelebritySampleOrder.setPlatformOrderNo(sampleOrderResponse.getPlatformOriginalOrderNo());
        syncInternetCelebritySampleOrder.setOrderCreateDate(sampleOrderResponse.getOrderCreateDate());
        syncInternetCelebritySampleOrder.setBuyerNick(sampleOrderResponse.getBuyerNick());
        syncInternetCelebritySampleOrder.setLocation(sampleOrderResponse.getLocation());
        syncInternetCelebritySampleOrder.setBuyerUid(sampleOrderResponse.getBuyerUid());
        List<SyncInternetCelebritySampleOrderItem> syncInternetCelebritySampleOrderItems = new ArrayList<>();
        sampleOrderResponse.getOrderItems().forEach(orderItem -> {
            SyncInternetCelebritySampleOrderItem syncInternetCelebritySampleOrderItem = new SyncInternetCelebritySampleOrderItem();
            syncInternetCelebritySampleOrderItem.setSku(orderItem.getSku());
            syncInternetCelebritySampleOrderItem.setSellerSku(orderItem.getSellerSku());
            syncInternetCelebritySampleOrderItem.setQty(orderItem.getQty());
            syncInternetCelebritySampleOrderItem.setSellerSkuId(orderItem.getSellerSkuId());
            syncInternetCelebritySampleOrderItem.setSellerProductId(orderItem.getSellerProductId());
            syncInternetCelebritySampleOrderItem.setDeliveryStoreId(sampleOrderResponse.getStoreId());
            syncInternetCelebritySampleOrderItem.setPlatformOriginalOrderNo(sampleOrderResponse.getPlatformOriginalOrderNo());
            syncInternetCelebritySampleOrderItem.setOrderDeliveryDate(sampleOrderResponse.getOrderDeliverDate());
            syncInternetCelebritySampleOrderItem.setOrderDeliveryType(InternetCelebritySampleOrderDeliveryTypeEnum.FBT.getDesc());
            syncInternetCelebritySampleOrderItems.add(syncInternetCelebritySampleOrderItem);
        });
        syncInternetCelebritySampleOrder.setSyncInternetCelebritySampleOrderItems(syncInternetCelebritySampleOrderItems);
        return syncInternetCelebritySampleOrder;
    }

    @Override
    public void bind(InternetCelebrityBindOrderRequest internetCelebrityBindOrderRequest) {

        //网红信息
        List<ConfigInternetCelebrityResponse> configInternetCelebrityList = pmsApiService.getConfigInternetCelebrityList(new ConfigInternetCelebrityRequest(Collections.singletonList(internetCelebrityBindOrderRequest.getInternetCelebrityId())));
        if (CollectionUtils.isEmpty(configInternetCelebrityList)) {
            throw new BusinessServiceException("网红不存在");
        }

        InternetCelebritySampleOrderEntity internetCelebritySampleOrderEntity = this.getById(internetCelebrityBindOrderRequest.getInternetCelebritySampleOrderId());
        if (!Optional.ofNullable(internetCelebritySampleOrderEntity).isPresent()) {
            throw new BusinessServiceException("订单不存在");
        }
        ConfigInternetCelebrityResponse configInternetCelebrity = configInternetCelebrityList.get(0);

        if (StringUtils.isNotEmpty(internetCelebrityBindOrderRequest.getSku())) {
            if (!Optional.ofNullable(internetCelebrityBindOrderRequest.getInternetCelebritySampleOrderItemId()).isPresent()) {
                throw new BusinessServiceException("订单明细id不能传空");
            }

            InternetCelebritySampleOrderItemEntity internetCelebritySampleOrderItemEntity = internetCelebritySampleOrderItemService.getById(internetCelebrityBindOrderRequest.getInternetCelebritySampleOrderItemId());
            if (!Optional.ofNullable(internetCelebritySampleOrderItemEntity).isPresent()) {
                throw new BusinessServiceException("订单明细不存在");
            }

            List<ProductSpecDTO> productSpecDTOList = pmsApiService.specInfo(Collections.singletonList(internetCelebrityBindOrderRequest.getSku()));
            Map<String, String> specSkuMap = productSpecDTOList.stream().filter(productSpecDTO -> StringUtils.isNotEmpty(productSpecDTO.getImageUrl())).collect(Collectors.toMap(ProductSpecDTO::getSpecSku, ProductSpecDTO::getImageUrl, (value1, value2) -> value2));
            internetCelebritySampleOrderItemEntity.setSkuPictureUrl(specSkuMap.getOrDefault(internetCelebrityBindOrderRequest.getSku(), ""));
            internetCelebritySampleOrderItemEntity.setSku(internetCelebrityBindOrderRequest.getSku());
            //妥投时间
            internetCelebritySampleOrderItemEntity.setOrderCompromiseDate(internetCelebrityBindOrderRequest.getOrderCompromiseDate());

            internetCelebritySampleOrderItemService.updateById(internetCelebritySampleOrderItemEntity);
        }

        //网红信息
        internetCelebritySampleOrderEntity.setInternetCelebrityId(configInternetCelebrity.getId());
        internetCelebritySampleOrderEntity.setInternetCelebrityPlatform(configInternetCelebrity.getPlatform());
        internetCelebritySampleOrderEntity.setOwnerCode(configInternetCelebrity.getOwnerCode());
        internetCelebritySampleOrderEntity.setOwnerName(configInternetCelebrity.getOwnerName());
        internetCelebritySampleOrderEntity.setInternetCelebrityNickname(configInternetCelebrity.getNickName());

        SysDepartment sysDepartment = userApiService.getFirstLevelDepartmentByDeptId(configInternetCelebrity.getOwnerDeptId());
        internetCelebritySampleOrderEntity.setInternetCelebrityDeptId(sysDepartment.getDepartmentId());
        internetCelebritySampleOrderEntity.setInternetCelebrityDeptName(sysDepartment.getShortName());
        this.saveOrUpdate(internetCelebritySampleOrderEntity);

        //权限
        SyncInternetCelebritySampleOrder syncInternetCelebritySampleOrder = new SyncInternetCelebritySampleOrder();
        syncInternetCelebritySampleOrder.setOwnerUserId(configInternetCelebrity.getOwnerUserId());
        syncInternetCelebritySampleOrder.setOwnerDeptId(configInternetCelebrity.getOwnerDeptId());
        syncInternetCelebritySampleOrder.setUserId(configInternetCelebrity.getUserId());
        syncInternetCelebritySampleOrder.setDeptId(configInternetCelebrity.getDeptId());
        internetCelebritySampleOrderPermissionService.syncInternetCelebritySampleOrderPermission(internetCelebritySampleOrderEntity.getInternetCelebritySampleOrderId(), syncInternetCelebritySampleOrder);
    }


    @Override
    public void sync(List<SyncInternetCelebritySampleOrder> syncInternetCelebritySampleOrderList, Integer isInit) {
        if (CollectionUtils.isEmpty(syncInternetCelebritySampleOrderList)) {
            return;
        }

        Map<String, String> deptIdMap = userApiService.getDictionaryValues(StringConstant.INTERNET_CELEBRITY_SAMPLE_ORDER_DEPT).stream().collect(Collectors.toMap(BdDictionaryItem::getValue, BdDictionaryItem::getLabel, (a, b) -> a));

        HashMap<Integer, SaStoreEntity> saStoreDetailResponseMap = new HashMap<>();
        HashMap<Integer, SysDepartment> sysDepartmentMap = new HashMap<>();
        syncInternetCelebritySampleOrderList.forEach(syncInternetCelebritySampleOrder -> {
            SaStoreEntity saStoreDetailResponse = Optional.ofNullable(saStoreDetailResponseMap.get(syncInternetCelebritySampleOrder.getStoreId())).orElseGet(() -> {
                SaStoreEntity saStoreDetail = saStoreService.getById(syncInternetCelebritySampleOrder.getStoreId());
                saStoreDetailResponseMap.put(syncInternetCelebritySampleOrder.getStoreId(), saStoreDetail);
                return saStoreDetail;
            });
            if (Objects.isNull(saStoreDetailResponse)) {
                syncInternetCelebritySampleOrder.setOwnerDeptId(0);
            } else {
                Integer ownerDeptId = getDeptId(saStoreDetailResponse, deptIdMap);
                log.info("sync saStoreDetail storeid:{},ownerDeptId:{}", saStoreDetailResponse.getId(), ownerDeptId);
                syncInternetCelebritySampleOrder.setOwnerDeptId(ownerDeptId);
            }
        });
        List<String> skus = syncInternetCelebritySampleOrderList.stream().flatMap(item -> item.getSyncInternetCelebritySampleOrderItems().stream().filter(Objects::nonNull)).map(SyncInternetCelebritySampleOrderItem::getSku).distinct().collect(Collectors.toList());
        //图片信息
        List<ProductSpecDTO> productSpecDTOList = pmsApiService.specInfo(skus);
        Map<String, String> specSkuMap = productSpecDTOList.stream().filter(productSpecDTO -> StringUtils.isNotEmpty(productSpecDTO.getImageUrl())).collect(Collectors.toMap(ProductSpecDTO::getSpecSku, ProductSpecDTO::getImageUrl, (value1, value2) -> value2));
        //网红信息
        syncInternetCelebritySampleOrderList.forEach(syncInternetCelebritySampleOrder -> {
            SysDepartment sysDepartment = Optional.ofNullable(sysDepartmentMap.get(syncInternetCelebritySampleOrder.getOwnerDeptId())).orElseGet(() -> {
                SysDepartment department = userApiService.getFirstLevelDepartmentByDeptId(syncInternetCelebritySampleOrder.getOwnerDeptId());
                log.info("sync saStoreDetail ownerDeptId:{},department:{}", syncInternetCelebritySampleOrder.getOwnerDeptId(), JSON.toJSONString(department));
                sysDepartmentMap.put(syncInternetCelebritySampleOrder.getOwnerDeptId(), department);
                return department;
            });
            syncInternetCelebritySampleOrder.setInternetCelebrityDeptId(sysDepartment.getDepartmentId());
            syncInternetCelebritySampleOrder.setInternetCelebrityDeptName(sysDepartment.getShortName());

            //img
            syncInternetCelebritySampleOrder.getSyncInternetCelebritySampleOrderItems().forEach(syncInternetCelebritySampleOrderItem -> {
                syncInternetCelebritySampleOrderItem.setSkuPictureUrl(specSkuMap.getOrDefault(syncInternetCelebritySampleOrderItem.getSku(), ""));
            });

            //官网包裹信息
            setPackageInfo(syncInternetCelebritySampleOrder);
        });

        //保存
        syncInternetCelebritySampleOrder(syncInternetCelebritySampleOrderList, isInit);
    }


    @Override
    public void setPackageInfo(SyncInternetCelebritySampleOrder syncInternetCelebritySampleOrder) {
        try {
            setFbaPackageInfo(syncInternetCelebritySampleOrder.getSyncInternetCelebritySampleOrderItems());
            setFbtPackageInfo(syncInternetCelebritySampleOrder.getSyncInternetCelebritySampleOrderItems());
        } catch (Exception e) {
            log.error("SyncInternetCelebritySampleOrderJob.setPackageInfo.error:{}", e.getMessage(), e);
        }
    }

    private void setFbaPackageInfo(List<SyncInternetCelebritySampleOrderItem> items) {
        items.stream().filter(t -> InternetCelebritySampleOrderDeliveryTypeEnum.FBA.getDesc().equals(t.getOrderDeliveryType())).collect(Collectors.groupingBy(t -> String.format("%s#%s", t.getPlatformOriginalOrderNo(), t.getDeliveryStoreId())))
                .forEach((key, values) -> {
                    String platformOriginalOrderNo = key.split("#")[0];
                    Integer deliveryStoreId = Integer.parseInt(key.split("#")[1]);
                    AmazonAuth amazonAuth = sauAmazonConfigService.getOrderGrabAuthInfo(deliveryStoreId);
                    GetPackagesRequest getPackagesRequest = new GetPackagesRequest();
                    getPackagesRequest.setAmazonAuth(amazonAuth);
                    getPackagesRequest.setOrderNo(platformOriginalOrderNo);
                    getPackagesRequest.setPlatform(PlatformTypeEnum.Amazon);
                    getPackagesRequest.setStoreId(deliveryStoreId);
                    GetPackagesResponse packages = thirdPartyApiService.getPackages(getPackagesRequest);
                    if (Objects.nonNull(packages)) {
                        values.forEach(item -> {
                            item.setPlatformPackageId(packages.getPackageId());
                            item.setPackageStatus(InternetCelebritySampleOrderPackageStatusEnum.TRANSPORT.getDesc());
                            item.setTrackingNumber(packages.getTrackingNumber());

                        });
                    }
                });
    }

    private void setFbtPackageInfo(List<SyncInternetCelebritySampleOrderItem> items) {
        items.stream().filter(t -> InternetCelebritySampleOrderDeliveryTypeEnum.FBT.getDesc().equals(t.getOrderDeliveryType())).collect(Collectors.groupingBy(t -> String.format("%s#%s", t.getPlatformOriginalOrderNo(), t.getDeliveryStoreId())))
                .forEach((key, values) -> {
                    String platformOriginalOrderNo = key.split("#")[0];
                    Integer deliveryStoreId = Integer.parseInt(key.split("#")[1]);
                    GetOrderListByOrderIdsRequest getOrderListByOrderIdsRequest = new GetOrderListByOrderIdsRequest();
                    getOrderListByOrderIdsRequest.setPlatform(PlatformTypeEnum.TikTok);
                    getOrderListByOrderIdsRequest.setOrderIdList(Collections.singletonList(platformOriginalOrderNo));
                    TiktokAuth tiktokAuth = new TiktokAuth();
                    SaStoreDetailByErpResponse saStoreDetailByErpResponse = setRequestTokenInfo(tiktokAuth, deliveryStoreId);
                    getOrderListByOrderIdsRequest.setTiktokAuth(tiktokAuth);
                    GetOrderListByDateResponse getOrderListByDateResponse = thirdPartyApiService.getOrderByIdList(getOrderListByOrderIdsRequest);
                    if (CollectionUtils.isNotEmpty(getOrderListByDateResponse.getOrderList())) {
                        Order order = getOrderListByDateResponse.getOrderList().get(0);
                        values.forEach(item -> {
                            item.setTrackingNumber(order.getTrackingNumber());
                            item.setPackageStatus(InternetCelebritySampleOrderPackageStatusEnum.TRANSPORT.getDesc());
                            item.setPlatformPackageId(order.getPackageId());
                        });
                    }
                    saveRefreshToken(getOrderListByDateResponse.getResponseTokenInfo(), saStoreDetailByErpResponse);
                });
    }

    private Integer getDeptId(SaStoreEntity saStoreDetailResponse, Map<String, String> deptIdMap) {
        if (StringUtils.isNotEmpty(deptIdMap.get(String.valueOf(saStoreDetailResponse.getId())))) {
            return Integer.parseInt(deptIdMap.get(String.valueOf(saStoreDetailResponse.getId())));
        }
        String key = "";
        if (saStoreDetailResponse.getPlatformName().contains(OrderGrabPlatformEnum.AMAZON.getDesc()) && DepartmentTypeEnum.B2C.getName().equals(saStoreDetailResponse.getDepartment())) {
            key = DepartmentTypeEnum.B2C.getName() + "_" + OrderGrabPlatformEnum.AMAZON.getDesc();
        } else if (saStoreDetailResponse.getPlatformName().toUpperCase(Locale.ROOT).contains(OrderGrabPlatformEnum.TIKTOK.getDesc().toUpperCase(Locale.ROOT)) && DepartmentTypeEnum.B2C.getName().equals(saStoreDetailResponse.getDepartment())) {
            key = DepartmentTypeEnum.B2C.getName() + "_" + OrderGrabPlatformEnum.TIKTOK.getDesc();
        } else if (saStoreDetailResponse.getPlatformName().contains(OrderGrabPlatformEnum.AMAZON.getDesc()) && DepartmentTypeEnum.DOKOTOO.getName().equals(saStoreDetailResponse.getDepartment())) {
            key = DepartmentTypeEnum.DOKOTOO.getName() + "_" + OrderGrabPlatformEnum.AMAZON.getDesc();
        } else if (saStoreDetailResponse.getPlatformName().toUpperCase(Locale.ROOT).contains(OrderGrabPlatformEnum.TIKTOK.getDesc().toUpperCase(Locale.ROOT)) && DepartmentTypeEnum.DOKOTOO.getName().equals(saStoreDetailResponse.getDepartment())) {
            key = DepartmentTypeEnum.DOKOTOO.getName() + "_" + OrderGrabPlatformEnum.TIKTOK.getDesc();
        }
        String deptId = deptIdMap.get(key);
        return StringUtils.isEmpty(deptId) ? 0 : Integer.parseInt(deptId);
    }


    @Override
    public void syncUnknownOrder() {
        throw new BusinessServiceException("未实现该方法");
    }

    @Override
    public void remark(InternetCelebrityOrderRemarkRequest request) {
        InternetCelebritySampleOrderEntity internetCelebritySampleOrderEntity = this.getById(request.getInternetCelebritySampleOrderId());
        if (!Optional.ofNullable(internetCelebritySampleOrderEntity).isPresent()) {
            throw new BusinessServiceException("订单不存在");
        }

        internetCelebritySampleOrderEntity.setRemark(StringUtils.isNotEmpty(request.getRemark()) ? request.getRemark() : "");
        this.updateById(internetCelebritySampleOrderEntity);
    }

    @Override
    public SyncInternetCelebritySampleOrder erpOrder(String platformOrderNo, Integer storeRelationId) {
        InternetCelebrityStoreRelationEntity storeRelationEntity = internetCelebrityStoreRelationService.getById(storeRelationId);
        GetSampleOrderRequest getSampleOrderRequest = new GetSampleOrderRequest();
        getSampleOrderRequest.setLocation(loginInfoService.getLocation());
        getSampleOrderRequest.setTidList(Collections.singletonList(platformOrderNo));
        GetSampleOrderResponse getSampleOrderResponse = erpApiService.getSampleOrder(getSampleOrderRequest);
        if (Objects.nonNull(getSampleOrderResponse) && !org.springframework.util.CollectionUtils.isEmpty(getSampleOrderResponse.getSampleOrderList())) {
            SampleOrder sampleOrderResponse = getSampleOrderResponse.getSampleOrderList().get(0);
            if (!storeRelationEntity.getStoreId().equals(sampleOrderResponse.getStoreId())) {
                throw new BusinessServiceException(String.format("订单店铺:%s 不匹配建联店铺!", sampleOrderResponse.getStoreName()));
            }
            SyncInternetCelebritySampleOrder syncInternetCelebritySampleOrder = getSyncInternetCelebritySampleOrder(sampleOrderResponse);
            if (!CollectionUtils.isEmpty(syncInternetCelebritySampleOrder.getSyncInternetCelebritySampleOrderItems())) {
                syncInternetCelebritySampleOrder.setSyncInternetCelebritySampleOrderItems(Collections.singletonList(syncInternetCelebritySampleOrder.getSyncInternetCelebritySampleOrderItems().get(0)));
            }
            return syncInternetCelebritySampleOrder;
        } else {
            SampleOrderRequest sampleOrderRequest = new SampleOrderRequest();
            sampleOrderRequest.setIsSampleOrder(1);
            sampleOrderRequest.setPlatformOriginalOrderNo(platformOrderNo);
            List<SampleOrderResponse> sampleOrderResponses = platformOrderService.getSampleOrders(sampleOrderRequest);
            if (CollectionUtils.isEmpty(sampleOrderResponses)) {
                throw new BusinessServiceException(String.format("订单号:%s 在系统不存在!", platformOrderNo));
            }
            SampleOrderResponse sampleOrderResponse = sampleOrderResponses.get(0);
            if (!storeRelationEntity.getStoreId().equals(sampleOrderResponse.getStoreId())) {
                throw new BusinessServiceException(String.format("订单店铺:%s 不匹配建联店铺!", sampleOrderResponse.getStoreName()));
            }
            SyncInternetCelebritySampleOrder syncInternetCelebritySampleOrder = getSyncInternetCelebritySampleOrderOms(sampleOrderResponse);
            if (!CollectionUtils.isEmpty(syncInternetCelebritySampleOrder.getSyncInternetCelebritySampleOrderItems())) {
                syncInternetCelebritySampleOrder.setSyncInternetCelebritySampleOrderItems(Collections.singletonList(syncInternetCelebritySampleOrder.getSyncInternetCelebritySampleOrderItems().get(0)));
            }
            return syncInternetCelebritySampleOrder;
        }

    }

    @Override
    public List<InternetCelebritySampleOrderEntity> listByIdList(List<Integer> oderIds) {
        if (org.springframework.util.CollectionUtils.isEmpty(oderIds)) {
            return Collections.emptyList();
        }
        return this.listByIds(oderIds);
    }

    @Override
    public void syncChangeInternetCelebrityOwner(SyncChangeInternetCelebrityOwnerRequest syncChangeInternetCelebrityOwnerRequest) {
        LambdaQueryWrapper<InternetCelebritySampleOrderEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(InternetCelebritySampleOrderEntity::getInternetCelebrityId, syncChangeInternetCelebrityOwnerRequest.getId());
        List<InternetCelebritySampleOrderEntity> internetCelebritySampleOrderEntityList = this.list(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(internetCelebritySampleOrderEntityList)) {
            return;
        }
        List<Integer> internetCelebritySampleOrderIds = internetCelebritySampleOrderEntityList.stream().map(InternetCelebritySampleOrderEntity::getInternetCelebritySampleOrderId).collect(Collectors.toList());
        internetCelebritySampleOrderEntityList.forEach(internetCelebritySampleOrderEntity -> {
            internetCelebritySampleOrderEntity.setOwnerCode(syncChangeInternetCelebrityOwnerRequest.getOwnerCode());
            internetCelebritySampleOrderEntity.setOwnerName(syncChangeInternetCelebrityOwnerRequest.getOwnerName());
        });
        internetCelebritySampleOrderPermissionService.syncChangeInternetCelebrityOwner(internetCelebritySampleOrderIds, syncChangeInternetCelebrityOwnerRequest);
        this.saveOrUpdateBatch(internetCelebritySampleOrderEntityList);
    }

    @Override
    public void saveSampleOrderDate(InternetCelebritySampleOrderEntity orderEntity) {
        List<InternetCelebritySampleOrderItemEntity> orderItemEntityList = internetCelebritySampleOrderItemService.getList(orderEntity.getInternetCelebritySampleOrderId());
        Date orderDeliveryDate = orderItemEntityList.stream().map(InternetCelebritySampleOrderItemEntity::getOrderDeliveryDate).filter(Objects::nonNull).min(Date::compareTo).orElse(null);
        Date orderCompromiseDate = orderItemEntityList.stream().map(InternetCelebritySampleOrderItemEntity::getOrderCompromiseDate).filter(Objects::nonNull).min(Date::compareTo).orElse(null);
        if (Objects.nonNull(orderDeliveryDate) || Objects.nonNull(orderCompromiseDate)) {
            if (Objects.nonNull(orderDeliveryDate)) {
                orderEntity.setOrderDeliveryDate(orderDeliveryDate);
            }
            if (Objects.nonNull(orderCompromiseDate)) {
                orderEntity.setOrderCompromiseDate(orderCompromiseDate);
            }
            this.updateById(orderEntity);
        }
    }

}
