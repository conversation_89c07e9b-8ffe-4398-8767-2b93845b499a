package com.nsy.oms.business.domain.download.points;

import com.nsy.api.core.apicore.annotation.NsyExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @description:
 * @author: linCheng
 * @create: 2025-05-08 09:45
 **/
@Data
public class B2bPointsRefundExport {

    @NsyExcelProperty("退款请求ID")
    private Integer refundId;

    @NsyExcelProperty("账期创建时间")
    private String accountCreateTime;

    @NsyExcelProperty("客户名称")
    private String customerName;

    @NsyExcelProperty("客户邮箱")
    private String customerEmail;

    @NsyExcelProperty("店铺名称")
    private String storeName;

    @NsyExcelProperty("业务员")
    private String salesman;

    @NsyExcelProperty("最后一次下单时间")
    private String lastOrderTime;

    @NsyExcelProperty("待退积分")
    private Integer pointsRefunded;

    @NsyExcelProperty("待退金额（USD）")
    private BigDecimal amountRefunded;

    @NsyExcelProperty("状态")
    private String state;

    @NsyExcelProperty("订单号")
    private String orderNo;

    @NsyExcelProperty("流水号")
    private String serialNumber;

    @NsyExcelProperty("退款平台")
    private String refundPlatform;

    @NsyExcelProperty("退款金额")
    private BigDecimal refundAmount;

    @NsyExcelProperty("退款状态")
    private String refundStatus;

    @NsyExcelProperty("退款操作人")
    private String refundOperator;

    @NsyExcelProperty("更新时间")
    private String updateDate;

    @NsyExcelProperty("备注")
    private String remark;

}
