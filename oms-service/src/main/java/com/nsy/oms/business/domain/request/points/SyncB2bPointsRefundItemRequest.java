package com.nsy.oms.business.domain.request.points;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description:
 * @author: linCheng
 * @create: 2025-05-09 09:35
 **/
@Data
public class SyncB2bPointsRefundItemRequest {

    @ApiModelProperty("退款请求ID")
    @NotNull(message = "退款请求ID不能为空")
    private Integer refundId;

    @ApiModelProperty("退款数据")
    private List<SyncB2bPointsRefundItem> syncB2bPointsRefundItems;

}
