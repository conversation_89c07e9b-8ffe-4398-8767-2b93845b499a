package com.nsy.oms.business.manage.amazon.request;

import java.util.List;

/**
 * @description:
 * @author: linCheng
 * @create: 2024-06-21 16:25
 **/
public class ReportGetFlatFileAllOrdersDataRequest {

    private Integer storeId;
    private Integer orderType;
    private List<String> orderNos;

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public List<String> getOrderNos() {
        return orderNos;
    }

    public void setOrderNos(List<String> orderNos) {
        this.orderNos = orderNos;
    }

    public ReportGetFlatFileAllOrdersDataRequest(Integer storeId, Integer orderType, List<String> orderNos) {
        this.storeId = storeId;
        this.orderType = orderType;
        this.orderNos = orderNos;
    }

    public ReportGetFlatFileAllOrdersDataRequest() {
    }
}
