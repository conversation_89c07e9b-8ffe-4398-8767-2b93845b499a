package com.nsy.oms.business.service.download.replenishment;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.DateUtils;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.oms.enums.QuartzDownloadQueueTypeEnum;
import com.nsy.oms.business.domain.download.replenishment.FbaReplenishmentSkuListExport;
import com.nsy.oms.business.domain.request.download.DownloadRequest;
import com.nsy.oms.business.domain.request.replenishment.FbaReplenishmentPageRequest;
import com.nsy.oms.business.domain.response.base.DownloadResponse;
import com.nsy.oms.business.domain.response.bd.brand.StoreSkcInfoRequest;
import com.nsy.oms.business.manage.erp.ErpApiService;
import com.nsy.oms.business.manage.erp.domain.GetCompanyStockViewDto;
import com.nsy.oms.business.manage.erp.domain.UserStore;
import com.nsy.oms.business.manage.erp.response.PrivateStockByBusinessTypeAndSkuResponse;
import com.nsy.oms.business.manage.user.UserApiService;
import com.nsy.oms.business.manage.user.response.BdDictionaryItem;
import com.nsy.oms.business.manage.user.response.SysUserInfo;
import com.nsy.oms.business.service.download.IDownloadService;
import com.nsy.oms.business.service.replenishment.FbaReplenishmentLabelInfoService;
import com.nsy.oms.business.service.replenishment.FbaReplenishmentSkcService;
import com.nsy.oms.business.service.replenishment.FbaReplenishmentSkuService;
import com.nsy.oms.constants.StringConstant;
import com.nsy.oms.enums.CommonStateEnum;
import com.nsy.oms.enums.DepartmentTypeEnum;
import com.nsy.oms.enums.SizeEnum;
import com.nsy.oms.enums.bd.DictionaryKeySelectEnum;
import com.nsy.oms.enums.replenishment.LabelTypeEnum;
import com.nsy.oms.repository.dao.sa.SaStoreDao;
import com.nsy.oms.repository.entity.replenishment.FbaReplenishmentLabelInfoEntity;
import com.nsy.oms.repository.entity.replenishment.FbaReplenishmentSkcEntity;
import com.nsy.oms.repository.entity.replenishment.FbaReplenishmentSkuEntity;
import com.nsy.oms.repository.entity.sa.SaStoreEntity;
import com.nsy.oms.utils.BigDecimalUtil;
import com.nsy.oms.utils.JsonMapper;
import com.nsy.oms.utils.mp.LocationContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FbaReplenishmentSkuListDownloadServiceImpl implements IDownloadService {
    private static final String SPECIAL_NAME = "林马玲真狗";
    @Autowired
    private FbaReplenishmentSkcService fbaReplenishmentSkcService;
    @Autowired
    private FbaReplenishmentSkuService fbaReplenishmentSkuService;
    @Autowired
    private FbaReplenishmentLabelInfoService fbaReplenishmentLabelInfoService;
    @Autowired
    private ErpApiService erpApiService;
    @Autowired
    private SaStoreDao saStoreDao;
    @Autowired
    private UserApiService userApiService;


    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.FBA_REPLENISHMENT_SKU_LIST;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        FbaReplenishmentPageRequest downloadRequest = JsonMapper.fromJson(request.getRequestContent(), FbaReplenishmentPageRequest.class);
        downloadRequest.setPageSize(request.getPageSize());
        downloadRequest.setPageIndex(request.getPageIndex());
        SysUserInfo sysUserInfo = userApiService.getUserInfoByUserAccount(request.getRequestedBy());
        if (Objects.isNull(sysUserInfo)) throw new BusinessServiceException("该用户不存在,请确认后重试");
        LocationContext.setLocation(sysUserInfo.getLocation());
        if (!Objects.equals(1, sysUserInfo.getIsSupperAccount()) && !isConfigAdmin(sysUserInfo.getUserAccount())) {
            UserStore userStore = erpApiService.getErpUserStoreResponse(sysUserInfo.getUserAccount());
            downloadRequest.setPermissionStoreIds(Optional.ofNullable(userStore.getStoreIdList()).filter(CollectionUtils::isNotEmpty).orElse(Lists.newArrayList(0)));
        }
        long totalCount;
        List<FbaReplenishmentSkcEntity> list;
        if (CollectionUtils.isNotEmpty(downloadRequest.getIds())) {
            if (SPECIAL_NAME.equals(downloadRequest.getBusinessUserAccount())) {
                List<Integer> filterIds = fbaReplenishmentSkuService.listByFbaReplenishmentSkcIdIn(downloadRequest.getIds()).stream().filter(item -> !StringUtils.hasText(item.getBusinessUserAccount())).map(FbaReplenishmentSkuEntity::getFbaReplenishmentSkcId).distinct().collect(Collectors.toList());
                list = fbaReplenishmentSkcService.listByIds(filterIds);
            } else if (StringUtils.hasText(downloadRequest.getBusinessUserAccount())) {
                List<Integer> filterIds = fbaReplenishmentSkuService.listByFbaReplenishmentSkcIdIn(downloadRequest.getIds()).stream().filter(item -> downloadRequest.getBusinessUserAccount().equals(item.getBusinessUserAccount())).map(FbaReplenishmentSkuEntity::getFbaReplenishmentSkcId).distinct().collect(Collectors.toList());
                list = fbaReplenishmentSkcService.listByIds(filterIds);
            } else {
                list = fbaReplenishmentSkcService.listByIds(downloadRequest.getIds());
            }
            totalCount = list.size();
        } else {
            Page<FbaReplenishmentSkcEntity> page = fbaReplenishmentSkcService.getBaseMapper().pageList(new Page<>(downloadRequest.getPageIndex(), downloadRequest.getPageSize()), downloadRequest);
            totalCount = page.getTotal();
            list = page.getRecords();
        }
        if (CollectionUtils.isEmpty(list)) return DownloadResponse.of("{}", 0L);
        // sku详细数据
        List<Integer> fbaReplenishmentSkcIds = list.stream().map(FbaReplenishmentSkcEntity::getId).distinct().collect(Collectors.toList());
        List<FbaReplenishmentSkuEntity> fbaReplenishmentSkuEntities = fbaReplenishmentSkuService.listByFbaReplenishmentSkcIdIn(fbaReplenishmentSkcIds);
        Map<Integer, List<FbaReplenishmentSkuEntity>> skuMap = fbaReplenishmentSkuEntities.stream().collect(Collectors.groupingBy(FbaReplenishmentSkuEntity::getFbaReplenishmentSkcId));
        Map<String, StoreSkcInfoRequest.StoreSkcInfo> spaceMap = fbaReplenishmentSkcService.buildSpaceMap(fbaReplenishmentSkuEntities);
        List<FbaReplenishmentSkuListExport> excelList = this.getExcelList(downloadRequest, list, skuMap, spaceMap);
        Map<String, List<FbaReplenishmentSkuListExport>> map = new HashMap<>();
        map.put("list", excelList);
        return DownloadResponse.of(JsonMapper.toJson(map), totalCount);
    }

    private boolean isConfigAdmin(String userAccount) {
        List<BdDictionaryItem> fbaReplenishAdmins = userApiService.getDictionaryValues(DictionaryKeySelectEnum.FBA_REPLENISH_ADMIN.getKey());
        return CollectionUtils.isNotEmpty(fbaReplenishAdmins) && fbaReplenishAdmins.stream().anyMatch(configAdmin -> userAccount.equals(configAdmin.getValue()));
    }

    public List<FbaReplenishmentSkuListExport> getExcelList(FbaReplenishmentPageRequest downloadRequest, List<FbaReplenishmentSkcEntity> list, Map<Integer, List<FbaReplenishmentSkuEntity>> skuMap,
                                            Map<String, StoreSkcInfoRequest.StoreSkcInfo> spaceMap) {
        List<String> skuList = skuMap.values().stream().flatMap(Collection::stream).map(FbaReplenishmentSkuEntity::getSku).distinct().collect(Collectors.toList());
        // 共享可用和个人可用库存，去erp系统取数据
        Map<String, List<GetCompanyStockViewDto>> companyStockGroupBySku = erpApiService.getCompanyStockBySkus(skuList).stream().collect(Collectors.groupingBy(GetCompanyStockViewDto::getSku));
        PrivateStockByBusinessTypeAndSkuResponse privateStockResponse = erpApiService.queryPrivateStockByBusinessTypeAndSku(DepartmentTypeEnum.B2C.getName(), skuList);
        PrivateStockByBusinessTypeAndSkuResponse dokotooPrivateStockResponse = erpApiService.queryPrivateStockByBusinessTypeAndSku(DepartmentTypeEnum.DOKOTOO.getName(), skuList);
        List<PrivateStockByBusinessTypeAndSkuResponse> privateStockList = Arrays.asList(privateStockResponse, dokotooPrivateStockResponse);
        List<FbaReplenishmentSkuListExport> excelData = new ArrayList<>();
        if (SPECIAL_NAME.equals(downloadRequest.getBusinessUserAccount())) {
            for (FbaReplenishmentSkcEntity record : list) {
                if (!skuMap.containsKey(record.getId())) continue;
                Integer spaceId = spaceMap.getOrDefault(String.format("%s_%s", record.getStoreId(), record.getSkc()), new StoreSkcInfoRequest.StoreSkcInfo()).getSpaceId();
                if (Objects.isNull(spaceId)) spaceId = 1;
                Integer finalSpaceId = spaceId;
                skuMap.get(record.getId()).stream().filter(item -> !StringUtils.hasText(item.getBusinessUserAccount())).sorted((a, b) -> SizeEnum.sortBySize(a.getSize(), b.getSize())).forEach(p -> {
                    excelData.add(build(record, p, companyStockGroupBySku, privateStockList, finalSpaceId));
                });
            }
        } else if (StringUtils.hasText(downloadRequest.getBusinessUserAccount())) {
            for (FbaReplenishmentSkcEntity record : list) {
                if (!skuMap.containsKey(record.getId())) continue;
                Integer spaceId = spaceMap.getOrDefault(String.format("%s_%s", record.getStoreId(), record.getSkc()), new StoreSkcInfoRequest.StoreSkcInfo()).getSpaceId();
                if (Objects.isNull(spaceId)) spaceId = 1;
                Integer finalSpaceId = spaceId;
                skuMap.get(record.getId()).stream().filter(item -> downloadRequest.getBusinessUserAccount().equals(item.getBusinessUserAccount())).sorted((a, b) -> SizeEnum.sortBySize(a.getSize(), b.getSize())).forEach(p -> {
                    excelData.add(build(record, p, companyStockGroupBySku, privateStockList, finalSpaceId));
                });
            }
        } else {
            for (FbaReplenishmentSkcEntity record : list) {
                if (!skuMap.containsKey(record.getId())) continue;
                Integer spaceId = spaceMap.getOrDefault(String.format("%s_%s", record.getStoreId(), record.getSkc()), new StoreSkcInfoRequest.StoreSkcInfo()).getSpaceId();
                if (Objects.isNull(spaceId)) spaceId = 1;
                Integer finalSpaceId = spaceId;
                skuMap.get(record.getId()).stream().sorted((a, b) -> SizeEnum.sortBySize(a.getSize(), b.getSize())).forEach(p -> {
                    excelData.add(build(record, p, companyStockGroupBySku, privateStockList, finalSpaceId));
                });
            }
        }
        return excelData;
    }

    private FbaReplenishmentSkuListExport build(FbaReplenishmentSkcEntity record, FbaReplenishmentSkuEntity p,
                                                Map<String, List<GetCompanyStockViewDto>> companyStockGroupBySku, List<PrivateStockByBusinessTypeAndSkuResponse> privateStockList, Integer spaceId) {
        FbaReplenishmentSkuListExport export = new FbaReplenishmentSkuListExport();
        export.setSkc(p.getSkc());
        export.setSku(p.getSku());
        export.setSize(p.getSize());
        export.setStoreName(p.getStoreName());
        export.setCategoryName(record.getCategoryName());
        List<FbaReplenishmentLabelInfoEntity> labelList = fbaReplenishmentLabelInfoService.listByStoreIdAndSku(p.getStoreId(), p.getSkc());
        export.setProductLabel(labelList.stream().filter(e -> LabelTypeEnum.PRODUCT_LABEL.getDesc().equals(e.getLabelType()))
                .map(FbaReplenishmentLabelInfoEntity::getLabelName).distinct().collect(Collectors.joining(StringConstant.COMMA)));
        export.setLastYearStoreLabel(labelList.stream().filter(e -> LabelTypeEnum.STORE_LABEL_LAST_YEAR.getDesc().equals(e.getLabelType()) && !"砍坑款".equals(e.getLabelName()))
                .map(FbaReplenishmentLabelInfoEntity::getLabelName).distinct().collect(Collectors.joining(StringConstant.COMMA)));
        export.setStoreLabel(labelList.stream().filter(e -> LabelTypeEnum.STORE_LABEL.getDesc().equals(e.getLabelType()) && !"砍坑款".equals(e.getLabelName()))
                .map(FbaReplenishmentLabelInfoEntity::getLabelName).distinct().collect(Collectors.joining(StringConstant.COMMA)));
        String beginDate = StrUtil.padPre(record.getSaleMaturityBeginDate().toString(), 4, "0");
        String endDate = StrUtil.padPre(record.getSaleMaturityEndDate().toString(), 4, "0");
        export.setMaturityDate(String.format("%s至%s", String.format("%s.%s", Integer.valueOf(beginDate.substring(0, 2)), Integer.valueOf(beginDate.substring(2))), String.format("%s.%s", Integer.valueOf(endDate.substring(0, 2)), Integer.valueOf(endDate.substring(2)))));
        export.setStoreBrand(record.getStoreBrandName());
        export.setFbaOnTheWayStock(p.getFbaOnTheWayStock());
        export.setFbaAvailableStockOfFulfilable(p.getFbaAvailableStockOfFulfilable());
        export.setFbaAvailableStockOfProcessing(p.getFbaAvailableStockOfProcessing());
        export.setFbaAvailableStockOfTransfer(p.getFbaAvailableStockOfTransfer());
        export.setPlanQty(p.getPlanQty());
        export.setLocalShareStock(p.getLocalShareStock());
        export.setLocalPrivateStock(p.getLocalPrivateStock());
        export.setSalesQtyIn7(p.getSalesQtyIn7());
        export.setSalesQtyIn14(p.getSalesQtyIn14());
        export.setSalesQtyIn30(p.getSalesQtyIn30());
        export.setReturnRate(p.getReturnRate() == null ? "0.00%" : String.format("%s%%", p.getReturnRate().multiply(BigDecimalUtil.ONE_HUNDRED)));
        export.setStockingDays(p.getStockingDays());
        export.setCategoryCoefficient(p.getCategoryCoefficient() == null ? "0.00" : p.getCategoryCoefficient().toPlainString());
        export.setForecastSales(p.getForecastSales());
        export.setOutOfStockDate(p.getOutOfStockDate() == null ? "" : DateUtils.format(p.getOutOfStockDate(), DateUtils.DATE_FORMAT_DATE, Locale.ROOT));
        export.setStockSalesRate(p.getStockSalesRate() == null ? "0.00" : p.getStockSalesRate().toPlainString());
        export.setSuggestDeliveryDate(Objects.nonNull(p.getSuggestDeliveryDate()) && org.apache.commons.lang3.time.DateUtils.truncate(p.getSuggestDeliveryDate(), Calendar.DAY_OF_MONTH).compareTo(org.apache.commons.lang3.time.DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH)) > 0 ? DateUtils.format(p.getSuggestDeliveryDate(), DateUtils.DATE_FORMAT_DATE, Locale.ROOT) : "");
        export.setSeaSuggestQty(p.getSeaSuggestQty());
        export.setAirSuggestQty(p.getAirSuggestQty());
        export.setApplyForAirQty(p.getApplyForAirQty());
        export.setApplyForSeaQty(p.getApplyForSeaQty());
        export.setApplyForExpressQty(p.getApplyForExpressQty());
        export.setExpressSuggestQty(p.getExpressSuggestQty());
        export.setApplyReason(StringUtils.hasText(p.getApplyReason()) ? p.getApplyReason() : "");
        export.setSellerSku(p.getSellerSku());
        export.setMarketplaceChinese(p.getMarketplaceChinese());
        export.setFnsku(p.getFnsku());
        export.setItemName(p.getItemName());
        export.setHasMultiSellerSku(p.getHasMultiSellerSku() == 1 ? CommonStateEnum.YES.getDesc() : CommonStateEnum.NO.getDesc());
        this.setOtherSystemData(export, p, companyStockGroupBySku, privateStockList, spaceId);
        return export;
    }


    private void setOtherSystemData(FbaReplenishmentSkuListExport export, FbaReplenishmentSkuEntity p,
                              Map<String, List<GetCompanyStockViewDto>> companyStockGroupBySku, List<PrivateStockByBusinessTypeAndSkuResponse> privateStockList, Integer spaceId) {
        export.setLocalShareStock(0);
        export.setLocalPrivateStock(0);
        if (StringUtils.hasText(p.getPendingSendAirQtyArray()) && StringUtils.hasText(p.getPendingSendAirDateArray())) {
            String[] splitDate = p.getPendingSendAirDateArray().split(",");
            String[] splitQty = p.getPendingSendAirQtyArray().split(",");
            StringBuilder stringBuilder = new StringBuilder();
            for (int i = 0; i < splitDate.length; i++) {
                stringBuilder.append(splitQty[i]).append((char) 10).append(splitDate[i]).append((char) 10);
            }
            export.setSuggestAirForFuture(stringBuilder.toString());
        }
        // 共享可用 = 公司库存 + 共享库存
        if (companyStockGroupBySku.containsKey(p.getSku())) {
            List<GetCompanyStockViewDto> companyStockViewDtos = companyStockGroupBySku.get(p.getSku()).stream().filter(item -> Objects.equals(item.getSpaceId(), spaceId)).collect(Collectors.toList());
            int localShareStock = companyStockViewDtos.stream().mapToInt(GetCompanyStockViewDto::getCompanyStock).sum();
            export.setLocalShareStock(localShareStock);
        }
        // 个人可用 = 本店铺私有未共享的库存 + 本店铺共享到部门的库存 + 本店铺共享到公司的库存 - 已预配的库存
        SaStoreEntity saStoreEntity = saStoreDao.getById(p.getStoreId());
        export.setLocalShareStock(export.getLocalShareStock() + privateStockList.get(0).storeShareStock(p.getStoreId(), p.getSku(), spaceId) + privateStockList.get(1).storeShareStock(p.getStoreId(), p.getSku(), spaceId));
        if (DepartmentTypeEnum.DOKOTOO.getName().equalsIgnoreCase(saStoreEntity.getDepartment())) {
            export.setLocalPrivateStock(privateStockList.get(1).privateStock(p.getStoreId(), p.getSku(), spaceId));
        } else {
            export.setLocalPrivateStock(privateStockList.get(0).privateStock(p.getStoreId(), p.getSku(), spaceId));
        }
        export.setUserName(p.getBusinessUserName());
        setApplyNum(export);
    }
    private void setApplyNum(FbaReplenishmentSkuListExport export) {
        if (export.getApplyForExpressQty() + export.getApplyForSeaQty() + export.getApplyForAirQty() > 0 || export.getLocalPrivateStock() + export.getLocalShareStock() == 0) {
            return;
        }
        int localStock = export.getLocalPrivateStock() + export.getLocalShareStock();
        int expressApplyNum = Math.min(export.getExpressSuggestQty(), localStock);
        export.setApplyForExpressQty(expressApplyNum);
        localStock -= expressApplyNum;
        int airApplyNum = Math.min(export.getAirSuggestQty(), localStock);
        export.setApplyForAirQty(airApplyNum);
        localStock -= airApplyNum;
        int seaApplyNum = Math.min(export.getSeaSuggestQty(), localStock);
        export.setApplyForSeaQty(seaApplyNum);
    }
}
