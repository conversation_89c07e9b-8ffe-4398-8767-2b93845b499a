package com.nsy.oms.business.manage.erp.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

/**
 * created by jun.
 **/
public class Sku {
    @JsonProperty("Sku")
    private String sku;

    @JsonProperty("CategoryId")
    private Integer categoryId;

    @JsonProperty("CategoryName")
    private String categoryName;

    @JsonProperty("PurchasePrice")
    private BigDecimal purchasePrice; // 采购价

    @JsonProperty("UserPrice")
    private BigDecimal userPrice; // 吊牌价

    @JsonProperty("SettlementPrice")
    private BigDecimal settlementPrice; // 决算价

    @JsonProperty("ImageUrl")
    private String imageUrl;

    @JsonProperty("ProductId")
    private Integer productId;

    @JsonProperty("SpecId")
    private Integer specId;

    @JsonProperty("ParentSku")
    private String parentSku;

    @JsonProperty("ColorSku")
    private String colorSku;

    @JsonProperty("Size")
    private String size;

    public BigDecimal getSettlementPrice() {
        return settlementPrice;
    }

    public void setSettlementPrice(BigDecimal settlementPrice) {
        this.settlementPrice = settlementPrice;
    }

    public String getColorSku() {
        return colorSku;
    }

    public void setColorSku(String colorSku) {
        this.colorSku = colorSku;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public BigDecimal getPurchasePrice() {
        return purchasePrice;
    }

    public void setPurchasePrice(BigDecimal purchasePrice) {
        this.purchasePrice = purchasePrice;
    }

    public BigDecimal getUserPrice() {
        return userPrice;
    }

    public void setUserPrice(BigDecimal userPrice) {
        this.userPrice = userPrice;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Integer getSpecId() {
        return specId;
    }

    public void setSpecId(Integer specId) {
        this.specId = specId;
    }

    public String getParentSku() {
        return parentSku;
    }

    public void setParentSku(String parentSku) {
        this.parentSku = parentSku;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }
}
