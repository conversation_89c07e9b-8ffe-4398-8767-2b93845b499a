package com.nsy.oms.business.service.platform;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nsy.oms.repository.entity.platform.PlatformOrderPushKingdeeQueueEntity;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【platform_order_push_kingdee_queue(平台订单推送金蝶队列表)】的数据库操作Service
* @createDate 2025-04-21 17:58:05
*/
public interface PlatformOrderPushKingdeeQueueService extends IService<PlatformOrderPushKingdeeQueueEntity> {

    List<PlatformOrderPushKingdeeQueueEntity> getFbaPushOrder(Date businessStarDate, Date businessEndDate, Integer fetchCount);

    List<PlatformOrderPushKingdeeQueueEntity> getFbtPushOrder(Date businessStarDate, Date businessEndDate, Integer fetchCount);

    List<PlatformOrderPushKingdeeQueueEntity> getCostPushOrder(Date businessStarDate, Date businessEndDate, Integer fetchCount);

    List<PlatformOrderPushKingdeeQueueEntity> getOtherStockoutPushOrder(Date businessStarDate, Date businessEndDate, Integer fetchCount);

    List<PlatformOrderPushKingdeeQueueEntity> getWfsPushOrder(Date businessStarDate, Date businessEndDate, Integer fetchCount);

    void updateStatusByPlatformOrderNo(String platformOrderNo, Integer status);

    void updatePushCostOrderStatusByPlatformOrderNo(String platformOrderNo, Integer status);
}
