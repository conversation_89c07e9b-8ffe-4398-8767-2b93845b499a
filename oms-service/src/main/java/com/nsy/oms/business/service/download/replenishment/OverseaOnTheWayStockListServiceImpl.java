package com.nsy.oms.business.service.download.replenishment;

import com.nsy.api.core.apicore.response.CustomExcelResponse;
import com.nsy.api.core.apicore.util.NsyCollUtil;
import com.nsy.api.core.apicore.util.NsyExcelUtil;
import com.nsy.api.core.apicore.util.NsyJacksonUtils;
import com.nsy.api.oms.enums.QuartzDownloadQueueTypeEnum;
import com.nsy.oms.business.domain.download.stockout.OverseaOnTheWayStockExport;
import com.nsy.oms.business.domain.request.download.DownloadRequest;
import com.nsy.oms.business.domain.request.stockout.OverseaSkcPageRequest;
import com.nsy.oms.business.domain.response.base.DownloadResponse;
import com.nsy.oms.business.domain.response.stockout.OverseaOnTheWayStockResponse;
import com.nsy.oms.business.service.download.IDownloadService;
import com.nsy.oms.business.service.stockout.OverseaSkcService;
import com.nsy.oms.utils.JsonMapper;
import com.nsy.oms.utils.mp.LocationContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


@Slf4j
@Service
public class OverseaOnTheWayStockListServiceImpl implements IDownloadService {
    @Autowired
    private OverseaSkcService overseaSkcService;

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.ON_THE_WAY_STOCK_LIST;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        log.info("OverseaOnTheWayStockListServiceImpl-接收到的下载请求:{}", JsonMapper.toJson(request));
        OverseaSkcPageRequest searchRequest = NsyJacksonUtils.toObj(request.getRequestContent(), OverseaSkcPageRequest.class);
        //请求页码
        searchRequest.setPageIndex(request.getPageIndex());
        searchRequest.setPageSize(request.getPageSize());
        LocationContext.setLocation(request.getLocation());

        List<OverseaOnTheWayStockResponse> list = overseaSkcService.onTheWayStockList(searchRequest.getDepartment(), searchRequest.getReplenishmentGroupId(), searchRequest.getSkcList());

        if (NsyCollUtil.isEmpty(list)) {
            return DownloadResponse.of(null, 0L);
        }
        DownloadResponse response = new DownloadResponse();
        List<List<Object>> data = new ArrayList<>();
        list.forEach(stockResponse -> {
            stockResponse.getItemList().forEach(stockItem -> {
                OverseaOnTheWayStockExport stockExport = new OverseaOnTheWayStockExport();
                stockExport.setSku(stockResponse.getSku());
                stockExport.setOrderNo(stockItem.getOrderNo());
                stockExport.setStoreName(stockItem.getStoreName());
                stockExport.setShipQty(stockItem.getShipQty());
                stockExport.setTargetSpaceName(stockItem.getTargetSpaceName());
                stockExport.setLogisticsCompany(stockItem.getLogisticsCompany());
                stockExport.setShipDate(stockItem.getShipDate());
                stockExport.setEstimatedArrivalDate(stockItem.getEstimatedArrivalDate());
                data.add(NsyExcelUtil.getData(OverseaOnTheWayStockExport.class, stockExport));
            });
        });
        CustomExcelResponse excelResponse = new CustomExcelResponse();
        excelResponse.setHeaders(NsyExcelUtil.getCommonHeads(OverseaOnTheWayStockExport.class));
        excelResponse.setData(data);
        response.setTotalCount((long) data.size());
        response.setDataJsonStr(JsonMapper.toJson(excelResponse));
        return response;
    }


}
