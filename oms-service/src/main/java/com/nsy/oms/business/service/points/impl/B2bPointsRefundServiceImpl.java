package com.nsy.oms.business.service.points.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.privilege.LoginInfoService;
import com.nsy.oms.business.domain.request.points.AffirmRefundItemRequest;
import com.nsy.oms.business.domain.request.points.B2bPointsRefundPageRequest;
import com.nsy.oms.business.domain.request.points.CancelRefundItemRequest;
import com.nsy.oms.business.domain.request.points.SyncB2bPointsRefund;
import com.nsy.oms.business.domain.response.base.PageResponse;
import com.nsy.oms.business.domain.response.points.B2bPointsRefundItemVO;
import com.nsy.oms.business.domain.response.points.B2bPointsRefundVO;
import com.nsy.oms.business.manage.user.UserApiService;
import com.nsy.oms.business.service.points.IB2bPointsRefundItemService;
import com.nsy.oms.business.service.points.IB2bPointsRefundService;
import com.nsy.oms.business.service.privilege.AccessControlService;
import com.nsy.oms.business.service.replenishment.FbaReplenishmentSkcService;
import com.nsy.oms.business.service.sa.SaStoreService;
import com.nsy.oms.enums.points.B2bPointsRefundItemStateEnum;
import com.nsy.oms.enums.points.B2bPointsRefundStateEnum;
import com.nsy.oms.repository.entity.points.B2bPointsRefundEntity;
import com.nsy.oms.repository.entity.points.B2bPointsRefundItemEntity;
import com.nsy.oms.repository.entity.sa.SaStoreEntity;
import com.nsy.oms.repository.sql.mapper.points.B2bPointsRefundMapper;
import com.nsy.oms.utils.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * B2B积分退款 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Slf4j
@Service
public class B2bPointsRefundServiceImpl extends ServiceImpl<B2bPointsRefundMapper, B2bPointsRefundEntity> implements IB2bPointsRefundService {

    @Resource
    private IB2bPointsRefundItemService b2bPointsRefundItemService;
    @Resource
    private LoginInfoService loginInfoService;
    @Resource
    private AccessControlService accessControlService;
    @Resource
    private UserApiService userApiService;
    @Resource
    private SaStoreService saStoreService;

    @Override
    public PageResponse<B2bPointsRefundVO> page(B2bPointsRefundPageRequest request) {

        List<Integer> permissionStoreIds = accessControlService.isAdmin() ? null : accessControlService.doPrivileged(new FbaReplenishmentSkcService.FbaReplenishmentSkcPrivilegeAction(userApiService, loginInfoService.getUserName()));

        LambdaQueryWrapper<B2bPointsRefundEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.orderByDesc(B2bPointsRefundEntity::getAccountCreateTime);
        lambdaQueryWrapper.in(CollectionUtils.isNotEmpty(permissionStoreIds), B2bPointsRefundEntity::getStoreId, permissionStoreIds);
        lambdaQueryWrapper.in(CollectionUtils.isNotEmpty(request.getRefundIds()), B2bPointsRefundEntity::getRefundId, request.getRefundIds());
        lambdaQueryWrapper.in(CollectionUtils.isNotEmpty(request.getStateList()), B2bPointsRefundEntity::getState, request.getStateList());
        if (request.getAccountCreateTimeStart() != null) {
            lambdaQueryWrapper.between(B2bPointsRefundEntity::getAccountCreateTime, request.getAccountCreateTimeStart(), request.getAccountCreateTimeEnd());
        }
        lambdaQueryWrapper.like(StringUtils.isNotEmpty(request.getCustomerEmail()), B2bPointsRefundEntity::getCustomerEmail, request.getCustomerEmail());
        lambdaQueryWrapper.like(StringUtils.isNotEmpty(request.getStoreName()), B2bPointsRefundEntity::getStoreName, request.getStoreName());
        lambdaQueryWrapper.like(StringUtils.isNotEmpty(request.getCustomerName()), B2bPointsRefundEntity::getCustomerName, request.getCustomerName());
        Page<B2bPointsRefundEntity> page = this.page(new Page<>(request.getPageIndex(), request.getPageSize()), lambdaQueryWrapper);

        List<B2bPointsRefundEntity> b2bPointsRefundEntities = page.getRecords();
        PageResponse<B2bPointsRefundVO> pageResponse = PageResponse.of(page.getTotal(), page.getPages());

        if (CollectionUtils.isNotEmpty(b2bPointsRefundEntities)) {
            List<Integer> refundIds = b2bPointsRefundEntities.stream().map(B2bPointsRefundEntity::getRefundId).collect(Collectors.toList());
            Map<Integer, List<B2bPointsRefundItemEntity>> b2bPointsRefundItemMap = b2bPointsRefundItemService.getListByRefundIds(refundIds).stream().collect(Collectors.groupingBy(B2bPointsRefundItemEntity::getRefundId));
            pageResponse.setContent(b2bPointsRefundEntities.stream().map(b2bPointsRefundEntity -> {
                B2bPointsRefundVO b2bPointsRefundVO = new B2bPointsRefundVO();
                BeanUtil.copyProperties(b2bPointsRefundEntity, b2bPointsRefundVO);
                b2bPointsRefundVO.setB2bPointsRefundItemVOList(BeanUtil.copyToList(b2bPointsRefundItemMap.getOrDefault(b2bPointsRefundEntity.getRefundId(), Collections.emptyList()), B2bPointsRefundItemVO.class));
                return b2bPointsRefundVO;
            }).collect(Collectors.toList()));
        }
        return pageResponse;
    }

    @Override
    public void syncB2bPointsRefund(List<SyncB2bPointsRefund> syncB2bPointsRefunds) {
        syncB2bPointsRefunds.forEach(syncB2bPointsRefund -> {
            B2bPointsRefundEntity refundEntity = getByRefundId(syncB2bPointsRefund.getRefundId());
            if (ObjectUtil.isNotEmpty(refundEntity)) {
                log.info(String.format("syncB2bPointsRefund.同步了重复数据.退款订单已存在:%s,请核对同步数据", syncB2bPointsRefund.getRefundId()));
                return;
            }
            B2bPointsRefundEntity b2bPointsRefundEntity = new B2bPointsRefundEntity();
            BeanUtils.copyPropertiesIgnoreNull(syncB2bPointsRefund, b2bPointsRefundEntity);
            b2bPointsRefundEntity.setState(B2bPointsRefundStateEnum.WAIT_CONFIRM.getStatus());
            b2bPointsRefundEntity.setCreateDate(new Date());
            SaStoreEntity storeEntity = saStoreService.getById(b2bPointsRefundEntity.getStoreId());
            b2bPointsRefundEntity.setStoreName(storeEntity.getErpStoreName());
            b2bPointsRefundEntity.setCreateBy("syncB2bPointsRefund");
            this.save(b2bPointsRefundEntity);
        });
    }


    @Override
    public void affirmWaitConfirm(List<Integer> refundIds) {
        List<B2bPointsRefundEntity> b2bPointsRefundEntities = getListByRefundIds(refundIds);
        if (CollectionUtils.isEmpty(b2bPointsRefundEntities)) {
            return;
        }
        b2bPointsRefundEntities.forEach(b2bPointsRefundEntity -> {
            b2bPointsRefundEntity.setState(B2bPointsRefundStateEnum.WAIT_DISTRIBUTE.getStatus());
            b2bPointsRefundEntity.setCreateDate(new Date());
            b2bPointsRefundEntity.setCreateBy(loginInfoService.getName());
        });
        this.updateBatchById(b2bPointsRefundEntities);
    }


    @Override
    public void cancelWaitConfirm(List<Integer> refundIds) {
        List<B2bPointsRefundEntity> b2bPointsRefundEntities = getListByRefundIds(refundIds);
        if (CollectionUtils.isEmpty(b2bPointsRefundEntities)) {
            return;
        }
        refundIds.forEach(refundId -> {
            B2bPointsRefundEntity b2bPointsRefundEntity = getByRefundId(refundId);
            if (!Optional.ofNullable(b2bPointsRefundEntity).isPresent() || !B2bPointsRefundStateEnum.WAIT_CONFIRM.getStatus().equals(b2bPointsRefundEntity.getState())) {
                throw new BusinessServiceException(String.format("订单不存在或者状态不为待确认,退款id:%s", refundId));
            }
            b2bPointsRefundItemService.cancelPushMall(b2bPointsRefundEntity.getStoreId(), refundId, b2bPointsRefundEntity.getAmountRefunded());
            b2bPointsRefundEntity.setState(B2bPointsRefundStateEnum.CANCEL.getStatus());
            b2bPointsRefundEntity.setCreateDate(new Date());
            b2bPointsRefundEntity.setUpdateBy(loginInfoService.getName());
            this.updateById(b2bPointsRefundEntity);
        });
    }


    @Override
    public void affirmRefundItem(AffirmRefundItemRequest request) {
        B2bPointsRefundEntity b2bPointsRefundEntity = getByRefundId(request.getRefundId());
        if (!Optional.ofNullable(b2bPointsRefundEntity).isPresent() || !B2bPointsRefundStateEnum.WAIT_PROCESS.getStatus().equals(b2bPointsRefundEntity.getState())) {
            throw new BusinessServiceException(String.format("订单不存在或者状态不为待处理,退款id:%s", request.getRefundId()));
        }
        if (!Optional.ofNullable(request.getB2bPointsRefundItemId()).isPresent()) {
            b2bPointsRefundEntity.setState(B2bPointsRefundStateEnum.ALREADY_PROCESS.getStatus());
        } else {
            b2bPointsRefundItemService.affirmOrCancelRefundItem(request.getB2bPointsRefundItemId(), request.getRemark(), B2bPointsRefundItemStateEnum.ALREADY_REFUND.getStatus(), b2bPointsRefundEntity.getStoreId());

            //更新状态
            List<B2bPointsRefundItemEntity> b2bPointsRefundItemEntities = b2bPointsRefundItemService.getListByRefundIds(Collections.singletonList(request.getRefundId()));
            boolean alreadyProcessFlag = b2bPointsRefundItemEntities.stream().allMatch(item -> B2bPointsRefundItemStateEnum.ALREADY_REFUND.getStatus().equals(item.getRefundStatus()) || B2bPointsRefundItemStateEnum.CANCEL.getStatus().equals(item.getRefundStatus()));
            boolean cancelFlag = b2bPointsRefundItemEntities.stream().allMatch(item -> B2bPointsRefundItemStateEnum.CANCEL.getStatus().equals(item.getRefundStatus()));

            if (cancelFlag) {
                b2bPointsRefundEntity.setState(B2bPointsRefundStateEnum.CANCEL.getStatus());
            } else if (alreadyProcessFlag) {
                b2bPointsRefundEntity.setState(B2bPointsRefundStateEnum.ALREADY_PROCESS.getStatus());
            }
        }
        b2bPointsRefundEntity.setUpdateDate(new Date());
        b2bPointsRefundEntity.setUpdateBy(loginInfoService.getName());
        this.updateById(b2bPointsRefundEntity);
    }

    @Override
    public void cancelRefundItem(CancelRefundItemRequest request) {
        B2bPointsRefundEntity b2bPointsRefundEntity = getByRefundId(request.getRefundId());
        if (!Optional.ofNullable(b2bPointsRefundEntity).isPresent() || !B2bPointsRefundStateEnum.WAIT_PROCESS.getStatus().equals(b2bPointsRefundEntity.getState())) {
            throw new BusinessServiceException(String.format("订单不存在或者状态不为待处理,退款id:%s", request.getRefundId()));
        }
        Integer b2bPointsRefundItemId = request.getB2bPointsRefundItemId();
        if (ObjectUtil.isEmpty(b2bPointsRefundItemId)) {
            b2bPointsRefundEntity.setState(B2bPointsRefundStateEnum.CANCEL.getStatus());
            b2bPointsRefundItemService.cancelPushMall(b2bPointsRefundEntity.getStoreId(), b2bPointsRefundEntity.getRefundId(), b2bPointsRefundEntity.getAmountRefunded());
        } else {
            b2bPointsRefundItemService.affirmOrCancelRefundItem(b2bPointsRefundItemId, null, B2bPointsRefundItemStateEnum.CANCEL.getStatus(), b2bPointsRefundEntity.getStoreId());
            List<B2bPointsRefundItemEntity> b2bPointsRefundItemEntities = b2bPointsRefundItemService.getListByRefundIds(Collections.singletonList(request.getRefundId()));
            boolean alreadyProcessFlag = b2bPointsRefundItemEntities.stream().allMatch(item -> B2bPointsRefundItemStateEnum.ALREADY_REFUND.getStatus().equals(item.getRefundStatus()) || B2bPointsRefundItemStateEnum.CANCEL.getStatus().equals(item.getRefundStatus()));
            boolean cancelFlag = b2bPointsRefundItemEntities.stream().allMatch(item -> B2bPointsRefundItemStateEnum.CANCEL.getStatus().equals(item.getRefundStatus()));

            if (cancelFlag) {
                b2bPointsRefundEntity.setState(B2bPointsRefundStateEnum.CANCEL.getStatus());
            } else if (alreadyProcessFlag) {
                b2bPointsRefundEntity.setState(B2bPointsRefundStateEnum.ALREADY_PROCESS.getStatus());
            }
        }
        b2bPointsRefundEntity.setUpdateDate(new Date());
        b2bPointsRefundEntity.setUpdateBy(loginInfoService.getName());
        this.updateById(b2bPointsRefundEntity);
    }


    @Override
    public List<B2bPointsRefundEntity> getListByState(String state) {
        LambdaQueryWrapper<B2bPointsRefundEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(B2bPointsRefundEntity::getState, state);
        return this.list(lambdaQueryWrapper);
    }

    public List<B2bPointsRefundEntity> getListByRefundIds(List<Integer> refundIds) {
        LambdaQueryWrapper<B2bPointsRefundEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(B2bPointsRefundEntity::getRefundId, refundIds);
        return this.list(lambdaQueryWrapper);
    }

    @Override
    public B2bPointsRefundEntity getByRefundId(Integer refundId) {
        LambdaQueryWrapper<B2bPointsRefundEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(B2bPointsRefundEntity::getRefundId, refundId);
        return this.getOne(lambdaQueryWrapper);
    }

}
