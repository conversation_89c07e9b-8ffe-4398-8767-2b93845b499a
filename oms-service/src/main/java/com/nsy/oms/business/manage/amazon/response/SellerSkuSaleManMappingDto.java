package com.nsy.oms.business.manage.amazon.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/08/22
 */
@Getter
@Setter
public class SellerSkuSaleManMappingDto {
    @ApiModelProperty("主键id")
    private Integer id;

    @ApiModelProperty("店铺id")
    private Integer amazonStoreId;

    @ApiModelProperty("店铺名称")
    private String storeName;

    @ApiModelProperty("Seller SKU")
    private String sellerSku;

    @ApiModelProperty("ERP SKU")
    private String erpSku;

    @ApiModelProperty("ERP PARENT SKU")
    private String erpParentSku;

    @ApiModelProperty("ERP SPEC ID")
    private Integer erpSpecId;

    @ApiModelProperty("ERP PRODUCT ID")
    private Integer erpProductId;

    @ApiModelProperty("业务员账号")
    private String userAccount;

    @ApiModelProperty("业务员")
    private String userName;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getAmazonStoreId() {
        return amazonStoreId;
    }

    public void setAmazonStoreId(Integer amazonStoreId) {
        this.amazonStoreId = amazonStoreId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getSellerSku() {
        return sellerSku;
    }

    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    public String getErpSku() {
        return erpSku;
    }

    public void setErpSku(String erpSku) {
        this.erpSku = erpSku;
    }

    public String getErpParentSku() {
        return erpParentSku;
    }

    public void setErpParentSku(String erpParentSku) {
        this.erpParentSku = erpParentSku;
    }

    public Integer getErpSpecId() {
        return erpSpecId;
    }

    public void setErpSpecId(Integer erpSpecId) {
        this.erpSpecId = erpSpecId;
    }

    public Integer getErpProductId() {
        return erpProductId;
    }

    public void setErpProductId(Integer erpProductId) {
        this.erpProductId = erpProductId;
    }

    public String getUserAccount() {
        return userAccount;
    }

    public void setUserAccount(String userAccount) {
        this.userAccount = userAccount;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
}
