package com.nsy.oms.business.service.celebrity.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.constant.enums.IsEnum;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.pms.dto.response.BaseListResponse;
import com.nsy.oms.business.domain.request.celebrity.BusinessDeveloperEditRequest;
import com.nsy.oms.business.domain.request.celebrity.BusinessDeveloperPageRequest;
import com.nsy.oms.business.domain.request.celebrity.BusinessDeveloperUpdateStatusRequest;
import com.nsy.oms.business.domain.response.celebrity.BusinessDeveloperResponse;
import com.nsy.oms.business.manage.user.request.shared.SelectModel;
import com.nsy.oms.business.service.celebrity.CelebrityBusinessDeveloperService;
import com.nsy.oms.business.service.celebrity.InternetCelebrityStoreRelationService;
import com.nsy.oms.constants.MybatisQueryConstant;
import com.nsy.oms.constants.StringConstant;
import com.nsy.oms.repository.entity.celebrity.CelebrityBusinessDeveloperEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebrityStoreRelationEntity;
import com.nsy.oms.repository.sql.mapper.celebrity.CelebrityBusinessDeveloperMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.inject.Inject;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class CelebrityBusinessDeveloperServiceImpl
        extends ServiceImpl<CelebrityBusinessDeveloperMapper, CelebrityBusinessDeveloperEntity>
        implements CelebrityBusinessDeveloperService {
    @Inject
    private InternetCelebrityStoreRelationService internetCelebrityStoreRelationService;

    @Override
    public void edit(BusinessDeveloperEditRequest request) {
        List<CelebrityBusinessDeveloperEntity> list = listByEmail(request.getBdEmail());
        if (Objects.nonNull(request.getId())) {
            CelebrityBusinessDeveloperEntity entity = getById(request.getId());
            if (Objects.isNull(entity)) {
                throw new BusinessServiceException("客服不存在");
            }
            if (list.stream().anyMatch(s -> !s.getId().equals(entity.getId()) && s.getBdEmail().equals(request.getBdEmail()))) {
                throw new BusinessServiceException(String.format("客服邮箱%s已存在", request.getBdEmail()));
            }
            entity.setBdName(request.getBdName());
            entity.setBdAccount(request.getBdAccount());
            entity.setBdEmail(request.getBdEmail());
            entity.setStatus(request.getStatus());
            updateById(entity);
            List<InternetCelebrityStoreRelationEntity> storeRelationEntityList = internetCelebrityStoreRelationService.listByBdIds(Collections.singletonList(entity.getId()));
            storeRelationEntityList.forEach(s -> {
                s.setBdName(entity.getBdName());
                s.setBdAccount(entity.getBdAccount());
                s.setBdEmail(entity.getBdEmail());
            });
            internetCelebrityStoreRelationService.updateBatchById(storeRelationEntityList);
        } else {
            if (!CollectionUtils.isEmpty(list)) {
                throw new BusinessServiceException(String.format("客服邮箱%s已存在", request.getBdEmail()));
            }
            CelebrityBusinessDeveloperEntity entity = new CelebrityBusinessDeveloperEntity();
            entity.setBdName(request.getBdName());
            entity.setBdAccount(request.getBdAccount());
            entity.setBdEmail(request.getBdEmail());
            entity.setStatus(request.getStatus());
            save(entity);
        }
    }

    @Override
    public void updateStatus(BusinessDeveloperUpdateStatusRequest request) {
        List<CelebrityBusinessDeveloperEntity> list = listByIds(request.getIds());
        list.forEach(s -> s.setStatus(request.getStatus()));
        updateBatchById(list);
    }

    @Override
    public BaseListResponse<BusinessDeveloperResponse> search(BusinessDeveloperPageRequest request) {
        BaseListResponse<BusinessDeveloperResponse> response = new BaseListResponse<>();
        if (StringUtils.hasText(request.getBdEmail()) && request.getBdEmail().contains(StringConstant.COMMA)) {
            request.setBdEmailList(Arrays.asList(request.getBdEmail().split(StringConstant.COMMA)));
            request.setBdEmail(StringConstant.EMPTY);
        }
        LambdaQueryWrapper<CelebrityBusinessDeveloperEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(!CollectionUtils.isEmpty(request.getIds()), CelebrityBusinessDeveloperEntity::getId, request.getIds())
                .in(!CollectionUtils.isEmpty(request.getBdEmailList()), CelebrityBusinessDeveloperEntity::getBdEmail, request.getBdEmailList())
                .likeRight(StringUtils.hasText(request.getBdEmail()), CelebrityBusinessDeveloperEntity::getBdEmail, request.getBdEmail())
                .likeRight(StringUtils.hasText(request.getBdName()), CelebrityBusinessDeveloperEntity::getBdName, request.getBdName())
                .eq(Objects.nonNull(request.getStatus()), CelebrityBusinessDeveloperEntity::getStatus, request.getStatus())
                .ge(Objects.nonNull(request.getCreateStartDate()), CelebrityBusinessDeveloperEntity::getCreateDate, request.getCreateStartDate())
                .le(Objects.nonNull(request.getCreateEndDate()), CelebrityBusinessDeveloperEntity::getCreateDate, request.getCreateEndDate())
                .orderByDesc(CelebrityBusinessDeveloperEntity::getUpdateDate);
        Page<CelebrityBusinessDeveloperEntity> page = this.page(new Page<>(request.getPageIndex(), request.getPageSize()), wrapper);
        response.setTotalCount(page.getTotal());
        response.setContent(page.getRecords().stream().map(s -> {
            BusinessDeveloperResponse businessDeveloperResponse = new BusinessDeveloperResponse();
            BeanUtilsEx.copyProperties(s, businessDeveloperResponse);
            businessDeveloperResponse.setStatusDesc(IsEnum.IS.getCode().equals(s.getStatus()) ? "启用" : "停用");
            return businessDeveloperResponse;
        }).collect(Collectors.toList()));
        return response;
    }

    @Override
    public CelebrityBusinessDeveloperEntity findByEmail(String bdEmail) {
        return this.getOne(new LambdaQueryWrapper<CelebrityBusinessDeveloperEntity>()
                .eq(CelebrityBusinessDeveloperEntity::getBdEmail, bdEmail)
                .last(MybatisQueryConstant.QUERY_FIRST));
    }

    public List<CelebrityBusinessDeveloperEntity> listByEmail(String bdEmail) {
        return this.list(new LambdaQueryWrapper<CelebrityBusinessDeveloperEntity>()
                .eq(CelebrityBusinessDeveloperEntity::getBdEmail, bdEmail));
    }

    @Override
    public List<SelectModel> bdNameSelect() {
        return this.list(new LambdaQueryWrapper<CelebrityBusinessDeveloperEntity>().eq(CelebrityBusinessDeveloperEntity::getStatus, 1)).stream().map(s -> {
            SelectModel selectModel = new SelectModel();
            selectModel.setLabel(s.getBdName());
            selectModel.setValue(s.getId().toString());
            return selectModel;
        }).collect(Collectors.toList());
    }

    @Override
    public List<SelectModel> bdSelect() {
        return this.list(new LambdaQueryWrapper<CelebrityBusinessDeveloperEntity>().eq(CelebrityBusinessDeveloperEntity::getStatus, 1)).stream().map(s -> {
            SelectModel selectModel = new SelectModel();
            selectModel.setLabel(s.getBdEmail());
            selectModel.setValue(s.getBdEmail());
            return selectModel;
        }).collect(Collectors.toList());
    }
}