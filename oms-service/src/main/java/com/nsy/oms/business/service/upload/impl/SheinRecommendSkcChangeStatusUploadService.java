package com.nsy.oms.business.service.upload.impl;

import com.nsy.oms.business.domain.request.upload.UploadRequest;
import com.nsy.oms.business.domain.response.upload.UploadResponse;
import com.nsy.oms.business.domain.upload.SheinRecommendSkcChangeStatusImport;
import com.nsy.oms.business.manage.search.SearchApiService;
import com.nsy.oms.business.manage.search.domain.ProductIndex;
import com.nsy.oms.business.service.sa.SaStoreService;
import com.nsy.oms.business.service.shein.RecommendSaleSkcAdapter;
import com.nsy.oms.business.service.upload.IUploadService;
import com.nsy.oms.constants.StringConstant;
import com.nsy.oms.constants.upload.QuartzUploadQueueTypeEnum;
import com.nsy.oms.enums.shein.SheinRecommendPlatformEnum;
import com.nsy.oms.enums.shein.SheinRecommendSaleSkcStatusEnum;
import com.nsy.oms.enums.shein.SheinRecommendTypeEnum;
import com.nsy.oms.repository.dao.shein.SheinRecommendSaleSkcDao;
import com.nsy.oms.repository.entity.sa.SaStoreEntity;
import com.nsy.oms.repository.entity.shein.SheinRecommendSaleSkcEntity;
import com.nsy.oms.utils.JsonMapper;
import com.nsy.oms.utils.Validator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 亚马逊排位赛商品导入
 *
 * <AUTHOR>
 * @date 2023-02-24
 */
@Service
public class SheinRecommendSkcChangeStatusUploadService implements IUploadService {
    @Autowired
    private SheinRecommendSaleSkcDao sheinRecommendSaleSkcDao;
    @Autowired
    private SaStoreService saStoreService;
    @Autowired
    private RecommendSaleSkcAdapter recommendSaleSkcAdapter;
    @Autowired
    private SearchApiService searchApiService;

    @Override
    public QuartzUploadQueueTypeEnum uploadType() {
        return QuartzUploadQueueTypeEnum.OMS_SHEIN_RECOMMEND_SKC_CHANGE_STATUS;
    }

    @Override
    public UploadResponse processUploadData(UploadRequest request) {
        UploadResponse uploadResponse = new UploadResponse();
        if (!StringUtils.hasText(request.getDataJsonStr()) || !StringUtils.hasText(request.getUploadParams())) {
            return uploadResponse;
        }
        List<SheinRecommendSkcChangeStatusImport> importList = JsonMapper.jsonStringToObjectArray(request.getDataJsonStr(), SheinRecommendSkcChangeStatusImport.class);
        if (CollectionUtils.isEmpty(importList)) {
            return uploadResponse;
        }
        List<SheinRecommendSkcChangeStatusImport> errorList = new ArrayList<>();
        processData(importList, errorList, request.getCreateBy());
        if (!errorList.isEmpty()) {
            uploadResponse.setDataJsonStr(JsonMapper.toJson(errorList));
        }
        return uploadResponse;
    }

    private void processData(List<SheinRecommendSkcChangeStatusImport> importList, List<SheinRecommendSkcChangeStatusImport> errorList, String createBy) {
        List<SaStoreEntity> saStoreEntityList = saStoreService.getStoreByStoreNames(importList.stream().map(SheinRecommendSkcChangeStatusImport::getPublishStoreName).distinct().collect(Collectors.toList()));
        for (SheinRecommendSkcChangeStatusImport importModel : importList) {
            try {
                SaStoreEntity saStoreEntity = saStoreEntityList.stream().filter(t -> t.getErpStoreName().equals(importModel.getPublishStoreName())).findAny().orElse(null);
                Validator.valid(saStoreEntity, Objects::isNull, "店铺不存在，请核对店铺名称");
                SheinRecommendSaleSkcEntity targetRecommendStoreEntity = sheinRecommendSaleSkcDao.getOneIncludeDeleteBySkcAndRecommendStore(importModel.getSkc(), saStoreEntity.getId());
                if (Objects.isNull(targetRecommendStoreEntity)) {
                    Validator.valid(saStoreEntity, item -> !SheinRecommendPlatformEnum.SHEIN.equals(SheinRecommendPlatformEnum.of(item.allPlatform())), "不存在待上架数据");
                    Validator.valid(saStoreEntity, item -> {
                        SheinRecommendPlatformEnum.of(item.allPlatform());
                        return false;
                    }, "店铺不支持推送");
                    ProductIndex productIndex = searchApiService.getProductIndex(importModel.getSkc().substring(0, importModel.getSkc().indexOf("-")));
                    Validator.valid(productIndex, Objects::isNull, "商品不存在，请核对商品SKU");
                    targetRecommendStoreEntity = new SheinRecommendSaleSkcEntity();
                    targetRecommendStoreEntity.setProductId((int) productIndex.getId());
                    targetRecommendStoreEntity.setSkc(importModel.getSkc());
                    targetRecommendStoreEntity.setRecommendStoreId(saStoreEntity.getId());
                    targetRecommendStoreEntity.setRecommendStoreName(saStoreEntity.getErpStoreName());
                    targetRecommendStoreEntity.setPlatform(SheinRecommendPlatformEnum.of(saStoreEntity.allPlatform()).getOmsPublishPlatform());
                    targetRecommendStoreEntity.setCreateBy(createBy);
                    targetRecommendStoreEntity.setUpdateBy(createBy);
                    targetRecommendStoreEntity.setCreateDate(new Date());
                    targetRecommendStoreEntity.setRecommendType(SheinRecommendTypeEnum.SELECTION.name());
                    targetRecommendStoreEntity.setLocation(saStoreEntity.getLocation());
                    sheinRecommendSaleSkcDao.saveOrUpdate(targetRecommendStoreEntity);
                }
                targetRecommendStoreEntity.setStatus(SheinRecommendSaleSkcStatusEnum.UN_SHELF.getCode());
                targetRecommendStoreEntity.setReason(0);
                targetRecommendStoreEntity.setPublishStoreId(0);
                targetRecommendStoreEntity.setPublishStoreName(StringConstant.EMPTY);
                recommendSaleSkcAdapter.markAsPublish(Collections.singletonList(targetRecommendStoreEntity), saStoreEntity, createBy);
            } catch (Exception e) {
                setErrorMsg(e.getMessage(), importModel, errorList);
            }
        }
    }

    private void setErrorMsg(String message, SheinRecommendSkcChangeStatusImport importModel, List<SheinRecommendSkcChangeStatusImport> errorList) {
        importModel.setErrorMsg(message);
        errorList.add(importModel);
    }
}
