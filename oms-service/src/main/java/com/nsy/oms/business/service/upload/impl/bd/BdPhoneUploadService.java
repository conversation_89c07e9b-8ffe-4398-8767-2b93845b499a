package com.nsy.oms.business.service.upload.impl.bd;

import com.alibaba.fastjson.JSON;
import com.nsy.oms.business.domain.request.upload.UploadRequest;
import com.nsy.oms.business.domain.response.upload.UploadResponse;
import com.nsy.oms.business.domain.upload.sa.BdPhoneImport;
import com.nsy.oms.business.service.bd.BdPhoneService;
import com.nsy.oms.business.service.upload.IUploadService;
import com.nsy.oms.constants.upload.QuartzUploadQueueTypeEnum;
import com.nsy.oms.utils.JsonMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: LXW
 * @create: 2023-10-23 15:53
 **/
@Slf4j
@Service
public class BdPhoneUploadService implements IUploadService {
    @Autowired
    private BdPhoneService bdPhoneService;
    @Autowired
    private Validator validator;

    @Override
    public QuartzUploadQueueTypeEnum uploadType() {
        return QuartzUploadQueueTypeEnum.OMS_SM_PHONE_LIST_IMPORT;
    }


    @Override
    public UploadResponse processUploadData(UploadRequest request) {
        log.error("BdPhoneUploadService.processUploadData.request:{}", JSON.toJSONString(request));
        UploadResponse uploadResponse = new UploadResponse();
        if (!StringUtils.hasText(request.getDataJsonStr()) || !StringUtils.hasText(request.getUploadParams())) {
            return uploadResponse;
        }

        List<BdPhoneImport> importList = JsonMapper.jsonStringToObjectArray(request.getDataJsonStr(), BdPhoneImport.class).stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(importList)) {
            return uploadResponse;
        }

        //数据解析
        List<BdPhoneImport> errorList = new ArrayList<>();
        List<BdPhoneImport> needInsertList = new ArrayList<>();
        importList.forEach(importItem -> {
            Set<ConstraintViolation<BdPhoneImport>> validateErrorList = validator.validate(importItem);
            if (!validateErrorList.isEmpty()) {
                importItem.setErrorMsg(validateErrorList.stream().map(ConstraintViolation::getMessage).collect(Collectors.toList()).toString());
                errorList.add(importItem);
                return;
            }
            needInsertList.add(importItem);
        });
        if (!needInsertList.isEmpty()) {
            bdPhoneService.importDataByList(needInsertList, errorList, request.getCreateBy(), request.getLocation());
        }
        if (!errorList.isEmpty()) {
            uploadResponse.setDataJsonStr(JsonMapper.toJson(errorList));
            return uploadResponse;
        }
        return new UploadResponse();
    }


}
