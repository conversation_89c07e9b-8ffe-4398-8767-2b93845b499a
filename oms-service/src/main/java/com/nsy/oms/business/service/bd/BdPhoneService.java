package com.nsy.oms.business.service.bd;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nsy.oms.business.domain.request.bd.sm.BdPhonePageRequest;
import com.nsy.oms.business.domain.request.bd.sm.BdPhoneRequest;
import com.nsy.oms.business.domain.response.base.PageResponse;
import com.nsy.oms.business.domain.response.bd.sm.BdPhonePageResponse;
import com.nsy.oms.business.domain.response.bd.sm.BdPhoneResponse;
import com.nsy.oms.business.domain.upload.sa.BdPhoneImport;
import com.nsy.oms.business.manage.user.request.shared.SelectIntegerModel;
import com.nsy.oms.business.manage.user.request.shared.SelectModel;
import com.nsy.oms.repository.entity.bd.BdPhoneEntity;
import com.nsy.oms.repository.entity.sa.SaAccountContactInfoApplyEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【bd_phone(社媒账号-电话表)】的数据库操作Service
 * @createDate 2024-10-22 17:33:38
 */
public interface BdPhoneService extends IService<BdPhoneEntity> {

    BdPhoneResponse getInfo(Integer id);

    PageResponse<BdPhonePageResponse> getList(BdPhonePageRequest phonePageRequest);


    void updateInfo(BdPhoneRequest phoneRequest);

    String getPhoneNumer(Integer id);


    void importDataByList(List<BdPhoneImport> needInsertList, List<BdPhoneImport> errorList, String createBy, String location);

    List<SelectModel> getPhoneSelect(String phone);

    List<BdPhoneEntity> getPhoneIdListByPhone(List<String> phoneList);

    BdPhoneEntity getPhoneInfo(Integer phoneId);

    List<SelectIntegerModel> getPhoneList();

    BdPhoneEntity getPhoneId(String searchValue);

    void addAccount(SaAccountContactInfoApplyEntity saAccountApplyEntity);
}
