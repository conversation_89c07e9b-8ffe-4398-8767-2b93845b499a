package com.nsy.oms.business.domain.response.rule;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class ConfigRecommendProductRuleItemResponse {


    private String createDate;

    private Integer saleVol;

    private Integer day;

    private List<Integer> labelIds;

    private List<Integer> filterLabelIds;

    private List<Integer> spaceIds;

    private List<String> spaceStoreBrandList;

    private List<String> infringementNameList;

    private String fabricType;

    private List<Integer> seasonLabelIds;

    private List<String> productTypes;

    private List<String> publishPlatformList;

    private List<StockRuleResponse> stockRuleResponses;

    private Integer addSizeStock;

    /**
     * 库存更新相关配置
     */
    private RuleStockItemResponse ruleStockItem;

    /**
     * 补货相关配置
     */
    @ApiModelProperty("补货相关配置")
    private ReplenishmentResponse replenishmentResponse;

    public List<String> getPublishPlatformList() {
        return publishPlatformList;
    }

    public void setPublishPlatformList(List<String> publishPlatformList) {
        this.publishPlatformList = publishPlatformList;
    }

    public List<String> getSpaceStoreBrandList() {
        return spaceStoreBrandList;
    }

    public void setSpaceStoreBrandList(List<String> spaceStoreBrandList) {
        this.spaceStoreBrandList = spaceStoreBrandList;
    }

    public List<Integer> getFilterLabelIds() {
        return filterLabelIds;
    }

    public void setFilterLabelIds(List<Integer> filterLabelIds) {
        this.filterLabelIds = filterLabelIds;
    }

    public String getFabricType() {
        return fabricType;
    }

    public void setFabricType(String fabricType) {
        this.fabricType = fabricType;
    }

    public List<String> getInfringementNameList() {
        return infringementNameList;
    }

    public void setInfringementNameList(List<String> infringementNameList) {
        this.infringementNameList = infringementNameList;
    }

    public List<Integer> getSpaceIds() {
        return spaceIds;
    }

    public void setSpaceIds(List<Integer> spaceIds) {
        this.spaceIds = spaceIds;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public Integer getSaleVol() {
        return saleVol;
    }

    public void setSaleVol(Integer saleVol) {
        this.saleVol = saleVol;
    }

    public Integer getDay() {
        return day;
    }

    public void setDay(Integer day) {
        this.day = day;
    }

    public List<Integer> getLabelIds() {
        return labelIds;
    }

    public void setLabelIds(List<Integer> labelIds) {
        this.labelIds = labelIds;
    }

    public List<String> getProductTypes() {
        return productTypes;
    }

    public void setProductTypes(List<String> productTypes) {
        this.productTypes = productTypes;
    }

    public List<StockRuleResponse> getStockRuleResponses() {
        return stockRuleResponses;
    }

    public void setStockRuleResponses(List<StockRuleResponse> stockRuleResponses) {
        this.stockRuleResponses = stockRuleResponses;
    }

    public RuleStockItemResponse getRuleStockItem() {
        return ruleStockItem;
    }

    public void setRuleStockItem(RuleStockItemResponse ruleStockItem) {
        this.ruleStockItem = ruleStockItem;
    }

    public Integer getAddSizeStock() {
        return addSizeStock;
    }

    public void setAddSizeStock(Integer addSizeStock) {
        this.addSizeStock = addSizeStock;
    }

    public ReplenishmentResponse getReplenishmentResponse() {
        return replenishmentResponse;
    }

    public void setReplenishmentResponse(ReplenishmentResponse replenishmentResponse) {
        this.replenishmentResponse = replenishmentResponse;
    }

    public List<Integer> getSeasonLabelIds() {
        return seasonLabelIds;
    }

    public void setSeasonLabelIds(List<Integer> seasonLabelIds) {
        this.seasonLabelIds = seasonLabelIds;
    }
}