package com.nsy.oms.business.manage.transfer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.nsy.api.core.apicore.util.NsyJacksonUtils;
import com.nsy.api.transfer.domain.common.TiktokAuthResponse;
import com.nsy.api.transfer.domain.model.tiktokshop.api.response.ResponseTokenInfo;
import com.nsy.api.transfer.domain.request.platform.PlatformOrderRequest;
import com.nsy.api.transfer.domain.request.platform.tiktok.TikTokShopStatementOrdersRequest;
import com.nsy.api.transfer.domain.request.platform.tiktok.TikTokShopStatementRequest;
import com.nsy.api.transfer.domain.request.platform.tiktok.TikTokShopStatementTransactionsRequest;
import com.nsy.api.transfer.domain.request.platform.tiktok.TiktokFbtInventRecordsRequest;
import com.nsy.api.transfer.domain.request.platform.tiktok.TiktokReturnOrderQueryRequest;
import com.nsy.api.transfer.domain.response.platform.PlatformOrderInfoResponse;
import com.nsy.api.transfer.domain.response.platform.tiktok.invent.TikTokInventPageResponse;
import com.nsy.api.transfer.domain.response.platform.tiktok.refund.TiktokRefundDataResponse;
import com.nsy.api.transfer.domain.response.platform.tiktok.statement.TikTokShopOrderTransactionsResponse;
import com.nsy.api.transfer.domain.response.platform.tiktok.statement.TikTokShopStatementResponse;
import com.nsy.api.transfer.domain.response.platform.tiktok.statement.TikTokShopStatementTransactionsResponse;
import com.nsy.api.transfer.utils.TransferUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.inject.Inject;

import static com.nsy.oms.constants.TransferApiUrlConstants.GET_ORDER_LIST_DATE;
import static com.nsy.oms.constants.TransferApiUrlConstants.GET_ORDER_LIST_IDS;
import static com.nsy.oms.constants.TransferApiUrlConstants.GET_TIKTOK_INVENTORY_RECORDS;
import static com.nsy.oms.constants.TransferApiUrlConstants.GET_TIKTOK_REFUND_LIST;
import static com.nsy.oms.constants.TransferApiUrlConstants.GET_TIKTOK_STATEMENT_LIST;
import static com.nsy.oms.constants.TransferApiUrlConstants.GET_TIKTOK_STATEMENT_TRANSACTION;
import static com.nsy.oms.constants.TransferApiUrlConstants.GET_TIKTOK_STATEMENT_TRANSACTION_ORDER;
import static com.nsy.oms.constants.TransferApiUrlConstants.TIKTOK_REFRESH_TOKEN;
import static com.nsy.oms.utils.WebUtil.url;

@Service
@Slf4j
public class TransferApiService {
    @Inject
    private RestTemplate restTemplate;

    @Value("${nsy.service.url.auth}")
    private String authServiceUrl;

    @Value("${nsy.service.url.auth-ng}")
    private String authNgServiceUrl;

    @Value("${auth.app-key}")
    private String appKey;
    @Value("${auth.secret}")
    private String secret;


    /**
     * 销售平台抓单重构--根据订单号抓订单明细
     */
    public PlatformOrderInfoResponse getOrderListByIds(String platform, PlatformOrderRequest request) {
        log.info("getOrderListByIds request: {}", NsyJacksonUtils.toJson(request));
        String result = TransferUtils.setPostRequest(restTemplate, appKey, secret, url(authServiceUrl, platform + GET_ORDER_LIST_IDS), request.getStoreId().toString(), JSON.toJSONString(request));
        return JSONObject.parseObject(result, PlatformOrderInfoResponse.class);
    }

    /**
     * 销售平台抓单重构--根据时间区间抓订单信息
     */
    public PlatformOrderInfoResponse getOrderListByDate(String platform, PlatformOrderRequest request) {
        log.info("getOrderListByDate request: {}", NsyJacksonUtils.toJson(request));
        String result = TransferUtils.setPostRequest(restTemplate, appKey, secret, url(authServiceUrl, platform + GET_ORDER_LIST_DATE), request.getStoreId().toString(), JSON.toJSONString(request));
        return JSONObject.parseObject(result, PlatformOrderInfoResponse.class);
    }

    /**
     * fbt库存变动
     */
    public TikTokInventPageResponse getFbtInventPage(String platform, TiktokFbtInventRecordsRequest request, TiktokAuthResponse tiktokAuth) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("storeId", String.valueOf(tiktokAuth.getStoreId()));
        headers.add("tiktok-store-id", String.valueOf(tiktokAuth.getStoreId()));
        headers.add("accessToken", tiktokAuth.getAccessToken());
        headers.add("refreshToken", tiktokAuth.getRefreshToken());
        headers.add("accessTokenExpireIn", String.valueOf(tiktokAuth.getAccessTokenExpireIn()));
        log.info("getFbtInventPage request: {}", NsyJacksonUtils.toJson(request));
        String result = TransferUtils.setPostRequestWithHeard(restTemplate, appKey, secret, url(authNgServiceUrl, platform + GET_TIKTOK_INVENTORY_RECORDS), request.getStoreId().toString(), JSON.toJSONString(request), headers);
        return JSONObject.parseObject(result, TikTokInventPageResponse.class);
    }

    /**
     * TK账单列表
     */
    public TikTokShopStatementResponse getStatementList(String platform, TikTokShopStatementRequest request, TiktokAuthResponse tiktokAuth) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("storeId", String.valueOf(tiktokAuth.getStoreId()));
        headers.add("tiktok-store-id", String.valueOf(tiktokAuth.getStoreId()));
        headers.add("accessToken", tiktokAuth.getAccessToken());
        headers.add("refreshToken", tiktokAuth.getRefreshToken());
        headers.add("accessTokenExpireIn", String.valueOf(tiktokAuth.getAccessTokenExpireIn()));
        log.info("getStatementList request: {}", NsyJacksonUtils.toJson(request));
        String result = TransferUtils.setPostRequestWithHeard(restTemplate, appKey, secret, url(authNgServiceUrl, platform + GET_TIKTOK_STATEMENT_LIST), request.getStoreId().toString(), JSON.toJSONString(request), headers);
        return JSONObject.parseObject(result, TikTokShopStatementResponse.class);
    }

    /**
     * tk账单详情
     */
    public TikTokShopStatementTransactionsResponse getStatementTransaction(String platform, TikTokShopStatementTransactionsRequest request, TiktokAuthResponse tiktokAuth) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("storeId", String.valueOf(tiktokAuth.getStoreId()));
        headers.add("tiktok-store-id", String.valueOf(tiktokAuth.getStoreId()));
        headers.add("accessToken", tiktokAuth.getAccessToken());
        headers.add("refreshToken", tiktokAuth.getRefreshToken());
        headers.add("accessTokenExpireIn", String.valueOf(tiktokAuth.getAccessTokenExpireIn()));
        log.info("getStatementTransaction request: {}", NsyJacksonUtils.toJson(request));
        String result = TransferUtils.setPostRequestWithHeard(restTemplate, appKey, secret, url(authNgServiceUrl, platform + GET_TIKTOK_STATEMENT_TRANSACTION), request.getStoreId().toString(), JSON.toJSONString(request), headers);
        return JSONObject.parseObject(result, TikTokShopStatementTransactionsResponse.class);
    }

    /**
     * tk账单订单明细
     */
    public TikTokShopOrderTransactionsResponse getStatementTransactionOrder(String platform, TikTokShopStatementOrdersRequest request, TiktokAuthResponse tiktokAuth) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("storeId", String.valueOf(tiktokAuth.getStoreId()));
        headers.add("tiktok-store-id", String.valueOf(tiktokAuth.getStoreId()));
        headers.add("accessToken", tiktokAuth.getAccessToken());
        headers.add("refreshToken", tiktokAuth.getRefreshToken());
        headers.add("accessTokenExpireIn", String.valueOf(tiktokAuth.getAccessTokenExpireIn()));
        log.info("getStatementTransactionOrder request: {}", NsyJacksonUtils.toJson(request));
        String result = TransferUtils.setPostRequestWithHeard(restTemplate, appKey, secret, url(authNgServiceUrl, platform + GET_TIKTOK_STATEMENT_TRANSACTION_ORDER), request.getStoreId().toString(), JSON.toJSONString(request), headers);
        return JSONObject.parseObject(result, TikTokShopOrderTransactionsResponse.class);
    }

    /**
     * tk退货账单订单明细
     */
    public TiktokRefundDataResponse getTkReturnRefundList(String platform, TiktokReturnOrderQueryRequest request, TiktokAuthResponse tiktokAuth) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("storeId", String.valueOf(tiktokAuth.getStoreId()));
        headers.add("tiktok-store-id", String.valueOf(tiktokAuth.getStoreId()));
        headers.add("accessToken", tiktokAuth.getAccessToken());
        headers.add("refreshToken", tiktokAuth.getRefreshToken());
        headers.add("accessTokenExpireIn", String.valueOf(tiktokAuth.getAccessTokenExpireIn()));
        log.info("getTkReturnRefundList request: {}", NsyJacksonUtils.toJson(request));
        String result = TransferUtils.setPostRequestWithHeard(restTemplate, appKey, secret, url(authNgServiceUrl, platform + GET_TIKTOK_REFUND_LIST), request.getStoreId().toString(), JSON.toJSONString(request), headers);
        return JSONObject.parseObject(result, TiktokRefundDataResponse.class);
    }

    /**
     * TIKTOK刷新token
     */
    public ResponseTokenInfo refreshToken(String platform, String storeId, TiktokAuthResponse tiktokAuth) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("storeId", String.valueOf(tiktokAuth.getStoreId()));
        headers.add("tiktok-store-id", String.valueOf(tiktokAuth.getStoreId()));
        headers.add("accessToken", tiktokAuth.getAccessToken());
        headers.add("refreshToken", tiktokAuth.getRefreshToken());
        headers.add("accessTokenExpireIn", String.valueOf(tiktokAuth.getAccessTokenExpireIn()));
        String result = TransferUtils.setPostRequestWithHeard(restTemplate, appKey, secret, url(authNgServiceUrl, platform + TIKTOK_REFRESH_TOKEN), storeId, null, headers);
        return JSONObject.parseObject(result, ResponseTokenInfo.class);
    }


}
