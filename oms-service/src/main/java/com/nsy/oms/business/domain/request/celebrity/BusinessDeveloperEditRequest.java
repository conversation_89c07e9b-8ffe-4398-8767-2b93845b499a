package com.nsy.oms.business.domain.request.celebrity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @Author: lhh
 * @Date: 2025-05-05
 * @Description:
 */
@ApiModel("bd编辑")
public class BusinessDeveloperEditRequest {
    @ApiModelProperty("客服id")
    private Integer id;
    @NotEmpty(message = "客服名称不能为空")
    @ApiModelProperty("客服名称")
    private String bdName;
    @NotEmpty(message = "客服账号不能为空")
    @ApiModelProperty("客服账号")
    private String bdAccount;
    @NotEmpty(message = "客服邮箱不能为空")
    @ApiModelProperty("客服邮箱")
    private String bdEmail;
    @NotNull(message = "客服状态不能为空")
    @ApiModelProperty("客服状态")
    private Integer status;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getBdName() {
        return bdName;
    }

    public void setBdName(String bdName) {
        this.bdName = bdName;
    }

    public String getBdAccount() {
        return bdAccount;
    }

    public void setBdAccount(String bdAccount) {
        this.bdAccount = bdAccount;
    }

    public String getBdEmail() {
        return bdEmail;
    }

    public void setBdEmail(String bdEmail) {
        this.bdEmail = bdEmail;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
