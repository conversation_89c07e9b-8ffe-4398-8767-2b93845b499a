package com.nsy.oms.business.service.platform;

import com.nsy.oms.business.domain.dto.OrderGrabParameterDTO;
import com.nsy.oms.repository.entity.order.OrderAddressGrabQueueEntity;
import com.nsy.oms.repository.entity.order.OrderItemGrabQueueEntity;
import com.nsy.oms.repository.entity.order.OrderMissedQueueEntity;
import com.nsy.oms.repository.entity.sa.SaStoreEntity;

import java.util.List;

/**
 * 销售平台服务类
 */
public interface PlatformBaseService {
    /**
     * 根据时间区间抓单
     */
    void getOrderListByDate(OrderGrabParameterDTO orderGrabParameterDTO);

    /**
     * 根据订单号抓单
     */
    void getOrderListByIdList(SaStoreEntity storeEntity, List<OrderMissedQueueEntity> orderMissedQueueEntityList);

    /**
     * 按订单号抓订单明细
     */
    void getPlatformOrderItemListByIds(SaStoreEntity storeEntity, List<OrderItemGrabQueueEntity> orderItemGrabQueueEntityList);

    /**
     * 根据订单号抓收件人信息
     */
    void getPlatformOrderReceiveListByIds(SaStoreEntity storeEntity, List<OrderAddressGrabQueueEntity> orderAddressGrabQueueEntityList);
}
