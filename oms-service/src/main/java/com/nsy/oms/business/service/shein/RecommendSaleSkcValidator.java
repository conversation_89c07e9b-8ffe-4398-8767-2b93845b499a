package com.nsy.oms.business.service.shein;

import com.nsy.oms.enums.shein.SheinRecommendPlatformEnum;
import com.nsy.oms.enums.shein.SheinRecommendSaleSkcStatusEnum;
import com.nsy.oms.repository.entity.sa.SaStoreEntity;
import com.nsy.oms.repository.entity.shein.SheinRecommendSaleSkcEntity;
import com.nsy.oms.utils.Validator;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 *
 */
public class RecommendSaleSkcValidator {

    public static void validMultiPlatform(List<SaStoreEntity> saStoreEntities) {
        if (CollectionUtils.isEmpty(saStoreEntities)) {
            return;
        }
        Validator.valid(saStoreEntities.stream().map(SaStoreEntity::getPlatformId).filter(Objects::nonNull), stream -> stream.distinct().count() > 1, "不能跨平台推荐");
    }

    public static void validMarkAsPublish(List<SheinRecommendSaleSkcEntity> sourceEntities, SaStoreEntity targetStore) {
        Validator.valid(sourceEntities.stream().map(SheinRecommendSaleSkcEntity::getPlatform), stream -> stream.distinct().count() > 1, "不能跨平台推荐");
        Validator.valid(sourceEntities, items -> items.stream().anyMatch(item -> !SheinRecommendPlatformEnum.of(targetStore.allPlatform()).getOmsPublishPlatform().equals(item.getPlatform())), "不能跨平台推荐");
        Validator.valid(sourceEntities.stream().map(SheinRecommendSaleSkcEntity::getRecommendStoreId).collect(Collectors.toList()), items -> !items.contains(targetStore.getId()), "不允许标识其它建议上架店铺的款式，如需自主上架，请使用导入标识功能");
        Validator.valid(sourceEntities, items -> items.stream().anyMatch(item -> !SheinRecommendSaleSkcStatusEnum.UN_SHELF.getCode().equals(item.getStatus())), "非待上架状态不能标识上架");
    }

    public static void validModifyRecommendStore(List<SheinRecommendSaleSkcEntity> sheinRecommendSaleSkcEntities, String createBy, SaStoreEntity saStoreEntity, RecommendSaleSkcPlatformService platformService) {
        Validator.valid(saStoreEntity, Objects::isNull, "该店铺不存在");
        Validator.valid(platformService, Objects::isNull, "该店铺不支持推荐请确认");
        Validator.valid(sheinRecommendSaleSkcEntities, CollectionUtils::isEmpty, "内贸推荐不存在");
        List<String> platforms = sheinRecommendSaleSkcEntities.stream().map(SheinRecommendSaleSkcEntity::getPlatform).distinct().collect(Collectors.toList());
        Validator.valid(platforms, items -> items.size() > 1, "不能跨平台修改");
        Validator.valid(SheinRecommendPlatformEnum.of(saStoreEntity.allPlatform()).getOmsPublishPlatform(), item -> !platforms.get(0).equals(item), "平台不一致不能修改");
        List<String> notPendingSkcList = sheinRecommendSaleSkcEntities.stream()
                .filter(sheinRecommendSaleSkcEntity -> !SheinRecommendSaleSkcStatusEnum.UN_SHELF.getCode().equals(sheinRecommendSaleSkcEntity.getStatus()))
                .map(SheinRecommendSaleSkcEntity::getSkc)
                .collect(Collectors.toList());
        Validator.valid(notPendingSkcList, CollectionUtils::isNotEmpty, String.format("%s%s", "存在非待上架的商品不支持修改,skc:", String.join(",", notPendingSkcList)));
        List<String> cannotUpdateSkcList = sheinRecommendSaleSkcEntities.stream()
                .filter(sheinRecommendSaleSkcEntity -> !RecommendSaleSkcAdapter.SYSTEM_OPERATOR.equals(sheinRecommendSaleSkcEntity.getCreateBy()))
                .filter(sheinRecommendSaleSkcEntity -> !createBy.equals(sheinRecommendSaleSkcEntity.getCreateBy()))
                .map(SheinRecommendSaleSkcEntity::getSkc)
                .collect(Collectors.toList());
        Validator.valid(cannotUpdateSkcList, CollectionUtils::isNotEmpty, String.format("%s%s", "只支持修改系统建议或自己建议的商品,无法修改skc:", String.join(",", cannotUpdateSkcList)));
        platformService.validModifyRecommendStore(sheinRecommendSaleSkcEntities, saStoreEntity);
    }



}
