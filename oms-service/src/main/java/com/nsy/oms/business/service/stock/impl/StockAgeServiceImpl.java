package com.nsy.oms.business.service.stock.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.oms.business.domain.request.stock.StockUpdateRequest;
import com.nsy.oms.business.service.stock.StockAgeLogService;
import com.nsy.oms.business.service.stock.StockAgeService;
import com.nsy.oms.repository.entity.stock.StockAgeEntity;
import com.nsy.oms.repository.entity.stock.StockEntity;
import com.nsy.oms.repository.sql.mapper.stock.StockAgeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @author: caishaohui
 * @version: v1.0
 * @description: 库龄表业务实现
 * @date: 2025-04-02 14:40
 */
@Service
public class StockAgeServiceImpl extends ServiceImpl<StockAgeMapper, StockAgeEntity> implements StockAgeService {


    @Autowired
    private StockAgeLogService stockAgeLogService;

    /**
     * 库龄变化更新
     * 1.根据stockId查询库龄记录
     * 2.更新库存记录
     * 2.1 若库存增加，新增一条库龄记录，入库时间为当前时间
     * 2.2 若库存减少，遵循先进先出原则，入库时间更早的优先扣除，为0则删除记录
     * 3.记录库龄变动日志
     *
     * @param stockEntity
     * @param stockUpdateRequest
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void stockAgeUpdate(StockEntity stockEntity, StockUpdateRequest stockUpdateRequest) {

        // 获取库存变化数量
        Integer changeQty = stockUpdateRequest.getQty();

        //2.更新库龄记录
        if (changeQty > 0) {
            //2.1 若库存增加，新增一条库龄记录，入库时间为当前时间
            StockAgeEntity newStockAge = new StockAgeEntity();
            newStockAge.setStockId(stockEntity.getStockId());
            newStockAge.setStock(changeQty);
            newStockAge.setLeftStock(changeQty);
            newStockAge.setStockinDate(new Date());
            newStockAge.setType(stockUpdateRequest.getChangeLogType().name());
            newStockAge.setContent(stockUpdateRequest.getContent());

            // 保存新的库龄记录
            this.save(newStockAge);
            // 记录库龄变动日志
            stockAgeLogService.addStockAgeChangeLog(newStockAge, stockUpdateRequest, 0);

        } else if (changeQty < 0) {
            //根据stockId查询库龄记录
            List<StockAgeEntity> stockAgeList = this.list(new LambdaQueryWrapper<StockAgeEntity>()
                    .eq(StockAgeEntity::getStockId, stockEntity.getStockId())
                    .gt(StockAgeEntity::getLeftStock, 0));

            //2.2 若库存减少，遵循先进先出原则，入库时间更早的优先扣除，为0则删除记录
            int remainingQty = Math.abs(changeQty);

            // 按入库时间排序，先进先出
            stockAgeList.sort((a, b) -> a.getStockinDate().compareTo(b.getStockinDate()));

            for (StockAgeEntity stockAge : stockAgeList) {
                if (remainingQty <= 0) {
                    break;
                }

                int deductQty = Math.min(remainingQty, stockAge.getLeftStock());
                remainingQty -= deductQty;

                // 记录库龄变动日志
                stockAgeLogService.addStockAgeChangeLog(stockAge, stockUpdateRequest, stockAge.getLeftStock());

                // 更新库龄记录
                stockAge.setLeftStock(stockAge.getLeftStock() - deductQty);

                this.updateById(stockAge);
            }
        }
    }
}
