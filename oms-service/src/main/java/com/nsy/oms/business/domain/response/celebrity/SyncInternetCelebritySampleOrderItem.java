package com.nsy.oms.business.domain.response.celebrity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: linCheng
 * @create: 2024-09-02 10:30
 **/
@Data
public class SyncInternetCelebritySampleOrderItem {

    /**
     * sku
     */
    private String sku;

    /**
     * 数量
     */
    private Integer qty;

    /**
     * sku
     */
    private String sellerSku;

    /**
     * sku图片
     */
    private String skuPictureUrl;

    /**
     * 创建人
     */
    private String createBy;

    private String sellerSkuId;

    private String sellerProductId;

    // 发货店铺id
    private Integer deliveryStoreId;
    // 平台原始订单号
    private String platformOriginalOrderNo;
    // 订单发货时间
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date orderDeliveryDate;
    // 订单妥投时间
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date orderCompromiseDate;
    // 物流单号
    private String trackingNumber;
    // 订单物流类型(erp)
    private String orderDeliveryType;
    // 平台包裹id
    private String platformPackageId;
    // 包裹状态
    private String packageStatus;

    private List<SyncInternetCelebritySampleOrderItemPost> syncInternetCelebritySampleOrderItemPosts;
}
