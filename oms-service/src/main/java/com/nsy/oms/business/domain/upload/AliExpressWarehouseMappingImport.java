package com.nsy.oms.business.domain.upload;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 速卖通活动仓库位映射
 * <AUTHOR>
 * @date 2023-04-18
 */
public class AliExpressWarehouseMappingImport extends BaseErrorTemplate {
    @ExcelProperty("部门名称")
    private String departmentName;
    @ExcelProperty("库位编码")
    private String code;
    @ExcelProperty("店铺名称")
    private String storeName;

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }
}
