package com.nsy.oms.business.service.report.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.transfer.domain.common.TiktokAuthResponse;
import com.nsy.api.transfer.domain.request.platform.tiktok.TikTokShopStatementRequest;
import com.nsy.api.transfer.domain.request.platform.tiktok.TikTokShopStatementTransactionsRequest;
import com.nsy.api.transfer.domain.response.platform.tiktok.statement.Statement;
import com.nsy.api.transfer.domain.response.platform.tiktok.statement.TikTokShopStatementResponse;
import com.nsy.api.transfer.domain.response.platform.tiktok.statement.TikTokShopStatementTransactionsResponse;
import com.nsy.api.transfer.domain.response.platform.tiktok.statement.Transaction;
import com.nsy.api.transfer.enums.PlatformTypeEnum;
import com.nsy.oms.business.domain.dto.BaseParameterDTO;
import com.nsy.oms.business.domain.dto.TkStatementParameterDTO;
import com.nsy.oms.business.manage.transfer.TransferApiService;
import com.nsy.oms.business.service.report.ReportTkStatementService;
import com.nsy.oms.repository.entity.report.ReportTkStatementEntity;
import com.nsy.oms.repository.mongo.document.tiktok.ReportTkStatementTransactionDocument;
import com.nsy.oms.repository.mongo.repository.tiktok.ReportTkStatementTransactionDocumentRepository;
import com.nsy.oms.repository.sql.mapper.report.ReportTkStatementMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 针对表【report_tk_statement(TK账单表)】的数据库操作Service实现
 * @createDate 2025-05-08 15:02:13
 */
@Service
@Slf4j
public class ReportTkStatementServiceImpl extends ServiceImpl<ReportTkStatementMapper, ReportTkStatementEntity>
        implements ReportTkStatementService {
    @Autowired
    private TransferApiService transferApiService;
    @Autowired
    private ReportTkStatementService reportTkStatementService;
    @Autowired
    private ReportTkStatementTransactionDocumentRepository reportTkStatementDocumentRepository;

    @Override
    public void saveRecords(List<TiktokAuthResponse> data, TkStatementParameterDTO parameterDTO) {
        if (!CollectionUtils.isEmpty(data)) {
            data.forEach(item -> {
                // 如果不为空就只执行一个店铺
                if (Objects.nonNull(parameterDTO.getStoreId()) && !item.getStoreId().equals(parameterDTO.getStoreId())) {
                    return;
                }
                if (StringUtils.isNotBlank(item.getAccessToken())) {
                    doGetRecords(parameterDTO, item);
                }
            });
        }
    }

    @Override
    public void saveTransactionRecords(List<TiktokAuthResponse> data, BaseParameterDTO tkStatementParameterDTO) {
        if (!CollectionUtils.isEmpty(data)) {
            doGetRequest(data, tkStatementParameterDTO);
        }
    }

    private void doGetRequest(List<TiktokAuthResponse> data, BaseParameterDTO tkStatementParameterDTO) {
        data.forEach(item -> {
            if (StringUtils.isNotBlank(item.getAccessToken())) {
                List<ReportTkStatementEntity> reportTkStatementEntities = baseMapper.selectList(
                        new LambdaQueryWrapper<ReportTkStatementEntity>()
                                .eq(ReportTkStatementEntity::getIsPullStatus, 0)
                                .eq(ReportTkStatementEntity::getStoreId, item.getStoreId())
                                .orderByDesc(ReportTkStatementEntity::getStatementTime)
                                .last("limit " + tkStatementParameterDTO.getFetchCount())
                );
                if (!CollectionUtils.isEmpty(reportTkStatementEntities)) {
                    doSaveTkStatementTransactionEntity(tkStatementParameterDTO, item, reportTkStatementEntities);
                }
            }
        });
    }

    private void doSaveTkStatementTransactionEntity(BaseParameterDTO tkStatementParameterDTO, TiktokAuthResponse item, List<ReportTkStatementEntity> reportTkStatementEntities) {
        reportTkStatementEntities.forEach(reportTkStatementEntity -> {
            PlatformTypeEnum platformTypeEnum = PlatformTypeEnum.getPlatform(item.getPlatformId());
            if (platformTypeEnum == null) {
                log.error("根据ID获取平台失败！");
                return;
            }
            TikTokShopStatementTransactionsRequest request = new TikTokShopStatementTransactionsRequest();
            request.setStatementId(reportTkStatementEntity.getStatementId());
            request.setLocation(tkStatementParameterDTO.getLocation());
            request.setStoreId(item.getStoreId());
            request.setPageSize(tkStatementParameterDTO.getFetchCount());
            request.setPlatform(platformTypeEnum);
            try {
                getTransactionList(item, platformTypeEnum, request, reportTkStatementEntity);
            } catch (InterruptedException e) {
                log.error(String.format("%s获取tk账单交易详情失败:%s", item.getStoreId(), e.getMessage()));
            }
        });
    }

    private void getTransactionList(TiktokAuthResponse tiktokAuth, PlatformTypeEnum platformTypeEnum, TikTokShopStatementTransactionsRequest request, ReportTkStatementEntity reportTkStatementEntity) throws InterruptedException {
        TikTokShopStatementTransactionsResponse tikTokResponse = null;
        int maxRetries = 5;
        int currentRetry = 0;
        boolean success = false;

        while (!success && currentRetry < maxRetries) {
            try {
                tikTokResponse = transferApiService.getStatementTransaction(platformTypeEnum.getUrl(), request, tiktokAuth);
                success = true;
                currentRetry = 0; // 请求成功后重置重试次数
            } catch (Exception e) {
                currentRetry++;
                log.error("获取tk账单交易详情失败，当前重试次数：{}，错误信息：{}", currentRetry, e.getMessage());
                if (currentRetry < maxRetries) {
                    Thread.sleep(5000); // 等待5秒后重试
                } else {
                    log.error("获取tk账单交易详情失败，已达到最大重试次数：{}", maxRetries);
                    throw new BusinessServiceException("获取tk账单交易详情失败，已达到最大重试次数");
                }
            }
        }
        if (Objects.isNull(tikTokResponse) || Objects.isNull(tikTokResponse.getData()) || tikTokResponse.getCode() != 0) {
            return;
        }
        if (reportTkStatementEntity.getTotalCount() == 0) {
            reportTkStatementEntity.setTotalCount(tikTokResponse.getData().getTotalCount());
            reportTkStatementEntity.setStatus(tikTokResponse.getData().getStatus());
            reportTkStatementEntity.setTotalSettlementAmount(tikTokResponse.getData().getTotalSettlementAmount());
            if (Objects.nonNull(tikTokResponse.getData().getTotalSettlementBreakdown())) {
                BeanUtils.copyProperties(tikTokResponse.getData().getTotalSettlementBreakdown(), reportTkStatementEntity);
                reportTkStatementEntity.setIsPullStatus(1);
                reportTkStatementService.updateById(reportTkStatementEntity);
            }
        }

        List<Transaction> records = tikTokResponse.getData().getTransactions();
        if (!CollectionUtils.isEmpty(records)) {
            List<ReportTkStatementTransactionDocument> list = new ArrayList<>();
            TikTokShopStatementTransactionsResponse finalTikTokResponse = tikTokResponse;
            records.forEach(item -> {
                ReportTkStatementTransactionDocument entity = new ReportTkStatementTransactionDocument();
                // 删除重复数据
                ReportTkStatementTransactionDocument document = reportTkStatementDocumentRepository.findAllByStoreIdAndStatementIdAndTransactionIdAndOrderIdAndAdjustmentId(tiktokAuth.getStoreId(), finalTikTokResponse.getData().getId(), item.getId(), item.getOrderId(), item.getAdjustmentId());
                if (!Objects.isNull(document)) {
                    reportTkStatementDocumentRepository.delete(document);
                }
                entity.setStatementId(finalTikTokResponse.getData().getId());
                entity.setOrderId(item.getOrderId());
                entity.setStoreId(tiktokAuth.getStoreId());
                entity.setStoreName(tiktokAuth.getStoreName());
                entity.setContent(JSONObject.toJSONString(item));
                entity.setTransactionId(item.getId());
                entity.setAdjustmentId(item.getAdjustmentId());
                entity.setLocation(request.getLocation());
                if (StringUtils.isBlank(entity.getOrderId())) {
                    entity.setOrderIdStatus(3);
                }
                entity.setCreateDate(new Date());
                entity.setUpdateDate(new Date());
                list.add(entity);
            });
            reportTkStatementDocumentRepository.saveAll(list);
        }
        if (StringUtils.isNotBlank(tikTokResponse.getData().getNextPageToken())) {
            if (checkTransactionToken(request, tikTokResponse)) return;
            Thread.sleep(10000);
            request.setNextPageToken(tikTokResponse.getData().getNextPageToken());
            getTransactionList(tiktokAuth, platformTypeEnum, request, reportTkStatementEntity);
        }
    }

    private boolean checkTransactionToken(TikTokShopStatementTransactionsRequest request, TikTokShopStatementTransactionsResponse tikTokResponse) {
        if (StringUtils.isNotBlank(request.getNextPageToken()) && request.getNextPageToken().equals(tikTokResponse.getData().getNextPageToken())) {
            return true;
        }
        return false;
    }

    private void doGetRecords(TkStatementParameterDTO parameterDTO, TiktokAuthResponse item) {
        PlatformTypeEnum platformTypeEnum = PlatformTypeEnum.getPlatform(item.getPlatformId());
        if (platformTypeEnum == null) {
            log.error("根据ID获取平台失败！");
            return;
        }
        TikTokShopStatementRequest request = new TikTokShopStatementRequest();
        request.setLocation(parameterDTO.getLocation());
        request.setStoreId(item.getStoreId());
        request.setPageSize(parameterDTO.getFetchCount());
        request.setPlatform(platformTypeEnum);

        if (StringUtils.isNotBlank(parameterDTO.getStartDate()) || StringUtils.isNotBlank(parameterDTO.getEndDate())) {
            setDate(parameterDTO, request);
        } else {
            // 获取两天前的日期，并设置为当天的零点
            Date twoDaysAgo = DateUtil.offsetDay(new Date(), -2);
            String twoDaysAgoStr = DateUtil.format(DateUtil.beginOfDay(twoDaysAgo), "yyyy-MM-dd HH:mm:ss");
            request.setStatementTimeGe(twoDaysAgoStr);
            String twoDaysAgoEndStr = DateUtil.format(DateUtil.endOfDay(twoDaysAgo), "yyyy-MM-dd HH:mm:ss");
            request.setStatementTimeLt(twoDaysAgoEndStr);
        }
        try {
            getTkStatementList(platformTypeEnum, request, item);
        } catch (InterruptedException e) {
            log.error(String.format("%s获取tk账单列表失败:%s", item.getStoreId(), e.getMessage()));
        }
    }

    private void getTkStatementList(PlatformTypeEnum platformTypeEnum, TikTokShopStatementRequest request, TiktokAuthResponse tiktokAuth) throws InterruptedException {
        TikTokShopStatementResponse tikTokResponse = null;
        int maxRetries = 5;
        int currentRetry = 0;
        boolean success = false;

        while (!success && currentRetry < maxRetries) {
            try {
                tikTokResponse = transferApiService.getStatementList(platformTypeEnum.getUrl(), request, tiktokAuth);
                success = true;
                currentRetry = 0; // 请求成功后重置重试次数
            } catch (Exception e) {
                currentRetry++;
                log.error("获取tk账单列表失败，当前重试次数：{}，错误信息：{}", currentRetry, e.getMessage());
                if (currentRetry < maxRetries) {
                    // 等待5秒后重试
                    Thread.sleep(5000);
                } else {
                    log.error("获取tk账单列表失败，已达到最大重试次数：{}", maxRetries);
                    throw new BusinessServiceException("获取tk账单列表失败，已达到最大重试次数");
                }
            }
        }

        if (Objects.isNull(tikTokResponse) || Objects.isNull(tikTokResponse.getData()) || tikTokResponse.getCode() != 0) {
            return;
        }
        List<Statement> records = tikTokResponse.getData().getStatements();
        if (!CollectionUtils.isEmpty(records)) {
            List<ReportTkStatementEntity> list = new ArrayList<>();
            records.forEach(item -> {
                ReportTkStatementEntity entity = new ReportTkStatementEntity();
                // 删除重复数据
                baseMapper.delete(new LambdaQueryWrapper<ReportTkStatementEntity>()
                        .eq(ReportTkStatementEntity::getStatementId, item.getId()));
                BeanUtils.copyProperties(item, entity);
                entity.setStatementId(item.getId().toString());
                entity.setStoreId(tiktokAuth.getStoreId());
                entity.setStoreName(tiktokAuth.getStoreName());
                // 时间戳转日期
                entity.setStatementTime(new Date(item.getStatementTime() * 1000));
                entity.setLocation(request.getLocation());
                entity.setCreateDate(new Date());
                entity.setUpdateDate(new Date());
                list.add(entity);
            });
            this.saveBatch(list);
        }
        if (StringUtils.isNotBlank(tikTokResponse.getData().getNextPageToken())) {
            if (checkToken(request, tikTokResponse)) return;
            Thread.sleep(10000);
            request.setNextPageToken(tikTokResponse.getData().getNextPageToken());
            getTkStatementList(platformTypeEnum, request, tiktokAuth);
        }
    }

    private static boolean checkToken(TikTokShopStatementRequest request, TikTokShopStatementResponse tikTokResponse) {
        if (StringUtils.isNotBlank(request.getNextPageToken()) && request.getNextPageToken().equals(tikTokResponse.getData().getNextPageToken())) {
            return true;
        }
        return false;
    }

    private void setDate(TkStatementParameterDTO parameterDTO, TikTokShopStatementRequest request) {
        if (StringUtils.isNotBlank(parameterDTO.getStartDate())) {
            request.setStatementTimeGe(parameterDTO.getStartDate());
        }
        if (StringUtils.isNotBlank(parameterDTO.getEndDate())) {
            request.setStatementTimeLt(parameterDTO.getEndDate());
        }
    }
}




