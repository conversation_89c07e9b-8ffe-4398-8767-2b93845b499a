package com.nsy.oms.business.domain.request.celebrity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: lhh
 * @Date: 2025-04-18
 * @Description: 网红查询请求类
 */
@NoArgsConstructor
@Data
public class InternetCelebritySyncCreatorRequest {

    @NotEmpty
    @JsonProperty("data")
    List<InternetCelebritySyncCreator> data;

    @NoArgsConstructor
    @Data
    public static class InternetCelebritySyncCreator {
        @NotBlank
        @JsonProperty("cid")
        String cid;
        @JsonProperty("store_id")
        String storeId;
        @JsonProperty("store_name")
        String storeName;
        @JsonProperty("bdName")
        String bdName;
        @JsonProperty("imagesData")
        String imagesData;
        @NotBlank
        @JsonProperty("accountHandle")
        String accountHandle;
        @JsonProperty("email")
        String email;
        @JsonProperty("followerRatio")
        String followerRatio;
        @JsonProperty("followerAge18To24Last1Months")
        BigDecimal followerAge18To24Last1Months;
        @JsonProperty("followerAge25To34Last1Months")
        BigDecimal followerAge25To34Last1Months;
        @JsonProperty("followerAge35To44Last1Months")
        BigDecimal followerAge35To44Last1Months;
        @JsonProperty("followerAge45To54Last1Months")
        BigDecimal followerAge45To54Last1Months;
        @JsonProperty("followerAge55UpLast1Months")
        BigDecimal followerAge55UpLast1Months;
        @JsonProperty("gmvLast1Months")
        String gmvLast1Months;
        @JsonProperty("itemsSoldLast1Months")
        String itemsSoldLast1Months;
        @JsonProperty("gpmLast1Months")
        String gpmLast1Months;
        @JsonProperty("gmvPerBuyerLast1Months")
        String gmvPerBuyerLast1Months;
        @JsonProperty("sampleFulfillmentRate")
        String sampleFulfillmentRate;
        @JsonProperty("industryGroups")
        List<IndustryGroupsDTO> industryGroups;

        @NoArgsConstructor
        @Data
        public static class IndustryGroupsDTO {
            @JsonProperty("key")
            String key;
            @JsonProperty("value")
            String value;
            @JsonProperty("name")
            String name;
        }
    }
    
}
