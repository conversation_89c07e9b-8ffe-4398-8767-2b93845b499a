package com.nsy.oms.business.manage.wms;

import com.nsy.api.core.apicore.util.NsyJacksonUtils;
import com.nsy.oms.business.manage.wms.request.StaBoxInfoDetailRequest;
import com.nsy.oms.business.manage.wms.request.StockTransferTrackingDetailRequest;
import com.nsy.oms.business.manage.wms.request.StockTransferTrackingSkuRequest;
import com.nsy.oms.business.manage.wms.response.StaBoxInfoDetailResponse;
import com.nsy.oms.business.manage.wms.response.StockTransferTrackingDetailResponse;
import com.nsy.oms.business.manage.wms.response.StockTransferTrackingSkuResponse;
import com.nsy.oms.utils.mp.LocationContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


@Service
public class WmsApiService {
    private static final Logger LOGGER = LoggerFactory.getLogger(WmsApiService.class);
    @Inject
    private RestTemplate restTemplate;
    @Value("${nsy.service.url.wms}")
    private String serviceUrl;

    public StaBoxInfoDetailResponse getStaBoxInfoDetailByErpTid(String erpTid) {
        StaBoxInfoDetailRequest request = new StaBoxInfoDetailRequest();
        request.setOrderNo(erpTid);
        return this.getStaBoxInfoDetail(request);
    }

    public StaBoxInfoDetailResponse getStaBoxInfoDetail(StaBoxInfoDetailRequest request) {
        String uri = String.format("%s/stockout-shipment/order/sta-box-info-detail", serviceUrl);
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add("Location", LocationContext.getLocation());
            HttpEntity<StaBoxInfoDetailRequest> entity = new HttpEntity<>(request, headers);
            LOGGER.info("tms getLogisticsInfo request:{}", NsyJacksonUtils.toJson(request));
            ResponseEntity<StaBoxInfoDetailResponse> respEntity = this.restTemplate.postForEntity(uri, entity, StaBoxInfoDetailResponse.class);
            LOGGER.info("tms getLogisticsInfo response:{}", NsyJacksonUtils.toJson(respEntity));
            return respEntity.getBody();
        } catch (Exception e) {
            LOGGER.error("tms getLogisticsInfo error: orderNo: {}, msg: {}", request.getOrderNo(), e.getMessage());
        }
        return null;
    }

    public List<StockTransferTrackingSkuResponse> getOnTheWayStock(StockTransferTrackingSkuRequest request) {
        String uri = String.format("%s/stock/transfer-tracking/on-the-way-stock", serviceUrl);
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add("Location", LocationContext.getLocation());
            HttpEntity<StockTransferTrackingSkuRequest> entity = new HttpEntity<>(request, headers);
            LOGGER.info("getOnTheWayStock request:{}", NsyJacksonUtils.toJson(request));
            ResponseEntity<String> respEntity = this.restTemplate.postForEntity(uri, entity, String.class);
            LOGGER.info("getOnTheWayStock response:{}", NsyJacksonUtils.toJson(respEntity));
            if (Objects.nonNull(respEntity.getBody())) {
                return NsyJacksonUtils.jsonToList(respEntity.getBody(), StockTransferTrackingSkuResponse.class);
            }
        } catch (Exception e) {
            LOGGER.error("getOnTheWayStock error: s: {}, msg: {}", request.getSkuInfoList(), e.getMessage());
        }
        return new ArrayList<>();
    }

    public List<StockTransferTrackingDetailResponse> getOnTheWayStockDetail(StockTransferTrackingDetailRequest request) {
        String uri = String.format("%s/stock/transfer-tracking/detail", serviceUrl);
        try {
            LOGGER.info("getOnTheWayStockDetail request:{}", NsyJacksonUtils.toJson(request));
            ResponseEntity<String> respEntity = this.restTemplate.postForEntity(uri, request, String.class);
            LOGGER.info("getOnTheWayStockDetail response:{}", NsyJacksonUtils.toJson(respEntity));
            if (Objects.nonNull(respEntity.getBody())) {
                return NsyJacksonUtils.jsonToList(respEntity.getBody(), StockTransferTrackingDetailResponse.class);
            }
        } catch (Exception e) {
            LOGGER.error("getOnTheWayStockDetail error: requestData: {}, msg: {}", request.getSkuList(), e.getMessage());
        }
        return new ArrayList<>();
    }
}
