package com.nsy.oms.business.domain.request.points;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:
 * @author: linCheng
 * @create: 2025-05-09 09:17
 **/
@Data
public class SyncB2bPointsRefund {

    @ApiModelProperty("退款请求ID")
    @NotNull(message = "退款请求ID不能为空")
    private Integer refundId;

    @ApiModelProperty("账期创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @NotNull(message = "账期创建时间不能为空")
    private Date accountCreateTime;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("客户邮箱")
    @NotBlank(message = "客户邮箱不能为空")
    private String customerEmail;

    @ApiModelProperty("店铺id")
    @NotNull(message = "店铺id不能为空")
    private Integer storeId;

    @ApiModelProperty("店铺名称")
    private String storeName;

    @ApiModelProperty("业务员")
    @NotBlank(message = "业务员不能为空")
    private String salesman;

    @ApiModelProperty("最后一次下单时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @NotNull(message = "最后一次下单时间不能为空")
    private Date lastOrderTime;

    @ApiModelProperty("待退积分")
    @NotNull(message = "待退积分")
    private Integer pointsRefunded;

    @ApiModelProperty("待退金额")
    @NotNull(message = "待退金额")
    private BigDecimal amountRefunded;

}
