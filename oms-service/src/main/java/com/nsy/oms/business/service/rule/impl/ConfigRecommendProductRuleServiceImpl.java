package com.nsy.oms.business.service.rule.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.exception.InvalidRequestException;
import com.nsy.api.core.apicore.privilege.LoginInfoService;
import com.nsy.api.core.apicore.util.JSONUtils;
import com.nsy.api.oms.dto.response.store.AmazonStoreListResponse;
import com.nsy.api.oms.dto.response.store.StoreDetailResponse;
import com.nsy.oms.business.domain.request.rule.ConfigRecommendProductRuleCheckBrandSpiltRequest;
import com.nsy.oms.business.domain.request.rule.ConfigRecommendProductRuleCopyRequest;
import com.nsy.oms.business.domain.request.rule.ConfigRecommendProductRuleCreateRequest;
import com.nsy.oms.business.domain.request.rule.ConfigRecommendProductRulePageRequest;
import com.nsy.oms.business.domain.request.rule.ConfigRecommendProductRuleRequest;
import com.nsy.oms.business.domain.response.base.PageResponse;
import com.nsy.oms.business.domain.response.rule.ConfigRecommendProductRule;
import com.nsy.oms.business.domain.response.rule.ConfigRecommendProductRuleItem;
import com.nsy.oms.business.domain.response.rule.ConfigRecommendProductRuleItemResponse;
import com.nsy.oms.business.domain.response.rule.ConfigRecommendProductRuleResponse;
import com.nsy.oms.business.domain.response.rule.ProductRuleResponse;
import com.nsy.oms.business.domain.response.rule.ReplenishmentResponse;
import com.nsy.oms.business.domain.response.rule.RuleStockItemResponse;
import com.nsy.oms.business.domain.response.rule.StockUpdateItem;
import com.nsy.oms.business.manage.erp.ErpApiService;
import com.nsy.oms.business.manage.erp.response.ErpSpaceInfoResponse;
import com.nsy.oms.business.manage.user.request.shared.SelectModel;
import com.nsy.oms.business.service.bd.BdBrandStoreService;
import com.nsy.oms.business.service.bd.BdOrderRuleService;
import com.nsy.oms.business.service.rule.ConfigRecommendProductRuleService;
import com.nsy.oms.constants.NumberConstant;
import com.nsy.oms.enums.shein.RuleTypeEnum;
import com.nsy.oms.enums.shein.SheinRecommendPlatformEnum;
import com.nsy.oms.enums.shein.UseTypeEnum;
import com.nsy.oms.repository.dao.auth.SauPlatformAuthConfigDao;
import com.nsy.oms.repository.dao.rule.ConfigRecommendProductRuleDao;
import com.nsy.oms.repository.dao.rule.ConfigRecommendProductRuleItemDao;
import com.nsy.oms.repository.dao.sa.SaStoreDao;
import com.nsy.oms.repository.entity.auth.SauPlatformAuthConfigEntity;
import com.nsy.oms.repository.entity.bd.BdBrandStoreEntity;
import com.nsy.oms.repository.entity.bd.BdOrderRuleEntity;
import com.nsy.oms.repository.entity.rule.ConfigRecommendProductRuleEntity;
import com.nsy.oms.repository.entity.rule.ConfigRecommendProductRuleItemEntity;
import com.nsy.oms.repository.entity.sa.SaStoreEntity;
import com.nsy.oms.utils.BeanUtils;
import com.nsy.oms.utils.BigDecimalUtil;
import com.nsy.oms.utils.JsonMapper;
import com.nsy.oms.utils.WebUtil;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *
 */
@Service
public class ConfigRecommendProductRuleServiceImpl implements ConfigRecommendProductRuleService {
    @Autowired
    private ConfigRecommendProductRuleDao configRecommendProductRuleDao;
    @Autowired
    private ConfigRecommendProductRuleItemDao configRecommendProductRuleItemDao;
    @Autowired
    private SaStoreDao saStoreDao;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private SauPlatformAuthConfigDao sauPlatformAuthConfigDao;
    @Autowired
    private BdOrderRuleService bdOrderRuleService;
    @Autowired
    private ErpApiService erpApiService;
    @Autowired
    private BdBrandStoreService bdBrandStoreService;

    @Override
    public List<ConfigRecommendProductRule> getByPlatform(ConfigRecommendProductRuleRequest request) {
        List<ConfigRecommendProductRuleEntity> productRuleEntities = configRecommendProductRuleDao.getByPlatform(request.getPlatform());
        if (CollectionUtils.isEmpty(productRuleEntities)) {
            return Collections.emptyList();
        }
        List<Integer> ruleIds = productRuleEntities.stream().map(ConfigRecommendProductRuleEntity::getConfigRecommendProductRuleId).distinct().collect(Collectors.toList());
        List<ConfigRecommendProductRuleItemEntity> configRecommendProductRuleItemEntities = configRecommendProductRuleItemDao.getByRuleIdIn(ruleIds, request.getUseType());
        List<Integer> storeIds = productRuleEntities.stream().map(ConfigRecommendProductRuleEntity::getStoreId).distinct().collect(Collectors.toList());
        List<SauPlatformAuthConfigEntity> list = sauPlatformAuthConfigDao.getInfoByStoreIds(storeIds);
        return productRuleEntities.stream().map(p -> {
            ConfigRecommendProductRule configRecommendProductRule = new ConfigRecommendProductRule();
            configRecommendProductRule.setStoreId(p.getStoreId());
            configRecommendProductRule.setPlatform(p.getPlatform());
            configRecommendProductRule.setStatus(p.getStatus());
            configRecommendProductRule.setConfigRecommendProductRuleId(p.getConfigRecommendProductRuleId());
            SauPlatformAuthConfigEntity sauPlatformAuthConfigEntity = list.stream().filter(l -> Objects.equals(l.getStoreId(), p.getStoreId())).findFirst().orElse(new SauPlatformAuthConfigEntity());
            configRecommendProductRule.setSheinStoreId(sauPlatformAuthConfigEntity.getAccountProperties1());
            List<ConfigRecommendProductRuleItemEntity> productRuleItemEntities = configRecommendProductRuleItemEntities.stream().filter(c -> Objects.equals(c.getConfigRecommendProductRuleId(), p.getConfigRecommendProductRuleId())).collect(Collectors.toList());
            ConfigRecommendProductRuleItemResponse productRuleItemResponse = new ConfigRecommendProductRuleItemResponse();
            buildConfigRecommendProductRuleItem(productRuleItemEntities, productRuleItemResponse);
            configRecommendProductRule.setRuleDetail(productRuleItemResponse);
            List<ConfigRecommendProductRuleItem> configRecommendProductRuleItems = productRuleItemEntities.stream().map(pr -> {
                ConfigRecommendProductRuleItem configRecommendProductRuleItem = new ConfigRecommendProductRuleItem();
                configRecommendProductRuleItem.setRuleKey(pr.getRuleKey());
                configRecommendProductRuleItem.setRuleValue(pr.getRuleValue());
                configRecommendProductRuleItem.setIsDefault(pr.getIsDefault());
                return configRecommendProductRuleItem;
            }).collect(Collectors.toList());
            configRecommendProductRule.setConfigRecommendProductRuleItems(configRecommendProductRuleItems);
            return configRecommendProductRule;
        }).collect(Collectors.toList());
    }

    @Override
    public PageResponse<ConfigRecommendProductRuleResponse> pageList(ConfigRecommendProductRulePageRequest request) {
        IPage<ConfigRecommendProductRuleEntity> entityIPage = configRecommendProductRuleDao.pageList(request);
        PageResponse<ConfigRecommendProductRuleResponse> productRuleResponsePageResponse = new PageResponse<>();
        productRuleResponsePageResponse.setTotalCount(entityIPage.getTotal());
        List<ConfigRecommendProductRuleEntity> configRecommendProductRuleEntities = entityIPage.getRecords();
        if (!CollectionUtils.isEmpty(configRecommendProductRuleEntities)) {
            List<Integer> storeIds = configRecommendProductRuleEntities.stream().map(ConfigRecommendProductRuleEntity::getStoreId).collect(Collectors.toList());
            List<SaStoreEntity> saStoreEntities = saStoreDao.listByIds(storeIds);
            List<ConfigRecommendProductRuleResponse> configRecommendProductRuleResponses = configRecommendProductRuleEntities.stream().map(c -> {
                ConfigRecommendProductRuleResponse response = new ConfigRecommendProductRuleResponse();
                response.setConfigRecommendProductRuleId(c.getConfigRecommendProductRuleId());
                response.setPlatform(!Objects.equals(c.getStoreId(), NumberConstant.ZERO) ? "-" : c.getPlatform());
                response.setRuleName(c.getRuleName());
                response.setStatusName(Objects.equals(c.getStatus(), NumberConstant.ZERO) ? "启动" : "停用");
                SaStoreEntity saStoreEntity = saStoreEntities.stream().filter(s -> Objects.equals(s.getId(), c.getStoreId())).findFirst().orElse(new SaStoreEntity());
                response.setStoreName(saStoreEntity.getErpStoreName());
                return response;
            }).collect(Collectors.toList());
            productRuleResponsePageResponse.setContent(configRecommendProductRuleResponses);
        }
        return productRuleResponsePageResponse;
    }

    @Override
    @Transactional
    public void deleteBatch(List<Integer> ids) {
        configRecommendProductRuleDao.removeByIds(ids);
        configRecommendProductRuleItemDao.removeByConfigRecommendProductRuleIds(ids);
    }

    @Override
    public ConfigRecommendProductRuleResponse detail(Integer id) {
        ConfigRecommendProductRuleEntity recommendProductRuleEntity = configRecommendProductRuleDao.getById(id);
        ConfigRecommendProductRuleResponse configRecommendProductRuleResponse = new ConfigRecommendProductRuleResponse();
        BeanUtils.copyPropertiesIgnoreNull(recommendProductRuleEntity, configRecommendProductRuleResponse);
        ConfigRecommendProductRuleItemResponse productRuleItemResponse = new ConfigRecommendProductRuleItemResponse();
        SaStoreEntity saStoreEntity = saStoreDao.getById(recommendProductRuleEntity.getStoreId());
        configRecommendProductRuleResponse.setStoreName(Objects.nonNull(saStoreEntity) ? saStoreEntity.getErpStoreName() : "");
        List<ConfigRecommendProductRuleItemEntity> configRecommendProductRuleItemEntities = configRecommendProductRuleItemDao.getByRuleIdIn(Lists.newArrayList(recommendProductRuleEntity.getConfigRecommendProductRuleId()), null);
        buildConfigRecommendProductRuleItem(configRecommendProductRuleItemEntities, productRuleItemResponse);
        configRecommendProductRuleResponse.setRuleDetail(productRuleItemResponse);
        return configRecommendProductRuleResponse;
    }

    private void buildConfigRecommendProductRuleItem(List<ConfigRecommendProductRuleItemEntity> configRecommendProductRuleItemEntities, ConfigRecommendProductRuleItemResponse productRuleItemResponse) {
        RuleStockItemResponse ruleStockItemResponse = new RuleStockItemResponse();
        ReplenishmentResponse replenishmentResponse = new ReplenishmentResponse();
        Map<String, List<ConfigRecommendProductRuleItemEntity>> listMap = configRecommendProductRuleItemEntities.stream().collect(Collectors.groupingBy(ConfigRecommendProductRuleItemEntity::getRuleKey));
        listMap.forEach((p, v) -> {

            if (Objects.equals(p, RuleTypeEnum.PRODUCT.name())) {
                for (ConfigRecommendProductRuleItemEntity recommendProductRuleItem : v) {
                    if (Objects.equals(recommendProductRuleItem.getUseType(), UseTypeEnum.SHELF.name())) {
                        ProductRuleResponse productRuleResponse = JSONUtils.fromJSON(recommendProductRuleItem.getRuleValue(), ProductRuleResponse.class);
                        productRuleItemResponse.setProductTypes(productRuleResponse.getProductTypes());
                        productRuleItemResponse.setSeasonLabelIds(productRuleResponse.getSeasonLabelIds());
                        productRuleItemResponse.setLabelIds(productRuleResponse.getLabelIds());
                        productRuleItemResponse.setCreateDate(productRuleResponse.getCreateDate());
                        productRuleItemResponse.setInfringementNameList(productRuleResponse.getInfringementNameList());
                        productRuleItemResponse.setSpaceIds(productRuleResponse.getSpaceIds());
                        productRuleItemResponse.setFabricType(productRuleResponse.getFabricType());
                        productRuleItemResponse.setFilterLabelIds(productRuleResponse.getFilterLabelIds());
                        productRuleItemResponse.setSpaceStoreBrandList(productRuleResponse.getSpaceStoreBrandList());
                        productRuleItemResponse.setPublishPlatformList(productRuleResponse.getPublishPlatformList());
                    }
                    if (Objects.equals(recommendProductRuleItem.getUseType(), UseTypeEnum.STOCK_UPDATE.name())) {
                        //库存更新配置
                        RuleStockItemResponse stockItemResponse = JSONUtils.fromJSON(recommendProductRuleItem.getRuleValue(), RuleStockItemResponse.class);
                        BeanUtils.copyPropertiesIgnoreNull(stockItemResponse, ruleStockItemResponse);
                    }
                    if (Objects.equals(recommendProductRuleItem.getUseType(), UseTypeEnum.REPLENISHMENT.name())) {
                        //库存更新配置
                        ReplenishmentResponse response = JSONUtils.fromJSON(recommendProductRuleItem.getRuleValue(), ReplenishmentResponse.class);
                        BeanUtils.copyPropertiesIgnoreNull(response, replenishmentResponse);
                        replenishmentResponse.setPrefixNames(WebUtil.jsonListStringToWebString(replenishmentResponse.getPrefixNames()));
                    }
                }

            } else if (Objects.equals(p, RuleTypeEnum.STOCK.name())) {
                for (ConfigRecommendProductRuleItemEntity recommendProductRuleItem : v) {
                    ConfigRecommendProductRuleItemResponse stockRuleResponses = JSONUtils.fromJSON(recommendProductRuleItem.getRuleValue(), ConfigRecommendProductRuleItemResponse.class);
                    if (Objects.equals(recommendProductRuleItem.getUseType(), UseTypeEnum.SHELF.name())) {
                        productRuleItemResponse.setStockRuleResponses(stockRuleResponses.getStockRuleResponses());
                        productRuleItemResponse.setAddSizeStock(stockRuleResponses.getAddSizeStock());
                    }

                    //库存更新
                    if (Objects.nonNull(stockRuleResponses.getRuleStockItem()) && Objects.equals(recommendProductRuleItem.getUseType(), UseTypeEnum.STOCK_UPDATE.name())) {
                        StockUpdateItem stockUpdateItem = new StockUpdateItem();
                        BeanUtils.copyPropertiesIgnoreNull(stockRuleResponses.getRuleStockItem(), stockUpdateItem);
                        BeanUtils.copyPropertiesIgnoreNull(stockUpdateItem, ruleStockItemResponse);
                    }
                }

            } else if (Objects.equals(p, RuleTypeEnum.SALE.name())) {
                for (ConfigRecommendProductRuleItemEntity recommendProductRuleItem : v) {
                    if (Objects.equals(recommendProductRuleItem.getUseType(), UseTypeEnum.SHELF.name())) {
                        ConfigRecommendProductRuleItemResponse productRuleResponse = JSONUtils.fromJSON(recommendProductRuleItem.getRuleValue(), ConfigRecommendProductRuleItemResponse.class);
                        productRuleItemResponse.setDay(productRuleResponse.getDay());
                        productRuleItemResponse.setSaleVol(productRuleResponse.getSaleVol());
                    }

                    if (Objects.equals(recommendProductRuleItem.getUseType(), UseTypeEnum.REPLENISHMENT.name())) {
                        ReplenishmentResponse productRuleResponse = JSONUtils.fromJSON(recommendProductRuleItem.getRuleValue(), ReplenishmentResponse.class);
                        BeanUtils.copyPropertiesIgnoreNull(productRuleResponse, replenishmentResponse);
                    }
                }
            }
            productRuleItemResponse.setRuleStockItem(ruleStockItemResponse);
            productRuleItemResponse.setReplenishmentResponse(replenishmentResponse);
        });
    }

    @Override
    @Transactional
    public void save(ConfigRecommendProductRuleCreateRequest request) {
        adjust(request);
        ConfigRecommendProductRuleEntity entity = new ConfigRecommendProductRuleEntity();
        BeanUtils.copyPropertiesIgnoreNull(request, entity);
        entity.setCreateBy(loginInfoService.getUserName());
        configRecommendProductRuleDao.save(entity);
        saveRuleItem(request.getRuleDetail(), entity.getConfigRecommendProductRuleId());
    }

    @Override
    @Transactional
    public void copy(ConfigRecommendProductRuleCopyRequest request) {
        List<ConfigRecommendProductRuleEntity> targetNewRules = copyRulesByType(request);

        List<ConfigRecommendProductRuleItemEntity> sourceRuleItems = configRecommendProductRuleItemDao.findByRuleId(request.getConfigRecommendProductRuleId());
        if (CollUtil.isEmpty(sourceRuleItems)) return;

        copyRuleItems(targetNewRules, sourceRuleItems);
    }

    private void copyRuleItems(List<ConfigRecommendProductRuleEntity> targetNewRules, List<ConfigRecommendProductRuleItemEntity> sourceRuleItems) {
        List<ConfigRecommendProductRuleItemEntity> newTargetRuleItems = new ArrayList<>();
        targetNewRules.forEach(targetRule -> sourceRuleItems.forEach(item -> {
            ConfigRecommendProductRuleItemEntity newTargetRuleItem = BeanUtil.copyProperties(item, ConfigRecommendProductRuleItemEntity.class, "configRecommendProductRuleItemId", "configRecommendProductRuleId");
            newTargetRuleItem.setConfigRecommendProductRuleId(targetRule.getConfigRecommendProductRuleId());
            newTargetRuleItem.setCreateBy(loginInfoService.getName());
            newTargetRuleItem.setCreateDate(new Date());
            newTargetRuleItem.setUpdateBy(loginInfoService.getName());
            newTargetRuleItem.setUpdateDate(new Date());
            newTargetRuleItems.add(newTargetRuleItem);
        }));
        configRecommendProductRuleItemDao.saveBatch(newTargetRuleItems);
    }

    private @NotNull List<ConfigRecommendProductRuleEntity> copyRulesByType(ConfigRecommendProductRuleCopyRequest request) {
        List<ConfigRecommendProductRuleEntity> targetNewRules = new ArrayList<>();
        if (CollUtil.isNotEmpty(request.getStoreIdList())) {
            List<ConfigRecommendProductRuleEntity> existsRules = configRecommendProductRuleDao.getByStoreIdIn(request.getStoreIdList());
            if (CollUtil.isNotEmpty(existsRules)) {
                List<String> storeNameList = saStoreDao.getStoreListByIds(existsRules.stream().map(ConfigRecommendProductRuleEntity::getStoreId).collect(Collectors.toList())).stream().map(StoreDetailResponse::getErpStoreName).collect(Collectors.toList());
                throw new BusinessServiceException(String.format("店铺存在配置[%s],无法复制", String.join(",", storeNameList)));
            }
            Map<Integer, StoreDetailResponse> storeMap = saStoreDao.getStoreListByIds(request.getStoreIdList()).stream().collect(Collectors.toMap(StoreDetailResponse::getId, Function.identity()));

            request.getStoreIdList().forEach(storeId -> {
                StoreDetailResponse storeDetailResponse = storeMap.get(storeId);
                if (Objects.isNull(storeDetailResponse)) {
                    throw new BusinessServiceException(String.format("店铺id[%s]不存在店铺,请确认", storeId));
                }
                String rulePlatform = SheinRecommendPlatformEnum.of(storeDetailResponse.allPlatform()).getRulePlatform();
                ConfigRecommendProductRuleEntity ruleEntity = new ConfigRecommendProductRuleEntity();
                ruleEntity.setStoreId(storeId);
                ruleEntity.setPlatform(rulePlatform);
                ruleEntity.setRuleName(rulePlatform);
                ruleEntity.setStatus(0);
                ruleEntity.setCreateBy(loginInfoService.getName());
                ruleEntity.setUpdateBy(loginInfoService.getName());
                targetNewRules.add(ruleEntity);
            });
        } else if (CollUtil.isNotEmpty(request.getPlatformList())) {
            List<ConfigRecommendProductRuleEntity> existsRules = configRecommendProductRuleDao.getPlatformRules(request.getPlatformList());
            if (CollUtil.isNotEmpty(existsRules)) {
                throw new BusinessServiceException(String.format("平台存在配置[%s],无法复制", existsRules.stream().map(ConfigRecommendProductRuleEntity::getPlatform).collect(Collectors.joining(","))));
            }
            request.getPlatformList().forEach(platform -> {
                String rulePlatform = SheinRecommendPlatformEnum.of(Collections.singletonList(platform)).getRulePlatform();
                ConfigRecommendProductRuleEntity ruleEntity = new ConfigRecommendProductRuleEntity();
                ruleEntity.setStoreId(0);
                ruleEntity.setPlatform(rulePlatform);
                ruleEntity.setRuleName(rulePlatform);
                ruleEntity.setStatus(0);
                ruleEntity.setCreateBy(loginInfoService.getName());
                ruleEntity.setUpdateBy(loginInfoService.getName());
                targetNewRules.add(ruleEntity);
            });
        } else {
            throw new BusinessServiceException("复制必须选择店铺或者平台");
        }
        configRecommendProductRuleDao.saveBatch(targetNewRules);
        return targetNewRules;
    }

    @Override
    @Transactional
    public void update(ConfigRecommendProductRuleCreateRequest request) {
        adjust(request);
        ConfigRecommendProductRuleEntity entity = configRecommendProductRuleDao.getById(request.getConfigRecommendProductRuleId());
        entity.setUpdateBy(loginInfoService.getUserName());
        entity.setStoreId(request.getStoreId());
        entity.setPlatform(request.getPlatform());
        entity.setRuleName(request.getRuleName());
        entity.setStatus(request.getStatus());
        configRecommendProductRuleDao.updateById(entity);
        configRecommendProductRuleItemDao.removeByConfigRecommendProductRuleIds(Lists.newArrayList(entity.getConfigRecommendProductRuleId()));
        saveRuleItem(request.getRuleDetail(), entity.getConfigRecommendProductRuleId());

    }


    private void adjust(ConfigRecommendProductRuleCreateRequest request) {
        if (Objects.nonNull(request.getStoreId())) {
            SaStoreEntity saStoreEntity = saStoreDao.getById(request.getStoreId());
            request.setPlatform(SheinRecommendPlatformEnum.of(saStoreEntity.allPlatform()).getRulePlatform());
        }
        ConfigRecommendProductRuleEntity configRecommendProductRuleEntity = configRecommendProductRuleDao.getByPlatformAndStoreId(request.getPlatform(), Objects.isNull(request.getStoreId()) ? NumberConstant.ZERO : request.getStoreId());
        if (Objects.nonNull(configRecommendProductRuleEntity) && !Objects.equals(configRecommendProductRuleEntity.getConfigRecommendProductRuleId(), request.getConfigRecommendProductRuleId())) {
            SaStoreEntity saStoreEntity = saStoreDao.getById(configRecommendProductRuleEntity.getStoreId());
            throw new BusinessServiceException(String.format("%s%s", Objects.nonNull(saStoreEntity) ? saStoreEntity.getErpStoreName() : "平台", "规则已经存在,请勿重复添加!"));
        }
        boolean hasDuplicates = request.getRuleDetail().getStockRuleResponses().stream()
                .anyMatch(obj -> request.getRuleDetail().getStockRuleResponses().stream().filter(o -> o != obj)
                        .anyMatch(o -> Objects.equals(o.getSize(), obj.getSize()) && Objects.equals(o.getCategoryId(), obj.getCategoryId())));
        if (hasDuplicates) {
            throw new BusinessServiceException("相同品类,不能配置相关的尺码");
        }
    }

    private void saveRuleItem(ConfigRecommendProductRuleItemResponse configRecommendProductRuleItemResponse, Integer configRecommendProductRuleId) {
        buildProduct(configRecommendProductRuleItemResponse, configRecommendProductRuleId);
        buildSale(configRecommendProductRuleItemResponse, configRecommendProductRuleId);
        buildStock(configRecommendProductRuleItemResponse, configRecommendProductRuleId);
    }

    private void buildProduct(ConfigRecommendProductRuleItemResponse configRecommendProductRuleItemResponse, Integer configRecommendProductRuleId) {
        saveConfigRecommendProductRuleItem(buildShelfProduct(configRecommendProductRuleItemResponse), configRecommendProductRuleId, UseTypeEnum.SHELF.name(), RuleTypeEnum.PRODUCT.name());
        saveConfigRecommendProductRuleItem(buildReplenishmentProduct(configRecommendProductRuleItemResponse), configRecommendProductRuleId, UseTypeEnum.REPLENISHMENT.name(), RuleTypeEnum.PRODUCT.name());
        saveConfigRecommendProductRuleItem(buildStockUpdateProduct(configRecommendProductRuleItemResponse), configRecommendProductRuleId, UseTypeEnum.STOCK_UPDATE.name(), RuleTypeEnum.PRODUCT.name());
    }

    private void buildSale(ConfigRecommendProductRuleItemResponse configRecommendProductRuleItemResponse, Integer configRecommendProductRuleId) {
        saveConfigRecommendProductRuleItem(buildShelfSale(configRecommendProductRuleItemResponse), configRecommendProductRuleId, UseTypeEnum.SHELF.name(), RuleTypeEnum.SALE.name());
        saveConfigRecommendProductRuleItem(buildReplenishmentSale(configRecommendProductRuleItemResponse), configRecommendProductRuleId, UseTypeEnum.REPLENISHMENT.name(), RuleTypeEnum.SALE.name());

    }

    private void buildStock(ConfigRecommendProductRuleItemResponse configRecommendProductRuleItemResponse, Integer configRecommendProductRuleId) {
        saveConfigRecommendProductRuleItem(buildShelfStock(configRecommendProductRuleItemResponse), configRecommendProductRuleId, UseTypeEnum.SHELF.name(), RuleTypeEnum.STOCK.name());
        saveConfigRecommendProductRuleItem(buildStockUpdateStock(configRecommendProductRuleItemResponse), configRecommendProductRuleId, UseTypeEnum.STOCK_UPDATE.name(), RuleTypeEnum.STOCK.name());
    }


    /**
     * 判断规则是否合法
     *
     * @param stockFormula
     */
    private void adjuctFormula(String stockFormula) {
        String formula = String.format("%s%s", "a", stockFormula);
        String temp = formula.replace('a', '2');
        String result = BigDecimalUtil.dealCal(temp);
        if ("error".equals(result) || BigDecimalUtil.isInteger(temp) && formula.contains("a") && !"a".equals(formula)) {
            throw new InvalidRequestException(stockFormula + "不合法");
        }
    }


    void saveConfigRecommendProductRuleItem(Map<String, Object> map, Integer configRecommendProductRuleId, String userType, String ruleKey) {
        if (CollectionUtils.isEmpty(map)) {
            return;
        }
        ConfigRecommendProductRuleItemEntity configRecommendProductRuleItemEntity = new ConfigRecommendProductRuleItemEntity();
        configRecommendProductRuleItemEntity.setRuleValue(JsonMapper.toJson(map));
        configRecommendProductRuleItemEntity.setRuleKey(ruleKey);
        configRecommendProductRuleItemEntity.setUseType(userType);
        configRecommendProductRuleItemEntity.setConfigRecommendProductRuleId(configRecommendProductRuleId);
        configRecommendProductRuleItemDao.save(configRecommendProductRuleItemEntity);

    }

    /**
     * 补货规则
     *
     * @param productRuleItemResponse
     */
    private Map<String, Object> buildReplenishmentProduct(ConfigRecommendProductRuleItemResponse productRuleItemResponse) {
        Map<String, Object> map = new LinkedHashMap<>();
        if (Objects.nonNull(productRuleItemResponse.getReplenishmentResponse())) {
            ReplenishmentResponse replenishmentResponse = productRuleItemResponse.getReplenishmentResponse();
            if (!CollectionUtils.isEmpty(replenishmentResponse.getNoReplenishGoodsLevelNames()) && StringUtils.hasText(replenishmentResponse.getNoReplenishGoodsLevelNameBetween())) {
                map.put("noReplenishGoodsLevelNames", replenishmentResponse.getNoReplenishGoodsLevelNames());
                map.put("noReplenishGoodsLevelNameBetween", replenishmentResponse.getNoReplenishGoodsLevelNameBetween());
            }
            if (StringUtils.hasText(replenishmentResponse.getPrefixNames()) && StringUtils.hasText(replenishmentResponse.getPrefixBetween())) {
                map.put("prefixNames", WebUtil.webStringToJsonListString(replenishmentResponse.getPrefixNames()));
                map.put("prefixBetween", replenishmentResponse.getPrefixBetween());
            }
        }
        return map;
    }

    /**
     * 库存更新规则
     *
     * @param productRuleItemResponse
     */
    private Map<String, Object> buildStockUpdateProduct(ConfigRecommendProductRuleItemResponse productRuleItemResponse) {
        Map<String, Object> map = new HashMap<>();
        if (Objects.nonNull(productRuleItemResponse.getRuleStockItem())) {
            RuleStockItemResponse ruleStockItemResponse = productRuleItemResponse.getRuleStockItem();
            if (StringUtils.hasText(ruleStockItemResponse.getShelfDayBetween()) && Objects.nonNull(ruleStockItemResponse.getShelfDay())) {
                map.put("shelfDayBetween", ruleStockItemResponse.getShelfDayBetween());
                map.put("shelfDay", ruleStockItemResponse.getShelfDay());
            }
            if (!CollectionUtils.isEmpty(ruleStockItemResponse.getGoodsLevelNames()) && Objects.nonNull(ruleStockItemResponse.getGoodsLevelNameBetween())) {
                map.put("goodsLevelNames", ruleStockItemResponse.getGoodsLevelNames());
                map.put("goodsLevelNameBetween", ruleStockItemResponse.getGoodsLevelNameBetween());
            }
            if (StringUtils.hasText(ruleStockItemResponse.getPrefix()) && Objects.nonNull(ruleStockItemResponse.getPrefixBetween())) {
                map.put("prefix", ruleStockItemResponse.getPrefix());
                map.put("prefixBetween", ruleStockItemResponse.getPrefixBetween());
            }
        }
        return map;
    }


    /**
     * 推荐规则
     *
     * @param productRuleItemResponse
     */
    private Map<String, Object> buildShelfProduct(ConfigRecommendProductRuleItemResponse productRuleItemResponse) {
        Map<String, Object> map = new HashMap();
        if (!CollectionUtils.isEmpty(productRuleItemResponse.getProductTypes())) {
            map.put("productTypes", productRuleItemResponse.getProductTypes());
        }
        if (!CollectionUtils.isEmpty(productRuleItemResponse.getLabelIds())) {
            map.put("labelIds", productRuleItemResponse.getLabelIds());
        }
        if (StringUtils.hasText(productRuleItemResponse.getCreateDate())) {
            map.put("createDate", productRuleItemResponse.getCreateDate());
        }
        if (StringUtils.hasText(productRuleItemResponse.getFabricType())) {
            map.put("fabricType", productRuleItemResponse.getFabricType());
        }
        if (CollUtil.isNotEmpty(productRuleItemResponse.getInfringementNameList())) {
            map.put("infringementNameList", productRuleItemResponse.getInfringementNameList());
        }
        if (CollUtil.isNotEmpty(productRuleItemResponse.getSpaceIds())) {
            map.put("spaceIds", productRuleItemResponse.getSpaceIds());
        }
        if (CollUtil.isNotEmpty(productRuleItemResponse.getSpaceIds())) {
            map.put("spaceStoreBrandList", productRuleItemResponse.getSpaceStoreBrandList());
        }
        if (CollUtil.isNotEmpty(productRuleItemResponse.getFilterLabelIds())) {
            map.put("filterLabelIds", productRuleItemResponse.getFilterLabelIds());
        }
        if (!CollectionUtils.isEmpty(productRuleItemResponse.getSeasonLabelIds())) {
            map.put("seasonLabelIds", productRuleItemResponse.getSeasonLabelIds());
        }
        if (!CollectionUtils.isEmpty(productRuleItemResponse.getPublishPlatformList())) {
            map.put("publishPlatformList", productRuleItemResponse.getPublishPlatformList());
        }
        return map;
    }


    /**
     * 推荐规则、销量
     *
     * @param productRuleItemResponse
     */
    private Map<String, Object> buildShelfSale(ConfigRecommendProductRuleItemResponse productRuleItemResponse) {
        Map<String, Object> map = new HashMap();
        if (Objects.nonNull(productRuleItemResponse.getDay()) && Objects.nonNull(productRuleItemResponse.getSaleVol())
                && !Objects.equals(productRuleItemResponse.getDay(), NumberConstant.ZERO)) {
            map.put("day", productRuleItemResponse.getDay());
            map.put("saleVol", productRuleItemResponse.getSaleVol());
        }
        return map;
    }


    /**
     * 补货规则、销量
     *
     * @param productRuleItemResponse
     */
    private Map<String, Object> buildReplenishmentSale(ConfigRecommendProductRuleItemResponse productRuleItemResponse) {
        Map<String, Object> map = new LinkedHashMap<>();
        if (Objects.isNull(productRuleItemResponse.getReplenishmentResponse())) {
            return map;
        }
        ReplenishmentResponse replenishmentResponse = productRuleItemResponse.getReplenishmentResponse();
        if (!CollectionUtils.isEmpty(replenishmentResponse.getNewSizeGroups())) {
            map.put("newSizeGroups", replenishmentResponse.getNewSizeGroups());
        }
        if (Objects.nonNull(replenishmentResponse.getAddColorReferenceSaleDay())) {
            map.put("addColorReferenceSaleDay", replenishmentResponse.getAddColorReferenceSaleDay());
        }
        if (!CollectionUtils.isEmpty(replenishmentResponse.getAddColorSizeGroups())) {
            map.put("addColorSizeGroups", replenishmentResponse.getAddColorSizeGroups());
        }
        if (Objects.nonNull(replenishmentResponse.getReorderLastMonthSale()) && Objects.nonNull(replenishmentResponse.getReorderThisMonthSale()) && Objects.nonNull(replenishmentResponse.getReorderThisOne())) {
            map.put("reorderLastMonthSale", replenishmentResponse.getReorderLastMonthSale());
            map.put("reorderThisMonthSale", replenishmentResponse.getReorderThisMonthSale());
            map.put("reorderThisOne", replenishmentResponse.getReorderThisOne());
        }
        if (Objects.nonNull(replenishmentResponse.getReorderReferenceSaleDay())) {
            map.put("reorderReferenceSaleDay", replenishmentResponse.getReorderReferenceSaleDay());
        }
        if (Objects.nonNull(replenishmentResponse.getReorderVmi())) {
            map.put("reorderVmi", replenishmentResponse.getReorderVmi());
        }
        if (Objects.nonNull(replenishmentResponse.getReorderMonthIncrease())) {
            map.put("reorderMonthIncrease", replenishmentResponse.getReorderMonthIncrease());
        }
        if (Objects.nonNull(replenishmentResponse.getAddSizeReferenceSaleDay())) {
            map.put("addSizeReferenceSaleDay", replenishmentResponse.getAddSizeReferenceSaleDay());
        }
        if (!CollectionUtils.isEmpty(replenishmentResponse.getAddSizeReplenishMents())) {
            map.put("addSizeReplenishMents", replenishmentResponse.getAddSizeReplenishMents());
        }
        return map;
    }

    /**
     * 推荐规则、库存
     *
     * @param productRuleItemResponse
     */
    private Map<String, Object> buildShelfStock(ConfigRecommendProductRuleItemResponse productRuleItemResponse) {
        Map<String, Object> map = new LinkedHashMap<>();
        if (!CollectionUtils.isEmpty(productRuleItemResponse.getStockRuleResponses())) {
            map.put("stockRuleResponses", productRuleItemResponse.getStockRuleResponses());
        }
        if (Objects.nonNull(productRuleItemResponse.getAddSizeStock())) {
            map.put("addSizeStock", productRuleItemResponse.getAddSizeStock());
        }
        return map;
    }


    /**
     * 推荐规则、库存
     *
     * @param productRuleItemResponse
     */
    private Map<String, Object> buildStockUpdateStock(ConfigRecommendProductRuleItemResponse productRuleItemResponse) {
        Map<String, Object> map = new HashMap<>();
        if (Objects.nonNull(productRuleItemResponse.getRuleStockItem())) {
            RuleStockItemResponse ruleStockItemResponse = productRuleItemResponse.getRuleStockItem();
            Map<String, Object> hashMap = new LinkedHashMap<>();
            if (StringUtils.hasText(ruleStockItemResponse.getPlatformStockBetween()) && Objects.nonNull(ruleStockItemResponse.getPlatformStock())) {
                hashMap.put("platformStock", ruleStockItemResponse.getPlatformStock());
                hashMap.put("platformStockBetween", ruleStockItemResponse.getPlatformStockBetween());
            }
            if (StringUtils.hasText(ruleStockItemResponse.getPlatformSkuPrefix()) && StringUtils.hasText(ruleStockItemResponse.getPlatformSkuPrefixBetween())) {
                hashMap.put("platformSkuPrefix", ruleStockItemResponse.getPlatformSkuPrefix());
                hashMap.put("platformSkuPrefixBetween", ruleStockItemResponse.getPlatformSkuPrefixBetween());
            }
            if (StringUtils.hasText(ruleStockItemResponse.getStockFormula())) {
                adjuctFormula(ruleStockItemResponse.getStockFormula());
                hashMap.put("stockFormula", ruleStockItemResponse.getStockFormula());
            }
            if (StringUtils.hasText(ruleStockItemResponse.getRestrictOneBetween()) && Objects.nonNull(ruleStockItemResponse.getRestrictOne())) {
                hashMap.put("restrictOneBetween", ruleStockItemResponse.getRestrictOneBetween());
                hashMap.put("restrictOne", ruleStockItemResponse.getRestrictOne());
            }
            if (StringUtils.hasText(ruleStockItemResponse.getRestrictTwoBetween()) && Objects.nonNull(ruleStockItemResponse.getRestrictTwo())) {
                hashMap.put("restrictTwoBetween", ruleStockItemResponse.getRestrictTwoBetween());
                hashMap.put("restrictTwo", ruleStockItemResponse.getRestrictTwo());
            }
            if (!CollectionUtils.isEmpty(hashMap)) {
                map.put("ruleStockItem", hashMap);
            }
        }
        return map;
    }


    @Override
    @Transactional
    public void updateOldDate() {
        List<ConfigRecommendProductRuleItemEntity> configRecommendProductRuleItemEntities = configRecommendProductRuleItemDao.list();
        for (ConfigRecommendProductRuleItemEntity entity : configRecommendProductRuleItemEntities) {
            buildStockData(entity);
            buildProductData(entity);
            buildSaleData(entity);
            configRecommendProductRuleItemDao.removeById(entity.getConfigRecommendProductRuleItemId());
        }

    }

    void buildSaleData(ConfigRecommendProductRuleItemEntity entity) {
        JSONObject object = JSONUtil.parseObj(entity.getRuleValue());
        if (Objects.nonNull(object.get("day")) || Objects.nonNull(object.get("saleVol"))) {
            JSONObject j = new JSONObject();
            if (Objects.nonNull(object.get("day"))) {
                j.put("day", object.get("day"));
            }
            if (Objects.nonNull(object.get("saleVol"))) {
                j.put("saleVol", object.get("saleVol"));
            }
            ConfigRecommendProductRuleItemEntity stockRuleResponses = new ConfigRecommendProductRuleItemEntity();
            stockRuleResponses.setRuleKey(entity.getRuleKey());
            stockRuleResponses.setRuleValue(j.toString());
            stockRuleResponses.setUseType(UseTypeEnum.SHELF.name());
            stockRuleResponses.setConfigRecommendProductRuleId(entity.getConfigRecommendProductRuleId());
            configRecommendProductRuleItemDao.save(stockRuleResponses);
        }
    }

    void buildStockData(ConfigRecommendProductRuleItemEntity entity) {
        JSONObject object = JSONUtil.parseObj(entity.getRuleValue());
        if (Objects.nonNull(object.get("stockRuleResponses")) || Objects.nonNull(object.get("addSizeStock"))) {
            JSONObject j = new JSONObject();
            if (Objects.nonNull(object.get("stockRuleResponses"))) {
                j.put("stockRuleResponses", object.get("stockRuleResponses"));
            }
            if (Objects.nonNull(object.get("addSizeStock"))) {
                j.put("addSizeStock", object.get("addSizeStock"));
            }
            ConfigRecommendProductRuleItemEntity stockRuleResponses = new ConfigRecommendProductRuleItemEntity();
            stockRuleResponses.setRuleKey(entity.getRuleKey());
            stockRuleResponses.setRuleValue(j.toString());
            stockRuleResponses.setUseType(UseTypeEnum.SHELF.name());
            stockRuleResponses.setConfigRecommendProductRuleId(entity.getConfigRecommendProductRuleId());
            configRecommendProductRuleItemDao.save(stockRuleResponses);
        }
        if (Objects.nonNull(object.get("ruleStockItem"))) {
            JSONObject j = new JSONObject();
            j.put("ruleStockItem", object.get("ruleStockItem"));
            ConfigRecommendProductRuleItemEntity stockRuleResponses = new ConfigRecommendProductRuleItemEntity();
            stockRuleResponses.setRuleKey(entity.getRuleKey());
            stockRuleResponses.setRuleValue(j.toString());
            stockRuleResponses.setUseType(UseTypeEnum.STOCK_UPDATE.name());
            stockRuleResponses.setConfigRecommendProductRuleId(entity.getConfigRecommendProductRuleId());
            configRecommendProductRuleItemDao.save(stockRuleResponses);

        }
    }

    void buildProductData(ConfigRecommendProductRuleItemEntity entity) {
        JSONObject object = JSONUtil.parseObj(entity.getRuleValue());
        if (Objects.nonNull(object.get("productTypes")) || Objects.nonNull(object.get("createDate"))) {
            JSONObject j = new JSONObject();
            if (Objects.nonNull(object.get("productTypes"))) {
                j.put("productTypes", object.get("productTypes"));
            }
            if (Objects.nonNull(object.get("createDate"))) {
                j.put("createDate", object.get("createDate"));
            }
            ConfigRecommendProductRuleItemEntity stockRuleResponses = new ConfigRecommendProductRuleItemEntity();
            stockRuleResponses.setRuleKey(entity.getRuleKey());
            stockRuleResponses.setRuleValue(j.toString());
            stockRuleResponses.setUseType(UseTypeEnum.SHELF.name());
            stockRuleResponses.setConfigRecommendProductRuleId(entity.getConfigRecommendProductRuleId());
            configRecommendProductRuleItemDao.save(stockRuleResponses);
        }

        if (Objects.nonNull(object.get("goodsLevelNameBetween")) || Objects.nonNull(object.get("prefix"))
                || Objects.nonNull(object.get("shelfDay")) || Objects.nonNull(object.get("shelfDayBetween"))
                || Objects.nonNull(object.get("goodsLevelNames")) || Objects.nonNull(object.get("prefixBetween"))) {
            JSONObject j = new JSONObject();
            if (Objects.nonNull(object.get("goodsLevelNameBetween"))) {
                j.put("goodsLevelNameBetween", object.get("goodsLevelNameBetween"));
            }
            if (Objects.nonNull(object.get("prefix"))) {
                j.put("prefix", object.get("prefix"));
            }
            if (Objects.nonNull(object.get("shelfDay"))) {
                j.put("shelfDay", object.get("shelfDay"));
            }
            if (Objects.nonNull(object.get("shelfDayBetween"))) {
                j.put("shelfDayBetween", object.get("shelfDayBetween"));
            }
            if (Objects.nonNull(object.get("goodsLevelNames"))) {
                j.put("goodsLevelNames", object.get("goodsLevelNames"));
            }
            if (Objects.nonNull(object.get("prefixBetween"))) {
                j.put("prefixBetween", object.get("prefixBetween"));
            }
            ConfigRecommendProductRuleItemEntity stockRuleResponses = new ConfigRecommendProductRuleItemEntity();
            stockRuleResponses.setRuleKey(entity.getRuleKey());
            stockRuleResponses.setRuleValue(j.toString());
            stockRuleResponses.setUseType(UseTypeEnum.STOCK_UPDATE.name());
            stockRuleResponses.setConfigRecommendProductRuleId(entity.getConfigRecommendProductRuleId());
            configRecommendProductRuleItemDao.save(stockRuleResponses);

        }


    }

    @Override
    public boolean checkBrandSpilt(ConfigRecommendProductRuleCheckBrandSpiltRequest request) {
        BdOrderRuleEntity orderRule = bdOrderRuleService.findRemoveBrandTopByStoreId(request.getStoreId());
        if (Objects.nonNull(orderRule) && orderRule.getBdOrderRuleId() > 0) {
            return false;
        }
        List<ErpSpaceInfoResponse.ErpSpaceInfo> erpSpaceInfos = erpApiService.getErpSpaceInfoList().stream().filter(space -> request.getSpaceIdList().contains(space.getSpaceId())).collect(Collectors.toList());
        return erpSpaceInfos.stream().anyMatch(space -> "BrandSpace".equalsIgnoreCase(space.getBrandType()));
    }

    @Override
    public List<SelectModel> getAmazonStoreWithBrand() {
        List<AmazonStoreListResponse> allAmazonStore = saStoreDao.getAllAmazonStore();
        if (CollUtil.isEmpty(allAmazonStore)) {
            return Collections.emptyList();
        }
        List<Integer> storeIds = allAmazonStore.stream().map(AmazonStoreListResponse::getId).collect(Collectors.toList());
        List<SelectModel> selectModels = new ArrayList<>();
        Map<Integer, List<BdBrandStoreEntity>> storeBrandGroup = bdBrandStoreService.getByStoreIds(storeIds).stream().filter(bdBrandStore -> bdBrandStore.getIsOpenPurchaseBrand() > 0).collect(Collectors.groupingBy(BdBrandStoreEntity::getStoreId));

        allAmazonStore.forEach(store -> {
            List<BdBrandStoreEntity> bdBrandStoreEntities = storeBrandGroup.get(store.getId());
            if (CollUtil.isEmpty(bdBrandStoreEntities)) {
                SelectModel selectModel = new SelectModel(store.getId().toString(), store.getId().toString(), store.getStoreName());
                selectModels.add(selectModel);
                return;
            }

            if (bdBrandStoreEntities.stream().anyMatch(bdBrandStore -> bdBrandStore.getIsOpenPurchaseBrand() == 2)) {
                SelectModel selectModel = new SelectModel(store.getId().toString(), store.getId().toString(), store.getStoreName());
                selectModels.add(selectModel);
            }

            bdBrandStoreEntities.forEach(bdBrandStoreEntity -> {
                String id = String.format("%s-%s", store.getId(), bdBrandStoreEntity.getBrandId());
                String label = String.format("%s(%s)", store.getStoreName(), bdBrandStoreEntity.getBrandName());
                SelectModel brandSelect = new SelectModel(id, id, label);
                selectModels.add(brandSelect);
            });
        });

        return selectModels;
    }
}
