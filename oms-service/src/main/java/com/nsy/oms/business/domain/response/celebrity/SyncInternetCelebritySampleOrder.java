package com.nsy.oms.business.domain.response.celebrity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: linCheng
 * @create: 2024-09-02 10:30
 **/
@Data
public class SyncInternetCelebritySampleOrder {

    /**
     * 店铺id
     */
    private Integer storeId;

    /**
     * 发货店铺id
     */
//    private Integer deliveryStoreId;

    /**
     * 店铺名
     */
    private String storeName;
    /**
     * 发货店铺id
     */
    private Integer sampleStoreId;

    /**
     * 店铺名
     */
    private String sampleStoreName;

    /**
     * 平台原始订单号
     */
//    private String platformOriginalOrderNo;

    /**
     * 平台订单号
     */
    private String platformOrderNo;

    /**
     * 网红id
     */
    private Integer internetCelebrityId;

    /**
     * 网红昵称
     */
    private String internetCelebrityNickname;

    /**
     * 网红部门id(网红负责人部门的一级部门ID)
     */
    private Integer internetCelebrityDeptId;

    /**
     * 网红部门(网红负责人部门的一级部门)
     */
    private String internetCelebrityDeptName;

    /**
     * 网红平台
     */
    private String internetCelebrityPlatform;

    /**
     * 网红负责人编号
     */
    private String ownerCode;

    /**
     * 网红负责人名称
     */
    private String ownerName;

    /**
     * 平台包裹id
     */
//    private String platformPackageId;

    /**
     * 包裹状态
     */
//    private String packageStatus;

    /**
     * 物流单号
     */
//    private String trackingNumber;

    /**
     * 订单创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date orderCreateDate;

    /**
     * 订单发货时间
     */
//    @JsonFormat(pattern = "yyyy-MM-dd")
//    @DateTimeFormat(pattern = "yyyy-MM-dd")
//    private Date orderDeliveryDate;

//    /**
//     * 订单妥投时间
//     */
//    @JsonFormat(pattern = "yyyy-MM-dd")
//    @DateTimeFormat(pattern = "yyyy-MM-dd")
//    private Date orderCompromiseDate;

    /**
     * 订单类型(erp)
     */
//    private String orderType;

    /**
     * 网红负责人id
     */
    private Integer ownerUserId;

    /**
     * 网红负责人部门id
     */
    private Integer ownerDeptId;

    /**
     * 网红创建人id
     */
    private Integer userId;

    /**
     * 网红创建人部门id
     */
    private Integer deptId;

    /**
     * 订单买家昵称
     */
    private String buyerNick;

    /**
     * 地区
     */
    private String location;

    /**
     * 创建人
     */
    private String createBy;

    private String buyerUid;
    /**
     * 明细
     */
    private List<SyncInternetCelebritySampleOrderItem> syncInternetCelebritySampleOrderItems;

}
