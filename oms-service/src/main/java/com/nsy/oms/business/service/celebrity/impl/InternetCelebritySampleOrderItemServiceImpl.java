package com.nsy.oms.business.service.celebrity.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.privilege.LoginInfoService;
import com.nsy.oms.business.domain.response.celebrity.SyncInternetCelebritySampleOrderItem;
import com.nsy.oms.business.service.celebrity.IInternetCelebritySampleOrderItemPostService;
import com.nsy.oms.business.service.celebrity.IInternetCelebritySampleOrderItemService;
import com.nsy.oms.enumstable.InternetCelebritySampleOrderDeliveryTypeEnum;
import com.nsy.oms.enumstable.InternetCelebritySampleOrderTrackingSyncStatusEnum;
import com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderItemEntity;
import com.nsy.oms.repository.sql.mapper.celebrity.InternetCelebritySampleOrderItemMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 网红样衣订单明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-29
 */
@Service
public class InternetCelebritySampleOrderItemServiceImpl extends ServiceImpl<InternetCelebritySampleOrderItemMapper, InternetCelebritySampleOrderItemEntity> implements IInternetCelebritySampleOrderItemService {

    @Autowired
    private LoginInfoService loginInfoService;

    @Autowired
    private IInternetCelebritySampleOrderItemPostService internetCelebritySampleOrderItemPostService;


    @Override
    public List<InternetCelebritySampleOrderItemEntity> getList(Integer internetCelebritySampleOrderId) {
        LambdaQueryWrapper<InternetCelebritySampleOrderItemEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(InternetCelebritySampleOrderItemEntity::getInternetCelebritySampleOrderId, internetCelebritySampleOrderId);
        return this.list(lambdaQueryWrapper);
    }

    @Override
    public void syncInternetCelebritySampleOrderItem(Integer internetCelebritySampleOrderId, List<SyncInternetCelebritySampleOrderItem> syncInternetCelebritySampleOrderItems) {
        List<InternetCelebritySampleOrderItemEntity> internetCelebritySampleOrderItemEntities = this.list(new LambdaQueryWrapper<InternetCelebritySampleOrderItemEntity>().eq(InternetCelebritySampleOrderItemEntity::getInternetCelebritySampleOrderId, internetCelebritySampleOrderId));
        List<InternetCelebritySampleOrderItemEntity> internetCelebritySampleOrderItemEntityList = new ArrayList<>();

        syncInternetCelebritySampleOrderItems.forEach(syncInternetCelebritySampleOrderItem -> {
            InternetCelebritySampleOrderItemEntity internetCelebritySampleOrderItemEntity = internetCelebritySampleOrderItemEntities.stream().filter(t -> t.getSku().equals(syncInternetCelebritySampleOrderItem.getSku())
                    && t.getTrackingNumber().equals(syncInternetCelebritySampleOrderItem.getTrackingNumber())).findFirst().orElse(null);
            if (Objects.isNull(internetCelebritySampleOrderItemEntity)) {
                internetCelebritySampleOrderItemEntity = internetCelebritySampleOrderItemEntities.stream().filter(t -> t.getSku().equals(syncInternetCelebritySampleOrderItem.getSku())
                        && StringUtils.isBlank(t.getTrackingNumber())).findFirst().orElse(null);
            }
            if (Objects.isNull(internetCelebritySampleOrderItemEntity)) {
                internetCelebritySampleOrderItemEntity = new InternetCelebritySampleOrderItemEntity();
            }
            BeanUtils.copyProperties(syncInternetCelebritySampleOrderItem, internetCelebritySampleOrderItemEntity);
            internetCelebritySampleOrderItemEntity.setInternetCelebritySampleOrderId(internetCelebritySampleOrderId);
            internetCelebritySampleOrderItemEntity.setUpdateDate(new Date());
            internetCelebritySampleOrderItemEntity.setUpdateBy(StringUtils.isEmpty(loginInfoService.getName()) ? this.getClass().getSimpleName() : loginInfoService.getName());
            internetCelebritySampleOrderItemEntity.setSellerProductId(syncInternetCelebritySampleOrderItem.getSellerProductId());
            internetCelebritySampleOrderItemEntity.setSellerSkuId(syncInternetCelebritySampleOrderItem.getSellerSkuId());
            if (Objects.isNull(internetCelebritySampleOrderItemEntity.getInternetCelebritySampleOrderItemId())) {
                internetCelebritySampleOrderItemEntity.setCreateDate(new Date());
                internetCelebritySampleOrderItemEntity.setCreateBy(StringUtils.isEmpty(loginInfoService.getName()) ? this.getClass().getSimpleName() : loginInfoService.getName());
            }
            internetCelebritySampleOrderItemEntityList.add(internetCelebritySampleOrderItemEntity);
        });
        this.saveOrUpdateBatch(internetCelebritySampleOrderItemEntityList);
    }


    @Override
    public List<InternetCelebritySampleOrderItemEntity> listByIdList(List<Integer> orderItemIds) {
        if (CollectionUtils.isEmpty(orderItemIds)) {
            return Collections.emptyList();
        }
        return this.listByIds(orderItemIds);
    }

    @Override
    public void syncInternetCelebritySampleOrderItemByImport(Integer internetCelebritySampleOrderId, String platformOrderNo, List<SyncInternetCelebritySampleOrderItem> syncInternetCelebritySampleOrderItems) {

        List<InternetCelebritySampleOrderItemEntity> internetCelebritySampleOrderItemEntities = this.list(new LambdaQueryWrapper<InternetCelebritySampleOrderItemEntity>().eq(InternetCelebritySampleOrderItemEntity::getInternetCelebritySampleOrderId, internetCelebritySampleOrderId));

        syncInternetCelebritySampleOrderItems.forEach(syncInternetCelebritySampleOrderItem -> {
            if (!platformOrderNo.startsWith(InternetCelebritySampleOrderDeliveryTypeEnum.NOT_ORDER.getDescription()) && StringUtils.isEmpty(syncInternetCelebritySampleOrderItem.getSku())) {
                return;
            }
            InternetCelebritySampleOrderItemEntity internetCelebritySampleOrderItemEntity = internetCelebritySampleOrderItemEntities.stream().filter(t -> t.getSku().equals(syncInternetCelebritySampleOrderItem.getSku())
                    && t.getTrackingNumber().equals(syncInternetCelebritySampleOrderItem.getTrackingNumber())).findFirst().orElse(new InternetCelebritySampleOrderItemEntity());
            if (Objects.isNull(internetCelebritySampleOrderItemEntity)) {
                internetCelebritySampleOrderItemEntity = internetCelebritySampleOrderItemEntities.stream().filter(t -> t.getSku().equals(syncInternetCelebritySampleOrderItem.getSku())).findFirst().orElse(null);
            }
            if (Objects.isNull(internetCelebritySampleOrderItemEntity)) {
                internetCelebritySampleOrderItemEntity = new InternetCelebritySampleOrderItemEntity();
            }
            BeanUtils.copyProperties(syncInternetCelebritySampleOrderItem, internetCelebritySampleOrderItemEntity);
            internetCelebritySampleOrderItemEntity.setInternetCelebritySampleOrderId(internetCelebritySampleOrderId);
            internetCelebritySampleOrderItemEntity.setUpdateDate(new Date());
            internetCelebritySampleOrderItemEntity.setUpdateBy(StringUtils.isEmpty(loginInfoService.getName()) ? this.getClass().getSimpleName() : loginInfoService.getName());
            if (Objects.isNull(internetCelebritySampleOrderItemEntity.getInternetCelebritySampleOrderItemId())) {
                internetCelebritySampleOrderItemEntity.setCreateDate(new Date());
                internetCelebritySampleOrderItemEntity.setCreateBy(StringUtils.isEmpty(loginInfoService.getName()) ? this.getClass().getSimpleName() : loginInfoService.getName());
            }
            this.saveOrUpdate(internetCelebritySampleOrderItemEntity);
            internetCelebritySampleOrderItemPostService.syncInternetCelebritySampleOrderItemPostByImport(internetCelebritySampleOrderItemEntity.getInternetCelebritySampleOrderItemId(), syncInternetCelebritySampleOrderItem.getSyncInternetCelebritySampleOrderItemPosts());
        });
    }

    @Override
    public List<InternetCelebritySampleOrderItemEntity> getListWhenDeliveryDateIsNull() {
        LambdaQueryWrapper<InternetCelebritySampleOrderItemEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.isNull(InternetCelebritySampleOrderItemEntity::getOrderCompromiseDate);
        lambdaQueryWrapper.ne(InternetCelebritySampleOrderItemEntity::getTrackingSyncStatus, InternetCelebritySampleOrderTrackingSyncStatusEnum.ABNORMAL.getCode());
        lambdaQueryWrapper.in(InternetCelebritySampleOrderItemEntity::getOrderDeliveryType, Arrays.asList(InternetCelebritySampleOrderDeliveryTypeEnum.FBA.getDescription(), InternetCelebritySampleOrderDeliveryTypeEnum.FBT.getDescription(), InternetCelebritySampleOrderDeliveryTypeEnum.OVERSEAS_WAREHOUSE.getDescription()));
        return this.list(lambdaQueryWrapper);
    }

}
