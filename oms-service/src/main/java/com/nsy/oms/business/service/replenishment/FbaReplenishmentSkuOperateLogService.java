package com.nsy.oms.business.service.replenishment;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.privilege.LoginInfoService;
import com.nsy.oms.repository.entity.replenishment.FbaReplenishmentSkuEntity;
import com.nsy.oms.repository.entity.replenishment.FbaReplenishmentSkuOperateLogEntity;
import com.nsy.oms.repository.sql.mapper.replenishment.FbaReplenishmentSkuOperateLogMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.inject.Inject;

/**
 * fba智能补货sku级别
 * <AUTHOR>
 * @date 2023-03-03
 */
@Service
public class FbaReplenishmentSkuOperateLogService extends ServiceImpl<FbaReplenishmentSkuOperateLogMapper, FbaReplenishmentSkuOperateLogEntity> {
    @Inject
    LoginInfoService loginInfoService;

    public void saveLog(FbaReplenishmentSkuEntity fbaReplenishmentSkuEntity, Integer operateType) {
        FbaReplenishmentSkuOperateLogEntity logEntity = new FbaReplenishmentSkuOperateLogEntity();
        BeanUtils.copyProperties(fbaReplenishmentSkuEntity, logEntity, "id", "createDate", "createBy", "updateDate", "updateBy");
        logEntity.setVersion(0);
        logEntity.setOperateType(operateType);
        logEntity.setCreateBy(loginInfoService.getName());
        this.save(logEntity);
    }
}