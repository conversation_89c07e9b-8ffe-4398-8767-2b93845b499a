package com.nsy.oms.business.service.download.inbound;

import com.nsy.api.core.apicore.response.CustomExcelResponse;
import com.nsy.api.core.apicore.util.NsyExcelUtil;
import com.nsy.api.oms.enums.QuartzDownloadQueueTypeEnum;
import com.nsy.oms.business.domain.download.inbound.InboundPlanExport;
import com.nsy.oms.business.domain.request.download.DownloadRequest;
import com.nsy.oms.business.domain.request.inbound.ShipmentPlanPageRequest;
import com.nsy.oms.business.domain.response.base.DownloadResponse;
import com.nsy.oms.business.domain.response.base.PageResponse;
import com.nsy.oms.business.domain.response.inbound.InboundPlan;
import com.nsy.oms.business.domain.response.inbound.InboundPlanItem;
import com.nsy.oms.business.service.download.IDownloadService;
import com.nsy.oms.business.service.inbound.InboundCreateService;
import com.nsy.oms.utils.JsonMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * @description:
 * @author: linCheng
 * @create: 2025-04-03 10:06
 **/
@Slf4j
@Service
public class InboundPlanDownloadServiceImpl implements IDownloadService {

    @Resource
    private InboundCreateService inboundCreateService;

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.OMS_FBT_PLAN;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        log.info("InboundPlanDownloadServiceImpl-DownloadRequest:{}", JsonMapper.toJson(request));
        DownloadResponse response = new DownloadResponse();
        ShipmentPlanPageRequest shipmentPlanPageRequest = JsonMapper.fromJson(request.getRequestContent(), ShipmentPlanPageRequest.class);
        shipmentPlanPageRequest.setPageIndex(request.getPageIndex());
        shipmentPlanPageRequest.setPageSize(request.getPageSize());
        PageResponse<InboundPlan> inboundPlanPageResponse = inboundCreateService.page(shipmentPlanPageRequest);
        Collection<InboundPlan> inboundPlans = inboundPlanPageResponse.getContent();
        if (CollectionUtils.isEmpty(inboundPlans)) {
            return DownloadResponse.of("[]", 0L);
        }

        List<List<Object>> data = new ArrayList<>();
        inboundPlans.forEach(inboundPlan -> {
            List<InboundPlanItem> inboundPlanItems = inboundPlan.getInboundPlanItems();
            if (CollectionUtils.isEmpty(inboundPlanItems)) {
                return;
            }
            inboundPlanItems.forEach(inboundPlanItem -> {
                data.add(NsyExcelUtil.getData(InboundPlanExport.class, getInboundPlanExport(inboundPlan, inboundPlanItem)));
            });
        });

        CustomExcelResponse excelResponse = new CustomExcelResponse();
        excelResponse.setHeaders(NsyExcelUtil.getCommonHeads(InboundPlanExport.class));
        excelResponse.setData(data);
        response.setTotalCount(inboundPlanPageResponse.getTotalCount());
        response.setDataJsonStr(JsonMapper.toJson(excelResponse));
        log.info("InboundPlanDownloadServiceImpl-DownloadResponse:{}", JsonMapper.toJson(response));
        return response;
    }

    private InboundPlanExport getInboundPlanExport(InboundPlan inboundPlan, InboundPlanItem inboundPlanItem) {
        InboundPlanExport inboundPlanExport = new InboundPlanExport();
        inboundPlanExport.setErpTid(inboundPlan.getErpTid());
        inboundPlanExport.setStatus(inboundPlan.getStatus());
        inboundPlanExport.setStoreName(inboundPlan.getStoreName());
        inboundPlanExport.setBrandName(inboundPlan.getBrandName());
        inboundPlanExport.setLogisticsCompanyName(inboundPlan.getLogisticsCompanyName());
        inboundPlanExport.setRemark(inboundPlan.getRemark());
        inboundPlanExport.setCreateDate(inboundPlan.getCreateDate());
        inboundPlanExport.setSellerSku(inboundPlanItem.getSellerSku());
        inboundPlanExport.setErpSku(inboundPlanItem.getErpSku());
        inboundPlanExport.setBrandName(inboundPlanItem.getBrandName());
        inboundPlanExport.setBarcode(inboundPlanItem.getBarcode());
        inboundPlanExport.setEstimatedShipmentQuantity(Optional.ofNullable(inboundPlanItem.getEstimatedShipmentQuantity()).isPresent() ? inboundPlanItem.getEstimatedShipmentQuantity() : 0);
        inboundPlanExport.setActualShipmentQuantity(Optional.ofNullable(inboundPlanItem.getActualShipmentQuantity()).isPresent() ? inboundPlanItem.getActualShipmentQuantity() : 0);
        return inboundPlanExport;
    }


}
