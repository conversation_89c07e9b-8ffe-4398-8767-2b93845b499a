package com.nsy.oms.business.service.auth;

import com.nsy.oms.business.domain.request.auth.ShopifyConfigPageRequest;
import com.nsy.oms.business.domain.request.auth.ShopifyConfigRequest;
import com.nsy.oms.business.domain.request.sa.DistributorsStoreRequest;
import com.nsy.oms.business.domain.response.auth.SauShopifyConfigResponse;
import com.nsy.oms.business.domain.response.base.PageResponse;
import com.nsy.oms.enums.sau.GrantAuthStatusEnum;
import com.nsy.oms.repository.entity.auth.SauShopifyConfigEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/25 13:54
 */
public interface SauShopifyConfigService {
    /**
     * 保存数据
     *
     * @param request
     */
    void saveInfo(ShopifyConfigRequest request);

    /**
     * 获取详情
     * @param id
     * @return
     */
    SauShopifyConfigResponse getInfo(Integer id);

    /**
     * 获取列表
     * @param request
     * @return
     */
    PageResponse<SauShopifyConfigResponse> getList(ShopifyConfigPageRequest request);

    /**
     * 更新数据
     * @param request
     */
    void updateData(ShopifyConfigRequest request);

    /**
     * 更新授权
     * @param id
     * @param open
     */
    void updateGrantAuthStatus(Integer id, GrantAuthStatusEnum open);

    /**
     * 获取详情
     * @param storeId
     * @return
     */
    SauShopifyConfigResponse getInfoResponse(Integer storeId);


    /**
     * 保存数据
     * @param saStoreRequest
     * @param storeId
     */
    void saveData(DistributorsStoreRequest saStoreRequest, Integer storeId);

    /**
     * 获取授权信息将要过期的店铺
     * @param notifyDay
     * @return
     */
    List<SauShopifyConfigEntity> getExpireStores(Integer notifyDay);

    SauShopifyConfigResponse getAuthInfoByWebsiteId(Integer websiteId);
}
