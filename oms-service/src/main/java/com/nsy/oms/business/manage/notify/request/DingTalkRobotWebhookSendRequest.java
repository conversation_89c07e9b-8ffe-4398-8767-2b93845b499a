package com.nsy.oms.business.manage.notify.request;

import java.util.List;

/**
 * 钉钉机器人Webhook发送消息请求
 */
public class DingTalkRobotWebhookSendRequest {

    /**
     * 机器人唯一标识
     */
    private String accessToken;

    /**
     * 是否加签
     */
    private Boolean isSign = Boolean.FALSE;

    /**
     * 加签秘钥
     */
    private String secret;

    /**
     * @ 人员电话
     */
    private List<String> atMobiles;

    /**
     * 消息类型
     */
    private String msgType;

    /**
     * 是否@所有人
     */
    private Boolean isAtAll = Boolean.FALSE;

    Text text;

    public Text getText() {
        return text;
    }

    public void setText(Text text) {
        this.text = text;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public Boolean getIsSign() {
        return isSign;
    }

    public void setIsSign(Boolean isSign) {
        this.isSign = isSign;
    }

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }


    public List<String> getAtMobiles() {
        return atMobiles;
    }

    public void setAtMobiles(List<String> atMobiles) {
        this.atMobiles = atMobiles;
    }

    public Boolean getSign() {
        return isSign;
    }

    public void setSign(Boolean sign) {
        isSign = sign;
    }

    public String getMsgType() {
        return msgType;
    }

    public void setMsgType(String msgType) {
        this.msgType = msgType;
    }

    public Boolean getAtAll() {
        return isAtAll;
    }

    public void setAtAll(Boolean atAll) {
        isAtAll = atAll;
    }

    public static class Text {
        private String content;

        public Text() {
        }

        public Text(String content) {
            this.content = content;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }
    }

}
