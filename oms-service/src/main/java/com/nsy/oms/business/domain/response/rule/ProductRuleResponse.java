package com.nsy.oms.business.domain.response.rule;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023年9月18日 0018 下午 02:01:37
 */
public class ProductRuleResponse {

    private String createDate;

    private String fabricType;

    private Integer saleVol;

    private Integer day;

    private List<Integer> labelIds;

    private List<Integer> filterLabelIds;

    private List<Integer> spaceIds;

    private List<String> infringementNameList;

    private List<String> productTypes;

    private List<Integer> seasonLabelIds;

    private List<String> spaceStoreBrandList;

    private List<String> publishPlatformList;

    public List<String> getPublishPlatformList() {
        return publishPlatformList;
    }

    public void setPublishPlatformList(List<String> publishPlatformList) {
        this.publishPlatformList = publishPlatformList;
    }

    public List<String> getSpaceStoreBrandList() {
        return spaceStoreBrandList;
    }

    public void setSpaceStoreBrandList(List<String> spaceStoreBrandList) {
        this.spaceStoreBrandList = spaceStoreBrandList;
    }

    public List<Integer> getFilterLabelIds() {
        return filterLabelIds;
    }

    public void setFilterLabelIds(List<Integer> filterLabelIds) {
        this.filterLabelIds = filterLabelIds;
    }

    public String getFabricType() {
        return fabricType;
    }

    public void setFabricType(String fabricType) {
        this.fabricType = fabricType;
    }

    public List<String> getInfringementNameList() {
        return infringementNameList;
    }

    public void setInfringementNameList(List<String> infringementNameList) {
        this.infringementNameList = infringementNameList;
    }

    public List<Integer> getSpaceIds() {
        return spaceIds;
    }

    public void setSpaceIds(List<Integer> spaceIds) {
        this.spaceIds = spaceIds;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public Integer getSaleVol() {
        return saleVol;
    }

    public void setSaleVol(Integer saleVol) {
        this.saleVol = saleVol;
    }

    public Integer getDay() {
        return day;
    }

    public void setDay(Integer day) {
        this.day = day;
    }

    public List<Integer> getLabelIds() {
        return labelIds;
    }

    public void setLabelIds(List<Integer> labelIds) {
        this.labelIds = labelIds;
    }

    public List<String> getProductTypes() {
        return productTypes;
    }

    public void setProductTypes(List<String> productTypes) {
        this.productTypes = productTypes;
    }


    public List<Integer> getSeasonLabelIds() {
        return seasonLabelIds;
    }

    public void setSeasonLabelIds(List<Integer> seasonLabelIds) {
        this.seasonLabelIds = seasonLabelIds;
    }
}
