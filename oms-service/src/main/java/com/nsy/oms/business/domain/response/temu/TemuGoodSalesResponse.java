package com.nsy.oms.business.domain.response.temu;


import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class TemuGoodSalesResponse {
    private Integer id;
    @ApiModelProperty("店铺id")
    private Integer storeId;
    @ApiModelProperty("店铺名称")
    private String storeName;
    @ApiModelProperty("日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    private Date saleDate;
    @ApiModelProperty("当日销量")
    private Integer todaySaleQuantity;
    @ApiModelProperty("Temu平台sku")
    private String temuSkuExtCode;
    @ApiModelProperty("数据来源")
    private String dataFrom;
    @ApiModelProperty("创建人员")
    private String createBy;
    @ApiModelProperty("创建时间")
    private Date createDate;
}