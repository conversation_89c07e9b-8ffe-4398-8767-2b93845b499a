package com.nsy.oms.business.service.stock;

import com.nsy.oms.business.domain.request.stock.StockUpdateRequest;
import com.nsy.oms.business.domain.stock.StockChangeInfo;
import com.nsy.oms.business.domain.stock.StockInfo;
import com.nsy.oms.enums.stock.StockBrandTypeEnum;
import com.nsy.oms.enums.stock.StockOperateTypeEnum;
import com.nsy.oms.enums.stock.StockUpdateTypeEnum;
import com.nsy.oms.repository.entity.stock.StockEntity;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: caish<PERSON><PERSON>
 * @version: v1.0
 * @description: 库存表业务实现
 * @date: 2025-04-02 14:40
 */
@Service
public interface StockService {

    //库存更新
    StockEntity updateStock(StockUpdateRequest stockUpdateRequest);

    //盘点 调拨库存扣减
    List<StockChangeInfo> handleStockDecrease(String sku, List<StockInfo> stockList, int decreaseQty,
                                              StockUpdateTypeEnum stockUpdateType, Integer isPreLabel);

    //获取库存
    List<StockInfo> getSkuStockInfo(Integer storeId, Integer spaceId, List<Integer> specIdList, StockOperateTypeEnum stockOperateTypeEnum, StockBrandTypeEnum stockBrandTypeEnum);
}
