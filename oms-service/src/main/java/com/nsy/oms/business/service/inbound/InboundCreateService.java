package com.nsy.oms.business.service.inbound;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.base.Preconditions;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.privilege.LoginInfoService;
import com.nsy.api.core.apicore.util.NsyCollUtil;
import com.nsy.api.oms.dto.request.amazon.ConvertErpSkuRequest;
import com.nsy.api.oms.dto.request.amazon.StoreSellerSkuRequest;
import com.nsy.api.oms.dto.response.amazon.ConvertErpSkuResponse;
import com.nsy.api.oms.dto.response.amazon.ErpSkuMappingResponse;
import com.nsy.api.oms.feign.AmazonFeignClient;
import com.nsy.business.base.enums.LocationEnum;
import com.nsy.oms.business.domain.request.inbound.InboundPlanExcelData;
import com.nsy.oms.business.domain.request.inbound.InboundPlanExcelRequest;
import com.nsy.oms.business.domain.request.inbound.ShipmentPlanPageRequest;
import com.nsy.oms.business.domain.request.inbound.StaShipmentPlan;
import com.nsy.oms.business.domain.request.inbound.StaShipmentPlanCloseRequest;
import com.nsy.oms.business.domain.request.inbound.StaShipmentPlanCreateRequest;
import com.nsy.oms.business.domain.request.inbound.StaShipmentPlanItem;
import com.nsy.oms.business.domain.response.base.PageResponse;
import com.nsy.oms.business.domain.response.inbound.FbaInboundShipmentPlanCreateResponse;
import com.nsy.oms.business.domain.response.inbound.FbaInboundShipmentPlanItem;
import com.nsy.oms.business.domain.response.inbound.InboundPlan;
import com.nsy.oms.business.domain.response.inbound.InboundPlanItem;
import com.nsy.oms.business.domain.response.inbound.InboundPlanItemStatistics;
import com.nsy.oms.business.domain.response.inbound.InboundPlanLog;
import com.nsy.oms.business.domain.response.inbound.InboundShipment;
import com.nsy.oms.business.domain.response.inbound.InboundShipmentItem;
import com.nsy.oms.business.domain.response.inbound.InboundShipmentPlanSummary;
import com.nsy.oms.business.domain.response.inbound.PlanDetailResponse;
import com.nsy.oms.business.domain.response.inbound.PlanUploadResponse;
import com.nsy.oms.business.domain.response.sa.SaStoreDetailResponse;
import com.nsy.oms.business.manage.amazon.request.AmazonBrandStoreProductSkuRequest;
import com.nsy.oms.business.manage.amazon.response.AmazonBrandStoreProductSkuResponse;
import com.nsy.oms.business.manage.erp.ErpApiService;
import com.nsy.oms.business.manage.erp.domain.SpaceStockInfo;
import com.nsy.oms.business.manage.erp.request.GetSpaceStockRequest;
import com.nsy.oms.business.manage.erp.request.inbound.AddFbaShipmentPlanRequest;
import com.nsy.oms.business.manage.erp.request.inbound.FbaOrderInfo;
import com.nsy.oms.business.manage.erp.request.inbound.FbaTradeInfo;
import com.nsy.oms.business.manage.erp.response.GetSpaceStockResponse;
import com.nsy.oms.business.manage.erp.response.Sku;
import com.nsy.oms.business.manage.erp.response.inbound.AddFbaShipmentPlanResponse;
import com.nsy.oms.business.manage.omspublish.OmsPublishApiService;
import com.nsy.oms.business.manage.omspublish.response.PublishProductSpec;
import com.nsy.oms.business.manage.tms.TmsApiService;
import com.nsy.oms.business.manage.user.UserApiService;
import com.nsy.oms.business.manage.user.request.shared.SelectModel;
import com.nsy.oms.business.service.bd.BdBrandStoreSkcService;
import com.nsy.oms.business.service.privilege.AccessControlService;
import com.nsy.oms.business.service.replenishment.FbaReplenishmentSkcService;
import com.nsy.oms.business.service.sa.SaStoreConfigService;
import com.nsy.oms.business.service.sa.SaStoreService;
import com.nsy.oms.enums.inbound.FbaAuditStatusEnum;
import com.nsy.oms.enums.inbound.InboundPlanStatusEnum;
import com.nsy.oms.enums.inbound.SkuStatus;
import com.nsy.oms.enums.inbound.SkuStatusEnum;
import com.nsy.oms.enums.inbound.SkuStockRemarkEnum;
import com.nsy.oms.enums.inbound.SpaceName;
import com.nsy.oms.enums.sa.PlatformEnum;
import com.nsy.oms.repository.dao.sa.SaStoreDao;
import com.nsy.oms.repository.dao.sa.SaStoreWebsiteDao;
import com.nsy.oms.repository.entity.inbound.InboundPlanEntity;
import com.nsy.oms.repository.entity.inbound.InboundPlanItemEntity;
import com.nsy.oms.repository.entity.inbound.InboundPlanLogEntity;
import com.nsy.oms.repository.entity.inbound.InboundShipmentEntity;
import com.nsy.oms.repository.entity.inbound.InboundShipmentItemEntity;
import com.nsy.oms.repository.entity.sa.SaStoreConfigEntity;
import com.nsy.oms.repository.entity.sa.SaStoreEntity;
import com.nsy.oms.repository.entity.sa.SaStoreWebsiteEntity;
import com.nsy.oms.utils.RedisClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
@Slf4j
public class InboundCreateService {
    @Autowired
    private AccessControlService accessControlService;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private ErpApiService erpApiService;
    @Autowired
    private SaStoreService storeService;
    @Autowired
    private InboundPlanLogService inboundPlanLogService;
    @Autowired
    private InboundShipmentItemService inboundShipmentItemService;
    @Autowired
    private InboundPlanService inboundPlanService;
    @Autowired
    private InboundPlanItemService inboundPlanItemService;
    @Autowired
    private InboundShipmentService inboundShipmentService;
    @Autowired
    private BdBrandStoreSkcService bdBrandStoreSkcService;
    @Autowired
    private SaStoreWebsiteDao saStoreWebsiteDao;
    @Autowired
    private AmazonFeignClient amazonFeignClient;
    @Autowired
    private UserApiService userApiService;
    @Autowired
    private TmsApiService tmsApiService;
    @Resource
    private InboundPlanItemService itemEntityService;
    @Resource
    private InboundCreateService selfService;
    @Resource
    private InboundPlanLogService logEntityService;
    @Autowired
    private OmsPublishApiService omsPublishApiService;
    @Resource
    private SaStoreConfigService saStoreConfigService;
    @Resource
    private SaStoreDao saStoreDao;

    public static final String INBOUND_COUNT_PREFIX = "INBOUND_COUNT_PREFIX:";

    public PageResponse<InboundPlan> page(ShipmentPlanPageRequest request) {
        List<Integer> permissionStoreIds = accessControlService.isAdmin() ? null : accessControlService.doPrivileged(new FbaReplenishmentSkcService.FbaReplenishmentSkcPrivilegeAction(userApiService, loginInfoService.getUserName()));
        IPage<InboundPlanEntity> page = inboundPlanService.getByPage(request, permissionStoreIds);

        PageResponse<InboundPlan> pageResponse = PageResponse.of(page.getTotal(), page.getPages());
        List<InboundPlanEntity> records = page.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            List<Integer> ids = records.stream().map(InboundPlanEntity::getId).collect(Collectors.toList());
            Map<Integer, List<InboundPlanItemEntity>> inboundPlanItemMap = inboundPlanItemService.getByPlanIds(ids).stream().collect(Collectors.groupingBy(InboundPlanItemEntity::getPlanId));

            List<InboundShipmentEntity> inboundShipmentEntities = inboundShipmentService.getByPlanIds(ids);
            Map<Integer, List<InboundShipmentEntity>> inboundShipmentMap = inboundShipmentEntities.stream().collect(Collectors.groupingBy(InboundShipmentEntity::getPlanId));
            List<InboundShipmentItemEntity> inboundShipmentItemEntities = inboundShipmentItemService.getByShipmentIds(inboundShipmentEntities.stream().map(InboundShipmentEntity::getShipmentId).collect(Collectors.toList()));
            Map<String, List<InboundShipmentItemEntity>> inboundShipmentItemMap = inboundShipmentItemEntities.stream().collect(Collectors.groupingBy(InboundShipmentItemEntity::getShipmentId));
            pageResponse.setContent(records.stream().map(inboundPlanEntity -> {
                InboundPlan inboundPlan = new InboundPlan();
                BeanUtil.copyProperties(inboundPlanEntity, inboundPlan);
                inboundPlan.setTotalBoxCount(inboundPlanEntity.getBoxCount() != null ? inboundPlanEntity.getBoxCount() : 0);
                List<InboundPlanItemEntity> inboundPlanItemEntities = inboundPlanItemMap.get(inboundPlanEntity.getId());
                if (CollectionUtils.isNotEmpty(inboundPlanItemEntities)) {
                    List<InboundPlanItem> inboundPlanItems = BeanUtil.copyToList(inboundPlanItemEntities, InboundPlanItem.class);
                    inboundPlan.setInboundPlanItems(inboundPlanItems);
                    inboundPlan.setTotalActualShipmentQuantity(inboundPlanItemEntities.stream().mapToInt(InboundPlanItemEntity::getActualShipmentQuantity).sum());
                    inboundPlan.setSkuNum(inboundPlanItemEntities.size());
                    inboundPlan.setTotalEstimatedShipmentQuantity(inboundPlanItemEntities.stream().mapToInt(InboundPlanItemEntity::getEstimatedShipmentQuantity).sum());
                }

                // 待发货 实际发货：0，已出库：0，未出库：0
                // 已装箱 实际发货：0，已出库：0，未出库：0
                // 待发货 实际发货：0，已出库：actualShipment，未出库：0
                // 已发货 实际发货：0，已出库：actualShipment，未出库：0
                // 已出库
                int shippedQuantity = 0;
                int unshippedQuantity = 0;
                //待发货状态下 已出库等于0,未出库=实际发货数量
                if (InboundPlanStatusEnum.WAITING_SHIPMENT.getStatus().equals(inboundPlanEntity.getStatus())) {
                    unshippedQuantity = inboundPlan.getTotalActualShipmentQuantity();
                }
                //SHIPPED->已出库=实际发货数量,未出库=0
                if (InboundPlanStatusEnum.SHIPPED.getStatus().equals(inboundPlanEntity.getStatus())) {
                    shippedQuantity = inboundPlan.getTotalActualShipmentQuantity();
                }
                inboundPlan.setShippedQuantity(shippedQuantity);
                inboundPlan.setUnshippedQuantity(unshippedQuantity);

                List<InboundShipmentEntity> inboundShipmentEntityList = inboundShipmentMap.get(inboundPlanEntity.getId());
                if (CollectionUtils.isNotEmpty(inboundShipmentEntityList)) {
                    fillItemData(inboundShipmentEntityList, inboundShipmentItemMap, inboundPlan);
                }
                return inboundPlan;
            }).collect(Collectors.toList()));
        }
        return pageResponse;
    }

    private static void fillItemData(List<InboundShipmentEntity> inboundShipmentEntityList, Map<String, List<InboundShipmentItemEntity>> inboundShipmentItemMap, InboundPlan inboundPlan) {
        //ShipmentItem
        Map<String, InboundShipmentEntity> inboundShipmentByShipmentIdMap = inboundShipmentEntityList.stream().collect(Collectors.toMap(InboundShipmentEntity::getShipmentId, a -> a, (k1, k2) -> k1));
        List<InboundShipmentItemEntity> inboundShipmentItemEntity = inboundShipmentEntityList
                .stream()
                .map(inboundShipmentEntity -> inboundShipmentItemMap.get(inboundShipmentEntity.getShipmentId()))
                .flatMap(item -> item.stream().filter(Objects::nonNull))
                .collect(Collectors.toList());
        List<InboundShipmentItem> inboundShipmentItems = BeanUtil.copyToList(inboundShipmentItemEntity, InboundShipmentItem.class);
        inboundShipmentItems.forEach(inboundShipmentItem -> {
            InboundShipmentEntity inboundShipmentEntity = inboundShipmentByShipmentIdMap.get(inboundShipmentItem.getShipmentId());
            inboundShipmentItem.setPlatformStatus(inboundShipmentEntity.getPlatformStatus());
            inboundShipmentItem.setLogisticsName(inboundShipmentEntity.getLogisticsName());
            inboundShipmentItem.setLogisticsNumber(inboundShipmentEntity.getLogisticsNumber());
            inboundShipmentItem.setDestinationFulfillmentCenter(inboundShipmentEntity.getDestinationFulfillmentCenter());
            inboundShipmentItem.setDifferencesQuantity((Optional.ofNullable(inboundShipmentItem.getQuantityReceived()).isPresent() ? inboundShipmentItem.getQuantityReceived() : 0) - (Optional.ofNullable(inboundShipmentItem.getEstimatedShipmentQuantity()).isPresent() ? inboundShipmentItem.getEstimatedShipmentQuantity() : 0));
            inboundShipmentItem.setDeliveryTime(inboundShipmentEntity.getDeliveryTime());
            inboundShipmentItem.setEstimateReceiveDate(inboundShipmentEntity.getEstimateReceiveDate());
            inboundShipmentItem.setReceiveDate(inboundShipmentEntity.getReceiveDate());
        });
        inboundPlan.setInboundShipmentItems(inboundShipmentItems);

        //Shipment
        List<InboundShipment> inboundShipments = BeanUtil.copyToList(inboundShipmentEntityList, InboundShipment.class);
        inboundShipments.forEach(inboundShipment -> {
            List<InboundShipmentItemEntity> inboundShipmentItemEntityList = inboundShipmentItemMap.get(inboundShipment.getShipmentId());
            inboundShipment.setSkuNum(inboundShipmentItemEntityList.size());
            inboundShipment.setActualShipmentQuantity(inboundShipmentItemEntityList.stream().mapToInt(entity -> Optional.ofNullable(entity.getActualShipmentQuantity()).isPresent() ? entity.getActualShipmentQuantity() : 0).sum());
            inboundShipment.setQuantityReceived(inboundShipmentItemEntityList.stream().mapToInt(entity -> Optional.ofNullable(entity.getQuantityReceived()).isPresent() ? entity.getQuantityReceived() : 0).sum());
            inboundShipment.setDifferencesQuantity(inboundShipment.getDifferencesQuantity());
        });
        inboundPlan.setInboundShipments(inboundShipments);
    }

    public PlanDetailResponse detail(Integer planId) {

        PlanDetailResponse planDetailResponse = new PlanDetailResponse();

        //plan
        InboundPlanEntity inboundPlanEntity = inboundPlanService.getById(planId);
        InboundPlan inboundShipmentPlan = new InboundPlan();
        BeanUtil.copyProperties(inboundPlanEntity, inboundShipmentPlan);

        List<InboundPlanItemEntity> inboundPlanItemEntities = inboundPlanItemService.getByPlanIds(Collections.singletonList(planId));

        //获取商通数据
        List<String> erpSkus = inboundPlanItemEntities.stream().distinct().map(InboundPlanItemEntity::getErpSku).collect(Collectors.toList());
        List<Sku> erpSkuInfoList = erpApiService.getSkuInfoList(erpSkus.toArray(new String[0]));
        Map<String, Sku> skuMap = erpSkuInfoList.stream().collect(Collectors.toMap(Sku::getSku, Function.identity(), (k1, k2) -> k2));

        //plan_item
        List<InboundPlanItem> inboundPlanItems = BeanUtil.copyToList(inboundPlanItemEntities, InboundPlanItem.class);
        inboundPlanItems.forEach(inboundPlanItem -> {
            Sku erpSku = skuMap.get(inboundPlanItem.getErpSku());
            if (Objects.nonNull(erpSku)) {
                inboundPlanItem.setImageUrl(erpSku.getImageUrl());
            }
        });
        Map<String, InboundPlanItem> inboundPlanItemMap = inboundPlanItems.stream().collect(Collectors.toMap(InboundPlanItem::getSellerSku, a -> a, (k1, k2) -> k1));

        //log
        List<InboundPlanLogEntity> inboundPlanLogEntities = inboundPlanLogService.getByPlanIds(Collections.singletonList(planId));
        List<InboundPlanLog> inboundPlanLogs = BeanUtil.copyToList(inboundPlanLogEntities, InboundPlanLog.class);


        //Shipment
        List<InboundShipmentEntity> inboundShipmentEntities = inboundShipmentService.getByPlanIds(Collections.singletonList(planId));

        //Shipment_item
        List<InboundShipmentItemEntity> inboundShipmentItemEntities = inboundShipmentItemService.getByShipmentIds(inboundShipmentEntities.stream().map(InboundShipmentEntity::getShipmentId).collect(Collectors.toList()));
        Map<String, List<InboundShipmentItemEntity>> inboundShipmentItemMap = inboundShipmentItemEntities.stream().collect(Collectors.groupingBy(InboundShipmentItemEntity::getShipmentId));

        List<InboundShipment> inboundShipments = inboundShipmentEntities.stream().map(inboundShipmentEntity -> {
            InboundShipment inboundShipment = new InboundShipment();
            BeanUtil.copyProperties(inboundShipmentEntity, inboundShipment);

            String format = StrUtil.format("{},{},{},{},{},({})", StrUtil.isEmpty(inboundShipmentEntity.getShipToName()) ? "" : inboundShipmentEntity.getShipToName(), inboundShipmentEntity.getShipToAddressLine1(), inboundShipmentEntity.getShipToCity(),
                    inboundShipmentEntity.getShipToStateOrProvinceCode(), inboundShipmentEntity.getShipToCountryCode(), inboundShipmentEntity.getDestinationFulfillmentCenter());
            inboundShipment.setReceiveAddressInfo(format);

            List<InboundShipmentItemEntity> inboundShipmentItemEntityList = inboundShipmentItemMap.get(inboundShipment.getShipmentId());
            List<InboundShipmentItem> inboundShipmentItems = BeanUtil.copyToList(inboundShipmentItemEntityList, InboundShipmentItem.class);
            inboundShipmentItems.forEach(inboundShipmentItem -> {
                Optional.ofNullable(skuMap.get(inboundShipmentItem.getErpSku())).ifPresent(sku -> {
                    inboundShipmentItem.setImageUrl(sku.getImageUrl());
                });
                Optional.ofNullable(inboundPlanItemMap.get(inboundShipmentItem.getSellerSku())).ifPresent(inboundPlanItem -> {
                    inboundShipmentItem.setBarcode(inboundPlanItem.getBarcode());
                });
                inboundShipmentItem.setDifferencesQuantity((Optional.ofNullable(inboundShipmentItem.getQuantityReceived()).isPresent() ? inboundShipmentItem.getQuantityReceived() : 0) - (Optional.ofNullable(inboundShipmentItem.getEstimatedShipmentQuantity()).isPresent() ? inboundShipmentItem.getEstimatedShipmentQuantity() : 0));
            });
            int totalEstimatedShipmentQuantity = inboundShipmentItems.stream()
                    .mapToInt(inboundShipmentItem -> Optional.ofNullable(inboundShipmentItem.getEstimatedShipmentQuantity()).isPresent() ? inboundShipmentItem.getEstimatedShipmentQuantity() : 0)
                    .sum();
            inboundShipment.setTotalEstimatedShipmentQuantity(totalEstimatedShipmentQuantity);
            inboundShipment.setInboundShipmentItems(inboundShipmentItems);
            return inboundShipment;
        }).collect(Collectors.toList());
        planDetailResponse.setInboundPlan(inboundShipmentPlan);
        planDetailResponse.setInboundPlanItems(inboundPlanItems);
        planDetailResponse.setInboundPlanLogs(inboundPlanLogs);
        //统计
        planDetailResponse.setInboundPlanItemStatistics(setInboundPlanItemStatistics(inboundPlanItems, inboundShipmentPlan));
        planDetailResponse.setInboundShipments(inboundShipments);

        return planDetailResponse;
    }

    private InboundPlanItemStatistics setInboundPlanItemStatistics(List<InboundPlanItem> inboundPlanItems, InboundPlan inboundShipmentPlan) {
        InboundPlanItemStatistics staInboundShipmentPlanItemStatistics = new InboundPlanItemStatistics();
        int totalActualShipmentQuantity = inboundPlanItems.stream()
                .mapToInt(staInboundShipmentPlanItem -> Optional.ofNullable(staInboundShipmentPlanItem.getActualShipmentQuantity()).isPresent() ? staInboundShipmentPlanItem.getActualShipmentQuantity() : 0)
                .sum();
        int totalEstimatedShipmentQuantity = inboundPlanItems.stream()
                .mapToInt(staInboundShipmentPlanItem -> Optional.ofNullable(staInboundShipmentPlanItem.getEstimatedShipmentQuantity()).isPresent() ? staInboundShipmentPlanItem.getEstimatedShipmentQuantity() : 0)
                .sum();
        staInboundShipmentPlanItemStatistics.setTotalEstimatedShipmentQuantity(totalEstimatedShipmentQuantity);
        staInboundShipmentPlanItemStatistics.setTotalActualShipmentQuantity(totalActualShipmentQuantity);
        inboundShipmentPlan.setTotalEstimatedShipmentQuantity(totalEstimatedShipmentQuantity);
        inboundShipmentPlan.setSkuNum(inboundPlanItems.size());
        return staInboundShipmentPlanItemStatistics;
    }


    public PlanUploadResponse uploadExcel(InboundPlanExcelRequest request) {
        try {
            List<InboundPlanExcelData> errorList = request.getExcelDataRecords().stream()
                    .filter(fbaInboundPlanExcelData -> !Optional.ofNullable(fbaInboundPlanExcelData.getEstimatedShipmentQuantity()).isPresent() || fbaInboundPlanExcelData.getEstimatedShipmentQuantity() < 1)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(errorList)) {
                throw new BusinessServiceException("SellerSku补货数量为空或者补货数量小于1:" + errorList.stream().map(InboundPlanExcelData::getSellerSku).collect(Collectors.joining(",")));
            }
            List<InboundPlanExcelData> fbaInboundPlanExcelDataList = new ArrayList<>();
            Map<String, List<InboundPlanExcelData>> checkMap = request.getExcelDataRecords().stream().collect(Collectors.groupingBy(InboundPlanExcelData::getSellerSku));
            checkMap.forEach((key, value) -> value.stream().reduce((a, b) -> new InboundPlanExcelData(a.getSellerSku(),
                            a.getErpSku(),
                            a.getEstimatedShipmentQuantity() + b.getEstimatedShipmentQuantity()))
                    .ifPresent(fbaInboundPlanExcelDataList::add));

            BeanUtil.trimStrFields(fbaInboundPlanExcelDataList);
            List<String> sellerSkus = fbaInboundPlanExcelDataList.stream().map(InboundPlanExcelData::getSellerSku).distinct().collect(Collectors.toList());
            // 转换 seller sku -> erp sku
            Map<String, String> erpSkuMap = getSkuMappingMap(sellerSkus, saStoreDao.getAssociatedStoreIdWithThrowEx(request.getStoreId()));
            //这边erpSkuMap的value有可能是null,如果是null的话就报错
            List<String> errorSku = new ArrayList<>();
            erpSkuMap.forEach((k, v) -> {
                if (v == null) {
                    errorSku.add(k);
                }
            });
            if (CollUtil.isNotEmpty(errorSku)) {
                throw new Exception(StrUtil.format("sellerSku: {} 未找到对应的erpSku", errorSku.toString()));
            }
            fbaInboundPlanExcelDataList.forEach(dataRow -> dataRow.setErpSku(erpSkuMap.get(dataRow.getSellerSku())));

            List<String> erpSkuList = fbaInboundPlanExcelDataList.stream().map(InboundPlanExcelData::getErpSku).collect(Collectors.toList());

            //查询商品系统校验sku以及获取品牌,如果有问题直接报错
            return uploadNonPackExcel(request, erpSkuMap, buildBrandMap(request, erpSkuList));

        } catch (Exception e) {
            log.error("InboundCreateService.uploadExcel.error:{}", e.getMessage(), e);
            PlanUploadResponse uploadResponse = new PlanUploadResponse();
            uploadResponse.setMessage(e.getMessage());
            return uploadResponse;
        }
    }

    private List<PublishProductSpec> getPublishProduct(Integer storeId, List<String> sellerSku) {
        List<SaStoreWebsiteEntity> saStoreWebsiteEntities = saStoreWebsiteDao.getList(saStoreDao.getAssociatedStoreIdWithThrowEx(storeId));
        if (CollectionUtils.isEmpty(saStoreWebsiteEntities)) {
            throw new BusinessServiceException("平台与店铺的配置数据不存在");
        }
        SaStoreWebsiteEntity saStoreWebsiteEntity = saStoreWebsiteEntities.stream().findFirst().get();
        Integer websiteId = saStoreWebsiteEntity.getWebsiteId();

        List<PublishProductSpec> publishProducts = omsPublishApiService.getPublishProducts(PlatformEnum.TIKTOK.name(), websiteId, sellerSku);
        return CollectionUtils.isEmpty(publishProducts) ? Collections.emptyList() : publishProducts;
    }


    public Map<String, String> getSkuMappingMap(List<String> sellerSkus, Integer storeId) {
        try {
            if (NsyCollUtil.isEmpty(sellerSkus)) {
                return Collections.emptyMap();
            }
            ConvertErpSkuRequest erpSkuRequest = new ConvertErpSkuRequest();
            List<StoreSellerSkuRequest> storeSellerSkuRequestList = sellerSkus.stream().map(sellerSku -> {
                StoreSellerSkuRequest storeSellerSkuRequest = new StoreSellerSkuRequest();
                storeSellerSkuRequest.setStoreId(storeId);
                storeSellerSkuRequest.setSellerSku(sellerSku);
                return storeSellerSkuRequest;
            }).collect(Collectors.toList());
            erpSkuRequest.setSellerSkuStores(storeSellerSkuRequestList);

            ConvertErpSkuResponse convertErpSkuResponse = amazonFeignClient.convertErpSku(erpSkuRequest);

            if (Objects.isNull(convertErpSkuResponse) || NsyCollUtil.isEmpty(convertErpSkuResponse.getErpSkuMappingList())) {
                return Collections.emptyMap();
            }
            return convertErpSkuResponse.getErpSkuMappingList().stream().collect(Collectors.toMap(ErpSkuMappingResponse::getSellerSku, ErpSkuMappingResponse::getErpSku, (value1, value2) -> value2));
        } catch (Exception e) {
            log.error(String.format("调用amazon映射erpSku失败：%s", e.getMessage()), e);
        }
        return Collections.emptyMap();
    }

    public PlanUploadResponse uploadNonPackExcel(InboundPlanExcelRequest request,
                                                 Map<String, String> erpSkuMap,
                                                 Map<String, AmazonBrandStoreProductSkuResponse.AmazonBrandStoreProductSku> brandMap) {
        PlanUploadResponse uploadResponse = new PlanUploadResponse();
        List<InboundPlanExcelData> inboundPlanExcelDataList = request.getExcelDataRecords();
        inboundPlanExcelDataList.forEach(dataRow -> {
            dataRow.setErpSku(erpSkuMap.get(dataRow.getSellerSku()));
        });
        SaStoreDetailResponse store = storeService.getInfo(request.getStoreId());
        List<FbaInboundShipmentPlanItem> planItems = buildShipmentPlanItemList(inboundPlanExcelDataList, store, request.getSpaceId(), brandMap);
        uploadResponse.setFbaInboundShipmentPlanItemList(planItems);
        uploadResponse.setPlanSummary(buildPlanSummary(planItems));
        log.info("InboundCreateService.uploadNonPackExcel.uploadResponse:{}", JSON.toJSONString(uploadResponse));
        return uploadResponse;
    }

    public List<FbaInboundShipmentPlanItem> buildShipmentPlanItemList(List<InboundPlanExcelData> inboundPlanExcelDataList,
                                                                      SaStoreDetailResponse store,
                                                                      Integer spaceId,
                                                                      Map<String, AmazonBrandStoreProductSkuResponse.AmazonBrandStoreProductSku> brandMap) {
        // 1、汇总、去重Seller Sku
        List<InboundPlanExcelData> inboundPlanAggregationData = this.aggregateEstimatedShipmentQuantityBySellerSku(inboundPlanExcelDataList);
        // 2、批量获取SKU在ERP系统里的库存、待审占用数量、在途数量等信息
        List<String> erpSkuList = inboundPlanAggregationData.stream().map(InboundPlanExcelData::getErpSku).collect(Collectors.toList());
        Map<String, List<SpaceStockInfo>> erpSkuStockInfoMap = this.bulkGetErpSkuStockInfoListMap(store.getId(), spaceId, erpSkuList);

        Map<String, SpaceStockInfo> promotionStockMap = SpaceName.Main.getId() == spaceId ? this.bulkGetErpSkuStockInfoMap(store.getId(), SpaceName.Promotion.getId(), erpSkuList) : null;
        List<String> sellerSkuList = inboundPlanExcelDataList.stream().map(InboundPlanExcelData::getSellerSku).collect(Collectors.toList());

        Map<String, String> skuMappingMap = getSkuMappingMap(sellerSkuList, store.getId());
        String[] erpSkuArray = erpSkuList.toArray(new String[0]);
        List<Sku> erpSkuInfoList = erpApiService.getSkuInfoList(erpSkuArray);
        Map<String, Sku> skuMap = erpSkuInfoList.stream().collect(Collectors.toMap(Sku::getSku, Function.identity(), (k1, k2) -> k2));

        //刊登商品信息
        List<PublishProductSpec> publishProducts = getPublishProduct(store.getId(), sellerSkuList);
        publishProducts = publishProducts.stream().filter(x -> StrUtil.isNotEmpty(x.getGoodsId())).collect(Collectors.toList());
        Map<String, PublishProductSpec> publishProductSpecMap = publishProducts.stream().collect(Collectors.toMap(PublishProductSpec::getWebsiteItemSku, a -> a, (k1, k2) -> k1));

        return inboundPlanAggregationData.stream().map(data -> {
            SpaceStockInfo promotionStockInfo = promotionStockMap == null ? null : promotionStockMap.get(data.getErpSku());
            FbaInboundShipmentPlanItem fbaInboundShipmentPlanItem = this.buildPlanItems(data,
                    erpSkuStockInfoMap.get(data.getErpSku()),
                    promotionStockInfo,
                    brandMap.getOrDefault(data.getErpSku(), new AmazonBrandStoreProductSkuResponse.AmazonBrandStoreProductSku()));
            Sku erpSku = skuMap.get(skuMappingMap.get(fbaInboundShipmentPlanItem.getSellerSku()));
            if (Objects.nonNull(erpSku)) {
                fbaInboundShipmentPlanItem.setImageUrl(erpSku.getImageUrl());
                fbaInboundShipmentPlanItem.setColorSku(erpSku.getColorSku());
            }
            Optional.ofNullable(publishProductSpecMap.get(fbaInboundShipmentPlanItem.getSellerSku())).ifPresent(publishProductSpec -> {
                fbaInboundShipmentPlanItem.setBarcode(publishProductSpec.getBarcode());
                fbaInboundShipmentPlanItem.setGoodsId(publishProductSpec.getGoodsId());
                fbaInboundShipmentPlanItem.setGoodsName(publishProductSpec.getWebsiteItemName());
            });
            SpaceStockInfo newPromotionStockInfo = promotionStockMap == null ? null : promotionStockMap.get(data.getErpSku());
            List<SpaceStockInfo> newSpaceStockInfos = erpSkuStockInfoMap.get(data.getErpSku());
            this.buildNewPlanItem(fbaInboundShipmentPlanItem,
                    newSpaceStockInfos,
                    newPromotionStockInfo,
                    brandMap.getOrDefault(data.getErpSku(), new AmazonBrandStoreProductSkuResponse.AmazonBrandStoreProductSku()));
            return fbaInboundShipmentPlanItem;
        }).collect(Collectors.toList());
    }

    private void buildNewPlanItem(FbaInboundShipmentPlanItem planItem, List<SpaceStockInfo> newStockInfos, SpaceStockInfo newPromotionStockInfo, AmazonBrandStoreProductSkuResponse.AmazonBrandStoreProductSku amazonBrandStoreProductSku) {
        planItem.setNewPromotionAvailableQuantity(newPromotionStockInfo == null ? 0 : newPromotionStockInfo.getStock());
        planItem.setNewAvailableQuantity(this.getStock(planItem.getBrandId(), Boolean.TRUE, newStockInfos));
        planItem.setNewFbaOutOfStockQuantity(this.getFbaOutOfStockQuantity(planItem.getBrandId(), Boolean.TRUE, newStockInfos));
        planItem.setNewFbaPreMatchQuantity(this.getFbaPreMatchQuantity(planItem.getBrandId(), Boolean.TRUE, newStockInfos));
        planItem.setNewOnTheWayQuantityOfApplyOrder(this.getOnTheWayQuantityOfApplyOrder(planItem.getBrandId(), Boolean.TRUE, newStockInfos));
        planItem.setNewRemarks(this.setSkuRemark(newStockInfos, planItem.getBrandId(), Boolean.TRUE, planItem.getAllocatedQuantity(), planItem.getEstimatedShipmentQuantity()));
        Integer brandId = amazonBrandStoreProductSku.getBrandId();
        //无品牌标识的，就不要返回品牌库存了
        if (brandId == null) {
            planItem.setBrandStock(0);
            brandId = 0;
        }
        planItem.setNewBrandId(brandId);
        planItem.setNewBrandName(StringUtils.isBlank(amazonBrandStoreProductSku.getBrandName()) ? "" : amazonBrandStoreProductSku.getBrandName());
    }


    private FbaInboundShipmentPlanItem buildPlanItems(@NotNull InboundPlanExcelData excelData,
                                                      List<SpaceStockInfo> spaceStockInfos,
                                                      SpaceStockInfo promotionStockInfo,
                                                      AmazonBrandStoreProductSkuResponse.AmazonBrandStoreProductSku amazonBrandStoreProductSku) {
        FbaInboundShipmentPlanItem planItem = new FbaInboundShipmentPlanItem();
        planItem.setBrandId(Objects.isNull(amazonBrandStoreProductSku.getBrandId()) ? Integer.valueOf(0) : amazonBrandStoreProductSku.getBrandId());
        planItem.setBrandName(StringUtils.isBlank(amazonBrandStoreProductSku.getBrandName()) ? "" : amazonBrandStoreProductSku.getBrandName());
        planItem.setSellerSku(excelData.getSellerSku());
        planItem.setErpSku(excelData.getErpSku());
        // 优先使用库里存在的FnSku，库里面不存在，则使用用户上传的FnSku
        planItem.setEstimatedShipmentQuantity(excelData.getEstimatedShipmentQuantity());
        planItem.setBrandStock(this.getBrandStock(spaceStockInfos));
        planItem.setNotBrandStock(this.getNotBrandStock(spaceStockInfos));
        planItem.setAvailableQuantity(this.getStock(planItem.getBrandId(), Boolean.TRUE, spaceStockInfos));
        planItem.setFbaOutOfStockQuantity(this.getFbaOutOfStockQuantity(planItem.getBrandId(), Boolean.TRUE, spaceStockInfos));
        planItem.setFbaPreMatchQuantity(this.getFbaPreMatchQuantity(planItem.getBrandId(), Boolean.TRUE, spaceStockInfos));
        planItem.setOnTheWayQuantityOfApplyOrder(this.getOnTheWayQuantityOfApplyOrder(planItem.getBrandId(), Boolean.TRUE, spaceStockInfos));
        planItem.setPromotionAvailableQuantity(promotionStockInfo == null ? 0 : promotionStockInfo.getStock());

        //审核建议
        planItem.setRemarks(this.setSkuRemark(spaceStockInfos, planItem.getBrandId(), Boolean.TRUE, planItem.getAllocatedQuantity(), planItem.getEstimatedShipmentQuantity()));
        return planItem;
    }


    public int getOnTheWayQuantityOfApplyOrder(Integer brandId, boolean isCreate, List<SpaceStockInfo> stockInfos) {
        if (CollectionUtils.isEmpty(stockInfos)) {
            return 0;
        }
        boolean isBrand = brandId != null && brandId > 0;
        if (isCreate) {
            return isBrand ? stockInfos.stream().mapToInt(SpaceStockInfo::getOnTheWayQuantityOfApplyOrder).sum()
                    : stockInfos.stream().filter(spaceStockInfo -> Arrays.asList(2, 3).contains(spaceStockInfo.getBrandType())).mapToInt(SpaceStockInfo::getOnTheWayQuantityOfApplyOrder).sum();
        } else {
            return isBrand ? stockInfos.stream().filter(spaceStockInfo -> Arrays.asList(1, 3).contains(spaceStockInfo.getBrandType())).mapToInt(SpaceStockInfo::getOnTheWayQuantityOfApplyOrder).sum()
                    : stockInfos.stream().filter(spaceStockInfo -> Arrays.asList(2, 3).contains(spaceStockInfo.getBrandType())).mapToInt(SpaceStockInfo::getOnTheWayQuantityOfApplyOrder).sum();
        }
    }

    public int getFbaOutOfStockQuantity(Integer brandId, boolean isCreate, List<SpaceStockInfo> stockInfos) {
        if (CollectionUtils.isEmpty(stockInfos)) {
            return 0;
        }
        boolean isBrand = brandId != null && brandId > 0;
        if (isCreate) {
            return isBrand ? stockInfos.stream().mapToInt(SpaceStockInfo::getFbaOutOfStockQuantity).sum()
                    : stockInfos.stream().filter(spaceStockInfo -> Arrays.asList(2, 3).contains(spaceStockInfo.getBrandType())).mapToInt(SpaceStockInfo::getFbaOutOfStockQuantity).sum();
        } else {
            return isBrand ? stockInfos.stream().filter(spaceStockInfo -> Arrays.asList(1, 3).contains(spaceStockInfo.getBrandType())).mapToInt(SpaceStockInfo::getFbaOutOfStockQuantity).sum()
                    : stockInfos.stream().filter(spaceStockInfo -> Arrays.asList(2, 3).contains(spaceStockInfo.getBrandType())).mapToInt(SpaceStockInfo::getFbaOutOfStockQuantity).sum();
        }
    }

    public int getFbaPreMatchQuantity(Integer brandId, boolean isCreate, List<SpaceStockInfo> stockInfos) {
        if (CollectionUtils.isEmpty(stockInfos)) {
            return 0;
        }
        boolean isBrand = brandId != null && brandId > 0;
        if (isCreate) {
            return isBrand ? stockInfos.stream().mapToInt(SpaceStockInfo::getFbaPreMatchQuantity).sum()
                    : stockInfos.stream().filter(spaceStockInfo -> Arrays.asList(2, 3).contains(spaceStockInfo.getBrandType())).mapToInt(SpaceStockInfo::getFbaPreMatchQuantity).sum();
        } else {
            return isBrand ? stockInfos.stream().filter(spaceStockInfo -> Arrays.asList(1, 3).contains(spaceStockInfo.getBrandType())).mapToInt(SpaceStockInfo::getFbaPreMatchQuantity).sum()
                    : stockInfos.stream().filter(spaceStockInfo -> Arrays.asList(2, 3).contains(spaceStockInfo.getBrandType())).mapToInt(SpaceStockInfo::getFbaPreMatchQuantity).sum();
        }
    }

    public int getBrandStock(List<SpaceStockInfo> stockInfos) {
        if (CollectionUtils.isEmpty(stockInfos)) {
            return 0;
        }
        return stockInfos.stream().filter(spaceStockInfo -> Arrays.asList(1, 3).contains(spaceStockInfo.getBrandType())).mapToInt(SpaceStockInfo::getStockByBrand).sum();
    }

    public int getNotBrandStock(List<SpaceStockInfo> stockInfos) {
        if (CollectionUtils.isEmpty(stockInfos)) {
            return 0;
        }
        return stockInfos.stream().filter(spaceStockInfo -> Arrays.asList(2, 3).contains(spaceStockInfo.getBrandType())).mapToInt(SpaceStockInfo::getStockByNoBrand).sum();
    }


    /**
     * 判断库存是否充足
     * true: 预计发货数+待审已占用在途>ERP可用库存+在途数量
     * false: 与上面相反
     *
     * @param estimatedShipmentQuantity    预计发货数量
     * @param fbaOutOfStockQuantity        待审已占用在途
     * @param stock                        ERP可用库存
     * @param onTheWayQuantityOfApplyOrder 在途数量
     * @return true or false
     */
    public boolean isInventoryShortage(Integer estimatedShipmentQuantity, Integer fbaOutOfStockQuantity, Integer stock,
                                       Integer onTheWayQuantityOfApplyOrder) {
        int estimatedShipmentQty = estimatedShipmentQuantity == null ? 0 : estimatedShipmentQuantity;
        int fbaOutOfStockQty = fbaOutOfStockQuantity == null ? 0 : fbaOutOfStockQuantity;
        int stockQty = stock == null ? 0 : stock;
        int onTheWayQty = onTheWayQuantityOfApplyOrder == null ? 0 : onTheWayQuantityOfApplyOrder;
        return estimatedShipmentQty + fbaOutOfStockQty > stockQty + onTheWayQty;
    }

    public int getStock(Integer brandId, boolean isCreate, List<SpaceStockInfo> stockInfos) {
        if (CollectionUtils.isEmpty(stockInfos)) {
            return 0;
        }
        boolean isBrand = brandId != null && brandId > 0;
        if (isCreate) {
            return isBrand ? stockInfos.stream().mapToInt(SpaceStockInfo::getStock).sum()
                    : stockInfos.stream().filter(spaceStockInfo -> Arrays.asList(2, 3).contains(spaceStockInfo.getBrandType())).mapToInt(SpaceStockInfo::getStockByNoBrand).sum();
        } else {
            return isBrand ? stockInfos.stream().filter(spaceStockInfo -> Arrays.asList(1, 3).contains(spaceStockInfo.getBrandType())).mapToInt(SpaceStockInfo::getStockByBrand).sum()
                    : stockInfos.stream().filter(spaceStockInfo -> Arrays.asList(2, 3).contains(spaceStockInfo.getBrandType())).mapToInt(SpaceStockInfo::getStockByNoBrand).sum();
        }
    }


    public InboundShipmentPlanSummary buildPlanSummary(List<FbaInboundShipmentPlanItem> planItemList) {
        long count = planItemList.stream()
                .filter(item -> item.getEstimatedShipmentQuantity() > item.getAvailableQuantity())
                .count();
        InboundShipmentPlanSummary inboundShipmentPlanSummary = buildPlanSummary(planItemList.stream().mapToInt(FbaInboundShipmentPlanItem::getEstimatedShipmentQuantity).sum(),
                planItemList.size(),
                planItemList.stream().map(FbaInboundShipmentPlanItem::getRemarks).collect(Collectors.toList()));
        inboundShipmentPlanSummary.setOutOfStockCount(count);
        return inboundShipmentPlanSummary;
    }

    public InboundShipmentPlanSummary buildPlanSummary(Integer totalEstimatedShipmentQuantity, int skuCount, List<String> remarks) {
        InboundShipmentPlanSummary planSummary = new InboundShipmentPlanSummary();
        planSummary.setSkuCount(skuCount);
        planSummary.setTotalEstimatedShipmentQuantity(totalEstimatedShipmentQuantity);
        Integer availableSkuCount = 0;
        Integer needPurchaseSkuCount = 0;
        Integer cannotPurchaseSkuCount = 0;
        Integer errorSkuCount = 0;
        for (String remark : remarks) {
            SkuStockRemarkEnum skuStockRemarkEnum = SkuStockRemarkEnum.getSkuStockRemarkEnum(remark);
            if (skuStockRemarkEnum == null) {
                continue;
            }
            switch (skuStockRemarkEnum) {
                case AVAILABLE:
                    availableSkuCount++;
                    break;
                case NEED_PURCHASE:
                    needPurchaseSkuCount++;
                    break;
                case UNABLE_PURCHASE:
                case INVENTORY_SHORTAGE:
                    cannotPurchaseSkuCount++;
                    break;
                case SKU_ERROR:
                    errorSkuCount++;
                    break;
                default:
            }
        }
        planSummary.setAvailableSkuCount(availableSkuCount);
        planSummary.setNeedPurchaseSkuCount(needPurchaseSkuCount);
        planSummary.setCannotPurchaseSkuCount(cannotPurchaseSkuCount);
        planSummary.setErrorSkuCount(errorSkuCount);
        return planSummary;
    }

    private Map<String, AmazonBrandStoreProductSkuResponse.AmazonBrandStoreProductSku> buildBrandMap(InboundPlanExcelRequest request, List<String> erpSkuList) {
        //查询商品系统校验sku以及获取品牌,如果有问题直接报错
        AmazonBrandStoreProductSkuRequest amazonBrandStoreProductSkuRequest = new AmazonBrandStoreProductSkuRequest();
        List<AmazonBrandStoreProductSkuRequest.AmazonBrandStoreProductSku> list = new ArrayList<>();
        erpSkuList.forEach(sku -> {
            AmazonBrandStoreProductSkuRequest.AmazonBrandStoreProductSku amazonBrandStoreProductSku = new AmazonBrandStoreProductSkuRequest.AmazonBrandStoreProductSku();
            amazonBrandStoreProductSku.setSku(sku);
            amazonBrandStoreProductSku.setStoreId(request.getStoreId());
            list.add(amazonBrandStoreProductSku);
        });
        amazonBrandStoreProductSkuRequest.setList(list);
        amazonBrandStoreProductSkuRequest.setSpaceId(request.getSpaceId());
        amazonBrandStoreProductSkuRequest.setSpaceName(request.getSpaceName());

        AmazonBrandStoreProductSkuResponse amazonBrandStoreProductSkuResponse = bdBrandStoreSkcService.queryByStoreSku(amazonBrandStoreProductSkuRequest);

        if (StrUtil.isNotEmpty(amazonBrandStoreProductSkuResponse.getErrorMessage())) {
            throw new BusinessServiceException(amazonBrandStoreProductSkuResponse.getErrorMessage());
        }
        Map<String, AmazonBrandStoreProductSkuResponse.AmazonBrandStoreProductSku> brandMap;
        log.debug("InboundCreateService.buildBrandMap.amazonBrandStoreProductSkuResponse:{}", JSON.toJSONString(amazonBrandStoreProductSkuResponse));
        if (CollectionUtils.isEmpty(amazonBrandStoreProductSkuResponse.getList())) {
            brandMap = new HashMap<>();
        } else {
            brandMap = amazonBrandStoreProductSkuResponse.getList().stream().collect(Collectors.toMap(AmazonBrandStoreProductSkuResponse.AmazonBrandStoreProductSku::getSku, Function.identity(), (k1, k2) -> k2));
        }
        return brandMap;
    }


    /**
     * 汇总相同SKU的预计发货数量/补货数量
     *
     * @param fbaInboundPlanExcelDataList 用户上传数据
     * @return 汇总后的SKU数据
     */

    private List<InboundPlanExcelData> aggregateEstimatedShipmentQuantityBySellerSku(List<InboundPlanExcelData> fbaInboundPlanExcelDataList) {
        Map<String, Optional<InboundPlanExcelData>> inboundPlanAggregationDataMap = fbaInboundPlanExcelDataList.stream()
                .collect(Collectors.groupingBy(InboundPlanExcelData::getSellerSku,
                        Collectors.reducing((item1, item2) -> {
                            item1.setEstimatedShipmentQuantity(item1.getEstimatedShipmentQuantity() + item2.getEstimatedShipmentQuantity());
                            return item1;
                        })));
        return inboundPlanAggregationDataMap.values().stream().map(Optional::get).collect(Collectors.toList());
    }


    @Transactional
    public FbaInboundShipmentPlanCreateResponse createInboundShipmentPlan(StaShipmentPlanCreateRequest request) {
        FbaInboundShipmentPlanCreateResponse createResponse = new FbaInboundShipmentPlanCreateResponse();
        List<StaShipmentPlanItem> staShipmentPlanItemList = request.getStaShipmentPlanItemList();
        staShipmentPlanItemList.stream().filter(item -> Objects.nonNull(item.getBrandId())).collect(Collectors.groupingBy(StaShipmentPlanItem::getBrandId)).forEach((brandId, staShipmentPlanItems) -> {
            Preconditions.checkArgument(staShipmentPlanItems.size() <= 800, "补货单sku不能超过800个!");
        });
        List<StaShipmentPlanItem> staShipmentPlanItems = staShipmentPlanItemList.stream().filter(item -> Objects.isNull(item.getBrandId())).collect(Collectors.toList());
        Preconditions.checkArgument(staShipmentPlanItems.size() <= 800, "补货单sku不能超过800个!");
        // 按 erpSku 分组，统计每个 erpSku 出现的次数
        checkExistRepeatErpSku(staShipmentPlanItemList);
        //调整完的plan单
        StaShipmentPlan requestStaShipmentPlan = request.getStaShipmentPlan();
        List<StaShipmentPlanItem> planItemList = getPlanItemList(request);
        Integer storeId = requestStaShipmentPlan.getStoreId();
        SaStoreEntity amazonStore = storeService.getById(storeId);
        Set<String> erpSkuSet = planItemList.stream().map(StaShipmentPlanItem::getErpSku).collect(Collectors.toSet());
        Map<String, List<SpaceStockInfo>> skuStockInfoMap = bulkGetErpSkuStockInfoListMap(storeId, requestStaShipmentPlan.getSpaceId(), new ArrayList<>(erpSkuSet));

        log.info("createInboundShipmentPlan.skuStockInfoMap:{}", JSON.toJSONString(skuStockInfoMap));
        this.verifyData(request, skuStockInfoMap);
        //根据仓库库存处理item
        Integer planId = persistPlanGroupByBrand(planItemList, requestStaShipmentPlan, skuStockInfoMap, amazonStore);
        createResponse.setPlanIdList(Arrays.asList(planId));
        return createResponse;
    }

    private List<StaShipmentPlanItem> getPlanItemList(StaShipmentPlanCreateRequest request) {
        List<StaShipmentPlanItem> planItemList = request.getStaShipmentPlanItemList();
        if (Optional.ofNullable(request.getBrandTypeStockCreate()).isPresent() && 0 == request.getBrandTypeStockCreate()) {
            planItemList.forEach(planItem -> {
                planItem.setBrandId(0);
                planItem.setBrandName("");
            });
        }
        return planItemList;
    }


    private static void checkExistRepeatErpSku(List<StaShipmentPlanItem> staShipmentPlanItemList) {
        Map<String, Long> erpSkuCountMap = staShipmentPlanItemList.stream().collect(Collectors.groupingBy(StaShipmentPlanItem::getErpSku, Collectors.counting()));
        List<String> duplicateErpSkus = erpSkuCountMap.entrySet().stream()
                .filter(entry -> entry.getValue() > 1) // 出现次数大于 1
                .map(Map.Entry::getKey) // 提取 erpSku
                .collect(Collectors.toList());
        if (!duplicateErpSkus.isEmpty()) {
            throw new BusinessServiceException(StrUtil.format("存在重复ERPSku{},请检查", duplicateErpSkus));
        }
    }

    public Map<String, List<SpaceStockInfo>> bulkGetErpSkuStockInfoListMap(Integer storeId, Integer spaceId, List<String> erpSkuList) {
        Integer associatedStoreId = saStoreDao.getAssociatedStoreIdWithThrowEx(storeId);
        List<SpaceStockInfo> stockInfoList = ListUtils.partition(erpSkuList, 100).stream().map(partition -> {
            GetSpaceStockRequest getSpaceStockRequest = new GetSpaceStockRequest();
            getSpaceStockRequest.setErpSku(partition);
            getSpaceStockRequest.setDisAInfoId(associatedStoreId);
            getSpaceStockRequest.setSpaceId(spaceId);
            getSpaceStockRequest.setFbaStore(true);
            return Optional.ofNullable(erpApiService.getStockInfoBySpaceList(getSpaceStockRequest))
                    .map(GetSpaceStockResponse::getSpaceStockInfos)
                    .filter(CollectionUtils::isNotEmpty)
                    .orElseGet(Collections::emptyList);
        }).flatMap(List::stream).collect(Collectors.toList());
        Map<String, List<SpaceStockInfo>> result = CollectionUtils.isEmpty(stockInfoList) ? Collections.emptyMap() : stockInfoList.stream().collect(Collectors.groupingBy(SpaceStockInfo::getErpSku));
        log.info("StaShipmentPlanCreateService.bulkGetErpSkuStockInfoListMap.SpaceStockInfoMap:{}", JSON.toJSONString(result));
        return result;
    }

    public void verifyData(StaShipmentPlanCreateRequest request, Map<String, List<SpaceStockInfo>> skuStockInfoMap) {
        StaShipmentPlan plan = request.getStaShipmentPlan();
        List<StaShipmentPlanItem> planItemList = request.getStaShipmentPlanItemList();
        //如果fba_inbound_shipment_plan_id前端有传数据。要校验数据是否还存在
        if (ObjectUtil.isNotEmpty(plan.getId()) && inboundPlanService.getById(plan.getId()) != null) {
            throw new BusinessServiceException("创建失败，补货单不存在");
        }

        // 校验sku是否有误
        List<String> skuErrorList = planItemList.stream()
                .filter(item -> SkuStockRemarkEnum.SKU_ERROR.getRemark().equals(item.getRemarks()))
                .map(StaShipmentPlanItem::getSellerSku).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(skuErrorList)) {
            throw new BusinessServiceException(StrUtil.format("存在有误的数据sku[{}],不能创建计划单: ", String.join(",", skuErrorList)));
        }

        List<String> inventoryShortageSellerSkuList = this.verifyErpInventory(planItemList, skuStockInfoMap);
        if (CollectionUtils.isNotEmpty(inventoryShortageSellerSkuList)) {
            throw new BusinessServiceException(StrUtil.format("以下Seller Sku[{}]的库存不足【预计发货数>ERP可用库存】, 请调整: ", String.join(",", inventoryShortageSellerSkuList)));
        }
    }

    private List<String> verifyErpInventory(List<StaShipmentPlanItem> planItemList, Map<String, List<SpaceStockInfo>> skuStockInfoMap) {
        List<String> inventoryShortageSellerSkuList = new ArrayList<>();
        for (StaShipmentPlanItem item : planItemList) {
            List<SpaceStockInfo> spaceStockInfo = skuStockInfoMap.get(item.getErpSku());
            if (Objects.isNull(spaceStockInfo) || this.isInventoryShortage(
                    item.getEstimatedShipmentQuantity(),
                    this.getFbaOutOfStockQuantity(item.getBrandId(), Boolean.TRUE, spaceStockInfo),
                    this.getStock(item.getBrandId(), Boolean.TRUE, spaceStockInfo),
                    this.getOnTheWayQuantityOfApplyOrder(item.getBrandId(), Boolean.TRUE, spaceStockInfo))) {
                inventoryShortageSellerSkuList.add(item.getSellerSku());
            }
        }
        return inventoryShortageSellerSkuList;
    }

    private Integer persistPlanGroupByBrand(List<StaShipmentPlanItem> staShipmentPlanItemList,
                                            StaShipmentPlan requestStaShipmentPlan,
                                            Map<String, List<SpaceStockInfo>> skuStockInfoMap,
                                            SaStoreEntity amazonStore) {
        log.info("StaShipmentPlanCreateService.persistPlanGroupByBrand.staShipmentPlanItemList:{}", JSON.toJSONString(staShipmentPlanItemList));
        Map<Integer, List<StaShipmentPlanItem>> staShipmentPlanItemMap = staShipmentPlanItemList.stream().collect(Collectors.groupingBy(StaShipmentPlanItem::getBrandId));
        if (MapUtils.isNotEmpty(staShipmentPlanItemMap) && staShipmentPlanItemMap.size() > 1) {
            List<String> brandSukInfo = new ArrayList<>();
            staShipmentPlanItemList.stream().filter(x -> x.getBrandName() == null).forEach(x -> x.setBrandName(""));
            staShipmentPlanItemList.stream().collect(Collectors.groupingBy(StaShipmentPlanItem::getBrandName)).forEach((k, v) -> {
                brandSukInfo.add(String.format("%s:%s", StringUtils.isEmpty(k) ? "无品牌" : k, v.stream().map(StaShipmentPlanItem::getSellerSku).collect(Collectors.toList())));
            });
            throw new BusinessServiceException(String.format("品牌商品同时存在品牌和非品牌库存无法创建补货单请先确认是否先【强制使用非品牌库存】或【强制使用品牌库存】再继续点创建,明细如下：%s", String.join(";", brandSukInfo)));
        }

        Integer id = requestStaShipmentPlan.getId();
        String userName = loginInfoService.getName();
        SaStoreConfigEntity storeConfigEntity = saStoreConfigService.getInfo(amazonStore.getId());
        InboundPlanEntity planEntity = Objects.nonNull(id) ? inboundPlanService.getById(id) : new InboundPlanEntity();
        planEntity.setCreateBy(userName);
        planEntity.setStoreId(amazonStore.getId());
        planEntity.setStoreName(amazonStore.getErpStoreName());
        planEntity.setStatus(InboundPlanStatusEnum.WAITING_AUDIT.getStatus());
        planEntity.setLogisticsCompanyId(requestStaShipmentPlan.getLogisticsCompanyId());
        planEntity.setLogisticsCompanyName(requestStaShipmentPlan.getLogisticsCompanyName());
        String location = amazonStore.getLocation().toUpperCase(Locale.ROOT);
        planEntity.setLocation(location);
        planEntity.setSpaceId(requestStaShipmentPlan.getSpaceId());
        planEntity.setPlatform(requestStaShipmentPlan.getPlatform());
        planEntity.setErpTid(generatePlanErpTid(location));
        planEntity.setBrandId(staShipmentPlanItemList.get(0).getBrandId());
        planEntity.setBrandName(staShipmentPlanItemList.get(0).getBrandName());
        planEntity.setSpaceId(requestStaShipmentPlan.getSpaceId());
        planEntity.setSpaceName(requestStaShipmentPlan.getSpaceName());
        String remark = requestStaShipmentPlan.getRemark();
        if (storeConfigEntity != null && storeConfigEntity.getNeedConvertBarcode()) {
            remark = "需贴公司条码" + remark;
        }
        planEntity.setRemark(remark);
        planEntity.setUpdateBy(userName);
        inboundPlanService.save(planEntity);
        redisClient.incr(buildInboundIdxRedisKey(location), 1);
        this.persistFbaPlanItem(staShipmentPlanItemList, skuStockInfoMap, planEntity.getId());
        //预留审核
//        if (!storeConfigEntity.getInboundNeedCheck()) {
        this.preAllocateStock(planEntity);
//        }
        return planEntity.getId();
    }

    public void persistFbaPlanItem(List<StaShipmentPlanItem> planItemList, Map<String, List<SpaceStockInfo>> skuStockInfoMap, Integer planId) {
        List<InboundPlanItemEntity> fbaInboundShipmentPlanItemEntities = planItemList.stream().map(item -> {
            InboundPlanItemEntity itemEntity = Objects.isNull(item.getId()) ? new InboundPlanItemEntity() : itemEntityService.getById(item.getId());
            itemEntity.setSellerSku(item.getSellerSku());
            itemEntity.setPlanId(planId);
            itemEntity.setErpSku(item.getErpSku());
            itemEntity.setBarcode(item.getBarcode());
            itemEntity.setGoodsId(item.getGoodsId());
            itemEntity.setGoodsName(item.getGoodsName());
            itemEntity.setBrandId(item.getBrandId());
            itemEntity.setBrandName(item.getBrandName());
            itemEntity.setEstimatedShipmentQuantity(item.getEstimatedShipmentQuantity());
            itemEntity.setCreateBy(loginInfoService.getName());
            List<SpaceStockInfo> spaceStockInfos = skuStockInfoMap.get(item.getErpSku());
            itemEntity.setAvailableQuantity(this.getStock(itemEntity.getBrandId(), Boolean.TRUE, spaceStockInfos));
            String skuRemark = this.setSkuRemark(spaceStockInfos, item.getBrandId(), Boolean.TRUE, item.getEstimatedShipmentQuantity());
            itemEntity.setRemark(skuRemark);
            return itemEntity;
        }).collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(fbaInboundShipmentPlanItemEntities)) {
            itemEntityService.saveBatch(fbaInboundShipmentPlanItemEntities);
        }
    }


    public String setSkuRemark(List<SpaceStockInfo> stockInfos, Integer brandId, boolean isCreate, Integer allocatedQuantity, Integer estimatedShipmentQuantity) {
        if (CollectionUtils.isEmpty(stockInfos)) {
            return SkuStockRemarkEnum.SKU_ERROR.getRemark();
        }

        SpaceStockInfo spaceStockInfo = stockInfos.stream().findFirst().orElseThrow(() -> new BusinessServiceException("StaShipmentPlanCreateService.setSkuRemark.获取erp库存异常"));
        if (SkuStatusEnum.NoneSku.toString().equalsIgnoreCase(spaceStockInfo.getStatus())) {
            return SkuStockRemarkEnum.SKU_ERROR.getRemark();
        }

        if (isInventoryShortage(estimatedShipmentQuantity,
                this.getFbaOutOfStockQuantity(brandId, isCreate, stockInfos),
                this.getStock(brandId, Boolean.TRUE, stockInfos),
                this.getOnTheWayQuantityOfApplyOrder(brandId, isCreate, stockInfos))) {
            return SkuStockRemarkEnum.INVENTORY_SHORTAGE.getRemark();
        }

        if (Objects.nonNull(allocatedQuantity)) {
            return allocatedQuantity < estimatedShipmentQuantity ? SkuStockRemarkEnum.NEED_PURCHASE.getRemark() : SkuStockRemarkEnum.AVAILABLE.getRemark();
        }

        if (this.getStock(brandId, Boolean.TRUE, stockInfos) < estimatedShipmentQuantity) {
            return SkuStockRemarkEnum.NEED_PURCHASE.getRemark();
        }
        return SkuStockRemarkEnum.AVAILABLE.getRemark();
    }

    public String setSkuRemark(List<SpaceStockInfo> stockInfos, Integer brandId, boolean isCreate, Integer estimatedShipmentQuantity) {
        if (CollectionUtils.isEmpty(stockInfos)) {
            return SkuStockRemarkEnum.SKU_ERROR.getRemark();
        }

        SpaceStockInfo spaceStockInfo = stockInfos.stream().findFirst().orElseThrow(() -> new BusinessServiceException("StaShipmentPlanCreateService.setSkuRemark.获取erp库存异常"));
        if (SkuStatus.NoneSku.toString().equalsIgnoreCase(spaceStockInfo.getStatus())) {
            return SkuStockRemarkEnum.SKU_ERROR.getRemark();
        }

        if (isInventoryShortage(estimatedShipmentQuantity,
                this.getFbaOutOfStockQuantity(brandId, isCreate, stockInfos),
                this.getStock(brandId, Boolean.TRUE, stockInfos),
                this.getOnTheWayQuantityOfApplyOrder(brandId, isCreate, stockInfos))) {
            return SkuStockRemarkEnum.INVENTORY_SHORTAGE.getRemark();
        }
        if (this.getStock(brandId, Boolean.TRUE, stockInfos) < estimatedShipmentQuantity) {
            return SkuStockRemarkEnum.NEED_PURCHASE.getRemark();
        }
        return SkuStockRemarkEnum.AVAILABLE.getRemark();
    }

    public void preAllocateStock(InboundPlanEntity planEntity) {
        // 系统自动审核通过---写日志并更改状态 (WAITING_AUDIT ---->WAITING_ALLOCATE)
        selfService.auditStatus(FbaAuditStatusEnum.APPROVE.toString(), "系统自动审核", planEntity);
        // 预配库存 状态置为 WAITING_ALLOCATE ---> ALLOCATED
        this.preMatchStock(planEntity);

    }

    @Transactional
    public void auditStatus(String auditStatus, String auditRemarks, InboundPlanEntity planEntity) {
        if (auditStatus.equalsIgnoreCase(FbaAuditStatusEnum.APPROVE.toString())) {
            planEntity.setStatus(InboundPlanStatusEnum.WAITING_ALLOCATE.status());
        } else {
            planEntity.setStatus(InboundPlanStatusEnum.REJECTED.status());
        }
        planEntity.setAuditRemarks(StrUtil.isNotEmpty(auditRemarks) ? auditRemarks : "");
        planEntity.setUpdateBy(loginInfoService.getName());
        inboundPlanService.updateById(planEntity);
        logEntityService.saveFbaInboundShipmentPlanLog(planEntity.getId(), "补货单审核", "补货单-" + planEntity.getStatus(), planEntity.getStatus());
    }


    public void preMatchStock(InboundPlanEntity planEntity) {
        Integer planId = planEntity.getId();
        log.info("planId:{}, 开始预配库存。。。", planId);
        AddFbaShipmentPlanRequest addFbaShipmentPlanRequest = buildErpAddFbaShipmentPlanRequest(planEntity);
        AddFbaShipmentPlanResponse addFbaShipmentPlanResponse = erpApiService.addFbaShipmentPlan(addFbaShipmentPlanRequest);
        log.info("开始预配库存" + JSONUtil.toJsonStr(addFbaShipmentPlanResponse));
//        if (null != addFbaShipmentPlanResponse && null != addFbaShipmentPlanResponse.getFbaTradeInfo()) {
//            planEntity.setStatus(InboundPlanStatusEnum.ALLOCATED.status());
//            inboundPlanEntityService.updateById(planEntity);
//
//            //日志
//            logEntityService.saveFbaInboundShipmentPlanLog(planId, "预配库存", "FBA补货单-" + planEntity.getStatus(), planEntity.getStatus());
//        }
        planEntity.setStatus(InboundPlanStatusEnum.ALLOCATED.status());
        inboundPlanService.updateById(planEntity);

        //日志
        logEntityService.saveFbaInboundShipmentPlanLog(planId, "预配库存", "补货单-" + planEntity.getStatus(), planEntity.getStatus());
        log.info("planId:{}, 预配库存结束。。。", planId);
    }

    private AddFbaShipmentPlanRequest buildErpAddFbaShipmentPlanRequest(InboundPlanEntity planEntity) {
        AddFbaShipmentPlanRequest addFbaShipmentPlanRequest = new AddFbaShipmentPlanRequest();
        FbaTradeInfo fbaTradeInfo = new FbaTradeInfo();
        //得改成关联店铺id
        fbaTradeInfo.setDisAInfoId(saStoreDao.getAssociatedStoreIdWithThrowEx(planEntity.getStoreId()));
        fbaTradeInfo.setPlanId(String.valueOf(planEntity.getId()));
        fbaTradeInfo.setSpaceId(planEntity.getSpaceId());
        fbaTradeInfo.setCreateBy(loginInfoService.getName());
        fbaTradeInfo.setReceiverName(planEntity.getCreateBy());
//        fbaTradeInfo.setReceiverPhone(Optional.ofNullable(storeCacheService.getByStoreId(planEntity.getAmazonStoreId())).map(AmazonStore::getAmazonStoreAddressInfo).map(AmazonStoreAddressInfo::getShipAddressShipperPhone).orElse(""));
        fbaTradeInfo.setMode(4);
        fbaTradeInfo.setLogisticsCompanyId(planEntity.getLogisticsCompanyId());
        fbaTradeInfo.setLogisticsCompanyName(planEntity.getLogisticsCompanyName());
        fbaTradeInfo.setSellerMemo(planEntity.getRemark());
        fbaTradeInfo.setCreateByApi(false);
        fbaTradeInfo.setIsBrand(Optional.ofNullable(planEntity.getBrandId()).isPresent() && planEntity.getBrandId() > 0);
        fbaTradeInfo.setPlatform(PlatformEnum.TIKTOK.name());
        fbaTradeInfo.setErpTid(planEntity.getErpTid());
        fbaTradeInfo.setSellerMemo(planEntity.getRemark());
        List<InboundPlanItemEntity> inboundShipmentPlanItemEntities = itemEntityService.getByPlanId(planEntity.getId());
        List<FbaOrderInfo> fbaOrderInfoList = inboundShipmentPlanItemEntities.stream()
                .map(itemEntity -> {
                    FbaOrderInfo fbaOrderInfo = new FbaOrderInfo();
                    fbaOrderInfo.setErpSku(itemEntity.getErpSku());
                    fbaOrderInfo.setSellerSku(itemEntity.getSellerSku());
                    fbaOrderInfo.setNum(itemEntity.getEstimatedShipmentQuantity());
                    fbaOrderInfo.setSpecId(0);
                    fbaOrderInfo.setProductId(0);
                    fbaOrderInfo.setBrandId(itemEntity.getBrandId());
                    fbaOrderInfo.setBrandName(itemEntity.getBrandName());
                    fbaOrderInfo.setFnSku(itemEntity.getBarcode());
                    fbaOrderInfo.setTitle(itemEntity.getGoodsName());
                    return fbaOrderInfo;
                }).collect(Collectors.toList());
        fbaTradeInfo.setFbaOrderInfos(fbaOrderInfoList);
        addFbaShipmentPlanRequest.setFbaTradeInfo(fbaTradeInfo);
        return addFbaShipmentPlanRequest;
    }

    public Map<String, SpaceStockInfo> bulkGetErpSkuStockInfoMap(Integer amazonStoreId, Integer spaceId, List<String> erpSkuList) {
        List<SpaceStockInfo> stockInfoList = ListUtils.partition(erpSkuList, 100).stream().map(partition -> {
            GetSpaceStockRequest getSpaceStockRequest = new GetSpaceStockRequest();
            getSpaceStockRequest.setErpSku(partition);
            getSpaceStockRequest.setDisAInfoId(amazonStoreId);
            getSpaceStockRequest.setSpaceId(spaceId);
            getSpaceStockRequest.setFbaStore(true);
            return Optional.ofNullable(erpApiService.getStockInfo(getSpaceStockRequest))
                    .map(GetSpaceStockResponse::getSpaceStockInfos)
                    .filter(CollectionUtils::isNotEmpty)
                    .orElseGet(Collections::emptyList);
        }).flatMap(List::stream).collect(Collectors.toList());
        return stockInfoList.stream().collect(Collectors.toMap(SpaceStockInfo::getErpSku, Function.identity(), (v1, v2) -> v2));
    }


    public void close(StaShipmentPlanCloseRequest request) {
        InboundPlanEntity planEntity = inboundPlanService.getById(request.getPlanId());
        dealCloseProcess(planEntity);
    }

    private void dealCloseProcess(InboundPlanEntity shipmentPlanEntity) {
        String erpTid = shipmentPlanEntity.getErpTid();
        if (!InboundPlanStatusEnum.WAITING_AUDIT.getStatus().equals(shipmentPlanEntity.getStatus())) {
            erpApiService.cancelFbaShipmentPlan(Collections.singletonList(erpTid), 0);
        }
        inboundPlanService.updateStatusByErpTid(erpTid, InboundPlanStatusEnum.CLOSED.status());
    }

    @Transactional
    public void backToBoxStatus(Integer planId) {
        InboundPlanEntity planEntity = inboundPlanService.getById(planId);
        if (ObjectUtil.isEmpty(planEntity)) {
            throw new BusinessServiceException("该补货计划单不存在");
        }
        if (!InboundPlanStatusEnum.WAITING_SHIPMENT.getStatus().equals(planEntity.getStatus())) {
            throw new BusinessServiceException("补货状态为待发货,才允许回退为已装箱");
        }
        List<InboundShipmentEntity> shipmentEntities = inboundShipmentService.getByPlanIds(Collections.singletonList(planId));
        List<String> shipmentIds = shipmentEntities.stream().map(InboundShipmentEntity::getShipmentId).collect(Collectors.toList());
        //删除fba_inbound_shipment
        inboundShipmentService.deleteByFbaInboundShipmentPlanId(planId);
        //删除fba_inbound_shipment_item
        inboundShipmentItemService.deleteByShipmentIds(shipmentIds);
        inboundPlanService.updateStatusByErpTid(planEntity.getErpTid(), InboundPlanStatusEnum.BOXED.status());
    }

    public List<SelectModel> getTikTokStore() {
        return storeService.getSaleStoreByPermission(PlatformEnum.TIKTOK.name());
    }


    public List<SelectModel> logisticList() {
        return tmsApiService.getLogisticsCompanyByDeliverLocation(loginInfoService.getLocation());
    }


    @Autowired
    private RedisClient redisClient;

    /**
     * 生成规则：FBT-BH+“公司首字母，比如QZ”+日期+计数，比如FBT-BHQZ20250205-0001；
     */
    public String generatePlanErpTid(String location) {
        LocationEnum deliveryLocationEnum = LocationEnum.valueOf(location);
        String prefix = "";
        switch (deliveryLocationEnum) {
            case QUANZHOU:
                prefix = "QZ";
                break;
            case XIAMEN:
                prefix = "XM";
                break;
            case GUANGZHOU:
                prefix = "GZ";
                break;
            case MISI:
                prefix = "MS";
                break;
            case TAILI:
                prefix = "TL";
                break;
            case WEIYUE:
                prefix = "WY";
                break;
            default:
        }
        return StrUtil.format("FBT-BH{}{}-{}",
                prefix,
                DateUtil.format(new Date(), "yyyyMMdd"),
                String.format("%04d", redisClient.getAndCreateDailyKey(buildInboundIdxRedisKey(location))));
    }

    public static String buildInboundIdxRedisKey(String location) {
        return INBOUND_COUNT_PREFIX + location;
    }

}
