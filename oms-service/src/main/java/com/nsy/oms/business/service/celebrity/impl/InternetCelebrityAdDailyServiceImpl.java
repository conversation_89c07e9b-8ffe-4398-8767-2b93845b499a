package com.nsy.oms.business.service.celebrity.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.oms.business.service.celebrity.InternetCelebrityAdDailyService;
import com.nsy.oms.repository.entity.celebrity.InternetCelebrityAdDailyEntity;
import com.nsy.oms.repository.sql.mapper.celebrity.InternetCelebrityAdDailyMapper;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class InternetCelebrityAdDailyServiceImpl
        extends ServiceImpl<InternetCelebrityAdDailyMapper, InternetCelebrityAdDailyEntity>
        implements InternetCelebrityAdDailyService {

    @Override
    public InternetCelebrityAdDailyEntity findTopByStoreIdAndAdNoAndVideoNoAndAdDate(Integer storeId, String adNo, String videoNo, Date adDate) {
        return this.getOne(new QueryWrapper<InternetCelebrityAdDailyEntity>().lambda()
                .eq(InternetCelebrityAdDailyEntity::getStoreId, storeId)
                .eq(InternetCelebrityAdDailyEntity::getAdNo, adNo)
                .eq(InternetCelebrityAdDailyEntity::getVideoNo, videoNo)
                .eq(InternetCelebrityAdDailyEntity::getAdDate, adDate)
                .last("limit 1")
        );
    }

    @Override
    public List<InternetCelebrityAdDailyEntity> findAllByPostIdIn(List<Integer> postIds) {
        return this.list(new QueryWrapper<InternetCelebrityAdDailyEntity>().lambda()
                .in(InternetCelebrityAdDailyEntity::getPostId, postIds)
        );
    }

    @Override
    public List<InternetCelebrityAdDailyEntity> findAllByStoreIdAndVideoNo(Integer storeId, String videoNo) {
        return this.list(new QueryWrapper<InternetCelebrityAdDailyEntity>().lambda()
                .eq(InternetCelebrityAdDailyEntity::getStoreId, storeId)
                .eq(InternetCelebrityAdDailyEntity::getVideoNo, videoNo)
        );
    }
}