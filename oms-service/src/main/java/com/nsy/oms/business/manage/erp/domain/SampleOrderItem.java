package com.nsy.oms.business.manage.erp.domain;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Date;

/**
 * @description:
 * @author: linCheng
 * @create: 2024-09-09 09:04
 **/
public class SampleOrderItem {

    @JsonProperty("Sku")
    private String sku;
    @JsonProperty("SellerSku")
    private String sellerSku;
    @JsonProperty("Qty")
    private Integer qty;
    @JsonProperty("sellerSkuId")
    private String sellerSkuId;
    @JsonProperty("sellerProductId")
    private String sellerProductId;
    @JsonProperty("DeliveryType")
    private String deliveryType;
    @JsonProperty("DeliveryStoreId")
    private Integer deliveryStoreId;
    @JsonProperty("PlatformOriginalOrderNo")
    private String platformOriginalOrderNo;
    @JsonProperty("LogisticsNo")
    private String logisticsNo;
    @JsonProperty("OrderDeliveryDate")
    private Date orderDeliveryDate;

    public Integer getQty() {
        return qty;
    }

    public void setQty(Integer qty) {
        this.qty = qty;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getSellerSku() {
        return sellerSku;
    }

    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    public String getSellerSkuId() {
        return sellerSkuId;
    }

    public void setSellerSkuId(String sellerSkuId) {
        this.sellerSkuId = sellerSkuId;
    }

    public String getSellerProductId() {
        return sellerProductId;
    }

    public void setSellerProductId(String sellerProductId) {
        this.sellerProductId = sellerProductId;
    }

    public String getDeliveryType() {
        return deliveryType;
    }

    public void setDeliveryType(String deliveryType) {
        this.deliveryType = deliveryType;
    }

    public Integer getDeliveryStoreId() {
        return deliveryStoreId;
    }

    public void setDeliveryStoreId(Integer deliveryStoreId) {
        this.deliveryStoreId = deliveryStoreId;
    }

    public String getPlatformOriginalOrderNo() {
        return platformOriginalOrderNo;
    }

    public void setPlatformOriginalOrderNo(String platformOriginalOrderNo) {
        this.platformOriginalOrderNo = platformOriginalOrderNo;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public Date getOrderDeliveryDate() {
        return orderDeliveryDate;
    }

    public void setOrderDeliveryDate(Date orderDeliveryDate) {
        this.orderDeliveryDate = orderDeliveryDate;
    }
}
