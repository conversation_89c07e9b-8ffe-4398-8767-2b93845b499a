package com.nsy.oms.business.service.stockout.impl;

import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.api.core.apicore.util.NsyCollUtil;
import com.nsy.oms.business.domain.request.stockout.GetBySkuBySkcRequest;
import com.nsy.oms.business.domain.request.stockout.OverseaSkcPageRequest;
import com.nsy.oms.business.domain.response.stockout.OverseaOnTheWayStockItemResponse;
import com.nsy.oms.business.domain.response.stockout.OverseaOnTheWayStockResponse;
import com.nsy.oms.business.domain.response.stockout.OverseaSkcPageResponse;
import com.nsy.oms.business.domain.response.stockout.OverseaSkuSpaceItemResponse;
import com.nsy.oms.business.domain.response.stockout.OverseaSkuStoreSpaceStockoutResponse;
import com.nsy.oms.business.manage.bi.BiApiService;
import com.nsy.oms.business.manage.erp.ErpApiService;
import com.nsy.oms.business.manage.erp.domain.SpaceStockInfo;
import com.nsy.oms.business.manage.erp.request.GetSpaceStockRequest;
import com.nsy.oms.business.manage.erp.response.GetSpaceStockResponse;
import com.nsy.oms.business.manage.scm.ScmApiService;
import com.nsy.oms.business.manage.scm.request.GetEstimatedArrivalDateRequest;
import com.nsy.oms.business.manage.scm.request.SpaceInfoRequest;
import com.nsy.oms.business.manage.scm.response.GetEstimatedArrivalDateResponse;
import com.nsy.oms.business.manage.wms.WmsApiService;
import com.nsy.oms.business.manage.wms.request.StockTransferTrackingDetailRequest;
import com.nsy.oms.business.manage.wms.request.StockTransferTrackingSkuRequest;
import com.nsy.oms.business.manage.wms.response.StockTransferTrackingDetailResponse;
import com.nsy.oms.business.manage.wms.response.StockTransferTrackingSkuResponse;
import com.nsy.oms.business.service.stockout.OverseaSkcService;
import com.nsy.oms.utils.SetValueUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class OverseaSkcServiceImpl implements OverseaSkcService {

    @Autowired
    private BiApiService biApiService;
    @Autowired
    private ErpApiService erpApiService;
    @Autowired
    private WmsApiService wmsApiService;
    @Autowired
    private ScmApiService scmApiService;

    @Override
    public PageResponse<OverseaSkcPageResponse> page(OverseaSkcPageRequest request) {
        PageResponse<OverseaSkcPageResponse> overseaSkcPage = biApiService.getOverseaSkcPage(request);
        if (NsyCollUtil.isEmpty(overseaSkcPage.getContent())) {
            return overseaSkcPage;
        }
        List<String> skuList = overseaSkcPage.getContent().stream().map(OverseaSkcPageResponse::getSku).collect(Collectors.toList());
        List<Integer> spaceIdList = overseaSkcPage.getContent().stream().flatMap(skcPageResponse -> skcPageResponse.getSpaceItemList().stream()).map(OverseaSkuSpaceItemResponse::getSpaceId).collect(Collectors.toList());
        // 调用商通获取 可用库存
        getErpAvailableStock(overseaSkcPage, skuList, spaceIdList);
        // 调用仓库获取在途库存
        getWmsOnTheWayStock(overseaSkcPage);
        // 计算可售天数
        calculateAvailableDays(overseaSkcPage.getContent());
        return overseaSkcPage;
    }

    private void getErpAvailableStock(PageResponse<OverseaSkcPageResponse> overseaSkcPage, List<String> skuList, List<Integer> spaceIdList) {
        GetSpaceStockRequest stockRequest = new GetSpaceStockRequest();
        stockRequest.setErpSku(skuList);
        stockRequest.setSpaceIdList(spaceIdList);
        GetSpaceStockResponse stockInfoBySpaceList = erpApiService.getStockInfo(stockRequest);
        if (Objects.nonNull(stockInfoBySpaceList) && NsyCollUtil.isNotEmpty(stockInfoBySpaceList.getSpaceStockInfos())) {
            overseaSkcPage.getContent().forEach(skcPage -> {
                skcPage.getSpaceItemList().forEach(spaceItem -> {
                    Optional<SpaceStockInfo> optSpaceStockInfo = stockInfoBySpaceList.spaceStockInfos.stream()
                            .filter(stock -> stock.getErpSku().equals(spaceItem.getSku()) && stock.getSpaceId().equals(spaceItem.getSpaceId()))
                            .findFirst();
                    if (optSpaceStockInfo.isPresent()) {
                        spaceItem.setAvailableStock(optSpaceStockInfo.get().getStock());
                    } else {
                        spaceItem.setAvailableStock(BigDecimal.ZERO.intValue());
                    }
                });
                skcPage.setAvailableStock(skcPage.getSpaceItemList().stream().filter(spaceItem -> Objects.nonNull(spaceItem.getAvailableStock())).mapToInt(OverseaSkuSpaceItemResponse::getAvailableStock).sum());
            });
        }
    }

    private void getWmsOnTheWayStock(PageResponse<OverseaSkcPageResponse> overseaSkcPage) {
        StockTransferTrackingSkuRequest trackingSkuRequest = new StockTransferTrackingSkuRequest();
        List<StockTransferTrackingSkuRequest.SkuInfo> skuInfoList = overseaSkcPage.getContent().stream().map(skcPage -> {
            StockTransferTrackingSkuRequest.SkuInfo skuInfo = new StockTransferTrackingSkuRequest.SkuInfo();
            skuInfo.setSku(skcPage.getSku());
            skuInfo.setErpSpaceIdList(skcPage.getSpaceItemList().stream().map(OverseaSkuSpaceItemResponse::getSpaceId).collect(Collectors.toList()));
            return skuInfo;
        }).collect(Collectors.toList());
        trackingSkuRequest.setSkuInfoList(skuInfoList);
        List<StockTransferTrackingSkuResponse> onTheWayStockResponseList = wmsApiService.getOnTheWayStock(trackingSkuRequest);
        if (NsyCollUtil.isNotEmpty(onTheWayStockResponseList)) {
            overseaSkcPage.getContent().forEach(skcPage -> {
                onTheWayStockResponseList.stream().filter(stock -> stock.getSku().equals(skcPage.getSku())).findFirst().ifPresent(stock -> {
                    skcPage.getSpaceItemList().forEach(spaceItem -> {
                        Optional<StockTransferTrackingSkuResponse.SpaceInfo> optionalSpaceInfo = stock.getSpaceInfoList().stream().filter(spaceInfo -> spaceInfo.getErpSpaceId().equals(spaceItem.getSpaceId())).findFirst();
                        if (optionalSpaceInfo.isPresent()) {
                            spaceItem.setOnTheWayStock(optionalSpaceInfo.get().getOnTheWayStock());
                        } else {
                            spaceItem.setOnTheWayStock(BigDecimal.ZERO.intValue());
                        }
                    });
                });
                skcPage.setOnTheWayStock(skcPage.getSpaceItemList().stream().filter(spaceItem -> Objects.nonNull(spaceItem.getOnTheWayStock())).mapToInt(OverseaSkuSpaceItemResponse::getOnTheWayStock).sum());
            });
        }
    }

    private void calculateAvailableDays(List<OverseaSkcPageResponse> list) {
        list.forEach(skcPage -> {
            // 先算item :
            skcPage.getSpaceItemList().forEach(spaceItem -> {
                if (BigDecimal.ZERO.compareTo(spaceItem.getDailyStockOutQtyIn14()) == 0) {
                    spaceItem.setAvailableDays(0);
                    return;
                }
                Integer stock = SetValueUtil.getOptimizeInteger(spaceItem.getAvailableStock()) + SetValueUtil.getOptimizeInteger(spaceItem.getOnTheWayStock());
                if (stock == 0) {
                    spaceItem.setAvailableDays(0);
                } else {
                    spaceItem.setAvailableDays(BigDecimal.valueOf(stock).divide(spaceItem.getDailyStockOutQtyIn14(), 0, RoundingMode.DOWN).intValue());
                }
            });

            if (BigDecimal.ZERO.compareTo(skcPage.getDailyStockOutQtyIn14()) == 0) {
                skcPage.setAvailableDays(0);
                return;
            }
            int stock = SetValueUtil.getOptimizeInteger(skcPage.getAvailableStock()) + SetValueUtil.getOptimizeInteger(skcPage.getOnTheWayStock());
            if (stock == 0) {
                skcPage.setAvailableDays(0);
            } else {
                skcPage.setAvailableDays(BigDecimal.valueOf(stock).divide(skcPage.getDailyStockOutQtyIn14(), 0, RoundingMode.DOWN).intValue());
            }
        });
    }

    @Override
    public List<OverseaOnTheWayStockResponse> onTheWayStockList(String department, Integer replenishmentGroupId, List<String> skcList) {
        GetBySkuBySkcRequest getBySkuBySkcRequest = new GetBySkuBySkcRequest();
        getBySkuBySkcRequest.setDepartment(department);
        getBySkuBySkcRequest.setReplenishmentGroupId(replenishmentGroupId);
        getBySkuBySkcRequest.setSkcList(skcList);
        List<String> sku = biApiService.getSkuBySkc(getBySkuBySkcRequest);
        StockTransferTrackingDetailRequest request = new StockTransferTrackingDetailRequest();
        request.setSkuList(sku);
        List<StockTransferTrackingDetailResponse> onTheWayStockDetailList = wmsApiService.getOnTheWayStockDetail(request);
        if (NsyCollUtil.isEmpty(onTheWayStockDetailList)) {
            return new ArrayList<>();
        }
        Map<String, List<StockTransferTrackingDetailResponse>> map = onTheWayStockDetailList.stream().collect(Collectors.groupingBy(StockTransferTrackingDetailResponse::getSku));
        List<OverseaOnTheWayStockResponse> stockResponseList = new ArrayList<>();
        map.forEach((k, v) -> {
            OverseaOnTheWayStockResponse stockResponse = new OverseaOnTheWayStockResponse();
            stockResponse.setSku(k);
            stockResponse.setItemList(v.stream().map(item -> {
                OverseaOnTheWayStockItemResponse stockItemResponse = new OverseaOnTheWayStockItemResponse();
                stockItemResponse.setOrderNo(item.getOrderNo());
                stockItemResponse.setLogisticsCompany(item.getLogisticsCompany());
                stockItemResponse.setShipDate(item.getShipDate());
                stockItemResponse.setShipQty(item.getShipQty());
                stockItemResponse.setStoreName(item.getStoreName());
                stockItemResponse.setTargetSpaceName(item.getTargetSpaceName());
                stockItemResponse.setErpSpaceId(item.getErpSpaceId());
                stockItemResponse.setShippingType(item.getShippingType());
                return stockItemResponse;
            }).collect(Collectors.toList()));
            stockResponseList.add(stockResponse);
        });
        // 预计到货时间
        if (NsyCollUtil.isNotEmpty(stockResponseList)) {
            getEstimatedArrivalDate(stockResponseList);
        }

        return stockResponseList;
    }

    private void getEstimatedArrivalDate(List<OverseaOnTheWayStockResponse> stockResponseList) {
        GetEstimatedArrivalDateRequest getEstimatedArrivalDateRequest = new GetEstimatedArrivalDateRequest();
        List<SpaceInfoRequest> requestList = stockResponseList.stream()
                .flatMap(stock -> stock.getItemList().stream())
                .filter(stock -> StringUtils.isNotBlank(stock.getLogisticsCompany()))
                .map(stock -> {
                    SpaceInfoRequest spaceInfoRequest = new SpaceInfoRequest();
                    spaceInfoRequest.setSpaceId(stock.getErpSpaceId());
                    spaceInfoRequest.setShipDate(stock.getShipDate());
                    spaceInfoRequest.setShippingType(stock.getShippingType());
                    spaceInfoRequest.setLogisticsCompany(stock.getLogisticsCompany());
                    return spaceInfoRequest;
                }).collect(Collectors.toList());

        if (NsyCollUtil.isEmpty(requestList)) {
            return;
        }
        getEstimatedArrivalDateRequest.setSpaceInfoRequestList(requestList);

        List<GetEstimatedArrivalDateResponse> estimatedArrivalDateResponseList = scmApiService.getEstimatedArrivalDate(getEstimatedArrivalDateRequest);

        if (NsyCollUtil.isEmpty(estimatedArrivalDateResponseList)) {
            return;
        }
        stockResponseList.forEach(stockResponse -> {
            stockResponse.getItemList().forEach(item -> {
                estimatedArrivalDateResponseList.stream()
                        .filter(estimatedArrivalDateResponse -> estimatedArrivalDateResponse.getSpaceId().equals(item.getErpSpaceId())
                         && StringUtils.isNotBlank(estimatedArrivalDateResponse.getLogisticsCompany())
                         && estimatedArrivalDateResponse.getLogisticsCompany().equals(item.getLogisticsCompany()))
                        .findFirst().ifPresent(estimatedArrivalDateResponse -> item.setEstimatedArrivalDate(estimatedArrivalDateResponse.getEstimatedArrivalDate()));
            });
        });
    }

    @Override
    public List<OverseaSkuStoreSpaceStockoutResponse> skuStoreSpaceStockout(String department, Integer replenishmentGroupId, String skc) {
        return biApiService.skuStoreSpaceStockout(department, replenishmentGroupId, skc);
    }
}
