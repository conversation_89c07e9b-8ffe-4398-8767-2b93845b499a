package com.nsy.oms.business.service.order.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.oms.business.service.order.SaleOrderItemService;
import com.nsy.oms.repository.entity.order.SaleOrderItemEntity;
import com.nsy.oms.repository.sql.mapper.order.SaleOrderItemMapper;
import org.springframework.stereotype.Service;

import java.util.List;


/**
* <AUTHOR>
* @description 针对表【order_item(订单详情表)】的数据库操作Service实现
* @createDate 2024-06-13 14:52:05
*/
@Service
public class SaleOrderItemServiceImpl extends ServiceImpl<SaleOrderItemMapper, SaleOrderItemEntity>
    implements SaleOrderItemService {

    @Override
    public List<SaleOrderItemEntity> getByPlatformItemId(List<String> platformItemIdList) {
        return list(Wrappers.<SaleOrderItemEntity>lambdaQuery().in(SaleOrderItemEntity::getPlatformItemId, platformItemIdList));
    }

    @Override
    public List<SaleOrderItemEntity> getByOrderId(Integer orderId) {
        return list(Wrappers.<SaleOrderItemEntity>lambdaQuery().eq(SaleOrderItemEntity::getOrderId, orderId));
    }

    @Override
    public List<SaleOrderItemEntity> getByOrderIdAndPlatformItemId(Integer orderId, List<String> platformItemIdList) {
        return list(Wrappers.<SaleOrderItemEntity>lambdaQuery().in(SaleOrderItemEntity::getPlatformItemId, platformItemIdList).eq(SaleOrderItemEntity::getOrderId, orderId));
    }
}




