package com.nsy.oms.business.service.stock.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.privilege.LoginInfoService;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.oms.business.domain.request.stock.StockUpdateRequest;
import com.nsy.oms.business.domain.stock.StockChangeInfo;
import com.nsy.oms.business.domain.stock.StockInfo;
import com.nsy.oms.business.manage.pms.PmsApiService;
import com.nsy.oms.business.manage.pms.response.ProductSpecResponse;
import com.nsy.oms.business.service.bd.BdSpaceService;
import com.nsy.oms.business.service.stock.*;
import com.nsy.oms.enums.stock.StockBrandTypeEnum;
import com.nsy.oms.enums.stock.StockOperateTypeEnum;
import com.nsy.oms.enums.stock.StockTypeEnum;
import com.nsy.oms.enums.stock.StockUpdateTypeEnum;
import com.nsy.oms.repository.entity.bd.BdSpaceEntity;
import com.nsy.oms.repository.entity.stock.StockEntity;
import com.nsy.oms.repository.sql.mapper.stock.StockMapper;
import com.nsy.oms.utils.BeanUtils;
import com.nsy.oms.utils.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @author: caishaohui
 * @version: v1.0
 * @description: 库存表业务实现
 * @date: 2025-04-02 14:40
 */
@Service
public class StockServiceImpl extends ServiceImpl<StockMapper, StockEntity> implements StockService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockServiceImpl.class);

    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private StockChangeLogService stockChangeLogService;
    @Autowired
    private StockAgeService stockAgeService;
    @Autowired
    private PmsApiService pmsApiService;
    @Autowired
    private StockRedRushRecordService stockRedRushRecordService;
    @Autowired
    private BdSpaceService bdSpaceService;
    @Autowired
    private StockPrematchService stockPrematchService;

    /**
     * * 库存更新
     * *  1.获取库存数据
     * * 2.更新对应库存
     * * 3、库存变动日志记录
     * * 4、库龄变化记录
     *
     * @param stockUpdateRequest
     */
    @Transactional
    @Override
    public StockEntity updateStock(StockUpdateRequest stockUpdateRequest) {

        LOGGER.info("库存更新JSON: {}", JsonMapper.toJson(stockUpdateRequest));

        // 1.获取库存数据
        StockEntity stockEntity = getStockEntityBy(stockUpdateRequest);
        Integer oldStock = stockEntity.getStock();
        int stock = oldStock + stockUpdateRequest.getQty();

        // 2.更新对应库存
        // 不允许设为负库存
        if (stock < 0) {
            if (StringUtils.hasText(stockUpdateRequest.getSku()))
                LOGGER.error("库存不足扣除提示: 【 {} 】，【 {} 】，库存数【 {} 】，不足以扣除【 {} 】件 ",
                        stockUpdateRequest.getSpaceId(), stockUpdateRequest.getSku(), oldStock, Math.abs(stockUpdateRequest.getQty()));
            stock = 0;
        }
        StockEntity stockUpdate = new StockEntity();
        stockUpdate.setStockId(stockEntity.getStockId());
        //如果存在共享库存 ，扣除共享库存
        if (stockUpdateRequest.getQty() < 0)
            stockUpdate.setShareStock(Math.max(stockEntity.getShareStock() + stockUpdateRequest.getQty(), 0));

        stockUpdate.setStock(stock);
        stockUpdate.setUpdateBy(loginInfoService.getUserName());
        stockUpdate.setVersion(stockEntity.getVersion());
        boolean isSuccess = this.updateById(stockUpdate);
        //不成功，可能存在并发，重试一次
        if (!isSuccess) {
            this.retryUpdateStock(stockEntity, stockUpdateRequest.getQty());
        }

        stockEntity.setStock(stock);

        // 3、库存变动日志记录
        stockChangeLogService.addStockChangeLog(oldStock, stockEntity, stockUpdateRequest);

        // 4、库龄变化记录
        stockAgeService.stockAgeUpdate(stockEntity, stockUpdateRequest);

        return stockEntity;
    }

    @Transactional
    public void updateStockBatch(List<StockUpdateRequest> stockUpdateRequestList) {
        stockUpdateRequestList.stream().forEach(request -> {
            this.updateStock(request);
        });
    }

    private void retryUpdateStock(StockEntity stockEntity, Integer qty) {
        LOGGER.info("重试更新库存 【 {} 】，【 {} 】，更新数【 {} 】", stockEntity.getStockId(), stockEntity.getSku(), qty);
        //事务隔离级别RC ，重新获取数据库最新数据
        StockEntity stockEntityLast = this.getById(stockEntity.getStockId());
        StockEntity stockUpdate = new StockEntity();
        stockUpdate.setStockId(stockEntity.getStockId());
        stockUpdate.setVersion(stockEntityLast.getVersion());
        stockUpdate.setStock(stockEntityLast.getStock() + qty);
        stockUpdate.setUpdateBy(loginInfoService.getUserName());
        if (qty < 0)
            stockUpdate.setShareStock(Math.max(stockEntity.getShareStock() + qty, 0));
        boolean isSuccess = this.updateById(stockUpdate);

        //再次失败则抛异常
        if (!isSuccess)
            throw new BusinessServiceException("系统繁忙，请重试！");
    }

    /**
     * 获取库存数据
     *
     * @param stockUpdateRequest
     * @return
     */
    private StockEntity getStockEntityBy(StockUpdateRequest stockUpdateRequest) {
        //1.stockId不为空，优先取对应的库存
        if (stockUpdateRequest.getStockId() != null) {
            StockEntity stockEntity = this.getById(stockUpdateRequest.getStockId());
            if (stockEntity != null) {
                return stockEntity;
            }
        }
        //若未传库存归属，默认为公司库存
        if (!StringUtils.hasText(stockUpdateRequest.getBusinessType()))
            stockUpdateRequest.setStockType(StockTypeEnum.COMPANY.name());

        LambdaQueryWrapper<StockEntity> queryWrapper = new LambdaQueryWrapper<StockEntity>()
                .eq(StockEntity::getSku, stockUpdateRequest.getSku())
                .eq(StockEntity::getSpaceId, stockUpdateRequest.getSpaceId())
                .eq(StockEntity::getIsPreLabel, stockUpdateRequest.getIsPreLabel())
                .eq(StockEntity::getStockType, stockUpdateRequest.getStockType());

        //若库存归属为部门或店铺，需传对应的部门和店铺Id
        if (StockTypeEnum.STORE.name().equals(stockUpdateRequest.getStockType())) {
            if (Objects.isNull(stockUpdateRequest.getStoreId()))
                throw new BusinessServiceException("库存归属店铺，店铺Id不能为空");
            queryWrapper.eq(StockEntity::getStoreId, stockUpdateRequest.getStoreId());

        }

        if (StockTypeEnum.DEPARTMENT.name().equals(stockUpdateRequest.getStockType())) {
            if (!StringUtils.hasText(stockUpdateRequest.getBusinessType()))
                throw new BusinessServiceException("库存归属部门，部门不能为空");
            queryWrapper.eq(StockEntity::getBusinessType, stockUpdateRequest.getBusinessType());
        }

        //2.根据请求条件(sku和spaceId)获取库存对象
        StockEntity stockEntity = this.getOne(queryWrapper.last("limit 1"));

        if (Objects.isNull(stockEntity)) {
            //初始化数据
            stockEntity = new StockEntity();
            BeanUtils.copyPropertiesIgnoreNull(stockUpdateRequest, stockEntity);
            stockEntity.setStock(0);
            stockEntity.setShareStock(0);
            stockEntity.setIsLock(0);
            stockEntity.setCreateBy(loginInfoService.getName());

            ProductSpecResponse productSpecResponse = pmsApiService.getProductSpecBySpecSku(stockUpdateRequest.getSku());
            stockEntity.setProductId(productSpecResponse.getProductId());
            stockEntity.setSpecId(productSpecResponse.getId());
            stockEntity.setErpSpecId(productSpecResponse.getErpSpecId());

            if (!StringUtils.hasText(stockEntity.getSpaceName())) {
                //赋值仓库名
                BdSpaceEntity bdSpaceEntity = bdSpaceService.getById(stockEntity.getSpaceId());
                stockEntity.setSpaceName(bdSpaceEntity.getSpaceName());
            }

            this.save(stockEntity);
        }
        return stockEntity;
    }

    public void changeQty(Integer stockId, int changeQty, StockUpdateTypeEnum stockUpdateTypeEnum) {
        StockUpdateRequest stockUpdateRequest = new StockUpdateRequest();
        stockUpdateRequest.setQty(changeQty);
        stockUpdateRequest.setStockId(stockId);
        stockUpdateRequest.setChangeLogType(stockUpdateTypeEnum);
        this.updateStock(stockUpdateRequest);
    }

    /**
     * #一.当盘点或调拨需要扣减库位库存时，需判断可用库存。
     * 可用库存=库存数量 - 预配库存数量（stock_prematch_info）
     * <p>
     * #二.调拨或盘点后扣减的总库存为0，不判断可用，直接更新库存数量为0
     * #三.库存归属只有一条记录，直接扣除库存
     * <p>
     * #四.存在多条库存记录，且可用库存足够时，需按照以下顺序扣除库存
     * #1.优先公司可用库存
     * #2.共享可用库存（stock表share_stock字段）
     * #3.私有可用库存
     * <p>
     * #五.存在多条库存记录，且可用库存不足时（总库存足够），需增加红冲记录
     * 1.足够扣减的部分先按照#四顺序扣减，不足部分记录到红冲表中
     *
     * @param sku
     * @param stockList       库存集合
     * @param decreaseQty     需扣减数量
     * @param stockUpdateType 库存变动类型
     * @return 返回库存扣减集合，用于记录货主转移
     */
    @Override
    public List<StockChangeInfo> handleStockDecrease(String sku, List<StockInfo> stockList, int decreaseQty, StockUpdateTypeEnum stockUpdateType, Integer isPreLabel) {
        List<StockChangeInfo> stockChangeInfos = new LinkedList<>();

        if (CollectionUtils.isEmpty(stockList)) {
            LOGGER.info(" {} 未找到库存记录，无法扣除 {} 件", sku, decreaseQty);
            return stockChangeInfos;
        }
        //调拨或盘点后扣减的总库存为0，不判断可用，直接更新库存数量为0
        int totalStock = stockList.stream().mapToInt(StockInfo::getStock).sum();
        if (decreaseQty >= totalStock) {
            List<StockChangeInfo> finalStockChangeInfos = stockChangeInfos;
            stockList.stream().filter(item -> item.getStock() > 0).forEach(item -> {
                //库存扣减至0
                this.changeQty(item.getStockId(), -item.getStock(), stockUpdateType);

                //记录库存扣减
                StockChangeInfo stockChangeInfo = new StockChangeInfo();
                BeanUtils.copyPropertiesIgnoreNull(item, stockChangeInfo);
                stockChangeInfo.setQty(item.getStock());
                finalStockChangeInfos.add(stockChangeInfo);
            });
            return finalStockChangeInfos;
        }
        //库存归属只有一条记录，直接扣除库存
        List<StockInfo> stockInfoList = stockList.stream().filter(item -> item.getStock() > 0).collect(Collectors.toList());
        if (stockInfoList.size() == 1) {
            StockInfo item = stockInfoList.get(0);
            this.changeQty(item.getStockId(), -decreaseQty, stockUpdateType);
            //记录库存扣减
            StockChangeInfo stockChangeInfo = new StockChangeInfo();
            BeanUtils.copyPropertiesIgnoreNull(item, stockChangeInfo);
            stockChangeInfo.setQty(decreaseQty);
            stockChangeInfos.add(stockChangeInfo);
            return stockChangeInfos;
        }

        List<StockInfo> availableStockList = stockList.stream().filter(item -> item.getAvailableStock() > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(availableStockList)) {
            LOGGER.info("【%s】 【%s】 库存记录不存在，无法扣减库存", sku, decreaseQty);
            return stockChangeInfos;
        }

        int availableStock = availableStockList.stream().mapToInt(StockInfo::getAvailableStock).sum();
        // 判断是否需要触发红冲
        if (availableStock >= decreaseQty) {
            // 可用库存足够，直接扣减
            stockChangeInfos = handleAvaliableStockDecrease(decreaseQty, availableStockList, stockUpdateType);
        } else {
            // 需要触发红冲机制
            // 先扣减可用库存
            stockChangeInfos = handleAvaliableStockDecrease(availableStock, availableStockList, stockUpdateType);

            //剩余的数量记录红冲记录
            int redQty = Math.abs(availableStock - decreaseQty);
            stockRedRushRecordService.saveRedRushRecord(sku, stockList.get(0).getSpaceId(), redQty, stockUpdateType, isPreLabel);
        }
        return stockChangeInfos;
    }

    /**
     * @param decreaseQty         需扣减数量
     * @param availableStockList  可用库存大于0的库存集合
     * @param stockUpdateTypeEnum 库存变动类型
     * @return 返回库存扣减集合，用于记录货主转移
     */
    public List<StockChangeInfo> handleAvaliableStockDecrease(int decreaseQty, List<StockInfo> availableStockList, StockUpdateTypeEnum stockUpdateTypeEnum) {
        //私有库存归属集合，用于记录货主转移
        List<StockChangeInfo> stockChangeInfos = new LinkedList<>();
        AtomicInteger waitDecreaseQty = new AtomicInteger(decreaseQty);
        Map<Integer, Integer> stockQtyMap = new LinkedHashMap<>();
        //设置扣减优先级
        // #1.优先公司可用库存
        availableStockList.stream().filter(item -> StockTypeEnum.COMPANY.name().equals(item.getStockType())).forEach(item -> {
            int qty = Math.min(waitDecreaseQty.get(), item.getAvailableStock());
            if (qty <= 0)
                return;

            Integer orDefault = stockQtyMap.getOrDefault(item.getStockId(), 0);
            stockQtyMap.put(item.getStockId(), orDefault + qty);
            waitDecreaseQty.addAndGet(-qty);
        });
        // #2.共享可用库存（stock表share_stock字段）
        if (waitDecreaseQty.get() > 0) {
            availableStockList.stream().filter(item -> !StockTypeEnum.COMPANY.name().equals(item.getStockType()) && item.getShareStock() > 0).forEach(item -> {
                int qty = Math.min(waitDecreaseQty.get(), item.getShareStock());
                if (qty <= 0)
                    return;

                Integer orDefault = stockQtyMap.getOrDefault(item.getStockId(), 0);
                stockQtyMap.put(item.getStockId(), orDefault + qty);
                waitDecreaseQty.addAndGet(-qty);
            });
        }
        // #3.私有可用库存
        if (waitDecreaseQty.get() > 0) {
            availableStockList.stream().filter(item -> !StockTypeEnum.COMPANY.name().equals(item.getStockType()) && (item.getAvailableStock() - item.getShareStock()) > 0).forEach(item -> {
                int qty = Math.min(waitDecreaseQty.get(), item.getAvailableStock() - item.getShareStock());
                if (qty <= 0)
                    return;

                Integer orDefault = stockQtyMap.getOrDefault(item.getStockId(), 0);
                stockQtyMap.put(item.getStockId(), orDefault + qty);
                waitDecreaseQty.addAndGet(-qty);
            });
        }
        stockQtyMap.forEach((stockId, qty) -> {
            //扣减库存
            this.changeQty(stockId, -qty, stockUpdateTypeEnum);
            StockInfo stockInfo = availableStockList.stream().filter(item -> item.getStockId().equals(stockId)).findAny().get();
            StockChangeInfo stockChangeInfo = new StockChangeInfo();
            BeanUtils.copyPropertiesIgnoreNull(stockInfo, stockChangeInfo);
            stockChangeInfo.setQty(qty);
            stockChangeInfos.add(stockChangeInfo);
        });
        return stockChangeInfos;
    }

    @Override
    public List<StockInfo> getStockList(Integer storeId, Integer spaceId, List<Integer> specIdList, StockOperateTypeEnum stockOperateTypeEnum, StockBrandTypeEnum stockBrandTypeEnum)
    {
       return stockPrematchService.getStockList(storeId, spaceId, specIdList, stockOperateTypeEnum, stockBrandTypeEnum);
    }

}
