package com.nsy.oms.business.service.celebrity.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.nsy.oms.business.manage.omspublish.OmsPublishApiService;
import com.nsy.oms.business.manage.omspublish.response.PublishProductSpecInfo;
import com.nsy.oms.business.manage.search.SearchApiService;
import com.nsy.oms.business.manage.search.domain.ProductIndex;
import com.nsy.oms.business.service.celebrity.IInternetCelebritySampleOrderItemPostService;
import com.nsy.oms.business.service.celebrity.IInternetCelebritySampleOrderItemService;
import com.nsy.oms.business.service.celebrity.IInternetCelebritySampleOrderService;
import com.nsy.oms.business.service.celebrity.InternetCelebrityAdDailyService;
import com.nsy.oms.business.service.celebrity.InternetCelebrityConsumerMessageService;
import com.nsy.oms.business.service.celebrity.InternetCelebrityNameRecordService;
import com.nsy.oms.business.service.celebrity.InternetCelebrityOrderItemPostMappingService;
import com.nsy.oms.business.service.celebrity.InternetCelebritySampleOrderItemPostDailyService;
import com.nsy.oms.business.service.celebrity.InternetCelebrityService;
import com.nsy.oms.business.service.celebrity.InternetCelebrityStoreRelationService;
import com.nsy.oms.business.service.sa.SaStoreService;
import com.nsy.oms.business.service.sa.impl.SaStoreWebsiteServiceImpl;
import com.nsy.oms.enums.tkcreator.InternetCelebrityAdIntentionType;
import com.nsy.oms.enums.tkcreator.InternetCelebrityLevelType;
import com.nsy.oms.enums.tkcreator.InternetCelebrityMessageType;
import com.nsy.oms.enums.tkcreator.InternetCelebrityRelationStatus;
import com.nsy.oms.repository.entity.celebrity.InternetCelebrityAdDailyEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebrityEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebrityNameRecordEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebrityOrderItemPostMappingEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderItemEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderItemPostDailyEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderItemPostEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebrityStoreRelationEntity;
import com.nsy.oms.repository.entity.sa.SaStoreEntity;
import com.nsy.oms.repository.sql.mapper.celebrity.InternetCelebritySampleOrderItemMapper;
import com.nsy.oms.repository.sql.mapper.celebrity.InternetCelebritySampleOrderItemPostMapper;
import com.nsy.oms.repository.sql.mapper.celebrity.InternetCelebrityStoreRelationMapper;
import com.nsy.oms.utils.IntranetContext;
import com.nsy.oms.utils.JsonMapper;
import com.nsy.oms.utils.Lists;
import com.nsy.oms.utils.TiktokCreatorUtil;
import com.nsy.oms.utils.mp.LocationContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
public class InternetCelebrityConsumerMessageServiceImpl implements InternetCelebrityConsumerMessageService {

    @Inject
    InternetCelebrityAdDailyService adDailyService;
    @Inject
    InternetCelebrityService creatorService;
    @Inject
    InternetCelebrityStoreRelationService relationService;
    @Inject
    InternetCelebrityStoreRelationMapper relationMapper;
    @Inject
    IInternetCelebritySampleOrderService orderService;
    @Inject
    IInternetCelebritySampleOrderItemService orderItemService;
    @Inject
    InternetCelebritySampleOrderItemMapper orderItemMapper;
    @Inject
    IInternetCelebritySampleOrderItemPostService videoService;
    @Inject
    InternetCelebritySampleOrderItemPostMapper videoMapper;
    @Inject
    InternetCelebritySampleOrderItemPostDailyService videoDailyService;
    @Inject
    InternetCelebrityOrderItemPostMappingService mappingService;
    @Inject
    InternetCelebrityNameRecordService nameRecordService;
    @Autowired
    private OmsPublishApiService omsPublishApiService;
    @Autowired
    private SaStoreWebsiteServiceImpl saStoreWebsiteService;
    @Autowired
    private SaStoreService saStoreService;
    @Autowired
    private SearchApiService searchApiService;


    @Override
    @Transactional
    public void consumerMessage(Integer id, InternetCelebrityMessageType type) {
        switch (type) {
            case AD:
                processAd(id);
                break;
            case CREATOR:
                processCreator(id);
                break;
            case VIDEO:
                processVideo(id);
                break;
            case ORDER:
                processOrder(id);
                break;
            case RELATION:
                processRelation(id);
                break;
            default:
                break;
        }
    }

    /**
     * @param id
     */
    @Transactional
    public void processAd(Integer id) {
        IntranetContext.setIntranet(1);
        InternetCelebrityAdDailyEntity adDaily = adDailyService.getById(id);
        if (Objects.isNull(adDaily)) {
            return;
        }
        LocationContext.setLocation(adDaily.getLocation());

        log.info("[1. find video] InternetCelebrityConsumerMessageServiceImpl processAd adDaily: {} ", JsonMapper.toJson(adDaily));
        InternetCelebritySampleOrderItemPostEntity video = videoService.findTopByStoreIdAndVideoCode(adDaily.getStoreId(), adDaily.getVideoNo());

        if (Objects.isNull(video)) {
            return;
        }

        log.info("[2. ad to video] InternetCelebrityConsumerMessageServiceImpl processAd video: {} ", JsonMapper.toJson(video));
        adDaily.setPostId(video.getInternetCelebritySampleOrderItemPostId());
        adDailyService.saveOrUpdate(adDaily);


        log.info("[3. update relation ad] InternetCelebrityConsumerMessageServiceImpl processAd adDaily: {} ", JsonMapper.toJson(adDaily));
        updateVideoAd(video);

    }

    /**
     * @param id
     */
    @Transactional
    public void processCreator(Integer id) {
        IntranetContext.setIntranet(1);
        InternetCelebrityEntity creator = creatorService.getById(id);
        if (Objects.isNull(creator)) {
            return;
        }
        LocationContext.setLocation(creator.getLocation());

        log.info("[1. updateRelationCreator] InternetCelebrityConsumerMessageServiceImpl processCreator : {} ", JsonMapper.toJson(creator));
        // 1. new relation
        updateCreatorRelation(creator);

        log.info("[2. updateCreatorOrder] InternetCelebrityConsumerMessageServiceImpl processCreator : {} ", JsonMapper.toJson(creator));
        // 2. update creator
        updateCreatorOrder(creator);

    }

    private void updateCreatorRelation(InternetCelebrityEntity creator) {
        List<Integer> relationIds = new ArrayList<>();
        List<Integer> relationIdsOfOrder = relationMapper.findOrderRelationWithNoCreator(creator.getInternetCelebrityNo(), creator.getInternetCelebrityName());
        List<Integer> relationIdsOfVideo = relationMapper.findVideoRelationWithNoCreator(creator.getInternetCelebrityNo(), creator.getInternetCelebrityName());
        if (CollUtil.isNotEmpty(relationIdsOfOrder)) relationIds.addAll(relationIdsOfOrder);
        if (CollUtil.isNotEmpty(relationIdsOfVideo)) relationIds.addAll(relationIdsOfVideo);
        if (CollUtil.isNotEmpty(relationIds)) {
            List<InternetCelebrityStoreRelationEntity> relationEntities = relationService.listByIds(relationIds);
            relationEntities.forEach(relation -> {
                relation.setInternetCelebrityId(creator.getId());
            });
            relationService.saveOrUpdateBatch(relationEntities);
        }
        List<Integer> relationIdsOfId = relationService.listByCelebrityId(creator.getId()).stream().map(InternetCelebrityStoreRelationEntity::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(relationIdsOfId)) {
            List<InternetCelebrityOrderItemPostMappingEntity> mappings = mappingService.findAllByRelationIdIn(relationIdsOfId);
            List<Integer> orderItemIds = mappings.stream().map(InternetCelebrityOrderItemPostMappingEntity::getInternetCelebritySampleOrderItemId).filter(item -> item > 0).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(orderItemIds)) {
                List<Integer> orderIds = orderItemService.listByIds(orderItemIds).stream().map(InternetCelebritySampleOrderItemEntity::getInternetCelebritySampleOrderId).collect(Collectors.toList());
                List<InternetCelebritySampleOrderEntity> orders = orderService.listByIds(orderIds);
                orders.forEach(video -> {
                    video.setInternetCelebrityId(creator.getId());
                });
                orderService.saveOrUpdateBatch(orders);
            }
            List<Integer> videoIds = mappings.stream().map(InternetCelebrityOrderItemPostMappingEntity::getInternetCelebritySampleOrderItemPostId).filter(item -> item > 0).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(videoIds)) {
                List<InternetCelebritySampleOrderItemPostEntity> videos = videoService.listByIds(videoIds);
                videos.forEach(video -> {
                    video.setInternetCelebrityId(creator.getId());
                });
                videoService.saveOrUpdateBatch(videos);
            }
        }
    }

    /**
     * @param id
     */
    @Transactional
    public void processVideo(Integer id) {
        IntranetContext.setIntranet(1);
        InternetCelebritySampleOrderItemPostEntity video = videoService.getById(id);
        if (Objects.isNull(video)) {
            return;
        }
        LocationContext.setLocation(video.getLocation());
        boolean noCreatorMessage = (Objects.isNull(video.getInternetCelebrityId()) || video.getInternetCelebrityId() == 0) && StrUtil.isEmpty(video.getInternetCelebrityName()) && StrUtil.isEmpty(video.getInternetCelebrityNo());
        if (noCreatorMessage) {
            log.info("[noCreatorMessage] InternetCelebrityConsumerMessageServiceImpl processVideo order: {} ", JsonMapper.toJson(video));
            return;
        }

        log.info("[1. updateAdWhenProcessVideo] InternetCelebrityConsumerMessageServiceImpl processVideo video: {} ", JsonMapper.toJson(video));
        // 1. 下游关联ad
        updateAdWhenProcessVideo(video);
        //    下游关联gmv
        updateGMVWhenProcessVideo(video);

        // 2. mapping
        InternetCelebrityOrderItemPostMappingEntity mappingEntity = mappingService.findTopByPostId(video.getInternetCelebritySampleOrderItemPostId());
        boolean newMapping = Objects.isNull(mappingEntity);
        if (newMapping) {
            mappingEntity = new InternetCelebrityOrderItemPostMappingEntity();
            mappingEntity.setInternetCelebritySampleOrderItemPostId(video.getInternetCelebritySampleOrderItemPostId());
            mappingService.saveOrUpdate(mappingEntity);
        }
        // 更新建联时间
        if (Objects.nonNull(video.getPostDate()) && Objects.isNull(mappingEntity.getRelationDate())) {
            mappingEntity.setRelationDate(DateUtils.addDays(video.getPostDate(), -15));
            mappingService.saveOrUpdate(mappingEntity);
        }
        log.info("[2. mapping] InternetCelebrityConsumerMessageServiceImpl processVideo mappingEntity: {} ", JsonMapper.toJson(mappingEntity));

        // 3. update creator before order
        InternetCelebrityEntity creator = findCreator(video.getInternetCelebrityId(), video.getInternetCelebrityNo(), video.getInternetCelebrityName());
        if (Objects.nonNull(creator) && video.getInternetCelebrityId() == 0) {
            video.setInternetCelebrityId(creator.getId());
            videoService.saveOrUpdate(video);
        }
        log.info("[3. creator] InternetCelebrityConsumerMessageServiceImpl processVideo creator: {} ", JsonMapper.toJson(creator));

        // 4. order
        boolean updateOrder = updateOrderWhenProcessVideo(newMapping, mappingEntity, video);
        log.info("[4. updateOrderWhenProcessVideo] InternetCelebrityConsumerMessageServiceImpl processVideo updateOrder: {} ", JsonMapper.toJson(updateOrder));

        // 5. relation
        updateRelationWhenProcessVideo(creator, video, mappingEntity, updateOrder);
        log.info("[5. updateRelationWhenProcessVideo] InternetCelebrityConsumerMessageServiceImpl processVideo");

        // 6. creator
        if (Objects.nonNull(creator)) {
            updateCreatorOrder(creator);
        }
        log.info("[6. updateCreatorOrder] InternetCelebrityConsumerMessageServiceImpl processVideo creator: {} ", JsonMapper.toJson(creator));

        // 7. video name
        if (updateOrder)
            updateVideoName(video, orderItemService.getById(mappingEntity.getInternetCelebritySampleOrderItemId()));
        log.info("[6. updateVideoName] InternetCelebrityConsumerMessageServiceImpl processVideo creator: {} ", JsonMapper.toJson(video));

    }

    private boolean updateOrderWhenProcessVideo(boolean newMapping, InternetCelebrityOrderItemPostMappingEntity mappingEntity, InternetCelebritySampleOrderItemPostEntity video) {
        boolean updateOrder = false;
        if (!newMapping && !Objects.equals(mappingEntity.getInternetCelebritySampleOrderItemId(), 0)) {
            return updateOrder;
        }
        InternetCelebritySampleOrderItemEntity videoOrderItem = findOrderItemWithNoVideo(video);
        if (Objects.isNull(videoOrderItem)) {
            return updateOrder;
        }
        updateOrder = true;
        mappingEntity.setInternetCelebritySampleOrderItemId(videoOrderItem.getInternetCelebritySampleOrderItemId());
        // 更新建联时间
        if (Objects.nonNull(videoOrderItem.getOrderDeliveryDate())) {
            mappingEntity.setRelationDate(DateUtils.addDays(videoOrderItem.getOrderDeliveryDate(), -3));
        }
        InternetCelebrityOrderItemPostMappingEntity mapping = mappingService.findTopByOrderItemId(videoOrderItem.getInternetCelebritySampleOrderItemId());
        if (Objects.nonNull(mapping)) {
            mappingService.removeById(mapping.getId());
            if (Objects.nonNull(mappingEntity.getStoreRelationId()) && mappingEntity.getStoreRelationId() == 0) {
                log.info("updateOrderWhenProcessVideo mappingEntity.setStoreRelationId: {}, VideoCode: {}", mapping.getStoreRelationId(), video.getVideoCode());
                mappingEntity.setStoreRelationId(mapping.getStoreRelationId());
            }
        }
        mappingService.saveOrUpdate(mappingEntity);
        return updateOrder;
    }

    @Transactional
    public void updateRelationWhenProcessVideo(InternetCelebrityEntity creator, InternetCelebritySampleOrderItemPostEntity video, InternetCelebrityOrderItemPostMappingEntity mappingEntity, boolean updateOrder) {
        InternetCelebrityStoreRelationEntity relation = Objects.nonNull(creator) && Objects.nonNull(creator.getId())
                ? findRelation(video.getStoreId(), null, creator.getId(), creator.getInternetCelebrityNo(), creator.getInternetCelebrityName())
                : findRelation(video.getStoreId(), mappingEntity.getStoreRelationId(), video.getInternetCelebrityId(), video.getInternetCelebrityNo(), video.getInternetCelebrityName());
        if (Objects.isNull(relation)) {
            relation = new InternetCelebrityStoreRelationEntity();
            relation.setStoreId(video.getStoreId());
            relation.setStoreName(video.getStoreName());
            relation.setRelationStatus(InternetCelebrityRelationStatus.NOT_RELATION.getValue());
            relation.setCommissionRate(BigDecimal.ZERO);
            relation.setRemark("");
            relation.setUpdateBy("processVideo");
            relation.setCreateBy("processVideo");
            relation.setInternetCelebrityId(video.getInternetCelebrityId());
            relation.setCommissionRate(new BigDecimal("0.12"));
            relation.setInternetCelebrityName(video.getInternetCelebrityName());
            relation.setInternetCelebrityNo(video.getInternetCelebrityNo());
            relation.setInternetCelebrityId(video.getInternetCelebrityId());
        }
        if (Objects.nonNull(creator) && Objects.nonNull(creator.getId())) {
            relation.setInternetCelebrityName(creator.getInternetCelebrityName());
            relation.setInternetCelebrityNo(creator.getInternetCelebrityNo());
            relation.setInternetCelebrityId(creator.getId());
        }
        relationService.saveOrUpdate(relation);
        mappingEntity.setStoreRelationId(relation.getId());
        log.info("updateRelationWhenProcessVideo mappingEntity.setStoreRelationId: {}, VideoCode: {}", relation.getId(), video.getVideoCode());
        mappingService.saveOrUpdate(mappingEntity);
        if (updateOrder) {
            updateRelationOrder(relation);
        }
        updateRelationVideo(relation);
    }

    /**
     * 查询没有匹配视频的订单
     *
     * @param video
     * @return
     */
    @Nullable
    public InternetCelebritySampleOrderItemEntity findOrderItemWithNoVideo(InternetCelebritySampleOrderItemPostEntity video) {
        InternetCelebritySampleOrderItemEntity videoOrderItem = Objects.nonNull(video.getInternetCelebrityId()) && video.getInternetCelebrityId() > 0
                ? orderItemMapper.findTopByStoreIdAndSellerProductIdAndCreatorIdWithNoMapping(video.getStoreId(), video.getSellerProductId(), video.getInternetCelebrityId())
                : null;
        if (Objects.nonNull(videoOrderItem)) {
            return videoOrderItem;
        }
        videoOrderItem = orderItemMapper.findTopByStoreIdAndSellerProductIdAndCreatorNoWithNoMapping(video.getStoreId(), video.getSellerProductId(), video.getInternetCelebrityNo());
        if (Objects.nonNull(videoOrderItem)) {
            return videoOrderItem;
        }
        videoOrderItem = orderItemMapper.findTopByStoreIdAndSellerProductIdAndCreatorNameWithNoMapping(video.getStoreId(), video.getSellerProductId(), video.getInternetCelebrityName());
        if (Objects.nonNull(videoOrderItem)) {
            return videoOrderItem;
        }

        List<Integer> websiteIdList = saStoreWebsiteService.getWebsiteIdList(video.getStoreId());
        if (CollUtil.isNotEmpty(websiteIdList)) {
            PublishProductSpecInfo publishProductSpecInfo = new PublishProductSpecInfo();
            publishProductSpecInfo.setStoreId(video.getStoreId());
            publishProductSpecInfo.setWebsiteId(websiteIdList.get(0));
            publishProductSpecInfo.setWebsiteProductCode(video.getSellerProductId());
            PublishProductSpecInfo specInfo = omsPublishApiService.getPublishSpecInfoBySellerProductId(publishProductSpecInfo);
            if (Objects.isNull(specInfo) || CollUtil.isEmpty(specInfo.getSkuList())) {
                return videoOrderItem;
            }
            videoOrderItem = Objects.nonNull(video.getInternetCelebrityId()) && video.getInternetCelebrityId() > 0
                    ? orderItemMapper.findTopByStoreIdAndSkuInAndCreatorIdWithNoMapping(video.getStoreId(), specInfo.getSkuList(), video.getInternetCelebrityId())
                    : null;
            if (Objects.isNull(videoOrderItem)) {
                videoOrderItem = orderItemMapper.findTopByStoreIdAndSkuInAndCreatorNoWithNoMapping(video.getStoreId(), specInfo.getSkuList(), video.getInternetCelebrityNo());
            }
            if (Objects.isNull(videoOrderItem)) {
                videoOrderItem = orderItemMapper.findTopByStoreIdAndSkuInAndCreatorNameWithNoMapping(video.getStoreId(), specInfo.getSkuList(), video.getInternetCelebrityName());
            }

            if (Objects.isNull(videoOrderItem)) {
                return videoOrderItem;
            }
            videoOrderItem.setSellerProductId(video.getSellerProductId());
            orderItemService.saveOrUpdate(videoOrderItem);
        }
        return videoOrderItem;
    }

    @Transactional
    public void updateAdWhenProcessVideo(InternetCelebritySampleOrderItemPostEntity video) {
        List<InternetCelebrityAdDailyEntity> adDailyEntitiesNoBounding = adDailyService.findAllByStoreIdAndVideoNo(video.getStoreId(), video.getVideoCode()).stream().filter(item -> item.getPostId() == 0).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(adDailyEntitiesNoBounding)) {
            adDailyEntitiesNoBounding.forEach(adDailyEntity -> {
                adDailyEntity.setPostId(video.getInternetCelebritySampleOrderItemPostId());
            });
            adDailyService.saveOrUpdateBatch(adDailyEntitiesNoBounding);
        }
        updateVideoAd(video);
    }

    @Transactional
    public void updateGMVWhenProcessVideo(InternetCelebritySampleOrderItemPostEntity video) {
        List<InternetCelebritySampleOrderItemPostDailyEntity> postDailies = videoDailyService.findAllByPostIdIn(Collections.singletonList(video.getInternetCelebritySampleOrderItemPostId()));
        video.setGmv(CollUtil.isEmpty(postDailies) ? BigDecimal.ZERO : postDailies.stream().map(InternetCelebritySampleOrderItemPostDailyEntity::getGmv).reduce(BigDecimal.ZERO, BigDecimal::add));
        videoService.saveOrUpdate(video);
    }

    /**
     * @param id
     */
    @Transactional
    public void processOrder(Integer id) {
        IntranetContext.setIntranet(1);
        InternetCelebritySampleOrderEntity order = orderService.getById(id);
        if (Objects.isNull(order)) {
            return;
        }

        boolean noCreatorMessage = (Objects.isNull(order.getInternetCelebrityId()) || order.getInternetCelebrityId() == 0) && StrUtil.isEmpty(order.getInternetCelebrityNickname()) && StrUtil.isEmpty(order.getInternetCelebrityNo());
        if (noCreatorMessage) {
            log.info("[noCreatorMessage] InternetCelebrityConsumerMessageServiceImpl processOrder order: {} ", JsonMapper.toJson(order));
            return;
        }

        LocationContext.setLocation(order.getLocation());

        List<InternetCelebritySampleOrderItemEntity> orderItems = orderItemService.getList(order.getInternetCelebritySampleOrderId());
        if (CollUtil.isEmpty(orderItems)) {
            return;
        }

        // 1. creator before video
        InternetCelebrityEntity creator = findCreator(order.getInternetCelebrityId(), order.getInternetCelebrityNo(), order.getInternetCelebrityNickname());
        if (Objects.nonNull(creator) && Objects.equals(order.getInternetCelebrityId(), 0)) {
            order.setInternetCelebrityId(creator.getId());
            orderService.saveOrUpdate(order);
        }

        List<InternetCelebrityOrderItemPostMappingEntity> mappings = mappingService.findAllByOrderItemIdIn(orderItems.stream().map(InternetCelebritySampleOrderItemEntity::getInternetCelebritySampleOrderItemId).collect(Collectors.toList()));
        Map<Integer, InternetCelebrityOrderItemPostMappingEntity> orderItemMappingMap = Lists.collectToMap(mappings, InternetCelebrityOrderItemPostMappingEntity::getInternetCelebritySampleOrderItemId);

        // 2. && 3. mapping and video
        boolean updateVideo = updateMappingAndVideoWhenProcessOrder(orderItems, orderItemMappingMap, order);

        // 4. relation
        mappings = mappingService.findAllByOrderItemIdIn(orderItems.stream().map(InternetCelebritySampleOrderItemEntity::getInternetCelebritySampleOrderItemId).collect(Collectors.toList()));
        updateRelationWhenProcessOrder(creator, mappings, order, updateVideo);

        // 5. creator
        if (Objects.nonNull(creator)) {
            updateCreatorOrder(creator);
        }
    }

    @Transactional
    public void updateRelationWhenProcessOrder(InternetCelebrityEntity creator, List<InternetCelebrityOrderItemPostMappingEntity> mappings, InternetCelebritySampleOrderEntity order, boolean updateVideo) {
        InternetCelebrityStoreRelationEntity relation = Objects.nonNull(creator) && Objects.nonNull(creator.getId())
                ? findRelation(order.getStoreId(), null, creator.getId(), creator.getInternetCelebrityNo(), creator.getInternetCelebrityName())
                : findRelation(order.getStoreId(), mappings.get(0).getStoreRelationId(), order.getInternetCelebrityId(), order.getInternetCelebrityNo(), order.getInternetCelebrityNickname());

        boolean isNewRelation = Objects.isNull(relation);
        if (isNewRelation) {
            relation = new InternetCelebrityStoreRelationEntity();
            relation.setStoreId(order.getStoreId());
            relation.setStoreName(order.getStoreName());
            relation.setRelationStatus(InternetCelebrityRelationStatus.AGREE.getValue());
            relation.setRemark("");
            relation.setCreateBy("processOrder");
            relation.setCommissionRate(new BigDecimal("0.12"));
            relation.setInternetCelebrityName(order.getInternetCelebrityNickname());
            relation.setInternetCelebrityNo(order.getInternetCelebrityNo());
            relation.setInternetCelebrityId(order.getInternetCelebrityId());
        }
        if (Objects.nonNull(creator) && Objects.nonNull(creator.getId())) {
            relation.setInternetCelebrityName(creator.getInternetCelebrityName());
            relation.setInternetCelebrityNo(creator.getInternetCelebrityNo());
            relation.setInternetCelebrityId(creator.getId());
        }
        relation.setUpdateBy("processOrder");
        relationService.saveOrUpdate(relation);
        for (InternetCelebrityOrderItemPostMappingEntity mapping : mappings) {
            mapping.setStoreRelationId(relation.getId());
            log.info("updateRelationWhenProcessOrder mappingEntity.setStoreRelationId: {}, OrderNo: {}", relation.getId(), order.getPlatformOrderNo());
        }
        mappingService.saveOrUpdateBatch(mappings);
        if (updateVideo) {
            updateRelationVideo(relation);
        }
        updateRelationOrder(relation);
    }

    @Transactional
    public boolean updateMappingAndVideoWhenProcessOrder(List<InternetCelebritySampleOrderItemEntity> orderItems, Map<Integer, InternetCelebrityOrderItemPostMappingEntity> orderItemMappingMap, InternetCelebritySampleOrderEntity order) {
        AtomicBoolean updateVideo = new AtomicBoolean(false);
        orderItems.forEach(orderItem -> {

            InternetCelebrityOrderItemPostMappingEntity mappingEntity = orderItemMappingMap.get(orderItem.getInternetCelebritySampleOrderItemId());
            // 2. mapping
            boolean newMapping = Objects.isNull(mappingEntity);
            if (newMapping) {
                mappingEntity = new InternetCelebrityOrderItemPostMappingEntity();
                mappingEntity.setInternetCelebritySampleOrderItemId(orderItem.getInternetCelebritySampleOrderItemId());
            }
            // 更新建联时间
            if (Objects.nonNull(orderItem.getOrderDeliveryDate())) {
                mappingEntity.setRelationDate(DateUtils.addDays(orderItem.getOrderDeliveryDate(), -3));
            }
            mappingService.saveOrUpdate(mappingEntity);
            // 3. video
            updateVideoWhenProcessOrder(order, orderItem, newMapping, mappingEntity, updateVideo);
        });
        return updateVideo.get();
    }

    @Transactional
    public void updateVideoWhenProcessOrder(InternetCelebritySampleOrderEntity order, InternetCelebritySampleOrderItemEntity orderItem, boolean newMapping, InternetCelebrityOrderItemPostMappingEntity mappingEntity, AtomicBoolean updateVideo) {
        if (!newMapping && !Objects.equals(mappingEntity.getInternetCelebritySampleOrderItemPostId(), 0)) {
            return;
        }
        InternetCelebritySampleOrderItemPostEntity video = findVideoWithNoOrder(order, orderItem);
        if (Objects.isNull(video)) {
            return;
        }
        updateVideo.set(true);
        mappingEntity.setInternetCelebritySampleOrderItemPostId(video.getInternetCelebritySampleOrderItemPostId());
        InternetCelebrityOrderItemPostMappingEntity mapping = mappingService.findTopByPostId(video.getInternetCelebritySampleOrderItemPostId());
        if (Objects.nonNull(mapping)) {
            mappingService.removeById(mapping.getId());
            if (Objects.nonNull(mapping.getStoreRelationId()) && mapping.getStoreRelationId() > 0) {
                log.info("updateVideoWhenProcessOrder mappingEntity.setStoreRelationId: {}, OrderNo: {}", mapping.getStoreRelationId(), order.getPlatformOrderNo());
                mappingEntity.setStoreRelationId(mapping.getStoreRelationId());
            }
        }
        mappingService.saveOrUpdate(mappingEntity);
        updateVideoName(video, orderItem);
        updateGMVWhenProcessVideo(video);
    }

    /**
     * 查询没有匹配订单的视频
     *
     * @param order
     * @param orderItem
     * @return
     */
    @Nullable
    public InternetCelebritySampleOrderItemPostEntity findVideoWithNoOrder(InternetCelebritySampleOrderEntity order, InternetCelebritySampleOrderItemEntity orderItem) {
        InternetCelebritySampleOrderItemPostEntity video = null;
        if (StrUtil.isEmpty(orderItem.getSellerProductId())) {
            List<Integer> websiteIdList = saStoreWebsiteService.getWebsiteIdList(order.getStoreId());
            if (CollUtil.isEmpty(websiteIdList)) {
                return video;
            }
            PublishProductSpecInfo publishProductSpecInfo = new PublishProductSpecInfo();
            publishProductSpecInfo.setStoreId(order.getStoreId());
            publishProductSpecInfo.setWebsiteId(websiteIdList.get(0));
            publishProductSpecInfo.setErpSku(orderItem.getSku());
            List<PublishProductSpecInfo> publishSpecInfos = omsPublishApiService.getPublishSpecInfos(Collections.singletonList(publishProductSpecInfo));
            if (CollUtil.isEmpty(publishSpecInfos) || StrUtil.isEmpty(publishSpecInfos.get(0).getWebsiteProductCode())) {
                return video;
            }
            orderItem.setSellerProductId(publishSpecInfos.get(0).getWebsiteProductCode());
            orderItem.setSellerSkuId(publishSpecInfos.get(0).getWebsiteItemCode());
            orderItemService.saveOrUpdate(orderItem);
        }

        if (StrUtil.isEmpty(orderItem.getSellerProductId())) {
            return video;
        }

        video = Objects.nonNull(order.getInternetCelebrityId()) && order.getInternetCelebrityId() > 0 ? videoMapper.findVideoByCreatorIdWithNoOrder(order.getInternetCelebrityId(), order.getStoreId(), orderItem.getSellerProductId()) : null;
        if (Objects.isNull(video) && StrUtil.isNotEmpty(order.getInternetCelebrityNo())) {
            video = videoMapper.findVideoByCreatorNoWithNoOrder(order.getInternetCelebrityNo(), order.getStoreId(), orderItem.getSellerProductId());
        }
        if (Objects.isNull(video) && StrUtil.isNotEmpty(order.getInternetCelebrityNickname())) {
            video = videoMapper.findVideoByCreatorNameWithNoOrder(order.getInternetCelebrityNickname(), order.getStoreId(), orderItem.getSellerProductId());
        }
        return video;
    }

    /**
     * @param id
     */
    @Transactional
    @Override
    public void processRelation(Integer id) {
        IntranetContext.setIntranet(1);
        InternetCelebrityStoreRelationEntity relation = relationService.getById(id);
        if (Objects.isNull(relation)) {
            return;
        }
        LocationContext.setLocation(relation.getLocation());

        updateRelationVideo(relation);
        updateRelationOrder(relation);

        if (relation.getInternetCelebrityId() == 0) {
            return;
        }

        InternetCelebrityEntity creator = creatorService.getById(relation.getInternetCelebrityId());
        updateCreatorOrder(creator);
        updateCreatorRelation(creator);
    }

    @Transactional
    public void updateRelationVideo(InternetCelebrityStoreRelationEntity relation) {
        if (Objects.isNull(relation)) {
            return;
        }
        List<Integer> videoIds = mappingService.findAllByRelationId(relation.getId()).stream().map(InternetCelebrityOrderItemPostMappingEntity::getInternetCelebritySampleOrderItemPostId).filter(item -> item > 0).collect(Collectors.toList());
        relation.setVideoNum(videoIds.size());
        relation.setVideoNumIn14(videoIds.size());

        boolean fixRelation = false;
        Date sixtyDaysAgo = DateUtils.addDays(new Date(), -60);
        if (Objects.nonNull(relation.getRelationDate()) && sixtyDaysAgo.after(relation.getRelationDate())) {
            fixRelation = true;
        }

        boolean fixBdNotCare = false;
        Date ninetyDaysAgo = DateUtils.addDays(new Date(), -90);
        if (Objects.nonNull(relation.getRelationDate()) && ninetyDaysAgo.after(relation.getRelationDate()) && StrUtil.isNotEmpty(relation.getBdEmail())) {
            fixBdNotCare = true;
        }
        relation.setBdNotCare(0);
        if (CollUtil.isNotEmpty(videoIds)) {

            List<InternetCelebritySampleOrderItemPostEntity> videoList = videoService.listByIds(videoIds);
            Date thirtyDaysAgo = DateUtils.addDays(new Date(), -30);
            videoList.forEach(internetCelebritySampleOrderItemPostEntity -> {
                this.updateVideoAd(internetCelebritySampleOrderItemPostEntity);
                this.updateVideoName(internetCelebritySampleOrderItemPostEntity, null);
            });
            fixRelation(relation, fixRelation, videoList, sixtyDaysAgo);
            List<InternetCelebritySampleOrderItemPostDailyEntity> videoDailyList = videoDailyService.findAllByPostIdIn(videoIds);
            fixBdNotCare(relation, fixBdNotCare, videoDailyList, ninetyDaysAgo);
            relation.setVideoGmvInTotal(videoDailyList.stream().map(InternetCelebritySampleOrderItemPostDailyEntity::getGmv).reduce(BigDecimal.ZERO, BigDecimal::add));
            relation.setVideoGmvIn30(videoDailyList.stream().filter(videoDaily -> !videoDaily.getGmvDate().before(thirtyDaysAgo)).map(InternetCelebritySampleOrderItemPostDailyEntity::getGmv).reduce(BigDecimal.ZERO, BigDecimal::add));
            relation.setVideoGmvIn90(videoDailyList.stream().filter(videoDaily -> !videoDaily.getGmvDate().before(ninetyDaysAgo)).map(InternetCelebritySampleOrderItemPostDailyEntity::getGmv).reduce(BigDecimal.ZERO, BigDecimal::add));
        } else {
            if (fixRelation) {
                relation.setRelationStatus(InternetCelebrityRelationStatus.NOT_ACTIVE.getValue());
            }
            if (fixBdNotCare) {
                relation.setBdNotCare(1);
            }
        }

        relationService.saveOrUpdate(relation);
    }

    private static void fixRelation(InternetCelebrityStoreRelationEntity relation, boolean fixRelation, List<InternetCelebritySampleOrderItemPostEntity> videoList, Date sixtyDaysAgo) {
        if (fixRelation) {
            Optional<InternetCelebritySampleOrderItemPostEntity> sixtyDaysHasVideo = videoList.stream().filter(videoDaily -> videoDaily.getPostDate().after(sixtyDaysAgo)).findAny();
            if (!sixtyDaysHasVideo.isPresent()) {
                relation.setRelationStatus(InternetCelebrityRelationStatus.NOT_ACTIVE.getValue());
            }
        }
    }

    private static void fixBdNotCare(InternetCelebrityStoreRelationEntity relation, boolean fixBdNotCare, List<InternetCelebritySampleOrderItemPostDailyEntity> videoDailyList, Date ninetyDaysAgo) {
        if (fixBdNotCare) {
            BigDecimal gmvOfNinetyDays = videoDailyList.stream().filter(videoDaily -> videoDaily.getGmvDate().after(ninetyDaysAgo)).map(InternetCelebritySampleOrderItemPostDailyEntity::getGmv).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (gmvOfNinetyDays.compareTo(BigDecimal.ZERO) == 0) {
                relation.setBdNotCare(1);
            }
        }
    }

    @Transactional
    public void updateRelationOrder(InternetCelebrityStoreRelationEntity relation) {
        if (Objects.isNull(relation)) {
            return;
        }
        List<InternetCelebrityOrderItemPostMappingEntity> orderItemMappingList = mappingService.findAllByRelationId(relation.getId()).stream().filter(orderItem -> orderItem.getInternetCelebritySampleOrderItemId() > 0).collect(Collectors.toList());

        if (CollUtil.isEmpty(orderItemMappingList)) {
            relation.setOrderFulfillmentRateIn30(BigDecimal.ZERO);
            relation.setOrderFulfillmentRateIn90(BigDecimal.ZERO);
            relation.setOrderFulfillmentRateInTotal(BigDecimal.ZERO);
            relation.setOrderSampleNum(0);
            relation.setCooperateTimes(0);
            relationService.saveOrUpdate(relation);
            return;
        }

        List<InternetCelebritySampleOrderItemEntity> orderItems = orderItemService.listByIds(orderItemMappingList.stream().map(InternetCelebrityOrderItemPostMappingEntity::getInternetCelebritySampleOrderItemId).collect(Collectors.toList()));
        relation.setOrderSampleNum(orderItems.stream().mapToInt(InternetCelebritySampleOrderItemEntity::getQty).sum());

        List<InternetCelebritySampleOrderEntity> orders = orderService.listByIds(orderItems.stream().map(InternetCelebritySampleOrderItemEntity::getInternetCelebritySampleOrderId).collect(Collectors.toList()));
        relation.setCooperateTimes(orders.size());
        Date firstOrderDate = orders.stream().map(InternetCelebritySampleOrderEntity::getOrderCreateDate).filter(Objects::nonNull).min(Date::compareTo).orElse(null);
        relation.setFirstOrderDate(firstOrderDate);
        Date sendDate = orders.stream().map(InternetCelebritySampleOrderEntity::getOrderDeliveryDate).filter(Objects::nonNull).min(Date::compareTo).orElse(null);
        if (Objects.nonNull(sendDate)) {
            Date relationDate = DateUtils.addDays(sendDate, -3);
            if (!Objects.equals(relationDate, relation.getRelationDate())) {
                relation.setRelationDate(DateUtils.addDays(sendDate, -3));
            }
            Date sixtyDaysAgo = DateUtils.addDays(new Date(), -61);
            if (sixtyDaysAgo.before(relationDate)) {
                relation.setRelationStatus(InternetCelebrityRelationStatus.AGREE.getValue());
            }
            relationService.saveOrUpdate(relation);
        }

        Map<Integer, Integer> videoOrderItemMap = Lists.collectToMap(
                orderItemMappingList.stream()
                        .filter(mappingService -> mappingService.getInternetCelebritySampleOrderItemId() > 0 && mappingService.getInternetCelebritySampleOrderItemPostId() > 0)
                        .collect(Collectors.toList()),
                InternetCelebrityOrderItemPostMappingEntity::getInternetCelebritySampleOrderItemPostId,
                InternetCelebrityOrderItemPostMappingEntity::getInternetCelebritySampleOrderItemId);
        List<InternetCelebritySampleOrderItemPostEntity> videos = CollUtil.isNotEmpty(videoOrderItemMap.keySet()) ? videoService.listByIds(videoOrderItemMap.keySet()) : new ArrayList<>();
        relation.setOrderFulfillmentRateInTotal(new BigDecimal(videos.size()).divide(new BigDecimal(orders.size()), 4, RoundingMode.HALF_UP));

        Map<Integer, InternetCelebritySampleOrderEntity> orderIdMap = Lists.collectToMap(orders, InternetCelebritySampleOrderEntity::getInternetCelebritySampleOrderId);

        AtomicInteger orderFulfillmentIn30 = new AtomicInteger(0);
        AtomicInteger orderFulfillmentIn90 = new AtomicInteger(0);
        videoOrderItemMap.forEach((videoId, orderItemId) -> {
            InternetCelebritySampleOrderItemEntity orderItemData = orderItems.stream().filter(orderItem -> Objects.equals(orderItem.getInternetCelebritySampleOrderItemId(), orderItemId)).findAny().orElse(new InternetCelebritySampleOrderItemEntity());
            InternetCelebritySampleOrderEntity orderData = orderIdMap.getOrDefault(orderItemData.getInternetCelebritySampleOrderId(), new InternetCelebritySampleOrderEntity());
            if (Objects.isNull(orderData.getOrderCompromiseDate())) {
                return;
            }
            InternetCelebritySampleOrderItemPostEntity videoData = videos.stream().filter(video -> Objects.equals(video.getInternetCelebritySampleOrderItemPostId(), videoId)).findAny().orElse(new InternetCelebritySampleOrderItemPostEntity());
            if (Objects.isNull(videoData.getPostDate())) {
                return;
            }
            long daysBetween = DateUtil.betweenDay(orderData.getOrderCompromiseDate(), videoData.getPostDate(), true);
            if (daysBetween <= 30) {
                orderFulfillmentIn30.incrementAndGet();
                orderFulfillmentIn90.incrementAndGet();
            } else if (daysBetween >= 90) {
                orderFulfillmentIn90.incrementAndGet();
            }

        });
        relation.setOrderFulfillmentRateIn30(new BigDecimal(orderFulfillmentIn30.get()).divide(new BigDecimal(orders.size()), 4, RoundingMode.HALF_UP));
        relation.setOrderFulfillmentRateIn90(new BigDecimal(orderFulfillmentIn90.get()).divide(new BigDecimal(orders.size()), 4, RoundingMode.HALF_UP));
        relationService.saveOrUpdate(relation);
    }

    @Transactional
    public void updateVideoAd(InternetCelebritySampleOrderItemPostEntity video) {
        if (Objects.isNull(video)) {
            return;
        }

        List<InternetCelebrityAdDailyEntity> allAds = adDailyService.findAllByPostIdIn(Collections.singletonList(video.getInternetCelebritySampleOrderItemPostId()));

        if (CollUtil.isEmpty(allAds)) return;

        Date date = allAds.stream().map(InternetCelebrityAdDailyEntity::getAdDate).min(Date::compareTo).get();

        video.setAdDate(date);

        video.setAdIntention(InternetCelebrityAdIntentionType.AGREE.getValue());

        Date lastYear = DateUtils.addYears(new Date(), -1);

        List<InternetCelebrityAdDailyEntity> adsIn1Year = allAds.stream().filter(ad -> lastYear.before(ad.getAdDate())).collect(Collectors.toList());

        if (CollUtil.isEmpty(adsIn1Year)) return;

        BigDecimal totalAdAmount = adsIn1Year.stream()
                .map(InternetCelebrityAdDailyEntity::getAdAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalAdSale = adsIn1Year.stream()
                .map(InternetCelebrityAdDailyEntity::getSaleAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        video.setAdAmountIn1Year(totalAdAmount);

        video.setAdRoasIn1Year(totalAdAmount.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : totalAdSale.divide(totalAdAmount, 4, RoundingMode.HALF_UP));

        videoService.saveOrUpdate(video);

    }

    public void updateVideoName(InternetCelebritySampleOrderItemPostEntity video, InternetCelebritySampleOrderItemEntity orderItem) {
        InternetCelebrityOrderItemPostMappingEntity mappingEntity = mappingService.findTopByPostId(video.getInternetCelebritySampleOrderItemPostId());
        if (Objects.isNull(mappingEntity)) {
            return;
        }
        SaStoreEntity store = saStoreService.getById(video.getStoreId());
        String department = Objects.isNull(store) ? "内贸部" : store.getDepartment();
        InternetCelebritySampleOrderItemEntity findOrderItem = Objects.nonNull(orderItem) ? orderItem : mappingEntity.getInternetCelebritySampleOrderItemId() == 0 ? null : orderItemService.getById(mappingEntity.getInternetCelebritySampleOrderItemId());
        String adName;
        if ("dokotoo".equalsIgnoreCase(department) || "B2C".equalsIgnoreCase(department)) {
            if (Objects.isNull(findOrderItem)) {
                return;
            }
            InternetCelebritySampleOrderEntity order = orderService.getById(findOrderItem.getInternetCelebritySampleOrderId());
            ProductIndex productIndex = searchApiService.getProductIndexBySku(findOrderItem.getSku());
            String creatorName = StrUtil.isNotBlank(order.getInternetCelebrityNickname()) ? order.getInternetCelebrityNickname() : video.getInternetCelebrityName();
            adName = String.format("%s-%s-%s", productIndex.getProductSku(), creatorName, video.getVideoCode());
        } else {
            if (Objects.isNull(findOrderItem)) {
                adName = String.format("%s-%s-%s", video.getInternetCelebrityName(), video.getSellerProductId(), video.getVideoCode());
            } else {
                InternetCelebritySampleOrderEntity order = orderService.getById(findOrderItem.getInternetCelebritySampleOrderId());
                String creatorName = StrUtil.isNotBlank(order.getInternetCelebrityNickname()) ? order.getInternetCelebrityNickname() : video.getInternetCelebrityName();
                String sellerProductId = StrUtil.isNotBlank(video.getSellerProductId()) ? video.getSellerProductId() : findOrderItem.getSellerProductId();
                adName = String.format("%s-%s-%s", creatorName, sellerProductId, video.getVideoCode());
            }
        }
        video.setAdName(adName);
        videoService.saveOrUpdate(video);
    }

    @Transactional
    public void updateCreatorOrder(InternetCelebrityEntity creator) {
        List<InternetCelebrityStoreRelationEntity> relationEntities = relationService.listByCelebrityId(creator.getId());

        if (CollUtil.isEmpty(relationEntities)) {
            creator.setLevel(InternetCelebrityLevelType.C.name());
        } else {
            BigDecimal videoGmvIn90 = relationEntities.stream().map(InternetCelebrityStoreRelationEntity::getVideoGmvIn90).filter(Objects::nonNull).max(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
            creator.setLevel(TiktokCreatorUtil.checkLevel(videoGmvIn90).name());
        }
        creatorService.saveOrUpdate(creator);
    }

    @Override
    public InternetCelebrityEntity findCreator(Integer internetCelebrityId, String internetCelebrityNo, String internetCelebrityName) {
        InternetCelebrityEntity creator = null;
        if (internetCelebrityId != null && internetCelebrityId > 0) {
            creator = creatorService.getById(internetCelebrityId);
        } else {
            if (StrUtil.isNotEmpty(internetCelebrityNo)) {
                creator = creatorService.findTopByNo(internetCelebrityNo);
            }
            if (Objects.nonNull(creator) || StrUtil.isEmpty(internetCelebrityName)) {
                return creator;
            }
            InternetCelebrityNameRecordEntity nameRecord = nameRecordService.findTopByCelebrityNameOrderByIdDesc(internetCelebrityName);
            if (Objects.nonNull(nameRecord)) {
                creator = creatorService.getById(nameRecord.getInternetCelebrityId());
            }
            if (Objects.isNull(creator)) {
                creator = creatorService.findTopByName(internetCelebrityName);
            }
        }
        return creator;
    }

    @Override
    public InternetCelebrityStoreRelationEntity findRelation(Integer storeId, Integer storeRelationId, Integer internetCelebrityId, String internetCelebrityNo, String internetCelebrityName) {
        InternetCelebrityStoreRelationEntity relation = null;
        if (storeRelationId != null && storeRelationId > 0) {
            relation = relationService.getById(storeRelationId);
        }

        if (internetCelebrityId != null && internetCelebrityId > 0 && Objects.isNull(relation)) {
            relation = relationService.findTopByCelebrityIdAndStoreId(internetCelebrityId, storeId);
        }

        if (Objects.isNull(relation) && StrUtil.isNotEmpty(internetCelebrityNo)) {
            relation = relationService.findTopByCelebrityNoAndStoreId(internetCelebrityNo, storeId);
        }

        if (Objects.isNull(relation) && StrUtil.isNotEmpty(internetCelebrityName)) {
            relation = relationService.findTopByCelebrityNameAndStoreId(internetCelebrityName, storeId);
        }
        return relation;
    }

}