package com.nsy.oms.business.domain.request.celebrity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * @Author: lhh
 * @Date: 2025-04-24
 * @Description:
 */
@ApiModel("建联ids")
public class StoreRelationIdsRequest {
    @ApiModelProperty("storeRelationIds")
    private List<Integer> storeRelationIds;
    @ApiModelProperty("mappingIds")
    private List<Integer> mappingIds;
    @ApiModelProperty("internetCelebritySampleOrderItemIds")
    private List<Integer> internetCelebritySampleOrderItemIds;

    public List<Integer> getStoreRelationIds() {
        return storeRelationIds;
    }

    public void setStoreRelationIds(List<Integer> storeRelationIds) {
        this.storeRelationIds = storeRelationIds;
    }

    public List<Integer> getMappingIds() {
        return mappingIds;
    }

    public void setMappingIds(List<Integer> mappingIds) {
        this.mappingIds = mappingIds;
    }

    public List<Integer> getInternetCelebritySampleOrderItemIds() {
        return internetCelebritySampleOrderItemIds;
    }

    public void setInternetCelebritySampleOrderItemIds(List<Integer> internetCelebritySampleOrderItemIds) {
        this.internetCelebritySampleOrderItemIds = internetCelebritySampleOrderItemIds;
    }
}
