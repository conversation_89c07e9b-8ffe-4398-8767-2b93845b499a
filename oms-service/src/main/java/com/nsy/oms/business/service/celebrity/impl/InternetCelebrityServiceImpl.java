package com.nsy.oms.business.service.celebrity.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.privilege.LoginInfoService;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.oms.dto.request.order.SampleOrderRequest;
import com.nsy.api.oms.dto.response.order.SampleOrderResponse;
import com.nsy.api.pms.dto.response.BaseListResponse;
import com.nsy.oms.business.domain.request.celebrity.InternetCelebrityEditRequest;
import com.nsy.oms.business.domain.request.celebrity.InternetCelebrityPageRequest;
import com.nsy.oms.business.domain.request.celebrity.InternetCelebrityRelationEditRequest;
import com.nsy.oms.business.domain.request.celebrity.InternetCelebrityRelationPageRequest;
import com.nsy.oms.business.domain.request.celebrity.UpdateInternetCelebrityNameRequest;
import com.nsy.oms.business.domain.response.celebrity.InternetCelebrityCategoryInfo;
import com.nsy.oms.business.domain.response.celebrity.InternetCelebrityDetail;
import com.nsy.oms.business.domain.response.celebrity.InternetCelebrityPostDailyInfo;
import com.nsy.oms.business.domain.response.celebrity.InternetCelebrityPostDailySearchRequest;
import com.nsy.oms.business.domain.response.celebrity.InternetCelebrityPostViewNum;
import com.nsy.oms.business.domain.response.celebrity.InternetCelebrityRelationDetail;
import com.nsy.oms.business.domain.response.celebrity.InternetCelebrityRelationResponse;
import com.nsy.oms.business.domain.response.celebrity.InternetCelebrityResponse;
import com.nsy.oms.business.domain.response.celebrity.InternetCelebrityStoreRelationInfo;
import com.nsy.oms.business.manage.erp.ErpApiService;
import com.nsy.oms.business.manage.erp.domain.GetSampleOrderResponse;
import com.nsy.oms.business.manage.erp.request.GetSampleOrderRequest;
import com.nsy.oms.business.manage.pms.PmsApiService;
import com.nsy.oms.business.manage.pms.response.ProductSpecResponse;
import com.nsy.oms.business.service.celebrity.IInternetCelebritySampleOrderItemPostService;
import com.nsy.oms.business.service.celebrity.IInternetCelebritySampleOrderItemService;
import com.nsy.oms.business.service.celebrity.IInternetCelebritySampleOrderService;
import com.nsy.oms.business.service.celebrity.InternetCelebrityCategoryService;
import com.nsy.oms.business.service.celebrity.InternetCelebrityNameRecordService;
import com.nsy.oms.business.service.celebrity.InternetCelebrityOrderItemPostMappingService;
import com.nsy.oms.business.service.celebrity.InternetCelebritySampleOrderItemPostDailyService;
import com.nsy.oms.business.service.celebrity.InternetCelebrityService;
import com.nsy.oms.business.service.celebrity.InternetCelebrityStoreRelationService;
import com.nsy.oms.business.service.platform.PlatformOrderService;
import com.nsy.oms.constants.StringConstant;
import com.nsy.oms.repository.entity.celebrity.InternetCelebrityCategoryEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebrityEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebrityNameRecordEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebrityOrderItemPostMappingEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderItemEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderItemPostDailyEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebritySampleOrderItemPostEntity;
import com.nsy.oms.repository.entity.celebrity.InternetCelebrityStoreRelationEntity;
import com.nsy.oms.repository.sql.mapper.celebrity.InternetCelebrityMapper;
import com.nsy.oms.repository.sql.mapper.celebrity.InternetCelebrityOrderItemPostMappingMapper;
import com.nsy.oms.repository.sql.mapper.celebrity.InternetCelebritySampleOrderItemPostMapper;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class InternetCelebrityServiceImpl
        extends ServiceImpl<InternetCelebrityMapper, InternetCelebrityEntity>
        implements InternetCelebrityService {
    @Inject
    private InternetCelebrityMapper internetCelebrityMapper;
    @Inject
    private InternetCelebrityService internetCelebrityService;
    @Inject
    private InternetCelebrityOrderItemPostMappingMapper internetCelebrityOrderItemPostMappingMapper;
    @Inject
    private InternetCelebrityNameRecordService internetCelebrityNameRecordService;
    @Inject
    private InternetCelebrityStoreRelationService internetCelebrityStoreRelationService;
    @Inject
    private InternetCelebrityCategoryService internetCelebrityCategoryService;
    @Inject
    private IInternetCelebritySampleOrderItemService internetCelebritySampleOrderItemService;
    @Inject
    private IInternetCelebritySampleOrderItemPostService internetCelebritySampleOrderItemPostService;
    @Inject
    private IInternetCelebritySampleOrderService internetCelebritySampleOrderService;
    @Inject
    private InternetCelebrityOrderItemPostMappingService internetCelebrityOrderItemPostMappingService;
    @Inject
    private InternetCelebritySampleOrderItemPostDailyService internetCelebritySampleOrderItemPostDailyService;
    @Inject
    private PmsApiService pmsApiService;
    @Inject
    private ErpApiService erpApiService;
    @Inject
    private LoginInfoService loginInfoService;
    @Inject
    private InternetCelebrityConsumerMessageServiceImpl internetCelebrityConsumerMessageService;
    @Autowired
    private PlatformOrderService platformOrderService;
    @Autowired
    private InternetCelebritySampleOrderItemPostMapper internetCelebritySampleOrderItemPostMapper;

    @Transactional
    @Override
    public void updateInternetCelebrityName(UpdateInternetCelebrityNameRequest request) {
        InternetCelebrityEntity entity = this.findTopByName(request.getInternetCelebrityName());
        if (Objects.nonNull(entity)) {
            throw new BusinessServiceException(String.format("%s达人账号已存在，请确认!", request.getInternetCelebrityName()));
        }
        if (request.getIds().size() > 1) {
            throw new BusinessServiceException("只能选择一条数据");
        }
        List<InternetCelebrityEntity> list = this.listByIds(request.getIds());
        list.forEach(s -> {
            s.setInternetCelebrityName(request.getInternetCelebrityName());
            internetCelebrityNameRecordService.updateNameRecord(s.getId(), s.getInternetCelebrityNo(), request.getInternetCelebrityName());
            List<InternetCelebrityStoreRelationEntity> storeRelationEntities = internetCelebrityStoreRelationService.listByCelebrityId(s.getId());
            storeRelationEntities.forEach(r -> r.setInternetCelebrityName(request.getInternetCelebrityName()));
            internetCelebrityStoreRelationService.updateBatchById(storeRelationEntities);
        });
        this.updateBatchById(list);
    }

    @Override
    public List<InternetCelebrityPostDailyInfo> postDailyGmv(InternetCelebrityPostDailySearchRequest request) {
        List<Integer> postIds = internetCelebrityOrderItemPostMappingService.findAllByRelationId(request.getStoreRelationId())
                .stream()
                .filter(s -> s.getInternetCelebritySampleOrderItemPostId() > 0)
                .map(InternetCelebrityOrderItemPostMappingEntity::getInternetCelebritySampleOrderItemPostId)
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(postIds)) {
            return Collections.emptyList();
        }
        Map<Date, BigDecimal> gmvByDate = internetCelebritySampleOrderItemPostDailyService.postDailyGmv(postIds, request)
                .stream()
                .collect(Collectors.groupingBy(
                        InternetCelebritySampleOrderItemPostDailyEntity::getGmvDate,
                        Collectors.mapping(
                                InternetCelebritySampleOrderItemPostDailyEntity::getGmv,
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add)
                        )
                ));
        return gmvByDate.entrySet().stream()
                .map(entry -> {
                    InternetCelebrityPostDailyInfo info = new InternetCelebrityPostDailyInfo();
                    info.setGmvDate(entry.getKey());
                    info.setGmv(entry.getValue());
                    return info;
                }).sorted(Comparator.comparing(InternetCelebrityPostDailyInfo::getGmvDate))
                .collect(Collectors.toList());
    }

    @Transactional
    @Override
    public void edit(InternetCelebrityEditRequest request) {
        InternetCelebrityStoreRelationEntity storeRelationEntity = internetCelebrityStoreRelationService.getById(request.getStoreRelationId());
        InternetCelebrityEntity entity = internetCelebrityService.getById(storeRelationEntity.getInternetCelebrityId());
        storeRelationEntity.setBdId(request.getBdId());
        storeRelationEntity.setBdAccount(request.getBdAccount());
        storeRelationEntity.setBdName(request.getBdName());
        storeRelationEntity.setRelationStatus(request.getRelationStatus());
        storeRelationEntity.setShiyingFitness(request.getShiyingFitness());
        storeRelationEntity.setCommissionRate(request.getCommissionRate());
        storeRelationEntity.setRemark(request.getRemark());
        internetCelebrityStoreRelationService.updateById(storeRelationEntity);
        entity.setEmail(request.getEmail());
        entity.setLevel(request.getLevel());
        entity.setHomePage(request.getHomePage());
        entity.setGmv(request.getGmv());
        entity.setFollowers(request.getFollowers());
        entity.setInternetCelebrityInfo(request.getInternetCelebrityInfo());
        entity.setInstagram(request.getInstagram());
        entity.setFacebook(request.getFacebook());
        entity.setWhatsapp(request.getWhatsapp());
        entity.setPhoneNumber(request.getPhoneNumber());
        entity.setImageUrl(request.getImageUrl());
        internetCelebrityService.updateById(entity);
    }

    @Override
    public InternetCelebrityDetail detail(Integer storeRelationId) {
        InternetCelebrityStoreRelationEntity storeRelationEntity = internetCelebrityStoreRelationService.getById(storeRelationId);
        if (Objects.isNull(storeRelationEntity)) {
            throw new BusinessServiceException("建联关系不存在");
        }
        InternetCelebrityEntity entity = internetCelebrityService.getById(storeRelationEntity.getInternetCelebrityId());
        if (Objects.isNull(entity)) {
            throw new BusinessServiceException("达人不存在");
        }
        InternetCelebrityDetail internetCelebrityDetail = new InternetCelebrityDetail();
        BeanUtilsEx.copyProperties(entity, internetCelebrityDetail);
        internetCelebrityDetail.setStoreRelationId(storeRelationEntity.getId());
        internetCelebrityDetail.setStoreId(storeRelationEntity.getStoreId());
        internetCelebrityDetail.setStoreName(storeRelationEntity.getStoreName());
        internetCelebrityDetail.setBdEmail(storeRelationEntity.getBdEmail());
        internetCelebrityDetail.setBdId(storeRelationEntity.getBdId());
        internetCelebrityDetail.setBdAccount(storeRelationEntity.getBdAccount());
        internetCelebrityDetail.setBdName(storeRelationEntity.getBdName());
        internetCelebrityDetail.setRelationStatus(storeRelationEntity.getRelationStatus());
        internetCelebrityDetail.setShiyingFitness(storeRelationEntity.getShiyingFitness());
        internetCelebrityDetail.setCommissionRate(storeRelationEntity.getCommissionRate());
        internetCelebrityDetail.setRemark(storeRelationEntity.getRemark());
        internetCelebrityDetail.setCategoryName(internetCelebrityCategoryService.findByInternetCelebrityId(entity.getId()).stream().map(InternetCelebrityCategoryEntity::getCategoryName).collect(Collectors.joining(",")));
        return internetCelebrityDetail;
    }

    @Override
    @Transactional
    public void relationEdit(InternetCelebrityRelationEditRequest request) {
        InternetCelebrityOrderItemPostMappingEntity entity = internetCelebrityOrderItemPostMappingService.getById(request.getId());
        checkData(request, entity);
        InternetCelebrityStoreRelationEntity storeRelationEntity = internetCelebrityStoreRelationService.getById(entity.getStoreRelationId());
        if (Objects.isNull(storeRelationEntity)) {
            throw new BusinessServiceException("建联关系不存在");
        }
        Optional.ofNullable(internetCelebrityService.getById(storeRelationEntity.getInternetCelebrityId())).ifPresent(c -> {
            c.setInternetCelebrityName(request.getInternetCelebrityName());
            internetCelebrityService.updateById(c);
            internetCelebrityNameRecordService.updateNameRecord(c.getId(), c.getInternetCelebrityNo(), request.getInternetCelebrityName());
        });
        storeRelationEntity.setInternetCelebrityName(request.getInternetCelebrityName());
        internetCelebrityStoreRelationService.updateById(storeRelationEntity);

        saveOrderInfo(request, entity, storeRelationEntity);
        InternetCelebritySampleOrderItemPostEntity video = saveVideoInfo(request, entity, storeRelationEntity);
        entity.setRemark(request.getRemark());
        entity.setRelationStatus(request.getRelationStatus());
        if (Objects.nonNull(request.getRelationDate())) {
            entity.setRelationDate(request.getRelationDate());
        }
        if (Objects.nonNull(request.getCreateDate())) {
            entity.setCreateDate(request.getCreateDate());
        }
        internetCelebrityOrderItemPostMappingService.updateById(entity);
        if (Objects.nonNull(video)) {
            internetCelebrityConsumerMessageService.updateVideoName(video, null);
        }
    }

    private InternetCelebritySampleOrderItemPostEntity saveVideoInfo(InternetCelebrityRelationEditRequest request, InternetCelebrityOrderItemPostMappingEntity entity, InternetCelebrityStoreRelationEntity storeRelationEntity) {
        if (StringUtils.hasText(request.getVideoCode())) {
            InternetCelebritySampleOrderItemPostEntity postEntity = Optional.ofNullable(internetCelebritySampleOrderItemPostService.findTopByStoreIdAndVideoCode(storeRelationEntity.getStoreId(), request.getVideoCode())).orElseGet(InternetCelebritySampleOrderItemPostEntity::new);
            if (Objects.isNull(postEntity.getInternetCelebritySampleOrderItemPostId())) {
                postEntity.setVideoUrl(request.getVideoUrl());
            }
            postEntity.setStoreId(storeRelationEntity.getStoreId());
            postEntity.setStoreName(storeRelationEntity.getStoreName());
            postEntity.setInternetCelebrityId(storeRelationEntity.getInternetCelebrityId());
            postEntity.setVideoAuthorization(request.getVideoAuthorization());
            postEntity.setVideoCode(request.getVideoCode());
            postEntity.setPostDate(request.getPostDate());
            postEntity.setAdIntention(request.getAdIntention());
            postEntity.setAdCode(request.getAdCode());
            postEntity.setAdDate(request.getAdDate());
            postEntity.setAdFeedback(request.getAdFeedback());
            internetCelebritySampleOrderItemPostService.saveOrUpdate(postEntity);
            entity.setInternetCelebritySampleOrderItemPostId(postEntity.getInternetCelebritySampleOrderItemPostId());
            // 更新建联时间
            if (Objects.nonNull(postEntity.getPostDate()) && Objects.isNull(entity.getRelationDate())) {
                entity.setRelationDate(DateUtils.addDays(postEntity.getPostDate(), -15));
            }
            return postEntity;
        } else {
            return null;
        }
    }

    private void saveOrderInfo(InternetCelebrityRelationEditRequest request, InternetCelebrityOrderItemPostMappingEntity entity, InternetCelebrityStoreRelationEntity storeRelationEntity) {
        if (StringUtils.hasText(request.getPlatformOrderNo())) {
            InternetCelebritySampleOrderEntity orderEntity = internetCelebritySampleOrderService.getByPlatformOrderNo(request.getPlatformOrderNo());
            InternetCelebritySampleOrderItemEntity orderItemEntity;
            if (Objects.isNull(orderEntity)) {
                orderEntity = new InternetCelebritySampleOrderEntity();
                orderItemEntity = new InternetCelebritySampleOrderItemEntity();
                orderEntity.setOrderCreateDate(request.getOrderCreateDate());
                orderEntity.setBuyerNick(request.getBuyerNick());
                orderEntity.setLocation(request.getLocation());
                orderEntity.setOrderType(request.getOrderType());
            } else {
                orderItemEntity = internetCelebritySampleOrderItemService.getList(orderEntity.getInternetCelebritySampleOrderId()).stream().filter(s -> s.getSku().equals(request.getSku())).findAny().orElseGet(InternetCelebritySampleOrderItemEntity::new);
            }
            if (Objects.isNull(orderItemEntity.getInternetCelebritySampleOrderItemId())) {
                orderItemEntity.setQty(request.getQty());
                orderItemEntity.setSellerSku(request.getSellerSku());
                orderItemEntity.setSellerSkuId(request.getSellerSkuId());
            }
            orderEntity.setStoreId(storeRelationEntity.getStoreId());
            orderEntity.setStoreName(storeRelationEntity.getStoreName());
            orderEntity.setPlatformOrderNo(request.getPlatformOrderNo());
            orderEntity.setOrderCompromiseDate(request.getOrderCompromiseDate());
            internetCelebritySampleOrderService.saveOrUpdate(orderEntity);
            orderItemEntity.setSku(request.getSku());
            orderItemEntity.setSellerProductId(request.getSellerProductId());
            orderItemEntity.setInternetCelebritySampleOrderId(orderEntity.getInternetCelebritySampleOrderId());
            orderItemEntity.setPlatformOriginalOrderNo(request.getPlatformOriginalOrderNo());
            orderItemEntity.setTrackingNumber(request.getTrackingNumber());
            orderItemEntity.setOrderCompromiseDate(request.getOrderCompromiseDate());
            orderItemEntity.setOrderDeliveryDate(request.getOrderDeliveryDate());
            internetCelebritySampleOrderItemService.saveOrUpdate(orderItemEntity);
            internetCelebritySampleOrderService.saveSampleOrderDate(orderEntity);
            entity.setInternetCelebritySampleOrderItemId(orderItemEntity.getInternetCelebritySampleOrderItemId());
            // 更新建联时间
            if (Objects.nonNull(orderItemEntity.getOrderDeliveryDate())) {
                entity.setRelationDate(DateUtils.addDays(orderItemEntity.getOrderDeliveryDate(), -3));
            }
        }
    }

    private void checkData(InternetCelebrityRelationEditRequest request, InternetCelebrityOrderItemPostMappingEntity entity) {
        if (Objects.isNull(entity)) {
            throw new BusinessServiceException("建联关系不存在");
        }
        if (StringUtils.hasText(request.getSku())) {
            ProductSpecResponse productSpecBySpecSku = pmsApiService.getProductSpecBySpecSku(request.getSku());
            if (Objects.isNull(productSpecBySpecSku)) {
                throw new BusinessServiceException(String.format("sku:%s 在系统不存在!", request.getSku()));
            }
        }
        if (StringUtils.hasText(request.getPlatformOrderNo())) {
            GetSampleOrderRequest getSampleOrderRequest = new GetSampleOrderRequest();
            getSampleOrderRequest.setLocation(loginInfoService.getLocation());
            getSampleOrderRequest.setTidList(Collections.singletonList(request.getPlatformOrderNo()));
            GetSampleOrderResponse getSampleOrderResponse = erpApiService.getSampleOrder(getSampleOrderRequest);
            SampleOrderRequest sampleOrderRequest = new SampleOrderRequest();
            sampleOrderRequest.setIsSampleOrder(1);
            sampleOrderRequest.setPlatformOriginalOrderNo(request.getPlatformOrderNo());
            List<SampleOrderResponse> sampleOrderResponses = platformOrderService.getSampleOrders(sampleOrderRequest);
            if ((!Optional.ofNullable(getSampleOrderResponse).isPresent() || CollectionUtils.isEmpty(getSampleOrderResponse.getSampleOrderList())) && CollectionUtils.isEmpty(sampleOrderResponses)) {
                throw new BusinessServiceException(String.format("订单号:%s 在系统不存在!", request.getPlatformOrderNo()));
            }
        }
        if (StringUtils.hasText(request.getSku()) && !StringUtils.hasText(request.getPlatformOrderNo()) || !StringUtils.hasText(request.getSku()) && StringUtils.hasText(request.getPlatformOrderNo())) {
            throw new BusinessServiceException("请同时填写订单号和sku");
        }
        if ((StringUtils.hasText(request.getSku()) || StringUtils.hasText(request.getSellerProductId()) || Objects.nonNull(request.getOrderCompromiseDate())) && !StringUtils.hasText(request.getPlatformOrderNo())) {
            throw new BusinessServiceException("填写订单号");
        }
    }

    @Override
    public InternetCelebrityRelationDetail relationDetail(Integer id) {
        InternetCelebrityOrderItemPostMappingEntity entity = internetCelebrityOrderItemPostMappingService.getById(id);
        if (Objects.isNull(entity)) {
            throw new BusinessServiceException("建联关系不存在");
        }
        InternetCelebrityRelationDetail response = new InternetCelebrityRelationDetail();
        response.setRelationStatus(entity.getRelationStatus());
        response.setRemark(entity.getRemark());
        response.setRelationDate(entity.getRelationDate());
        response.setCreateDate(entity.getCreateDate());
        Optional.ofNullable(internetCelebrityStoreRelationService.getById(entity.getStoreRelationId())).ifPresent(s -> {
            response.setStoreId(s.getStoreId());
            response.setStoreName(s.getStoreName());
            response.setInternetCelebrityName(s.getInternetCelebrityName());
            response.setBdEmail(s.getBdEmail());
            response.setStoreRelationStatus(s.getRelationStatus());
        });
        Optional.ofNullable(internetCelebritySampleOrderItemService.getById(entity.getInternetCelebritySampleOrderItemId())).ifPresent(s -> {
            response.setSku(s.getSku());
            response.setSellerProductId(s.getSellerProductId());
            response.setQty(s.getQty());
            response.setTrackingNumber(s.getTrackingNumber());
            response.setOrderCompromiseDate(s.getOrderCompromiseDate());
            response.setOrderDeliveryDate(s.getOrderDeliveryDate());
            Optional.ofNullable(internetCelebritySampleOrderService.getById(s.getInternetCelebritySampleOrderId())).ifPresent(o -> {
                response.setPlatformOrderNo(o.getPlatformOrderNo());
            });
        });
        Optional.ofNullable(internetCelebritySampleOrderItemPostService.getById(entity.getInternetCelebritySampleOrderItemPostId())).ifPresent(s -> {
            response.setVideoAuthorization(s.getVideoAuthorization());
            response.setVideoCode(s.getVideoCode());
            response.setPostDate(s.getPostDate());
            response.setAdIntention(s.getAdIntention());
            response.setAdCode(s.getAdCode());
            response.setAdName(s.getAdName());
            response.setAdDate(s.getAdDate());
            response.setAdFeedback(s.getAdFeedback());
        });
        return response;
    }

    @Override
    public long relationCount(InternetCelebrityRelationPageRequest request) {
        buildRequest(request);
        return internetCelebrityOrderItemPostMappingMapper.count(request);
    }

    @Override
    public BaseListResponse<InternetCelebrityRelationResponse> relationSearch(InternetCelebrityRelationPageRequest request) {
        BaseListResponse<InternetCelebrityRelationResponse> response = new BaseListResponse<>();
        buildRequest(request);
        Page<InternetCelebrityOrderItemPostMappingEntity> page = internetCelebrityOrderItemPostMappingMapper.page(new Page<>(request.getPageIndex(), request.getPageSize(), false), request);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return BaseListResponse.of(new ArrayList<>(), 0L);
        }
        response.setTotalCount(page.getTotal());
        response.setContent(buildRelation(page.getRecords()));
        return response;
    }

    private void buildRequest(InternetCelebrityRelationPageRequest request) {
        if (StringUtils.hasText(request.getInternetCelebrityName()) && request.getInternetCelebrityName().contains(",")) {
            request.setInternetCelebrityNameList(Arrays.asList(request.getInternetCelebrityName().split(",")));
            request.setInternetCelebrityName(StringConstant.EMPTY);
        }
        if (StringUtils.hasText(request.getPlatformOrderNo()) && request.getPlatformOrderNo().contains(",")) {
            request.setPlatformOrderNoList(Arrays.asList(request.getPlatformOrderNo().split(",")));
            request.setPlatformOrderNo(StringConstant.EMPTY);
        }
        if (StringUtils.hasText(request.getVideoCode()) && request.getVideoCode().contains(",")) {
            request.setVideoCodeList(Arrays.asList(request.getVideoCode().split(",")));
            request.setVideoCode(StringConstant.EMPTY);
        }
        if (!CollectionUtils.isEmpty(request.getBdEmailList())) {
            if (request.getBdEmailList().contains("未分配")) {
                request.getBdEmailList().remove("未分配");
                request.setBdId(0);
            }
            if (request.getBdEmailList().contains("公海未归属")) {
                request.getBdEmailList().remove("公海未归属");
                request.setBdNotCare(1);
            }
        }
    }

    private List<InternetCelebrityRelationResponse> buildRelation(List<InternetCelebrityOrderItemPostMappingEntity> records) {
        List<Integer> storeRelationIds = records.stream().map(InternetCelebrityOrderItemPostMappingEntity::getStoreRelationId).collect(Collectors.toList());
        List<Integer> orderItemIds = records.stream().map(InternetCelebrityOrderItemPostMappingEntity::getInternetCelebritySampleOrderItemId).collect(Collectors.toList());
        List<Integer> postIds = records.stream().map(InternetCelebrityOrderItemPostMappingEntity::getInternetCelebritySampleOrderItemPostId).collect(Collectors.toList());
        List<InternetCelebrityStoreRelationEntity> storeRelationEntityList = internetCelebrityStoreRelationService.listByIdList(storeRelationIds);
        List<InternetCelebritySampleOrderItemEntity> orderItemEntityList = internetCelebritySampleOrderItemService.listByIdList(orderItemIds);
        List<InternetCelebritySampleOrderItemPostEntity> postEntityList = internetCelebritySampleOrderItemPostService.listByIdList(postIds);
        List<Integer> celebrityIds = storeRelationEntityList.stream().map(InternetCelebrityStoreRelationEntity::getInternetCelebrityId).distinct().collect(Collectors.toList());
        List<InternetCelebrityEntity> internetCelebrityEntities = this.listByIds(celebrityIds);
        List<Integer> oderIds = orderItemEntityList.stream().map(InternetCelebritySampleOrderItemEntity::getInternetCelebritySampleOrderId).distinct().collect(Collectors.toList());
        List<InternetCelebritySampleOrderEntity> orderEntityList = internetCelebritySampleOrderService.listByIdList(oderIds);
        List<InternetCelebrityPostViewNum> videoViews = internetCelebritySampleOrderItemPostMapper.getVideoViewsByPostIdIn(postIds);
        return records.stream().map(s -> {
            InternetCelebrityRelationResponse response = new InternetCelebrityRelationResponse();
            response.setRelationStatus(s.getRelationStatus());
            response.setRemark(s.getRemark());
            response.setId(s.getId());
            response.setInternetCelebritySampleOrderItemPostId(s.getInternetCelebritySampleOrderItemPostId());
            response.setInternetCelebritySampleOrderItemId(s.getInternetCelebritySampleOrderItemId());
            response.setRelationDate(s.getRelationDate());
            response.setCreateDate(s.getCreateDate());
            storeRelationEntityList.stream().filter(c -> c.getId().equals(s.getStoreRelationId())).findAny().ifPresent(c -> {
                response.setStoreRelationId(c.getId());
                response.setFirstRelationDate(c.getRelationDate());
                response.setStoreName(c.getStoreName());
                response.setBdEmail(c.getBdEmail());
                response.setBdName(c.getBdName());
                response.setStoreRelationStatus(c.getRelationStatus());
                response.setInternetCelebrityName(c.getInternetCelebrityName());
                response.setBdNotCare(c.getBdNotCare());
                internetCelebrityEntities.stream().filter(i -> i.getId().equals(c.getInternetCelebrityId())).findAny().ifPresent(i -> {
                    response.setInternetCelebrityName(i.getInternetCelebrityName());
                    response.setFollowers(i.getFollowers());
                });
            });
            postEntityList.stream().filter(p -> p.getInternetCelebritySampleOrderItemPostId().equals(s.getInternetCelebritySampleOrderItemPostId())).findAny().ifPresent(p -> {
                buildVideoInfo(response, p, videoViews);
            });
            orderItemEntityList.stream().filter(o -> o.getInternetCelebritySampleOrderItemId().equals(s.getInternetCelebritySampleOrderItemId())).findAny().ifPresent(item -> {
                response.setSku(item.getSku());
                response.setImageUrl(item.getSkuPictureUrl());
                response.setOrderDeliveryDate(item.getOrderDeliveryDate());
                response.setOrderCompromiseDate(item.getOrderCompromiseDate());
                response.setLogisticsNo(item.getTrackingNumber());
                orderEntityList.stream().filter(o -> o.getInternetCelebritySampleOrderId().equals(item.getInternetCelebritySampleOrderId())).findAny().ifPresent(o -> {
                    response.setOrderCreateDate(o.getOrderCreateDate());
                    response.setPlatformOrderNo(o.getPlatformOrderNo());
                    response.setInternetCelebritySampleOrderId(o.getInternetCelebritySampleOrderId());
                    response.setManualOrder(!o.getSampleStoreId().equals(o.getStoreId()) ? 1 : 0);
                });
            });
            return response;
        }).collect(Collectors.toList());
    }

    private void buildVideoInfo(InternetCelebrityRelationResponse response, InternetCelebritySampleOrderItemPostEntity p, List<InternetCelebrityPostViewNum> videoViews) {
        response.setVideoCode(p.getVideoCode());
        response.setVideoUrl(p.getVideoUrl());
        response.setPostDate(p.getPostDate());
        response.setGmv(p.getGmv());
        videoViews.stream().filter(s -> s.getPostId().equals(p.getInternetCelebritySampleOrderItemPostId())).findAny().ifPresent(s -> {
            response.setVideoViews(s.getVideoViews());
        });
        response.setAdIntention(p.getAdIntention());
        response.setAdDate(p.getAdDate());
        response.setAdFeedback(p.getAdFeedback());
        response.setAdAmountIn1Year(p.getAdAmountIn1Year());
        response.setAdRoasIn1Year(p.getAdRoasIn1Year());
    }

    @Override
    public BaseListResponse<InternetCelebrityResponse> search(InternetCelebrityPageRequest request) {
        BaseListResponse<InternetCelebrityResponse> response = new BaseListResponse<>();
        if (StringUtils.hasText(request.getInternetCelebrityName()) && request.getInternetCelebrityName().contains(",")) {
            request.setInternetCelebrityNameList(Arrays.asList(request.getInternetCelebrityName().split(",")));
            request.setInternetCelebrityName(StringConstant.EMPTY);
        }
        if (!CollectionUtils.isEmpty(request.getBdEmailList())) {
            if (request.getBdEmailList().contains("未分配")) {
                request.getBdEmailList().remove("未分配");
                request.setBdId(0);
            }
            if (request.getBdEmailList().contains("公海未归属")) {
                request.getBdEmailList().remove("公海未归属");
                request.setBdNotCare(1);
            }
        }
        Page<InternetCelebrityEntity> page = internetCelebrityMapper.page(new Page<>(request.getPageIndex(), request.getPageSize()), request);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return BaseListResponse.of(new ArrayList<>(), 0L);
        }
        response.setTotalCount(page.getTotal());
        response.setContent(build(page.getRecords(), request));
        return response;
    }

    private List<InternetCelebrityResponse> build(List<InternetCelebrityEntity> records, InternetCelebrityPageRequest request) {
        List<Integer> celebrityIds = records.stream().map(InternetCelebrityEntity::getId).collect(Collectors.toList());
        List<InternetCelebrityNameRecordEntity> nameList = internetCelebrityNameRecordService.listByCelebrityIds(celebrityIds);
        List<InternetCelebrityStoreRelationEntity> storeRelationList = internetCelebrityStoreRelationService.listByCelebrityIds(celebrityIds);
        List<InternetCelebrityCategoryEntity> categoryEntityList = internetCelebrityCategoryService.listByCelebrityIds(celebrityIds);
        return records.stream().map(s -> {
            InternetCelebrityResponse internetCelebrityResponse = new InternetCelebrityResponse();
            BeanUtilsEx.copyProperties(s, internetCelebrityResponse);
            List<InternetCelebrityNameRecordEntity> recordEntityList = nameList.stream()
                    .filter(n -> n.getInternetCelebrityId().equals(s.getId()))
                    .sorted(Comparator.comparing(InternetCelebrityNameRecordEntity::getId)).collect(Collectors.toList());
            if (recordEntityList.size() > 0 && recordEntityList.get(recordEntityList.size() - 1).getInternetCelebrityName().equalsIgnoreCase(s.getInternetCelebrityName())) {
                recordEntityList = new ArrayList<>(recordEntityList.subList(0, recordEntityList.size() - 1));
            }
            internetCelebrityResponse.setOldInternetCelebrityName(recordEntityList.stream()
                    .map(InternetCelebrityNameRecordEntity::getInternetCelebrityName).distinct()
                    .collect(Collectors.joining(",")));
            List<InternetCelebrityStoreRelationEntity> storeRelationEntityList = storeRelationList.stream().filter(r -> r.getInternetCelebrityId().equals(s.getId())).collect(Collectors.toList());
            if (StringUtils.hasText(request.getSortFiled()) && "videoGmvIn30".equals(request.getSortFiled())) {
                if ("desc".equalsIgnoreCase(request.getSortOrder())) {
                    storeRelationEntityList = storeRelationEntityList.stream().sorted(Comparator.comparing(InternetCelebrityStoreRelationEntity::getVideoGmvIn30).reversed()).collect(Collectors.toList());
                }
                if ("asc".equalsIgnoreCase(request.getSortOrder())) {
                    storeRelationEntityList = storeRelationEntityList.stream().sorted(Comparator.comparing(InternetCelebrityStoreRelationEntity::getVideoGmvIn30)).collect(Collectors.toList());
                }
            }
            internetCelebrityResponse.setInternetCelebrityStoreRelationInfoList(storeRelationEntityList.stream().map(r -> {
                InternetCelebrityStoreRelationInfo internetCelebrityStoreRelationInfo = new InternetCelebrityStoreRelationInfo();
                BeanUtilsEx.copyProperties(r, internetCelebrityStoreRelationInfo);
                internetCelebrityStoreRelationInfo.setStoreRelationId(r.getId());
                if (Objects.nonNull(request.getGmvStartDate()) || Objects.nonNull(request.getGmvEndDate())) {
                    InternetCelebrityPostDailySearchRequest dailySearchRequest = new InternetCelebrityPostDailySearchRequest();
                    dailySearchRequest.setStoreRelationId(r.getId());
                    dailySearchRequest.setGmvStartDate(request.getGmvStartDate());
                    dailySearchRequest.setGmvEndDate(request.getGmvEndDate());
                    List<InternetCelebrityPostDailyInfo> dailyInfoList = postDailyGmv(dailySearchRequest);
                    internetCelebrityStoreRelationInfo.setVideoGmvInTotal(dailyInfoList.stream().map(InternetCelebrityPostDailyInfo::getGmv).reduce(BigDecimal.ZERO, BigDecimal::add));
                }
                return internetCelebrityStoreRelationInfo;
            }).collect(Collectors.toList()));
            internetCelebrityResponse.setInternetCelebrityCategoryInfos(categoryEntityList.stream().filter(c -> c.getInternetCelebrityId().equals(s.getId())).sorted(Comparator.comparing(InternetCelebrityCategoryEntity::getRate).reversed()).map(c -> {
                InternetCelebrityCategoryInfo categoryInfo = new InternetCelebrityCategoryInfo();
                categoryInfo.setCategoryName(c.getCategoryName());
                categoryInfo.setRate(c.getRate());
                return categoryInfo;
            }).collect(Collectors.toList()));
            return internetCelebrityResponse;
        }).collect(Collectors.toList());
    }

    @Override
    public InternetCelebrityEntity findTopByNo(String creatorNo) {
        return this.getOne(new LambdaQueryWrapper<InternetCelebrityEntity>()
                .eq(InternetCelebrityEntity::getInternetCelebrityNo, creatorNo)
                .orderByDesc(InternetCelebrityEntity::getId)
                .last("limit 1")
        );
    }

    @Override
    public InternetCelebrityEntity findTopByName(String internetCelebrityName) {
        return this.getOne(new LambdaQueryWrapper<InternetCelebrityEntity>()
                .eq(InternetCelebrityEntity::getInternetCelebrityName, internetCelebrityName)
                .orderByDesc(InternetCelebrityEntity::getId)
                .last("limit 1")
        );
    }

}