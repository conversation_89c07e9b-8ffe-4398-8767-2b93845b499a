package com.nsy.oms.business.domain.response.stockout;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * 海外仓skc表(分页用，主表)
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@ApiModel("海外仓sku仓库详情响应实体")
public class OverseaSkuSpaceItemResponse {

    @ApiModelProperty(value = "sku", name = "sku")
    private String sku;

    /**
     * 仓库ID
     */
    @ApiModelProperty(value = "仓库ID", name = "spaceId")
    private Integer spaceId;

    /**
     * 仓库名称
     */
    @ApiModelProperty(value = "仓库名称", name = "spaceName")
    private String spaceName;

    /**
     * 海外仓可用库存
     */
    @ApiModelProperty(value = "可用库存", name = "availableStock")
    private Integer availableStock;

    /**
     * 海外仓在途库存
     */
    @ApiModelProperty(value = "在途库存", name = "onTheWayStock")
    private Integer onTheWayStock;


    /**
     * 7日出库量
     */
    @ApiModelProperty(value = "7日出库量", name = "stockOutQtyIn7")
    private Integer stockOutQtyIn7;

    /**
     * 14日出库量
     */
    @ApiModelProperty(value = "14日出库量", name = "stockOutQtyIn14")
    private Integer stockOutQtyIn14;

    /**
     * 30日出库量
     */
    @ApiModelProperty(value = "30日出库量", name = "stockOutQtyIn30")
    private Integer stockOutQtyIn30;

    /**
     * 7日均出库量
     */
    @ApiModelProperty(value = "7日均出库量", name = "dailyStockOutQtyIn7")
    private BigDecimal dailyStockOutQtyIn7;

    /**
     * 14日均出库量
     */
    @ApiModelProperty(value = "14日均出库量", name = "dailyStockOutQtyIn14")
    private BigDecimal dailyStockOutQtyIn14;

    /**
     * 30日均出库量
     */
    @ApiModelProperty(value = "30日均出库量", name = "dailyStockOutQtyIn30")
    private BigDecimal dailyStockOutQtyIn30;

    /**
     * 可售天数
     */
    @ApiModelProperty(value = "可售天数", name = "availableDays")
    private Integer availableDays;

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public Integer getStockOutQtyIn7() {
        return stockOutQtyIn7;
    }

    public void setStockOutQtyIn7(Integer stockOutQtyIn7) {
        this.stockOutQtyIn7 = stockOutQtyIn7;
    }

    public Integer getStockOutQtyIn14() {
        return stockOutQtyIn14;
    }

    public void setStockOutQtyIn14(Integer stockOutQtyIn14) {
        this.stockOutQtyIn14 = stockOutQtyIn14;
    }

    public Integer getStockOutQtyIn30() {
        return stockOutQtyIn30;
    }

    public void setStockOutQtyIn30(Integer stockOutQtyIn30) {
        this.stockOutQtyIn30 = stockOutQtyIn30;
    }

    public BigDecimal getDailyStockOutQtyIn7() {
        return dailyStockOutQtyIn7;
    }

    public void setDailyStockOutQtyIn7(BigDecimal dailyStockOutQtyIn7) {
        this.dailyStockOutQtyIn7 = dailyStockOutQtyIn7;
    }

    public BigDecimal getDailyStockOutQtyIn14() {
        return dailyStockOutQtyIn14;
    }

    public void setDailyStockOutQtyIn14(BigDecimal dailyStockOutQtyIn14) {
        this.dailyStockOutQtyIn14 = dailyStockOutQtyIn14;
    }

    public BigDecimal getDailyStockOutQtyIn30() {
        return dailyStockOutQtyIn30;
    }

    public void setDailyStockOutQtyIn30(BigDecimal dailyStockOutQtyIn30) {
        this.dailyStockOutQtyIn30 = dailyStockOutQtyIn30;
    }

    public Integer getAvailableDays() {
        return availableDays;
    }

    public void setAvailableDays(Integer availableDays) {
        this.availableDays = availableDays;
    }

    public Integer getAvailableStock() {
        return availableStock;
    }

    public void setAvailableStock(Integer availableStock) {
        this.availableStock = availableStock;
    }

    public Integer getOnTheWayStock() {
        return onTheWayStock;
    }

    public void setOnTheWayStock(Integer onTheWayStock) {
        this.onTheWayStock = onTheWayStock;
    }
}