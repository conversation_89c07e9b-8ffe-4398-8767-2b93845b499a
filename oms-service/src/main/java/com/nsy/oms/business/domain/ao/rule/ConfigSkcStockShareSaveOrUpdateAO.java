package com.nsy.oms.business.domain.ao.rule;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * skc库存共享
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-01
 */
@Data
public class ConfigSkcStockShareSaveOrUpdateAO {

    @ApiModelProperty("skc库存共享id")
    private Integer configSkcStockShareId;

    @NotNull(message = "【仓库id】不能为空")
    @ApiModelProperty("仓库id")
    private Integer spaceId;

    @NotBlank(message = "【仓库名称】不能为空")
    @ApiModelProperty("仓库名称")
    private String spaceName;

    @NotBlank(message = "【skc】不能为空")
    @ApiModelProperty("skc")
    private String skc;

    @NotBlank(message = "【部门名称】不能为空")
    @ApiModelProperty("部门名称")
    private String departmentName;

    @Valid
    @ApiModelProperty("skc库存共享明细")
    private List<ConfigSkcStockShareSaveOrUpdateItemAO> configSkcStockShareItemList;

}
