package com.nsy.oms.business.domain.request.amazon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @date 2025-05-08
 */
@ApiModel("亚马逊订单利润率监控弹框下拉框请求")
public class AmazonOrderProfitMonitoringSelectRequest {
    @ApiModelProperty("spu")
    private String spu;
    @ApiModelProperty("店铺id")
    private Integer storeId;
    @ApiModelProperty("父asin")
    private String parentAsin;
    @ApiModelProperty("skc")
    private String skc;
    @ApiModelProperty("sku")
    private String sku;

    public String getSpu() {
        return spu;
    }

    public void setSpu(String spu) {
        this.spu = spu;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getParentAsin() {
        return parentAsin;
    }

    public void setParentAsin(String parentAsin) {
        this.parentAsin = parentAsin;
    }

    public String getSkc() {
        return skc;
    }

    public void setSkc(String skc) {
        this.skc = skc;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }
}
