package com.nsy.oms.business.service.order.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.MD5Util;
import com.nsy.api.core.apicore.util.NsyJacksonUtils;
import com.nsy.oms.business.domain.dto.CommonOrderDTO;
import com.nsy.oms.business.domain.request.external.CrmOrderPageListRequest;
import com.nsy.oms.business.domain.response.external.CrmOrderInoResponse;
import com.nsy.oms.business.domain.response.external.CrmOrderPageListResponse;
import com.nsy.oms.business.domain.response.order.GetPlatformOrderItemResponse;
import com.nsy.oms.business.domain.response.order.GetPlatformOrderResponse;
import com.nsy.oms.business.domain.response.platform.PlatformOrderItemResponse;
import com.nsy.oms.business.domain.response.platform.PlatformOrderResponse;
import com.nsy.oms.business.service.bd.BdMarketplaceService;
import com.nsy.oms.business.service.order.SaleOrderExtendService;
import com.nsy.oms.business.service.order.OrderGrabStatusService;
import com.nsy.oms.business.service.order.OrderIdempotentService;
import com.nsy.oms.business.service.order.OrderItemGrabQueueService;
import com.nsy.oms.business.service.order.SaleOrderItemService;
import com.nsy.oms.business.service.order.SaleOrderReceiverService;
import com.nsy.oms.business.service.order.SaleOrderService;
import com.nsy.oms.business.service.sa.SaStoreService;
import com.nsy.oms.business.service.sync.AsyncService;
import com.nsy.oms.enums.order.OrderTypeEnum;
import com.nsy.oms.enums.platform.PlatformOrderItemStatusEnum;
import com.nsy.oms.enums.platform.PlatformOrderStatusEnum;
import com.nsy.oms.repository.entity.bd.BdMarketplaceEntity;
import com.nsy.oms.repository.entity.order.SaleOrderEntity;
import com.nsy.oms.repository.entity.order.SaleOrderExtendEntity;
import com.nsy.oms.repository.entity.order.OrderGrabStatusEntity;
import com.nsy.oms.repository.entity.order.OrderIdempotentEntity;
import com.nsy.oms.repository.entity.order.SaleOrderItemEntity;
import com.nsy.oms.repository.entity.order.OrderItemGrabQueueEntity;
import com.nsy.oms.repository.entity.order.SaleOrderReceiverEntity;
import com.nsy.oms.repository.entity.sa.SaStoreEntity;
import com.nsy.oms.repository.sql.mapper.order.SaleOrderMapper;
import com.nsy.oms.utils.DateUtils;
import com.nsy.oms.utils.SetValueUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【order(订单表)】的数据库操作Service实现
 * @createDate 2024-06-13 14:52:05
 */
@Service
public class SaleOrderServiceImpl extends ServiceImpl<SaleOrderMapper, SaleOrderEntity>
        implements SaleOrderService {
    private static final Logger LOGGER = LoggerFactory.getLogger(SaleOrderServiceImpl.class);
    @Autowired
    private OrderIdempotentService orderIdempotentService;
    @Autowired
    private SaStoreService storeService;
    @Autowired
    private SaleOrderService saleOrderService;
    @Autowired
    private BdMarketplaceService marketplaceService;
    @Autowired
    private SaleOrderReceiverService saleOrderReceiverService;
    @Autowired
    private OrderItemGrabQueueService orderItemGrabQueueService;
    @Autowired
    private SaleOrderItemService saleOrderItemService;
    @Autowired
    private AsyncService asyncService;
    @Autowired
    private OrderGrabStatusService orderGrabStatusService;
    @Autowired
    private SaleOrderExtendService saleOrderExtendService;



    @Override
    public SaleOrderEntity getByOrderNo(Integer storeId, String orderNo) {
        return this.getOne(Wrappers.<SaleOrderEntity>lambdaQuery().eq(SaleOrderEntity::getStoreId, storeId).eq(SaleOrderEntity::getOrderNo, orderNo));
    }

    /**
     * 通过店铺Id和平台订单号查询订单
     */
    @Override
    public SaleOrderEntity getByPlatformOrderNo(Integer storeId, String platformOrderNo) {
        return this.getOne(Wrappers.<SaleOrderEntity>lambdaQuery().eq(SaleOrderEntity::getStoreId, storeId).eq(SaleOrderEntity::getPlatformOrderNo, platformOrderNo));
    }

    @Override
    public List<SaleOrderEntity> getListByStoreIdAndOrderNos(Integer storeId, List<String> orderNos) {
        LambdaQueryWrapper<SaleOrderEntity> lambdaQueryWrapper = Wrappers.<SaleOrderEntity>lambdaQuery()
                .eq(SaleOrderEntity::getStoreId, storeId)
                .in(SaleOrderEntity::getOrderNo, orderNos);
        return this.list(lambdaQueryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrder(Integer storeId, GetPlatformOrderResponse order) {
        // 检验订单幂等
        OrderIdempotentEntity orderIdempotentEntity = orderIdempotentService.getByStoreIdAndOrderNo(storeId, order.getOrderId());
        String orderCrypt = MD5Util.crypt(NsyJacksonUtils.toJson(order));
        if (Objects.nonNull(orderIdempotentEntity)) {
            LOGGER.info("店铺ID_订单号_幂等表加密_单据加密:{}", String.format("%s_%s_%s_%s", storeId, order.getOrderId(), orderIdempotentEntity.getContent(), orderCrypt));
            if (orderIdempotentEntity.getContent().equals(orderCrypt)) {
                return;
            }
        }
        SaStoreEntity storeEntity = storeService.getByStoreId(storeId);
        SaleOrderEntity saleOrderEntity = Optional.ofNullable(saleOrderService.getByOrderNo(storeEntity.getId(), order.getOrderId())).orElse(new SaleOrderEntity());
        // 订单状态：已发货不更新
        if (PlatformOrderStatusEnum.DELIVERY.getCode().equals(saleOrderEntity.getOrderStatus())) {
            return;
        }
        saveOrderEntity(storeEntity, order, saleOrderEntity);

        // 寄件人信息
        saveReceiverEntity(order, saleOrderEntity);

        // 保存幂等表
        saveOrderIdempotent(order.getOrderId(), orderCrypt, saleOrderEntity, orderIdempotentEntity);

        // 生成队列
        OrderItemGrabQueueEntity queueEntity = new OrderItemGrabQueueEntity();
        queueEntity.setStoreId(saleOrderEntity.getStoreId());
        queueEntity.setPlatformId(saleOrderEntity.getPlatformId());
        queueEntity.setOrderId(saleOrderEntity.getOrderId());
        queueEntity.setOrderNo(saleOrderEntity.getOrderNo());
        queueEntity.setOrderType(OrderTypeEnum.FBM.getCode());
        queueEntity.setPriority(1);
        queueEntity.setLocation(saleOrderEntity.getLocation());
        orderItemGrabQueueService.generateQueue(queueEntity);

        // 保存或更新抓单状态表
        OrderGrabStatusEntity grabStatusEntity = new OrderGrabStatusEntity();
        grabStatusEntity.setStoreId(saleOrderEntity.getStoreId());
        grabStatusEntity.setOrderId(saleOrderEntity.getOrderId());
        grabStatusEntity.setOrderNo(saleOrderEntity.getOrderNo());
        grabStatusEntity.setOrderType(OrderTypeEnum.FBM.getCode());
        grabStatusEntity.setLocation(saleOrderEntity.getLocation());
        orderGrabStatusService.saveOrUpdateEntity(grabStatusEntity);
    }

    public void saveOrderEntity(SaStoreEntity storeEntity, GetPlatformOrderResponse order, SaleOrderEntity saleOrderEntity) {
        BdMarketplaceEntity marketplaceEntity = null;
        if (StringUtils.isNotBlank(order.getMarketplaceId())) {
            saleOrderEntity.setMarketCode(order.getMarketplaceId());
            marketplaceEntity = marketplaceService.getByMarketplaceId(order.getMarketplaceId());
        }
        if (Objects.nonNull(marketplaceEntity)) {
            saleOrderEntity.setMarketName(marketplaceEntity.getMarketplace());
        }

        saleOrderEntity.setOrderNo(order.getOrderId());
        saleOrderEntity.setPlatformOrderNo(order.getOrderId());
        // 订单状态
        saleOrderEntity.setOrderStatus(PlatformOrderStatusEnum.getCodeByOutCode(order.getStatus()));
        saleOrderEntity.setPaymentAmount(SetValueUtil.getOptimizeBigDecimal(order.getPaymentFee()));
        saleOrderEntity.setProductDiscountAmount(SetValueUtil.getOptimizeBigDecimal(order.getDiscountFee()));
        saleOrderEntity.setProductTotalAmount(SetValueUtil.getOptimizeBigDecimal(order.getTotalFee()));
        saleOrderEntity.setFreightFee(SetValueUtil.getOptimizeBigDecimal(order.getPostFee()));
        saleOrderEntity.setCommissionFee(SetValueUtil.getOptimizeBigDecimal(order.getCommissionFee()));
        if (Objects.nonNull(order.getProcessFee()) && order.getProcessFee().compareTo(BigDecimal.ZERO) > 0) {
            saleOrderEntity.setProcessFee(order.getProcessFee());
        } else {
            saleOrderEntity.setProcessFee(BigDecimal.ZERO);
        }
        saleOrderEntity.setCurrency(order.getCurrency());
        saleOrderEntity.setShippingType(order.getShippingType());
        saleOrderEntity.setStoreId(storeEntity.getId());
        saleOrderEntity.setStoreName(storeEntity.getErpStoreName());
        saleOrderEntity.setLocation(storeEntity.getLocation());
        saleOrderEntity.setPlatformId(storeEntity.getPlatformId());
        saleOrderEntity.setPlatformName(storeEntity.getPlatformName());
        saleOrderEntity.setOrderCreateDate(order.getCreateDate());
        saleOrderEntity.setEarliestShipDate(order.getEarliestShipDate());
        saleOrderEntity.setLatestShipDate(order.getLatestShipDate());
        saleOrderEntity.setEarliestDeliveryDate(order.getEarliestDeliveryDate());
        saleOrderEntity.setLatestDeliveryDate(order.getLatestDeliveryDate());
        saleOrderEntity.setOrderPaymentDate(Objects.nonNull(order.getPaymentDate()) ? order.getPaymentDate() : new Date());
        saleOrderEntity.setOrderCancelDate(order.getCancelledDate());
        saleOrderEntity.setBuyerRemark(order.getBuyerMemo());
        saleOrderEntity.setBusinessOrder(order.getBusinessOrder());
        saleOrderService.saveOrUpdate(saleOrderEntity);
        // 根据市场时区id, 保存时区时间
        if (Objects.nonNull(marketplaceEntity)) {
            SaleOrderExtendEntity saleOrderExtendEntity = Optional.ofNullable(saleOrderExtendService.getByOrderNo(saleOrderEntity.getOrderNo())).orElse(new SaleOrderExtendEntity());
            if (Objects.isNull(saleOrderExtendEntity.getOrderId())) {
                saleOrderExtendEntity.setOrderId(saleOrderEntity.getOrderId());
                saleOrderExtendEntity.setOrderNo(saleOrderEntity.getOrderNo());
            }
            if (Objects.nonNull(saleOrderEntity.getOrderCreateDate())) {
                saleOrderExtendEntity.setOrderCreateDateTimeZone(DateUtils.getTimeByTimeZone(saleOrderEntity.getOrderCreateDate(), marketplaceEntity.getTimeZoneId()));
            }
            if (Objects.nonNull(saleOrderEntity.getEarliestShipDate())) {
                saleOrderExtendEntity.setEarliestShipDateTimeZone(DateUtils.getTimeByTimeZone(saleOrderEntity.getEarliestShipDate(), marketplaceEntity.getTimeZoneId()));
            }
            if (Objects.nonNull(saleOrderEntity.getLatestShipDate())) {
                saleOrderExtendEntity.setLatestShipDateTimeZone(DateUtils.getTimeByTimeZone(saleOrderEntity.getLatestShipDate(), marketplaceEntity.getTimeZoneId()));
            }
            if (Objects.nonNull(saleOrderEntity.getEarliestDeliveryDate())) {
                saleOrderExtendEntity.setEarliestDeliveryDateTimeZone(DateUtils.getTimeByTimeZone(saleOrderEntity.getEarliestDeliveryDate(), marketplaceEntity.getTimeZoneId()));
            }
            if (Objects.nonNull(saleOrderEntity.getLatestDeliveryDate())) {
                saleOrderExtendEntity.setLatestDeliveryDateTimeZone(DateUtils.getTimeByTimeZone(saleOrderEntity.getLatestDeliveryDate(), marketplaceEntity.getTimeZoneId()));
            }
            if (Objects.nonNull(saleOrderEntity.getOrderPaymentDate())) {
                saleOrderExtendEntity.setOrderPaymentDateTimeZone(DateUtils.getTimeByTimeZone(saleOrderEntity.getOrderPaymentDate(), marketplaceEntity.getTimeZoneId()));
            }
            if (Objects.nonNull(saleOrderEntity.getOrderCancelDate())) {
                saleOrderExtendEntity.setOrderCancelDateTimeZone(DateUtils.getTimeByTimeZone(saleOrderEntity.getOrderCancelDate(), marketplaceEntity.getTimeZoneId()));
            }
            saleOrderExtendEntity.setLocation(saleOrderEntity.getLocation());
            saleOrderExtendService.saveOrUpdate(saleOrderExtendEntity);
        }
    }

    /**
     * 保存幂等表
     */
    public void saveOrderIdempotent(String orderId, String orderCrypt, SaleOrderEntity saleOrderEntity, OrderIdempotentEntity orderIdempotentEntity) {
        OrderIdempotentEntity idempotentEntity = Optional.ofNullable(orderIdempotentEntity).orElse(new OrderIdempotentEntity());
        idempotentEntity.setStoreId(saleOrderEntity.getStoreId());
        idempotentEntity.setOrderNo(orderId);
        idempotentEntity.setContent(orderCrypt);
        idempotentEntity.setLocation(saleOrderEntity.getLocation());
        orderIdempotentService.saveOrUpdate(idempotentEntity);
    }

    /**
     * 保存寄件人
     *
     * @param order
     * @param saleOrderEntity
     */
    public void saveReceiverEntity(GetPlatformOrderResponse order, SaleOrderEntity saleOrderEntity) {
        SaleOrderReceiverEntity receiverEntity = Optional.ofNullable(saleOrderReceiverService.getByOrderId(saleOrderEntity.getOrderId())).orElse(new SaleOrderReceiverEntity());

        receiverEntity.setOrderId(saleOrderEntity.getOrderId());
        receiverEntity.setReceiverName(order.getReceiverName());
        receiverEntity.setCountry(order.getReceiverCountry());
        receiverEntity.setProvince(order.getReceiverState());
        receiverEntity.setCity(order.getReceiverCity());
        receiverEntity.setArea(order.getReceiverDistrict());
        receiverEntity.setAddress(order.getReceiverAddress());
        receiverEntity.setMobile(order.getReceiverMobile());
        receiverEntity.setPhone(order.getReceiverPhone());
        receiverEntity.setPostCode(order.getReceiverZip());
        receiverEntity.setBuyerNick(order.getBuyerNick());
        receiverEntity.setBuyerUid(order.getBuyerUid());
        receiverEntity.setBuyerEmail(order.getBuyerEmail());
        receiverEntity.setLocation(saleOrderEntity.getLocation());
        receiverEntity.setHouseNumber(order.getReceiverHouseNumber());
        saleOrderReceiverService.saveOrUpdate(receiverEntity);
    }

    @Override
    public void saveOrderItems(CommonOrderDTO orderDTO, List<GetPlatformOrderItemResponse> orderItemList) {

        List<String> platformItemIdList = orderItemList.stream().map(GetPlatformOrderItemResponse::getOrderItemId).collect(Collectors.toList());
        List<SaleOrderItemEntity> existItemList = saleOrderItemService.getByPlatformItemId(platformItemIdList);

        List<SaleOrderItemEntity> itemEntityList = orderItemList.stream().map(itemResponse ->
                buildItemEntity(orderDTO, itemResponse, existItemList)
        ).collect(Collectors.toList());

        // 异步映射本地sku
        asyncService.orderMatchLocalSku(orderDTO.getStoreId(), itemEntityList);
        saleOrderItemService.saveOrUpdateBatch(itemEntityList);
    }

    @Override
    public void orderItemFetchSuccessToUpdateOrder(CommonOrderDTO orderDTO) {
        SaleOrderEntity saleOrderEntity = this.getById(orderDTO.getOrderId());
        if (Objects.isNull(saleOrderEntity)) {
            throw new BusinessServiceException(String.format("查询不到订单, 店铺ID: %s, 订单ID: %s", orderDTO.getStoreId(), orderDTO.getOrderId()));
        }
        if (Objects.nonNull(saleOrderEntity.getProductDiscountAmount()) && saleOrderEntity.getProductDiscountAmount().compareTo(BigDecimal.ZERO) == 0) {
            saleOrderEntity.setProductDiscountAmount(SetValueUtil.getOptimizeBigDecimal(orderDTO.getDiscountFee()));
        }
        if (Objects.nonNull(saleOrderEntity.getProductTotalAmount()) && saleOrderEntity.getProductTotalAmount().compareTo(BigDecimal.ZERO) == 0) {
            saleOrderEntity.setProductTotalAmount(SetValueUtil.getOptimizeBigDecimal(orderDTO.getTotalFee()));
        }
        if (Objects.nonNull(saleOrderEntity.getFreightFee()) && saleOrderEntity.getFreightFee().compareTo(BigDecimal.ZERO) == 0) {
            saleOrderEntity.setFreightFee(SetValueUtil.getOptimizeBigDecimal(orderDTO.getPostFee()));
        }
        // 订单的加工费：付款金额 - 商品总金额 - 运费 - 运费险 + 商品折扣(优惠金额，是该商品单件优惠*数量。如果有平台折扣，需要按金额比例折算进来)
        // + 订单折扣（订单优惠 ：满减，打折，抵用券等）
        if (Objects.nonNull(saleOrderEntity.getProcessFee()) && saleOrderEntity.getProcessFee().compareTo(BigDecimal.ZERO) == 0) {
            BigDecimal processFee = SetValueUtil.getOptimizeBigDecimal(saleOrderEntity.getPaymentAmount()
                    .subtract(saleOrderEntity.getProductTotalAmount())
                    .subtract(saleOrderEntity.getFreightFee())
                    //.subtract(SetValueUtil.getOptimizeBigDecimal(saleOrderEntity.getYfxFee()))
                    .add(saleOrderEntity.getProductDiscountAmount())
                    .add(SetValueUtil.getOptimizeBigDecimal(orderDTO.getDiscountMoney())));
            saleOrderEntity.setProcessFee(processFee);
        }
        this.updateById(saleOrderEntity);
    }

    public SaleOrderItemEntity buildItemEntity(CommonOrderDTO orderDTO, GetPlatformOrderItemResponse itemResponse, List<SaleOrderItemEntity> existItemList) {

        SaleOrderItemEntity itemEntity = existItemList.stream().filter(existItem -> Objects.nonNull(existItem.getPlatformItemId()) && existItem.getPlatformItemId().equals(itemResponse.getOrderItemId())).findFirst().orElse(new SaleOrderItemEntity());

        itemEntity.setSellerProductId(itemResponse.getProductId());
        itemEntity.setSellerSkuId(itemResponse.getOuterSkuId());
        itemEntity.setRefundId(itemResponse.getRefundId());
        itemEntity.setRefundStatus(itemResponse.getRefundStatus());
        itemEntity.setIossNumber(itemResponse.getIossNumber());
        itemEntity.setPlatformItemId(itemResponse.getOrderItemId());
        itemEntity.setSellerSku(itemResponse.getSku());
        itemEntity.setOrderId(orderDTO.getOrderId());
        itemEntity.setLocation(orderDTO.getLocation());
        itemEntity.setUnitPrice(SetValueUtil.getOptimizeBigDecimal(itemResponse.getPrice()));
        itemEntity.setQty(SetValueUtil.getOptimizeInteger(itemResponse.getQty()));
        if (Objects.nonNull(itemEntity.getUnitPrice()) && Objects.nonNull(itemEntity.getQty())) {
            itemEntity.setTotalAmount(SetValueUtil.getOptimizeBigDecimal(itemEntity.getUnitPrice().multiply(BigDecimal.valueOf(itemEntity.getQty()))));
        }
        if (Objects.nonNull(itemResponse.getTotalDiscount())) {
            if (Objects.nonNull(itemEntity.getTotalAmount())) {
                itemEntity.setPaymentAmount(SetValueUtil.getOptimizeBigDecimal(itemEntity.getTotalAmount().subtract(itemResponse.getTotalDiscount())));
            }
            if (Objects.nonNull(itemEntity.getQty())) {
                itemEntity.setUnitDiscount(SetValueUtil.getOptimizeBigDecimal(itemResponse.getTotalDiscount().divide(BigDecimal.valueOf(itemResponse.getQty()), 4, RoundingMode.HALF_UP)));
            }
        } else {
            itemEntity.setPaymentAmount(SetValueUtil.getOptimizeBigDecimal(itemEntity.getTotalAmount()));
        }
        itemEntity.setItemStatus(PlatformOrderStatusEnum.CANCEL.getOutCode().equalsIgnoreCase(itemResponse.getStatus()) ? PlatformOrderItemStatusEnum.DELETE.getCode() : PlatformOrderItemStatusEnum.NORMAL.getCode());
        itemEntity.setIsTransparency(Objects.nonNull(itemResponse.getTransparency()) && Boolean.TRUE.equals(itemResponse.getTransparency()) ? 1 : 0);
        itemEntity.setAsin(itemResponse.getAsin());
        itemEntity.setFreightFee(itemResponse.getShippingPriceAmount());
        itemEntity.setFreightFeeDiscount(itemResponse.getShippingDiscountAmount());
        return itemEntity;
    }


    @Override
    public boolean isCancelOrder(Integer storeId, String orderNo) {
        Long count = count(Wrappers.<SaleOrderEntity>lambdaQuery()
                .eq(SaleOrderEntity::getStoreId, storeId)
                .eq(SaleOrderEntity::getOrderNo, orderNo)
                .eq(SaleOrderEntity::getOrderStatus, PlatformOrderStatusEnum.CANCEL.getCode())
        );

        return count > 0;
    }

    @Override
    public List<SaleOrderEntity> cancelOrders(Integer storeId, List<String> orderNoList) {
        return list(Wrappers.<SaleOrderEntity>lambdaQuery()
                .eq(SaleOrderEntity::getStoreId, storeId)
                .in(SaleOrderEntity::getOrderNo, orderNoList)
                .eq(SaleOrderEntity::getOrderStatus, PlatformOrderStatusEnum.CANCEL.getCode()));
    }

    @Override
    public Page<CrmOrderPageListResponse> crmOrderPageList(CrmOrderPageListRequest request) {
        return this.baseMapper.crmOrderPage(new Page<>(request.getPageIndex(), request.getPageSize()), request);
    }

    @Override
    public CrmOrderInoResponse getCrmOrderInfo(String orderNo, List<Integer> storeIdList) {
        return this.baseMapper.getCrmOrderInfo(orderNo, storeIdList);
    }

    @Override
    public PlatformOrderResponse getOrderInfo(Integer storeId, String platformOrderNo) {
        SaleOrderEntity saleOrderEntity = this.getOne(Wrappers.<SaleOrderEntity>lambdaQuery().eq(SaleOrderEntity::getStoreId, storeId).eq(SaleOrderEntity::getPlatformOrderNo, platformOrderNo));
        return Optional.ofNullable(saleOrderEntity).map(entity -> {
            PlatformOrderResponse platformOrderResponse = BeanUtil.copyProperties(saleOrderEntity, PlatformOrderResponse.class);
            List<SaleOrderItemEntity> saleOrderItemEntityList = saleOrderItemService.getByOrderId(saleOrderEntity.getOrderId());
            platformOrderResponse.setItemList(BeanUtil.copyToList(saleOrderItemEntityList, PlatformOrderItemResponse.class));
            Optional.ofNullable(saleOrderReceiverService.getByOrderId(saleOrderEntity.getOrderId())).ifPresent(orderReceiver -> {
                platformOrderResponse.setReceiverName(orderReceiver.getReceiverName());
                platformOrderResponse.setBuyerEmail(orderReceiver.getBuyerEmail());
            });
            return platformOrderResponse;
        }).orElseGet(PlatformOrderResponse::new);
    }
}




