package com.nsy.oms.business.domain.response.celebrity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @description:
 * @author: linCheng
 * @create: 2024-09-02 10:30
 **/
@Data
public class SyncInternetCelebritySampleOrderItemPost {

    /**
     * 视频code
     */
    private String videoCode;

    /**
     * 视频链接
     */
    private String videoUrl;

    /**
     * 发帖日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date postDate;
}
