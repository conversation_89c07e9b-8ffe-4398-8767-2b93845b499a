package com.nsy.oms.business.service.bd.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.privilege.LoginInfoService;
import com.nsy.api.pms.dto.product.ProductSpecDTO;
import com.nsy.oms.business.domain.request.bd.GetAllocateRuleRequest;
import com.nsy.oms.business.domain.request.bd.GetStoreDeliveryTargetStoresRequest;
import com.nsy.oms.business.domain.request.bd.OrderRuleDeleteRequest;
import com.nsy.oms.business.domain.request.bd.OrderRuleListRequest;
import com.nsy.oms.business.domain.request.bd.OrderRuleSaveRequest;
import com.nsy.oms.business.domain.response.base.PageResponse;
import com.nsy.oms.business.domain.response.bd.GetAlreadyAllocateRuleResponse;
import com.nsy.oms.business.domain.response.bd.GetStoreDeliveryTargetStoresResponse;
import com.nsy.oms.business.domain.response.bd.OrderRuleCodeResponse;
import com.nsy.oms.business.domain.response.bd.OrderRuleItemResponse;
import com.nsy.oms.business.domain.response.bd.OrderRuleListResponse;
import com.nsy.oms.business.domain.response.bd.RuleItemSaveRequest;
import com.nsy.oms.business.manage.pms.PmsApiService;
import com.nsy.oms.business.service.bd.BdOrderRuleCodeService;
import com.nsy.oms.business.service.bd.BdOrderRuleItemService;
import com.nsy.oms.business.service.bd.BdOrderRuleService;
import com.nsy.oms.enums.sa.BdOrderPlatformEnum;
import com.nsy.oms.enums.sa.BdOrderRuleTypeEnum;
import com.nsy.oms.enums.sa.SaleAccountEnableEnum;
import com.nsy.oms.enumstable.OrderRuleCodeRuleTypeEnum;
import com.nsy.oms.repository.entity.bd.BdOrderRuleCodeEntity;
import com.nsy.oms.repository.entity.bd.BdOrderRuleEntity;
import com.nsy.oms.repository.entity.bd.BdOrderRuleItemEntity;
import com.nsy.oms.repository.sql.mapper.bd.BdOrderRuleMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【bd_order_rule(订单规则表)】的数据库操作Service实现
 * @createDate 2024-02-01 15:01:08
 */
@Service
public class BdOrderRuleServiceImpl extends ServiceImpl<BdOrderRuleMapper, BdOrderRuleEntity> implements BdOrderRuleService {

    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private BdOrderRuleItemService orderRuleItemService;
    @Autowired
    private BdOrderRuleCodeService orderRuleCodeService;
    @Autowired
    private PmsApiService pmsApiService;

    @Override
    public PageResponse<OrderRuleListResponse> pageList(OrderRuleListRequest request) {
        LambdaQueryWrapper<BdOrderRuleEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjectUtil.isNotEmpty(request.getStatus()), BdOrderRuleEntity::getStatus, request.getStatus());
        queryWrapper.eq(ObjectUtil.isNotEmpty(request.getStoreId()), BdOrderRuleEntity::getStoreId, request.getStoreId());
        queryWrapper.eq(ObjectUtil.isNotEmpty(request.getStorePlatform()), BdOrderRuleEntity::getStorePlatform, request.getStorePlatform());
        queryWrapper.eq(ObjectUtil.isNotEmpty(request.getDepartmentName()), BdOrderRuleEntity::getDepartmentName, request.getDepartmentName());
        queryWrapper.eq(ObjectUtil.isNotEmpty(request.getRuleType()), BdOrderRuleEntity::getRuleType, request.getRuleType());
        queryWrapper.eq(StrUtil.isNotEmpty(loginInfoService.getLocation()), BdOrderRuleEntity::getLocation, loginInfoService.getLocation());
        Page<BdOrderRuleEntity> page = baseMapper.selectPage(new Page<>(request.getPageIndex(), request.getPageSize()), queryWrapper);
        List<OrderRuleListResponse> responses = BeanUtil.copyToList(page.getRecords(), OrderRuleListResponse.class);
        for (OrderRuleListResponse response : responses) {
            response.setRuleTypeName(BdOrderRuleTypeEnum.getDescByCode(response.getRuleType()));
            response.setStatusName(SaleAccountEnableEnum.getDescByCode(response.getStatus()));
        }
        PageResponse<OrderRuleListResponse> pageResponse = PageResponse.of(page.getTotal(), page.getPages());
        pageResponse.setContent(responses);
        return pageResponse;
    }

    @Override
    @Transactional
    public void update(OrderRuleSaveRequest request) {
        Integer ruleId = request.getBdOrderRuleId();
        BdOrderRuleEntity bdOrderRuleEntity = baseMapper.selectById(ruleId);
        bdOrderRuleEntity.setRuleName(request.getRuleName());
        bdOrderRuleEntity.setRuleType(request.getRuleType());
        bdOrderRuleEntity.setStatus(request.getStatus());
        bdOrderRuleEntity.setDepartmentName(StrUtil.isNotEmpty(request.getDepartmentName()) ? request.getDepartmentName() : "");
        bdOrderRuleEntity.setStorePlatform(StrUtil.isNotEmpty(request.getStorePlatform()) ? request.getStorePlatform() : "");
        bdOrderRuleEntity.setStoreId(request.getStoreId());
        bdOrderRuleEntity.setStoreName(request.getStoreName());
        bdOrderRuleEntity.setNeedRemoveBrandLabel(request.getNeedRemoveBrandLabel());
        bdOrderRuleEntity.setUpdateBy(loginInfoService.getName());
        baseMapper.updateById(bdOrderRuleEntity);
        //把id不在这里面的删掉
        if (CollUtil.isNotEmpty(request.getItemList())) {
            List<Integer> newItemsIds = request.getItemList().stream().map(RuleItemSaveRequest::getBdOrderRuleItemId).collect(Collectors.toList());
            orderRuleItemService.deleteByRuleIdAndItemIdsNotIn(ruleId, newItemsIds);
            addOrUpdateItemsByRuleSaveRequest(request.getItemList(), bdOrderRuleEntity.getBdOrderRuleId(), loginInfoService.getName());
        }
    }


    @Override
    @Transactional
    public void add(OrderRuleSaveRequest request) {
        String name = loginInfoService.getName();
        BdOrderRuleEntity bdOrderRuleEntity = BeanUtil.copyProperties(request, BdOrderRuleEntity.class);
        bdOrderRuleEntity.setCreateBy(name);
        bdOrderRuleEntity.setUpdateBy(name);
        baseMapper.insert(bdOrderRuleEntity);
        //前端这边需要传itemType标识是店铺还是仓库
//        Integer targetChannel = request.getTargetChannel();
        addOrUpdateItemsByRuleSaveRequest(request.getItemList(), bdOrderRuleEntity.getBdOrderRuleId(), name);
    }

    private void addOrUpdateItemsByRuleSaveRequest(List<RuleItemSaveRequest> ruleItemSaveRequestList,
                                                   Integer bdOrderRuleEntity, String updateName) {
        List<BdOrderRuleItemEntity> ruleItemEntities = new ArrayList<>();
        for (RuleItemSaveRequest ruleItem : ruleItemSaveRequestList) {
            BdOrderRuleItemEntity bdOrderRuleItemEntity;
            if (ObjectUtil.isEmpty(ruleItem.getBdOrderRuleItemId())) {
                bdOrderRuleItemEntity = BeanUtil.copyProperties(ruleItem, BdOrderRuleItemEntity.class);
                bdOrderRuleItemEntity.setOrderRuleId(bdOrderRuleEntity);
                bdOrderRuleItemEntity.setCreateBy(updateName);
            } else {
                bdOrderRuleItemEntity = orderRuleItemService.getById(ruleItem.getBdOrderRuleItemId());
            }
//            bdOrderRuleItemEntity.setOrderRuleId(bdOrderRuleEntity.getBdOrderRuleId());
            bdOrderRuleItemEntity.setItemTargetId(ruleItem.getItemTargetId());
            bdOrderRuleItemEntity.setItemTargetName(ruleItem.getItemTargetName());
            bdOrderRuleItemEntity.setItemType(ruleItem.getItemType());
            bdOrderRuleItemEntity.setTriggerConditions(ruleItem.getTriggerConditions());
            bdOrderRuleItemEntity.setInventoryAllocateRatio(ruleItem.getInventoryAllocateRatio());
            bdOrderRuleItemEntity.setReserveInventory(ruleItem.getReserveInventory());
            bdOrderRuleItemEntity.setReserveInventoryType(ruleItem.getReserveInventoryType());
            bdOrderRuleItemEntity.setAllocateOrder(ruleItem.getAllocateOrder());
            bdOrderRuleItemEntity.setUpdateBy(updateName);
            ruleItemEntities.add(bdOrderRuleItemEntity);
        }
        orderRuleItemService.saveOrUpdateBatch(ruleItemEntities);
    }

    @Override
    @Transactional
    public void delete(OrderRuleDeleteRequest request) {
        List<Integer> ids = request.getIds();
        orderRuleItemService.deleteByOrderRuleIds(ids);
        baseMapper.deleteBatchIds(ids);
    }

    @Override
    public OrderRuleSaveRequest detail(Integer id) {
        BdOrderRuleEntity spaceConfigEntity = baseMapper.selectById(id);
        OrderRuleSaveRequest response = BeanUtil.copyProperties(spaceConfigEntity, OrderRuleSaveRequest.class);
        List<BdOrderRuleItemEntity> itemEntities = orderRuleItemService.getByOrderRuleId(id);
        List<RuleItemSaveRequest> returnItemList = new ArrayList<>();
        for (BdOrderRuleItemEntity itemEntity : itemEntities) {
            RuleItemSaveRequest item = BeanUtil.copyProperties(itemEntity, RuleItemSaveRequest.class);
            item.setOrderStoreId(response.getStoreId());
            item.setOrderStoreName(response.getStoreName());
            returnItemList.add(item);
            //找到其他关联的
            GetAllocateRuleRequest request = new GetAllocateRuleRequest();
            request.setItemTargetId(itemEntity.getItemTargetId());
            request.setItemType(itemEntity.getItemType());
            GetAlreadyAllocateRuleResponse alreadyAllocateRule = getAlreadyAllocateRule(request);
            List<RuleItemSaveRequest> collect = alreadyAllocateRule.getAlreadyAllocateRuleItems().stream()
                    .filter(item1 -> !item1.getBdOrderRuleItemId().equals(itemEntity.getBdOrderRuleItemId())).collect(Collectors.toList());
            returnItemList.addAll(collect);
        }
        response.setItemList(returnItemList);
        return response;
    }

    @Override
    public GetStoreDeliveryTargetStoresResponse getStoreDeliveryTargetStores(Integer storeId) {
        BdOrderRuleEntity spaceConfigEntity = this.getStoreDeliveryTargetStores(storeId, 0, 1, BdOrderPlatformEnum.TIKTOK.getDesc());
        if (!Optional.ofNullable(spaceConfigEntity).isPresent()) {
            return new GetStoreDeliveryTargetStoresResponse();
        }
        List<BdOrderRuleItemEntity> itemEntities = orderRuleItemService.getByOrderRuleId(spaceConfigEntity.getBdOrderRuleId());
        List<BdOrderRuleCodeEntity> bdOrderRuleCodeByStoreEntities = orderRuleCodeService.getByOrderRuleIdAndRuleType(spaceConfigEntity.getBdOrderRuleId(), OrderRuleCodeRuleTypeEnum.STORE.getCode());
        List<BdOrderRuleCodeEntity> bdOrderRuleCodeBySpaceEntities = orderRuleCodeService.getByOrderRuleIdAndRuleType(spaceConfigEntity.getBdOrderRuleId(), OrderRuleCodeRuleTypeEnum.SPACE.getCode());
        return new GetStoreDeliveryTargetStoresResponse(CollectionUtils.isEmpty(itemEntities) ? Collections.emptyList() : BeanUtil.copyToList(itemEntities, OrderRuleItemResponse.class),
                getOrderRuleCodeResponses(bdOrderRuleCodeByStoreEntities),
                getOrderRuleCodeResponses(bdOrderRuleCodeBySpaceEntities));
    }


    @Override
    public GetStoreDeliveryTargetStoresResponse getStoreDeliveryTargetStores(GetStoreDeliveryTargetStoresRequest request) {
        BdOrderRuleEntity spaceConfigEntity = this.getStoreDeliveryTargetStores(request.getStoreId(), 0, 1, request.getStorePlatform());
        if (!Optional.ofNullable(spaceConfigEntity).isPresent()) {
            return new GetStoreDeliveryTargetStoresResponse();
        }
        List<BdOrderRuleItemEntity> itemEntities = orderRuleItemService.getByOrderRuleId(spaceConfigEntity.getBdOrderRuleId());
        List<BdOrderRuleCodeEntity> bdOrderRuleCodeByStoreEntities = orderRuleCodeService.getByOrderRuleIdAndRuleType(spaceConfigEntity.getBdOrderRuleId(), OrderRuleCodeRuleTypeEnum.STORE.getCode());
        List<BdOrderRuleCodeEntity> bdOrderRuleCodeBySpaceEntities = orderRuleCodeService.getByOrderRuleIdAndRuleType(spaceConfigEntity.getBdOrderRuleId(), OrderRuleCodeRuleTypeEnum.SPACE.getCode());
        return new GetStoreDeliveryTargetStoresResponse(
                CollectionUtils.isEmpty(itemEntities) ? Collections.emptyList() : BeanUtil.copyToList(itemEntities, OrderRuleItemResponse.class),
                getOrderRuleCodeResponses(bdOrderRuleCodeByStoreEntities),
                getOrderRuleCodeResponses(bdOrderRuleCodeBySpaceEntities));
    }

    public BdOrderRuleEntity getStoreDeliveryTargetStores(Integer storeId, Integer ruleType, Integer status, String storePlatform) {
        LambdaQueryWrapper<BdOrderRuleEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(storeId != null, BdOrderRuleEntity::getStoreId, storeId);
        queryWrapper.eq(ruleType != null, BdOrderRuleEntity::getRuleType, ruleType);
        queryWrapper.eq(status != null, BdOrderRuleEntity::getStatus, status);
        queryWrapper.eq(storePlatform != null, BdOrderRuleEntity::getStorePlatform, storePlatform);
        queryWrapper.last(" limit 1 ");
        return baseMapper.selectOne(queryWrapper);
    }


    @Override
    public BdOrderRuleEntity getRemoveBrandLabel(GetStoreDeliveryTargetStoresRequest request) {
        return this.getStoreDeliveryTargetStores(request.getStoreId(), BdOrderRuleTypeEnum.REMOVE_BRAND_LABEL.getCode(), 1, request.getStorePlatform());
    }


    @Override
    public BdOrderRuleEntity findRemoveBrandTopByStoreId(Integer storeId) {
        LambdaQueryWrapper<BdOrderRuleEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BdOrderRuleEntity::getStoreId, storeId);
        queryWrapper.eq(BdOrderRuleEntity::getRuleType, BdOrderRuleTypeEnum.REMOVE_BRAND_LABEL.getCode());
        queryWrapper.eq(BdOrderRuleEntity::getStatus, 1);
        queryWrapper.last(" limit 1 ");
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<OrderRuleItemResponse> getStoreByPlatform(String storePlatform) {
        LambdaQueryWrapper<BdOrderRuleEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BdOrderRuleEntity::getStorePlatform, storePlatform);
        queryWrapper.eq(BdOrderRuleEntity::getStatus, 1);
        List<BdOrderRuleEntity> bdOrderRuleEntities = baseMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(bdOrderRuleEntities)) {
            return Collections.emptyList();
        }
        List<BdOrderRuleItemEntity> itemEntities = orderRuleItemService.getByOrderRuleIds(bdOrderRuleEntities.stream().map(BdOrderRuleEntity::getBdOrderRuleId).collect(Collectors.toList()));
        return BeanUtil.copyToList(itemEntities, OrderRuleItemResponse.class);
    }


    @Override
    public GetAlreadyAllocateRuleResponse getAlreadyAllocateRule(GetAllocateRuleRequest request) {
        GetAlreadyAllocateRuleResponse response = new GetAlreadyAllocateRuleResponse();
        List<RuleItemSaveRequest> orderRuleItemResponses = new ArrayList<>();
        List<BdOrderRuleItemEntity> itemList = orderRuleItemService.getByItemTargetIdAndItemType(request.getItemTargetId(), request.getItemType());
        for (BdOrderRuleItemEntity itemEntity : itemList) {
            RuleItemSaveRequest item = BeanUtil.copyProperties(itemEntity, RuleItemSaveRequest.class);
            BdOrderRuleEntity orderRule = getById(item.getOrderRuleId());
            item.setOrderStoreName(orderRule.getStoreName());
            item.setOrderStoreId(orderRule.getStoreId());
            orderRuleItemResponses.add(item);
        }
        response.setAlreadyAllocateRuleItems(orderRuleItemResponses);
        return response;
    }

    private List<OrderRuleCodeResponse> getOrderRuleCodeResponses(List<BdOrderRuleCodeEntity> bdOrderRuleCodeEntities) {
        if (CollectionUtils.isEmpty(bdOrderRuleCodeEntities)) {
            return Collections.emptyList();
        }

        List<BdOrderRuleCodeEntity> skcRules = bdOrderRuleCodeEntities.stream().filter(entity -> "skc".equals(entity.getCodeType())).collect(Collectors.toList());
        List<BdOrderRuleCodeEntity> skuRules = bdOrderRuleCodeEntities.stream().filter(entity -> "sku".equals(entity.getCodeType())).collect(Collectors.toList());
        List<OrderRuleCodeResponse> orderRuleCodeResponses = BeanUtil.copyToList(skuRules, OrderRuleCodeResponse.class);

        Map<String, List<String>> skuMap = pmsApiService.getSkcInfo(skcRules.stream().map(BdOrderRuleCodeEntity::getCode).collect(Collectors.toList()))
                .stream().collect(Collectors.groupingBy(ProductSpecDTO::getColorSku, Collectors.mapping(ProductSpecDTO::getSpecSku, Collectors.toList())));

        Map<String, BdOrderRuleCodeEntity> resultMap = skuRules.stream().collect(Collectors.toMap(item -> StrUtil.format("{}_{}", OrderRuleCodeRuleTypeEnum.SPACE.getCode().equals(item.getRuleType()) ? item.getSpaceId() : item.getStoreId(), item.getCode()), Function.identity(), (k1, k2) -> k1));

        skcRules.forEach(skcRule -> {
            List<String> skus = skuMap.get(skcRule.getCode());
            skus.forEach(sku -> {
                BdOrderRuleCodeEntity bdOrderRuleCodeEntity = resultMap.get(StrUtil.format("{}_{}", OrderRuleCodeRuleTypeEnum.SPACE.getCode().equals(skcRule.getRuleType()) ? skcRule.getSpaceId() : skcRule.getStoreId(), sku));
                if (Optional.ofNullable(bdOrderRuleCodeEntity).isPresent()) {
                    return;
                }
                resultMap.put(StrUtil.format("{}_{}", OrderRuleCodeRuleTypeEnum.SPACE.getCode().equals(skcRule.getRuleType()) ? skcRule.getSpaceId() : skcRule.getStoreId(), sku), skcRule);
                OrderRuleCodeResponse orderRuleCodeResponse = new OrderRuleCodeResponse();
                BeanUtil.copyProperties(skcRule, orderRuleCodeResponse);
                orderRuleCodeResponse.setCode(sku);
                orderRuleCodeResponses.add(orderRuleCodeResponse);
            });
        });
        return orderRuleCodeResponses;
    }

}




