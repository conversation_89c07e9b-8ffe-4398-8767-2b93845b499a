package com.nsy.oms.business.domain.request.celebrity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: lhh
 * @Date: 2025-04-18
 * @Description: 网红查询请求类
 */
@NoArgsConstructor
@Data
public class InternetCelebritySyncMaxAdRequest {


    @JsonProperty("data")
    private List<DataDTO> data;

    @NoArgsConstructor
    @Data
    public static class DataDTO {
        @JsonProperty("products")
        private List<ProductsDTO> products;
        @JsonProperty("campaign_id")
        private String campaignId;
        @JsonProperty("analysis_date")
        private String analysisDate;
        @JsonProperty("analysis_date_end")
        private String analysisDateEnd;
        @JsonProperty("store_id")
        private String storeId;
        @JsonProperty("store_name")
        private String storeName;
        @JsonProperty("overview")
        private OverviewDTO overview;

        @NoArgsConstructor
        @Data
        public static class OverviewDTO {
            @JsonProperty("cost")
            private String cost;
            @JsonProperty("onsite_roi2_shopping")
            private String onsiteRoi2Shopping;
        }

        @NoArgsConstructor
        @Data
        public static class ProductsDTO {
            @JsonProperty("product_id")
            private String productId;
            @JsonProperty("creatives")
            private List<CreativesDTO> creatives;

            @NoArgsConstructor
            @Data
            public static class CreativesDTO {
                @JsonProperty("item_id")
                private String itemId;
                @JsonProperty("onsite_roi2_shopping_value")
                private String onsiteRoi2ShoppingValue;
                @JsonProperty("roi2_show_cnt")
                private String roi2ShowCnt;
            }
        }
    }
}
