package com.nsy.oms.business.domain.request.stockout;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;


public class GetBySkuBySkcRequest {

    /**
     * 部门
     */
    @ApiModelProperty(value = "部门", name = "department")
    private String department;

    /**
     * 补货组ID
     */
    @ApiModelProperty(value = "补货组ID", name = "replenishmentGroupId")
    private Integer replenishmentGroupId;

    /**
     * skcList
     */
    @ApiModelProperty(value = "skcList", name = "skcList")
    private List<String> skcList;

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public Integer getReplenishmentGroupId() {
        return replenishmentGroupId;
    }

    public void setReplenishmentGroupId(Integer replenishmentGroupId) {
        this.replenishmentGroupId = replenishmentGroupId;
    }

    public List<String> getSkcList() {
        return skcList;
    }

    public void setSkcList(List<String> skcList) {
        this.skcList = skcList;
    }
}