package com.nsy.oms.enums.sa;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2022/12/7 17:42
 * 账号类型 0 店铺账号 1 社媒号 2 直播号 3 广告账号
 */
public enum AccountTypeEnum {
    STORE_ACCOUNT(0, "店铺账号"),
    SOCIAL_MEDIA(1, "社媒号"),
    LIVE_ACCOUNT(2, "直播号"),
    AD_ACCOUNT(3, "广告账号");


    /**
     * 状态码
     */
    private final Integer code;
    /**
     * 名称
     */
    private final String desc;

    AccountTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据值映射到名称
     *
     * @param code 值
     * @return
     */
    public static String getNameByCode(Integer code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findAny().map(AccountTypeEnum::getDesc).orElse("未知");
    }


}
