package com.nsy.oms.enums.order;

import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.oms.constants.AmazonPlatformConstant;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */

public enum OrderGrabPlatformEnum {
    AMAZON(0, "亚马逊"),
    ALIBABA(13, "阿里巴巴"),
    ALIBABA_INTERNATION(49, "阿里国际站"),
    UMKA(44, "外部客户umka"),
    DHGATE(47, "敦煌"),
    WOO_COMMERCE(51, "WooCommerce"),
    SHEIN(64, "Shein"),
    SHOPIFY(40, "独立站Shopify"),
    SHOP_LAZZA(54, "店匠"),
    WALMART(50, "沃尔玛"),
    FAIRE(58, "Faire"),
    FASHION_GO(66, "FashionGo"),
    WAHOOL(67, "Wahool"),
    <PERSON><PERSON><PERSON>(65, "<PERSON>"),
    <PERSON><PERSON><PERSON>_<PERSON>O(1, "淘宝"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>(63, "TikTok"),
    TEM<PERSON>(57, "拼多多跨境"),
    TEMU_POP(69, "拼多多跨境（半托管流程）");

    private Integer code;
    private String desc;


    OrderGrabPlatformEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static List<Integer> getCodes(String platform) {
        OrderGrabPlatformEnum orderGrabPlatformEnum = Arrays.stream(values()).filter(e -> e.name().equals(platform)).findFirst().orElseThrow(() -> new BusinessServiceException("查询不到抓单平台！"));

        if (orderGrabPlatformEnum == AMAZON) {
            return AmazonPlatformConstant.AMAZON_PLATFORM_ID_LIST;
        }
        if (orderGrabPlatformEnum == TEMU_POP) {
            return Collections.singletonList(TEMU.code);
        }
        return Collections.singletonList(orderGrabPlatformEnum.code);
    }


    public static String getPlatform(Integer code) {
        Optional<OrderGrabPlatformEnum> optionalEnum = Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst();
        if (optionalEnum.isPresent()) {
            return optionalEnum.get().name();
        } else {
            AmazonPlatformConstant.AMAZON_PLATFORM_ID_LIST.stream().filter(platformId -> platformId.equals(code)).findFirst().orElseThrow(() -> new BusinessServiceException(String.format("查询不到平台, 平台ID: %s", code)));
            return AMAZON.name();
        }
    }

    public String getDesc() {
        return desc;
    }

    public Integer getCode() {
        return code;
    }
}
