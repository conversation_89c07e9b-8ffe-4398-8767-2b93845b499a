package com.nsy.oms.enums.tkcreator;

/**
 * 功能描述:
 *等级：达人的等级；单店铺，近3个月某月达到该标准；多个店铺的话按最高等级匹配，只打一个标；按照视频GMV分为5个等级：C、B、A、V、S
 *         1.1.1 等级划分标准
 *             ● C:小于1000美金
 *             ● B:1000美金（含）-5000美金
 *             ● A:5000美金（含）-1万美金
 *             ● V:1万美金（含）-3万美金
 *             ● S:>=3万美金
 * @author: qiu wanzi
 * @date: 2025年4月22日 星期二
 * @version: 1.0
 */
public enum InternetCelebrityRelationStatus {

    AGREE("同意"),
    REJECT("拒绝"),
    THINKING("考虑中"),
    NOT_ANSWERED("未回复"),
    ANSWERED("已回复"),
    NOT_RELATION("未建联"),
    NOT_ACTIVE("不活跃")
    ;


    private final String value;

    InternetCelebrityRelationStatus(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
