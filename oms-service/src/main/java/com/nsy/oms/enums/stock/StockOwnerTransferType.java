package com.nsy.oms.enums.stock;

/**
 * 描述： 货主转移类型
 *
 * <AUTHOR>
 * @date 2025/4/30 11:30
 */
public enum StockOwnerTransferType {
    APPLICATION_FORM_ASSIGNMENT("申请单分配"),
    SHELVING_ASSIGNMENT("上架分配"),
    PRE_ALLOCATION_TRANSFER("预配转移"),
    SHIPMENT_TRANSFER("发货转移"),
    EMBRYO_PROCESSING("肧款加工"),
    ALLOCATION_TRANSFER("调拨转移"),
    BORROWING_TRANSFER("借用转移");

    private String type;

    StockOwnerTransferType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }
}
