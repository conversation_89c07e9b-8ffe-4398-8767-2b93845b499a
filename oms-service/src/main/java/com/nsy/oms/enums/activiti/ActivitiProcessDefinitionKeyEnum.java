package com.nsy.oms.enums.activiti;

import java.util.Arrays;

public enum ActivitiProcessDefinitionKeyEnum {
    medic_account_apply("社媒账号申请", "maa"),
    medic_account_change("社媒账号变更申请", "mac"),
    medic_account_close_apply("社媒账号关店申请", "maca"),
    medic_store_card_change("社媒账号信用卡变更", "mscc"),
    medic_store_info_apply("社媒账号邮箱/电话申请", "msia");


    private final String desc;
    private final String prefix;

    ActivitiProcessDefinitionKeyEnum(String desc, String prefix) {
        this.desc = desc;
        this.prefix = prefix;
    }

    public String getDesc() {
        return desc;
    }

    public String getPrefix() {
        return prefix;
    }
//medic_account_apply


    public static String getPrefix(String name) {
        return Arrays.stream(values()).filter(e -> e.name().equals(name)).findAny().map(ActivitiProcessDefinitionKeyEnum::getPrefix).orElse("");
    }
}
