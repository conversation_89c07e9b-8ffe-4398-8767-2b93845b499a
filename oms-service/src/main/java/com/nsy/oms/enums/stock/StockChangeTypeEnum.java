package com.nsy.oms.enums.stock;

/**
 * 库存变动记录操作类型
 */
public enum StockChangeTypeEnum {
    PURCHASE_SHELVE("采购入库"),
    RETURN_SHELVE("销售退货入库"),
    LEND_RETURNED("借用归还"),
    PROCESS_SHELVE("加工入库"),
    TRANSFER("调拨"),
    TAKE_STOCK("盘点"),
    RETURN_QC_INFERIOR("出库质检次品下架"),
    STOCKOUT_SHIPPING("订单发货出库"),
    PROCESS_PICKING("加工出库"),
    PROCESS_WITHDRAWAL("加工次品出库"),
    LOCK("锁定库存");

    StockChangeTypeEnum(String name) {
        this.name = name;
    }

    private String name;

    public String getName() {
        return name;
    }
}
