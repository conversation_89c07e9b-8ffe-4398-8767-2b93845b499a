package com.nsy.oms.enums.rule;

import com.nsy.api.core.apicore.exception.BusinessServiceException;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum ConfigProductStockRuleItemConditionTypeEnum {
    PLATFORM_STOCK("platform_stock", "内贸可售库存"),
    PLATFORM_SKU_PREFIX("platform_sku_prefix", "sellerSku前缀"),
    PLATFORM_SKU_STATUS("platform_sku_status", "后台状态"),
    EXCLUDE_SKC("exclude_skc", "排除指定skc"),
    SALE_STOCK_INTERVAL("sale_stock_interval", "内贸可售库存区间");

    private final String conditionType;
    private final String conditionName;

    ConfigProductStockRuleItemConditionTypeEnum(String conditionType, String conditionName) {
        this.conditionType = conditionType;
        this.conditionName = conditionName;
    }

    public String getConditionType() {
        return conditionType;
    }

    public String getConditionName() {
        return conditionName;
    }

    public static ConfigProductStockRuleItemConditionTypeEnum of(List<String> platforms) {
        List<String> filterConditionTypes = platforms.stream().filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        ConfigProductStockRuleItemConditionTypeEnum platformEnum = Stream.of(ConfigProductStockRuleItemConditionTypeEnum.values()).filter(ruleConditionTypeEnum -> filterConditionTypes.contains(ruleConditionTypeEnum.getConditionType())).findAny().orElse(null);
        if (platformEnum == null) {
            throw new BusinessServiceException("找不到对应的平台");
        } else {
            return platformEnum;
        }
    }


}
