package com.nsy.oms.enums.replenishment;

import com.nsy.api.core.apicore.exception.BusinessServiceException;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: qiu wanzi
 * @date: 2024年3月8日 0008
 * @version: 1.0
 * @description: TODO
 */
public enum TargetSaleStatusEnum {
    /**
     * 目标销量状态 1 生效中 2 已过期
     */
    EFFECTIVE(1, "生效中", "目标销量-生效中"),
    EXPIRED(2, "已过期", "目标销量-已过期");


    private Integer dbStatus;
    private String status;
    private String queryStatus;

    TargetSaleStatusEnum(Integer dbStatus, String status, String queryStatus) {
        this.dbStatus = dbStatus;
        this.status = status;
        this.queryStatus = queryStatus;
    }

    public Integer getDbStatus() {
        return dbStatus;
    }

    public String getStatus() {
        return status;
    }

    public String getQueryStatus() {
        return queryStatus;
    }

    public static List<Integer> getDbStatusByQueryStatus(List<String> queryStatusList) {
        List<Integer> dbStatus = new ArrayList<>();
        if (CollectionUtils.isEmpty(queryStatusList)) return dbStatus;
        for (String queryStatus : queryStatusList) {
            for (TargetSaleStatusEnum statusEnum : TargetSaleStatusEnum.values()) {
                if (statusEnum.getQueryStatus().equals(queryStatus)) {
                    dbStatus.add(statusEnum.getDbStatus());
                }
            }
        }
        return dbStatus;
    }

    public static TargetSaleStatusEnum of(Integer dbStatus) {
        for (TargetSaleStatusEnum statusEnum : TargetSaleStatusEnum.values()) {
            if (statusEnum.getDbStatus().equals(dbStatus)) {
                return statusEnum;
            }
        }
        throw new BusinessServiceException("目标销量状态不存在");
    }

    public static TargetSaleStatusEnum of(String status) {
        for (TargetSaleStatusEnum statusEnum : TargetSaleStatusEnum.values()) {
            if (statusEnum.getStatus().equals(status)) {
                return statusEnum;
            }
        }
        throw new BusinessServiceException("目标销量状态不存在");
    }
}
