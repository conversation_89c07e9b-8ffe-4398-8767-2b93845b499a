package com.nsy.oms.enums.stock;

public enum StockOperateTypeEnum {
    // 根据配置获取跨仓包含本身款式和肧款库存
    SALE_ORDER("销售订单"),
    // 根据配置获取跨仓肧款库存
    PROCESS_ORDER("加工订单"),
    // 根据配置获取跨仓包含本身款式，同时需要区分品牌和非品牌
    PLATFORM_RESTOCK("平台仓补货"),
    // 根据配置获取跨仓包含本身款式和肧款库存，需要排除海外仓
    OVERSEAS_RESTOCK("海外仓补货"),
    // 单仓包含本身款式和肧款库存，不获取跨仓
    SINGLE_SPACE("单仓库存"),
    // 获取公司库存
    COMPANY_STOCK("取公司库存");

    StockOperateTypeEnum(String name) {
        this.name = name;
    }

    private String name;

    public String getName() {
        return name;
    }
}
