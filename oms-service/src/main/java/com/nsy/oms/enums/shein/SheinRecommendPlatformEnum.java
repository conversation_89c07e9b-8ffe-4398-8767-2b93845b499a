package com.nsy.oms.enums.shein;

import com.nsy.api.core.apicore.exception.BusinessServiceException;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum SheinRecommendPlatformEnum {
    SHEIN("Shein", "shein", "shein"),
    TEMU("拼多多", "temu", "temu"),
    TIKTOK_SHOP("Tiktok全托管", "TikTokShop", "Tiktok全托管"),
    TIKTOK("Tiktok", "TikTok", "Tiktok"),
    AMAZON("亚马逊", "amazon", "亚马逊");

    private final String platform;
    private final String omsPublishPlatform;
    private final String rulePlatform;

    SheinRecommendPlatformEnum(String platform, String omsPublishPlatform, String rulePlatform) {
        this.platform = platform;
        this.omsPublishPlatform = omsPublishPlatform;
        this.rulePlatform = rulePlatform;
    }

    public String getPlatform() {
        return platform;
    }

    public String getOmsPublishPlatform() {
        return omsPublishPlatform;
    }

    public String getRulePlatform() {
        return rulePlatform;
    }

    public static SheinRecommendPlatformEnum of(List<String> platforms) {
        if (platforms.stream().anyMatch(item -> item.contains(AMAZON.platform))) {
            return AMAZON;
        }
        if (platforms.stream().anyMatch(item -> "Tiktok直邮".equals(item))) {
            return TIKTOK;
        }

        List<String> filterPlatforms = platforms.stream().filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        SheinRecommendPlatformEnum platformEnum = Stream.of(SheinRecommendPlatformEnum.values()).filter(sheinRecommendPlatformEnum -> filterPlatforms.contains(sheinRecommendPlatformEnum.getPlatform()) || filterPlatforms.contains(sheinRecommendPlatformEnum.getOmsPublishPlatform())).findAny().orElse(null);
        if (platformEnum == null) {
            throw new BusinessServiceException("找不到对应的平台");
        } else {
            return platformEnum;
        }
    }


}
