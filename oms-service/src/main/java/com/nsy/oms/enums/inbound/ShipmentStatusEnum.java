package com.nsy.oms.enums.inbound;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public enum ShipmentStatusEnum {
    WORKING("待发货"),
    SHIPPED("已发货"),
    CLOSED("已完成");

    private String status;

    public static final List<String> STOCK_OUT_STATUS_LIST = Collections.unmodifiableList(Arrays.asList(SHIPPED.status, CLOSED.status));

    public static final List<String> STOCK_IN_STATUS_LIST = Collections.unmodifiableList(Collections.singletonList(CLOSED.status));

    ShipmentStatusEnum(String status) {
        this.status = status;
    }

    public String status() {
        return status;
    }


    public static String getDescByName(String name) {
        return Arrays.stream(values())
                .filter(s -> s.name().equalsIgnoreCase(name))
                .findAny()
                .map(ShipmentStatusEnum::status)
                .orElse(name);
    }
}
