package com.nsy.oms.enums.inbound;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/2/9 17:41
 */
public enum SkuStockRemarkEnum {
    AVAILABLE("可用"),
    NEED_PURCHASE("需采购"),
    UNABLE_PURCHASE("无法采购"),
    SKU_ERROR("sku有误"),
    INVENTORY_SHORTAGE("预计发货数>ERP可用库存");
    private final String remark;

    private static final Map<String, SkuStockRemarkEnum> REMARK_MAP;

    static {
        REMARK_MAP = new HashMap<>();
        for (SkuStockRemarkEnum value : values()) {
            REMARK_MAP.put(value.remark, value);
        }
    }

    SkuStockRemarkEnum(String remark) {
        this.remark = remark;
    }

    public String getRemark() {
        return remark;
    }

    public static SkuStockRemarkEnum getSkuStockRemarkEnum(String remark) {
        return REMARK_MAP.get(remark);
    }
}
