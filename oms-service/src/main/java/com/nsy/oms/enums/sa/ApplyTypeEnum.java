package com.nsy.oms.enums.sa;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024/4/3 16:31
 * 0 创建账号申请 1 关掉申请 2 变更申请
 */
public enum ApplyTypeEnum {
    CREATE_ACCOUNT_APPLY(0, "创建账号申请", "medic_account_apply"),
    CLOSE_ACCOUNT_APPLY(1, "关店申请", "medic_account_close_apply"),
    CHANGE(2, "变更申请", "medic_account_change"),
    CARD_CHANGE(3, "信用卡变更申请", "medic_store_card_change"),
    STORE_INFO_APPLY(4, "电话/邮箱申请", "medic_store_info_apply");
    /**
     * 状态码
     */
    private final Integer code;
    /**
     * 名称
     */
    private final String desc;

    private final String activity;

    public String getActivity() {
        return activity;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    ApplyTypeEnum(Integer code, String desc, String activity) {
        this.code = code;
        this.desc = desc;
        this.activity = activity;
    }

    /**
     * 根据值映射到名称
     *
     * @param code 值
     * @return
     */
    public static String getNameByCode(Integer code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findAny().map(ApplyTypeEnum::getDesc).orElse("");
    }

    public static String getActivityByCode(Integer code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findAny().map(ApplyTypeEnum::getActivity).orElse("");
    }
}
