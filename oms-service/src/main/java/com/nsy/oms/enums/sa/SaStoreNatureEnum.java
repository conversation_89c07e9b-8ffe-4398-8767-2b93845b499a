package com.nsy.oms.enums.sa;



import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2022/12/13 15:06
 */
public enum SaStoreNatureEnum {
    MAIN_BUSINESS(1, "主营电"),
    STANDBY_STORE(2, "备用店"),
    STATION_GROUP_LINE(3, "站群线"),
    LONG_TERM_LINE(4, "长期线"),
    BRAND_PROJECT(5, "品牌孵化项目");

    private static final Map<String, Integer> CODE_AND_NAME_MAP;

    static {
        CODE_AND_NAME_MAP = new HashMap<>();
        for (SaStoreNatureEnum saStoreNatureEnum : SaStoreNatureEnum.values()) {
            CODE_AND_NAME_MAP.put(saStoreNatureEnum.getDesc(), saStoreNatureEnum.getCode());
        }
    }

    /**
     * 状态码
     */
    private final Integer code;
    /**
     * 名称
     */
    private final String desc;

    SaStoreNatureEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static Integer getCodeByDesc(String name) {
        return CODE_AND_NAME_MAP.get(name);
    }

    public static String getDescByCode(Integer code) {
        return Arrays.stream(SaStoreNatureEnum.values()).filter(natureEnum -> natureEnum.getCode().equals(code)).findFirst().map(SaStoreNatureEnum::getDesc).orElse("");
    }
}
