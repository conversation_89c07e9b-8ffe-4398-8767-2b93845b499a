package com.nsy.oms.enums.activiti;


import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021-10-27
 */
public enum WorkCenterTaskEnum {
    /**
     * 网红发帖提醒
     */
    INTERNET_CELEBRITY_SAMPLE_POST_WARN("网红发帖提醒", "INTERNET_CELEBRITY_SAMPLE_POST_WARN", "INTERNET_CELEBRITY_SAMPLE_POST_WARN", "订单号%s,等待网红发帖");

    WorkCenterTaskEnum(String name, String process, String processKey, String description) {
        this.name = name;
        this.process = process;
        this.processKey = processKey;
        this.description = description;
    }

    private final String name;

    private final String process;

    private final String processKey;

    private final String description;

    public String getName() {
        return name;
    }

    public String getProcess() {
        return process;
    }

    public String getProcessKey() {
        return processKey;
    }

    public String getDescription() {
        return description;
    }


    public static String getNameByProcess(String process) {
        return Arrays.stream(values()).filter(e -> Objects.equals(e.getProcess(), process)).findAny().map(Enum::name).orElse("");
    }

    public static String getProcessKeyByProcess(String process) {
        return Arrays.stream(values()).filter(e -> Objects.equals(e.getProcess(), process)).findAny().map(WorkCenterTaskEnum::getProcessKey).orElse("");
    }

    public static String getDescriptionByProcess(String process) {
        return Arrays.stream(values()).filter(e -> Objects.equals(e.getProcess(), process)).findAny().map(WorkCenterTaskEnum::getDescription).orElse("");
    }
}
