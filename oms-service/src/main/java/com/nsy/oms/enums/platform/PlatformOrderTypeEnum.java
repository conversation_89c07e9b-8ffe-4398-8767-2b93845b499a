package com.nsy.oms.enums.platform;

import java.util.Arrays;

/**
 * 平台订单类型
 *  10-销售订单
 *  20-样品订单
 */
public enum PlatformOrderTypeEnum {
    SALES_ORDER(10, "销售订单"),
    SAMPLE_ORDER(20, "卖家样品订单"),
    PLATFORM_SAMPLE_ORDER(30, "平台样品订单");
    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;

    PlatformOrderTypeEnum(Integer code, String desc) {
        this.desc = desc;
        this.code = code;
    }


    /**
     * 根据值映射到名称
     *
     * @param code 值
     * @return
     */
    public static String getDescByCode(Integer code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findAny().map(PlatformOrderTypeEnum::getDesc).orElse("");
    }

    public String getDesc() {
        return desc;
    }

    public Integer getCode() {
        return code;
    }

}

