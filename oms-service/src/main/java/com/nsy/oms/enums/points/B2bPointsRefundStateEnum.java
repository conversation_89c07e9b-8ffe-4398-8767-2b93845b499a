package com.nsy.oms.enums.points;

import lombok.Getter;

/**
 * created by jun.
 **/
@Getter
public enum B2bPointsRefundStateEnum {

    WAIT_CONFIRM("待确认"),
    WAIT_DISTRIBUTE("待分配"),
    WAIT_PROCESS("待处理"),
    ALREADY_PROCESS("已处理"),
    CANCEL("已取消");


    private final String status;

    B2bPointsRefundStateEnum(String status) {
        this.status = status;
    }

    public String status() {
        return this.status;
    }
}
