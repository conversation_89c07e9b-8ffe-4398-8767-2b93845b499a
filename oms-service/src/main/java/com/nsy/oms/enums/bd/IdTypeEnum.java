package com.nsy.oms.enums.bd;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024/8/22 14:44
 */
public enum IdTypeEnum {
    IDENTITY_CARD(0, "身份证"),
    BUSINESS_LICENSE(1, "营业执照"),
    TAX_NUMBER(2, "税号");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String value;

    IdTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }


    /**
     * 根据编码映射值
     *
     * @param code 编码
     * @return
     */
    public static String getValueByCode(Integer code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findAny().map(IdTypeEnum::getValue).orElse("");
    }

    public String getValue() {
        return value;
    }

    public Integer getCode() {
        return code;
    }
}
