package com.nsy.oms.utils.excel;

import com.nsy.api.core.apicore.util.Convert;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.NumberUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.Date;

/**
 * Created by jun.
 */
public abstract class Reflects {
    //private static final Logger LOGGER = LoggerFactory.getLogger(Reflects.class);

    public static void makeAccessible(final Field field) {
        if ((!Modifier.isPublic(field.getModifiers()) || !Modifier.isPublic(field.getDeclaringClass().getModifiers())
            || Modifier.isFinal(field.getModifiers())) && !field.isAccessible()) {
//            AccessController.doPrivileged((PrivilegedAction<Void>) () -> {
            field.setAccessible(true);
//                return null;
//            });
        }
    }

    public static Object getField(Field field, Object target) {
        try {
            makeAccessible(field);
            return field.get(target);
        } catch (IllegalAccessException e) {
            throw new IllegalStateException(e);
        }
    }

    public static void setField(Field field, Object target, String value) {
        try {
            makeAccessible(field);
            Class t = field.getType();
            if (String.class == t) {
                field.set(target, value);
            } else if (Date.class == t) {
                if (StringUtils.isNotEmpty(value)) {
                    field.set(target, Convert.toDate(value, new Date(0)));
                }
            } else {
                field.set(target, NumberUtils.parseNumber(value, t));
            }

        } catch (IllegalAccessException e) {
            throw new IllegalStateException(e);
        }
    }

    public static Method findMethod(Object object, String methodName, Class<?>... parameterTypes) {
        try {
            return object.getClass().getMethod(methodName, parameterTypes);
        } catch (NoSuchMethodException ex) {
            //LOGGER.error(ex.getMessage(), ex);
            return null;
        }
    }
}
