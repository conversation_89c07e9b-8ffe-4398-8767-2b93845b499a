package com.nsy.oms.utils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: qiu wanzi
 * @date: 2024年2月28日 0028
 * @version: 1.0
 * @description: TODO
 */
public class Lists {


    public static <T, K, V> Map<K, V> collectToMap(List<T> list, Function<T, K> keyMapper, Function<T, V> valueMapper) {
        return list.stream().filter(item -> Objects.nonNull(keyMapper.apply(item))).collect(Collectors.toMap(keyMapper, valueMapper, (k1, k2) -> k1));
    }

    public static <T, K> Map<K, T> collectToMap(List<T> list, Function<T, K> keyMapper) {
        return list.stream().filter(item -> Objects.nonNull(keyMapper.apply(item))).collect(Collectors.toMap(keyMapper, Function.identity(), (k1, k2) -> k1));
    }

    public static <T, K> Map<K, List<T>> collectToGroupBy(List<T> list, Function<T, K> keyMapper) {
        return list.stream().filter(item -> Objects.nonNull(keyMapper.apply(item))).collect(Collectors.groupingBy(keyMapper));
    }

}
