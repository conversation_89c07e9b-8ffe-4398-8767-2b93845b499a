package com.nsy.oms.utils;

import com.nsy.api.core.apicore.util.CharacterEncodings;
import com.nsy.oms.utils.excel.Document;
import org.springframework.http.MediaType;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UncheckedIOException;
import java.net.URLEncoder;

/**
 * Created by jun.
 */
public class HttpResponseUtils {

    public static void write(String filename, HttpServletResponse response, Document document) {
        try (OutputStream outputStream = response.getOutputStream()) {
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-Disposition", String.format("attachment;filename=%s", URLEncoder.encode(filename, CharacterEncodings.UTF_8)));
            document.write(outputStream);
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }
}
