package com.nsy.oms.utils.excel;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.oms.utils.excel.converter.CellConverter;
import com.nsy.oms.utils.excel.converter.CellConverters;
import com.nsy.oms.utils.excel.validator.CellValidator;
import com.nsy.oms.utils.excel.validator.CellValidators;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.CellType;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UncheckedIOException;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static org.apache.poi.hssf.usermodel.HSSFDateUtil.isCellDateFormatted;

/**
 * Created by jun.
 */
@Deprecated
public class Excel implements Document {

    private final List<Rows> sheets = Lists.newArrayList();

    private final InputStream stream;

    public Excel(InputStream stream) {
        this.stream = stream;
    }

    public Excel addRows(Rows rows) {
        sheets.add(rows);
        return this;
    }

    @Override
    public void write(OutputStream stream) {
        try (HSSFWorkbook workbook = new HSSFWorkbook(this.stream)) {
            sheets.forEach(rows -> buildSheet(workbook, rows));
            workbook.write(stream);
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

    private void buildSheet(HSSFWorkbook workbook, Rows rows) {
        HSSFSheet hssfSheet = workbook.getSheetAt(rows.getIndex());
        IntStream.range(0, rows.getRows().size())
                .forEach(index -> buildSheetRow(hssfSheet, index + rows.getStart(), rows.getRows().get(index)));
    }

    private void buildSheetRow(HSSFSheet hssfSheet, int rowNumber, Object data) {
        HSSFRow hssfRow = hssfSheet.createRow(rowNumber);
        Arrays.stream(data.getClass().getDeclaredFields())
                .forEach(field -> buildSheetCellContent(hssfRow, field, data));
    }

    private void buildSheetCellContent(HSSFRow hssfRow, Field field, Object data) {
        Optional.ofNullable(field.getAnnotation(Cell.class))
                .ifPresent(cell -> {
                    HSSFCell hssfCell = hssfRow.createCell(cell.index());
                    String originValue = Optional.ofNullable(Reflects.getField(field, data)).map(String::valueOf).orElse("");
                    Class<? extends CellConverter>[] converters = cell.converters();
                    if (converters.length > 0) {
                        hssfCell.setCellValue(CellConverters.convert(originValue, converters));
                    } else {
                        hssfCell.setCellValue(originValue);
                    }
                });
    }

    /**
     * 读取每一行的单元格
     *
     * @param type         : Excel 对应的类型
     * @param headRowCount : 头部行数
     */

    public <T> List<T> read(Class<T> type, Integer headRowCount) throws IllegalAccessException, InstantiationException {
        List<T> result = new ArrayList<>();
        Map<String, List<Integer>> msgMap = Maps.newHashMap();
        try {
            HSSFWorkbook workbook = new HSSFWorkbook(this.stream);
            HSSFSheet sheet = workbook.getSheetAt(0);
            for (int ri = sheet.getFirstRowNum() + headRowCount; ri <= sheet.getLastRowNum(); ri++) {
                HSSFRow row = sheet.getRow(ri);
                if (null == row || isRowEmpty(row)) continue;
                T object = type.newInstance();
                Field[] fields = object.getClass().getDeclaredFields();
                Field.setAccessible(fields, true);
                int rowNum = ri;
                Arrays.stream(type.getDeclaredFields())
                        .forEach(field -> {
                            Optional.ofNullable(field.getAnnotation(Cell.class))
                                    .ifPresent(cell -> {
                                        HSSFCell hssfCell = row.getCell(cell.index());
                                        String value = "";
                                        if (hssfCell != null) {
                                            value = parseExcel(hssfCell);
                                        }
                                        CellValidator[] validators = cell.validators();
                                        buildCheckCellValidatorErrorMsg(msgMap, validators, rowNum, value);
                                        if (CollectionUtils.sizeIsEmpty(msgMap)) {
                                            Reflects.setField(field, object, value);
                                        }
                                    });
                        });
                result.add(object);
                Field.setAccessible(fields, false);
            }
            throwErrorMsg(msgMap);
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
        return result;
    }

    public <T> List<T> readByTitle(Class<T> type, Integer headRowCount) throws IllegalAccessException, InstantiationException {
        List<T> result = new ArrayList<>();
        Map<String, List<Integer>> msgMap = Maps.newHashMap();
        try {
            HSSFWorkbook workbook = new HSSFWorkbook(this.stream);
            HSSFSheet sheet = workbook.getSheetAt(0);
            HSSFRow titleRow = sheet.getRow(headRowCount - 1);
            Map<String, Integer> titleIndexMap = buildTitleIndexMap(titleRow);
            checkCellTitleExist(type, titleIndexMap);
            for (int rowIndex = sheet.getFirstRowNum() + headRowCount; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                HSSFRow row = sheet.getRow(rowIndex);
                if (null == row || isRowEmpty(row)) continue;
                T object = type.newInstance();
                Field[] fields = object.getClass().getDeclaredFields();
                Field.setAccessible(fields, true);
                int finalRowIndex = rowIndex;
                Arrays.stream(type.getDeclaredFields())
                        .forEach(field -> {
                            Cell cell = field.getAnnotation(Cell.class);
                            if (Objects.isNull(cell)) return;
                            HSSFCell hssfCell = row.getCell(titleIndexMap.get(cell.title()));
                            CellValidator[] validators = cell.validators();
                            String value = parseExcel(hssfCell);
                            buildCheckCellValidatorErrorMsg(msgMap, validators, finalRowIndex, value);
                            if (CollectionUtils.sizeIsEmpty(msgMap)) {
                                Reflects.setField(field, object, value);
                            }
                        });
                result.add(object);
                Field.setAccessible(fields, false);
            }
            throwErrorMsg(msgMap);
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
        return result;
    }

    private Map<String, Integer> buildTitleIndexMap(HSSFRow titleRow) {
        return IntStream.range(0, titleRow.getLastCellNum())
                .mapToObj(titleRow::getCell)
                .filter(Objects::nonNull)
                .filter(hssfCell -> org.apache.commons.lang3.StringUtils.isNotEmpty(hssfCell.getStringCellValue()))
                .collect(Collectors.toMap(HSSFCell::getStringCellValue, HSSFCell::getColumnIndex));
    }

    private <T> void checkCellTitleExist(Class<T> type, Map<String, Integer> titleIndexMap) {
        Arrays.stream(type.getDeclaredFields())
                .filter(field -> Optional.ofNullable(field.getAnnotation(Cell.class)).isPresent())
                .forEach(field -> {
                    Cell cell = field.getAnnotation(Cell.class);
                    if (!titleIndexMap.containsKey(cell.title())) {
                        throw new BusinessServiceException(String.format("%s未找到", cell.title()));
                    }
                });
    }

    private void buildCheckCellValidatorErrorMsg(Map<String, List<Integer>> msgMap, CellValidator[] validators, int rowNum, String cellValue) {
        if (null != validators && validators.length > 0) {
            Map<String, Integer> errorMsgMap = CellValidators.doCheck(rowNum, cellValue, validators);
            errorMsgMap.forEach((key, value) -> {
                if (msgMap.containsKey(key)) {
                    msgMap.put(key, Stream.of(msgMap.get(key), Collections.singletonList(value)).flatMap(Collection::stream).collect(Collectors.toList()));
                } else {
                    msgMap.put(key, Collections.singletonList(value));
                }
            });
        }
    }

    private void throwErrorMsg(Map<String, List<Integer>> msgMap) {
        StringBuilder sb = new StringBuilder(128);
        msgMap.forEach((key, value) -> sb.append(String.format("行号%s,%s\n", value.toString(), key)));
        Validator.check(sb, s -> s.length() <= 0, sb.toString());
    }

    private String parseExcel(HSSFCell cell) {
        String result = "";
        if (Objects.isNull(cell) || Objects.isNull(cell.getCellTypeEnum())) {
            return result;
        }
        switch (cell.getCellTypeEnum()) {
            case NUMERIC:// 数字类型
                if (isCellDateFormatted(cell)) {
                    // 处理日期格式、时间格式
                    SimpleDateFormat sdf;
                    if (cell.getCellStyle().getDataFormat() == HSSFDataFormat
                            .getBuiltinFormat("h:mm")) {
                        sdf = new SimpleDateFormat("HH:mm");
                    } else {
                        // 日期
                        sdf = new SimpleDateFormat("yyyy-MM-dd");
                    }
                    Date date = cell.getDateCellValue();
                    result = sdf.format(date);
                } else if (cell.getCellStyle().getDataFormat() == 58) {
                    // 处理自定义日期格式：m月d日(通过判断单元格的格式id解决，id的值是58)
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    double value = cell.getNumericCellValue();
                    Date date = org.apache.poi.ss.usermodel.DateUtil
                            .getJavaDate(value);
                    result = sdf.format(date);
                } else {
                    // 判断是否有小数
                    if (cell.getNumericCellValue() % 1 == 0) {
                        result = String.valueOf((long) cell.getNumericCellValue());
                    } else {
                        result = String.valueOf(cell.getNumericCellValue());
                    }
                }
                break;
            case STRING:// String类型
                result = cell.getRichStringCellValue().toString();
                break;
            case BLANK:
                result = "";
                break;
            default:
                result = "";
                break;
        }
        return result;
    }

    public boolean isRowEmpty(HSSFRow row) {
        for (int c = row.getFirstCellNum(); c < row.getLastCellNum(); c++) {
            HSSFCell cell = row.getCell(c);
            if (cell != null && cell.getCellTypeEnum() != CellType.BLANK)
                return false;
        }
        return true;

    }
}
