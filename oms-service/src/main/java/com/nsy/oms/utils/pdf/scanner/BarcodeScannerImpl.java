package com.nsy.oms.utils.pdf.scanner;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.BinaryBitmap;
import com.google.zxing.DecodeHintType;
import com.google.zxing.LuminanceSource;
import com.google.zxing.MultiFormatReader;
import com.google.zxing.NotFoundException;
import com.google.zxing.Result;
import com.google.zxing.ResultPoint;
import com.google.zxing.client.j2se.BufferedImageLuminanceSource;
import com.google.zxing.common.HybridBinarizer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.awt.image.BufferedImage;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 条形码扫描器实现类.
 */
public class BarcodeScannerImpl implements CodeScanner {
    private static final Logger LOGGER = LoggerFactory.getLogger(BarcodeScannerImpl.class);
    private final MultiFormatReader reader;

    public BarcodeScannerImpl() {
        reader = new MultiFormatReader();
        Map<DecodeHintType, Object> hints = new EnumMap<>(DecodeHintType.class);
        
        List<BarcodeFormat> formats = Arrays.asList(
            BarcodeFormat.CODE_128,
            BarcodeFormat.CODE_39,
            BarcodeFormat.CODE_93,
            BarcodeFormat.EAN_13,
            BarcodeFormat.EAN_8,
            BarcodeFormat.ITF
        );
        
        hints.put(DecodeHintType.POSSIBLE_FORMATS, formats);
        hints.put(DecodeHintType.TRY_HARDER, Boolean.TRUE);
        reader.setHints(hints);
    }

    @Override
    public List<String> scan(BufferedImage image) {
        List<String> results = new ArrayList<>();
        
        int width = image.getWidth();
        int height = image.getHeight();
        
        LuminanceSource source = new BufferedImageLuminanceSource(image);
        BinaryBitmap bitmap = new BinaryBitmap(new HybridBinarizer(source));
        
        try {
            Map<ResultPoint[], Result> found = new HashMap<>();
            int[] regions = {0, height / 3, 2 * height / 3, height};
            
            for (int i = 0; i < regions.length - 1; i++) {
                try {
                    Result result = reader.decode(
                        bitmap.crop(0, regions[i], width, regions[i + 1] - regions[i])
                    );
                    if (result != null) {
                        found.put(result.getResultPoints(), result);
                    }
                } catch (NotFoundException ignored) {
                    // 继续扫描其他区域
                }
            }
            
            for (Result result : found.values()) {
                String text = result.getText();
                if (text != null && !text.isEmpty()) {
                    results.add(text);
                    ResultPoint[] points = result.getResultPoints();
                    LOGGER.info("识别到条形码: {}, 位置: ({}, {})", 
                        text,
                        points[0].getX(),
                        points[0].getY()
                    );
                }
            }
            
        } catch (Exception e) {
            LOGGER.error("扫描条形码时发生错误: {}", e.getMessage());
        }
        
        return results;
    }

    @Override
    public String getCodeTypeName() {
        return "条形码";
    }
} 