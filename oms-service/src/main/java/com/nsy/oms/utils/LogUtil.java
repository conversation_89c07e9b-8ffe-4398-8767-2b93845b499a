package com.nsy.oms.utils;

import com.nsy.oms.annotation.FieldDesc;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2021/03/19
 */
public class LogUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(LogUtil.class);

    public static <T> String contrastObj(Object oldBean, Object newBean, Class<T> clazz, List<String> list, boolean isRecordValue) {
        StringBuilder sb = new StringBuilder();
        T pojo1 = (T) oldBean;
        T pojo2 = (T) newBean;
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            if (!list.contains(field.getName())) {
                continue;
            }
            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd");
            DecimalFormat decimalFormat = new DecimalFormat("0.00#");
            Method getMethod;
            String getterName = "get" + StringUtils.capitalize(field.getName());
            try {
                getMethod = clazz.getMethod(getterName);
            } catch (NoSuchMethodException e) {
                LOGGER.error(e.getMessage(), e);
                continue;
            }
            Object o1 = null;
            Object o2 = null;
            try {
                o1 = getMethod.invoke(pojo1);
                o2 = getMethod.invoke(pojo2);
            } catch (IllegalAccessException | InvocationTargetException e) {
                LOGGER.error(e.getMessage(), e);
            }
            if (o1 instanceof Date) {
                o1 = sdf2.format(o1);
            }
            if (o2 instanceof Date) {
                o2 = sdf2.format(o2);
            }
            if (o2 instanceof BigDecimal) {
                o2 = decimalFormat.format(o2);
            }
            if (o1 instanceof BigDecimal) {
                o1 = decimalFormat.format(o1);
            }
            if (o2 == null) {
                continue;
            }
            if (!o2.equals(o1)) {
                FieldDesc declaredAnnotation = field.getDeclaredAnnotation(FieldDesc.class);
                String customFieldValue = declaredAnnotation != null && StringUtils.isNotEmpty(declaredAnnotation.value()) ? declaredAnnotation.value() : field.getName();
                if (isRecordValue) {
                    sb.append(String.format(customFieldValue + "(旧值" + ":%s " + "新值" + ":%s);", o1, o2));
                } else {
                    sb.append(String.format(customFieldValue + "被修改"));
                }
            }
        }
        return sb.toString();

    }


}
