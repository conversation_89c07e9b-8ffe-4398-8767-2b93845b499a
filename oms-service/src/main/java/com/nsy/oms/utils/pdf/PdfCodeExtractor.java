package com.nsy.oms.utils.pdf;

import com.nsy.oms.utils.pdf.pdf.PdfImageExtractor;
import com.nsy.oms.utils.pdf.scanner.CodeScanner;
import com.nsy.oms.utils.pdf.scanner.CodeScannerFactory;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.awt.image.BufferedImage;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Set;

/**
 * PDF条码提取工具类 - 使用工厂模式和多态设计.
 */
@Slf4j
@Component
public class PdfCodeExtractor {
    private static final Logger LOGGER = LoggerFactory.getLogger(PdfCodeExtractor.class);

    private final PdfImageExtractor imageExtractor;
    private final List<CodeScanner> scanners;

    /**
     * 创建识别所有类型条码的提取器.
     */
    public PdfCodeExtractor() {
        this.imageExtractor = new PdfImageExtractor();
        this.scanners = new ArrayList<>();
        initializeScanners();
    }



    /**
     * 从PDF URL中提取所有代码
     *
     * @param pdfUrl PDF文件的URL
     * @return 代码列表
     */
    public Set<String> extractCodesFromUrl(String pdfUrl) {
        Set<String> codes = new HashSet<>();
        List<BufferedImage> images = imageExtractor.extractImagesFromUrl(pdfUrl);
        
        for (BufferedImage image : images) {
            for (CodeScanner scanner : scanners) {
                codes.addAll(scanner.scan(image));
            }
        }
        
        return codes;
    }

    private void initializeScanners() {
        configureScanners("all");
    }

    /**
     * 配置扫描器.
     * @param mode 扫描模式: all, qrcode, barcode
     */
    public void configureScanners(String mode) {
        scanners.clear();
        switch (mode.toLowerCase(Locale.ROOT)) {
            case "barcode":
                scanners.add(CodeScannerFactory.createBarcodeScanner());
                LOGGER.info("配置为仅使用条形码扫描器");
                break;
            case "all":
            default:
                scanners.addAll(CodeScannerFactory.createAllScanners());
                LOGGER.info("配置为使用所有扫描器");
                break;
        }
    }

} 