package com.nsy.oms.utils;

import com.nsy.oms.enums.tkcreator.InternetCelebrityLevelType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * 功能描述:
 *
 * @author: qiu wanzi
 * @date: 2025年4月22日 星期二
 * @version: 1.0
 */
@Slf4j
public class TiktokCreatorUtil {


    /**
     * $45.4K 转 45400
     *
     * @param str
     * @return
     */
    public static BigDecimal stringToBigDecimal(String oriStr) {

        if (oriStr == null || oriStr.isEmpty()) {
            return BigDecimal.ZERO;
        }
        // 去掉空格
        String str = oriStr.trim();
        // 确定缩写使用的倍数
        BigDecimal multiplier = BigDecimal.ONE;
        // 处理百分数
        if (str.endsWith("%")) {
            multiplier = new BigDecimal("0.01");
            str = str.substring(0, str.length() - 1);
        }

        if (str.endsWith("K") || str.endsWith("k")) {
            multiplier = new BigDecimal("1000");
            str = str.substring(0, str.length() - 1);
        } else if (str.endsWith("M") || str.endsWith("m")) {
            multiplier = new BigDecimal("1000000");
            str = str.substring(0, str.length() - 1);
        } else if (str.endsWith("B") || str.endsWith("b")) {
            multiplier = new BigDecimal("1000000000");
            str = str.substring(0, str.length() - 1);
        }
        // 去掉货币符号（如 $）
        if (str.startsWith("$")) {
            str = str.substring(1);
        }
        try {
            BigDecimal numericValue = new BigDecimal(str);
            return numericValue.multiply(multiplier);
        } catch (NumberFormatException e) {
            log.info("[NumberFormatException] TiktokCreatorUtil stringToBigDecimal : {} ", str);
        }
        return BigDecimal.ZERO;

    }

    /**
     * $45.4K 转 45400
     *
     * @param str
     * @return
     */
    public static int stringToInt(String oriStr) {
        if (oriStr == null || oriStr.isEmpty()) {
            return 0;
        }
        // 去掉空格
        String str = oriStr.trim();
        // 确定缩写使用的倍数
        BigDecimal multiplier = BigDecimal.ONE;
        // 处理百分数
        if (str.endsWith("%")) {
            multiplier = new BigDecimal("0.01");
            str = str.substring(0, str.length() - 1);
        }
        if (str.endsWith("K") || str.endsWith("k")) {
            multiplier = new BigDecimal("1000");
            str = str.substring(0, str.length() - 1);
        } else if (str.endsWith("M") || str.endsWith("m")) {
            multiplier = new BigDecimal("1000000");
            str = str.substring(0, str.length() - 1);
        } else if (str.endsWith("B") || str.endsWith("b")) {
            multiplier = new BigDecimal("1000000000");
            str = str.substring(0, str.length() - 1);
        }
        // 去掉货币符号（如 $）
        if (str.startsWith("$")) {
            str = str.substring(1);
        }
        try {
            BigDecimal numericValue = new BigDecimal(str);
            return numericValue.multiply(multiplier).intValue();
        } catch (NumberFormatException e) {
            log.info("[NumberFormatException] TiktokCreatorUtil stringToInt : {} ", str);
        }
        return 0;

    }

    public static BigDecimal divide(BigDecimal pri) {

        try {
            return pri.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP);
        } catch (NumberFormatException e) {
            log.info("[NumberFormatException] TiktokCreatorUtil strToBigDecimalAndDivide : {} ", pri);
        }

        return pri;
    }

    public static Date convertIntegerToDate(Integer publishTime) {
        if (publishTime == null) {
            log.info("[日期转换失败] TiktokCreatorUtil convertIntegerToDate null");
            throw new IllegalArgumentException("Publish time cannot be null");
        }

        // 转换秒数到毫秒数（如果需要按照秒转换）
        long timestampInMilliseconds = publishTime * 1000L;

        // 使用时间戳构造 Date 对象
        return DateUtils.addHours(new Date(timestampInMilliseconds), -16);
    }

    /**
     * C:小于1000美金
     *  *             ● B:1000美金（含）-5000美金
     *  *             ● A:5000美金（含）-1万美金
     *  *             ● V:1万美金（含）-3万美金
     *  *             ● S:>=3万美金
     * @param videoGmvIn90
     * @return
     */
    public static InternetCelebrityLevelType checkLevel(BigDecimal videoGmvIn90) {
        if (videoGmvIn90 == null || videoGmvIn90.compareTo(new BigDecimal(1000)) < 0) {
            return InternetCelebrityLevelType.C;
        } else if (videoGmvIn90.compareTo(new BigDecimal(5000)) < 0) {
            return InternetCelebrityLevelType.B;
        } else if (videoGmvIn90.compareTo(new BigDecimal(10000)) < 0) {
            return InternetCelebrityLevelType.A;
        } else if (videoGmvIn90.compareTo(new BigDecimal(30000)) < 0) {
            return InternetCelebrityLevelType.V;
        } else {
            return InternetCelebrityLevelType.S;
        }
    }

}
