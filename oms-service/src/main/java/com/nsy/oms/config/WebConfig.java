package com.nsy.oms.config;

import com.nsy.oms.interceptor.LongRequestInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(longRequestInterceptor());
    }

    @Bean
    public LongRequestInterceptor longRequestInterceptor() {
        return new LongRequestInterceptor();
    }
}
