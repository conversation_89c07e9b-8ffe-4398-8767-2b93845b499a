package com.nsy.oms.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.IndexInfo;
import org.springframework.data.mongodb.core.index.IndexOperations;
import org.springframework.data.mongodb.core.index.IndexResolver;
import org.springframework.data.mongodb.core.index.MongoPersistentEntityIndexResolver;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Configuration
public class MongoIndexConfig {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private MongoMappingContext mongoMappingContext;

    @PostConstruct
    public void initIndicesAfterStartup() {
        mongoMappingContext.getPersistentEntities()
                .stream()
                .filter(entity -> entity.getType().isAnnotationPresent(Document.class))
                .forEach(entity -> {
                    try {
                        IndexOperations indexOps = mongoTemplate.indexOps(entity.getType());
                        IndexResolver resolver = new MongoPersistentEntityIndexResolver(mongoMappingContext);

                        // 获取已存在的索引
                        List<IndexInfo> existingIndexes = indexOps.getIndexInfo();
                        Set<String> existingIndexNames = existingIndexes.stream()
                                .map(IndexInfo::getName)
                                .collect(Collectors.toSet());

                        // 只创建不存在的索引
                        resolver.resolveIndexFor(entity.getType())
                                .forEach(index -> {
                                    String indexName = (String) index.getIndexOptions().get("name");
                                    if (!existingIndexNames.contains(indexName)) {
                                        indexOps.ensureIndex(index);
                                    }
                                });
                    } catch (Exception e) {
                        // 记录错误但继续处理其他实体
                        log.error("Error creating indexes for " + entity.getType().getName() + ": " + e.getMessage());
                    }
                });
    }
}