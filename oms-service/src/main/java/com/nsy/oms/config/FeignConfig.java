package com.nsy.oms.config;

import com.nsy.api.core.apicore.privilege.LoginInfoService;
import com.nsy.api.core.apicore.util.EncodingUtils;
import com.nsy.oms.utils.mp.LocationContext;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FeignConfig implements RequestInterceptor {
    @Autowired
    private LoginInfoService loginInfoService;

    @Override
    public void apply(RequestTemplate template) {
        template.header("location", LocationContext.getLocation());
        template.header("real-name", EncodingUtils.url(loginInfoService.getName()));
        template.header("user-name", EncodingUtils.url(loginInfoService.getUserName()));
        template.header("user-code", loginInfoService.getUserCode());
        template.header("user-id", String.valueOf(loginInfoService.getUserId()));
        template.header("is-admin", String.valueOf(loginInfoService.isAdmin()));
    }
}
