package com.nsy.oms.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import com.nsy.oms.utils.IntranetContext;
import com.nsy.oms.utils.mp.IgnoreTenantContext;
import com.nsy.oms.utils.mp.LocationContext;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;


@Configuration
@MapperScan("com.nsy.oms.repository.sql.mapper")
public class MybatisPlusConfig {
    /**
     * 多租户标识
     */
    private static final String SYSTEM_LOCATION = "location";

    /**
     * 多租户忽略表
     */
    private static final List<String> IGNORE_TENANT_TABLE = new ArrayList<>();

    static {
        IGNORE_TENANT_TABLE.add("bd_marketplace");
        IGNORE_TENANT_TABLE.add("bd_platform");
        IGNORE_TENANT_TABLE.add("q_consumer_record");
        IGNORE_TENANT_TABLE.add("q_message");
        IGNORE_TENANT_TABLE.add("quartz_job_data");
        IGNORE_TENANT_TABLE.add("bd_account_type_config");
        IGNORE_TENANT_TABLE.add("quartz_job_log");
        IGNORE_TENANT_TABLE.add("bd_fba_cost_config");
        IGNORE_TENANT_TABLE.add("bd_oversea_replenishment_group");
        IGNORE_TENANT_TABLE.add("bd_oversea_replenishment_space_group");
        IGNORE_TENANT_TABLE.add("bd_oversea_replenishment_store_group");
    }


    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor mybatisPlusInterceptor = new MybatisPlusInterceptor();
        mybatisPlusInterceptor.addInnerInterceptor(new TenantLineInnerInterceptor(new MyTenantLineHandler()));
        mybatisPlusInterceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        mybatisPlusInterceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        return mybatisPlusInterceptor;
    }

    protected static final class MyTenantLineHandler implements TenantLineHandler {

        @Override
        public Expression getTenantId() {
            return new StringValue(Optional.ofNullable(LocationContext.getLocation()).orElse(StringUtils.EMPTY));
        }

        @Override
        public String getTenantIdColumn() {
            // 对应数据库租户ID的列名，是数据库列名，不是实体类
            return SYSTEM_LOCATION;
        }

        @Override
        public boolean ignoreTable(String tableName) {
            //  使用@IgnoreTenant注解忽略自动添加location
            if (BooleanUtils.isTrue(IgnoreTenantContext.getIgnoreTenant())) {
                return true;
            }
            if (Objects.nonNull(IntranetContext.getIntranet()) && IntranetContext.getIntranet() == 1 && StringUtils.isBlank(LocationContext.getLocation())) {
                return true;
            }
            return IGNORE_TENANT_TABLE.contains(tableName);
        }
    }
}
