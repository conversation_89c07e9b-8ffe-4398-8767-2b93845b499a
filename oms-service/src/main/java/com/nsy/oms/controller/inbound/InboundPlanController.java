package com.nsy.oms.controller.inbound;

import com.nsy.oms.business.domain.request.inbound.InboundPlanExcelRequest;
import com.nsy.oms.business.domain.request.inbound.ShipmentPlanPageRequest;
import com.nsy.oms.business.domain.request.inbound.StaShipmentPlanCloseRequest;
import com.nsy.oms.business.domain.request.inbound.StaShipmentPlanCreateRequest;
import com.nsy.oms.business.domain.request.inbound.UploadShipmentRequest;
import com.nsy.oms.business.domain.response.base.PageResponse;
import com.nsy.oms.business.domain.response.inbound.FbaInboundShipmentPlanCreateResponse;
import com.nsy.oms.business.domain.response.inbound.InboundPlan;
import com.nsy.oms.business.domain.response.inbound.PlanDetailResponse;
import com.nsy.oms.business.domain.response.inbound.PlanUploadResponse;
import com.nsy.oms.business.manage.user.request.shared.SelectModel;
import com.nsy.oms.business.service.inbound.InboundCreateService;
import com.nsy.oms.business.service.inbound.InboundIOService;
import com.nsy.oms.business.service.inbound.InboundPlanService;
import com.nsy.oms.enums.inbound.InboundPlanStatusEnum;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/inbound-plan")
public class InboundPlanController {

    @Resource
    private InboundCreateService inboundCreateService;
    @Resource
    private InboundIOService inboundIOService;
    @Resource
    private InboundPlanService inboundPlanService;

    @ApiOperation("创建补货单-创建")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public FbaInboundShipmentPlanCreateResponse createInboundShipmentPlan(@RequestBody @Valid StaShipmentPlanCreateRequest request) {
        return inboundCreateService.createInboundShipmentPlan(request);
    }

    @ApiOperation("列表")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public PageResponse<InboundPlan> page(@RequestBody ShipmentPlanPageRequest request) {
        return inboundCreateService.page(request);
    }

    @ApiOperation("创建补货单-上传")
    @RequestMapping(value = "/create/upload", method = RequestMethod.POST)
    public PlanUploadResponse uploadExcel(@RequestBody @Valid InboundPlanExcelRequest request) {
        return inboundCreateService.uploadExcel(request);
    }

    @ApiOperation("详情")
    @RequestMapping(value = "/detail/{planId}", method = RequestMethod.GET)
    public PlanDetailResponse detail(@PathVariable Integer planId) {
        return inboundCreateService.detail(planId);
    }

    @ApiOperation("补货单查看-关闭补货单")
    @RequestMapping(value = "/close", method = RequestMethod.POST)
    public void close(@RequestBody StaShipmentPlanCloseRequest request) {
        inboundCreateService.close(request);
    }

    @ApiOperation("补货单查看-退回已装箱")
    @RequestMapping(value = "/back-pack/{planId}", method = RequestMethod.POST)
    public void backToBox(@PathVariable Integer planId) {
        inboundCreateService.backToBoxStatus(planId);
    }

    @ApiOperation("补货单查看-退回已预配")
    @RequestMapping(value = "/back-to-allocated-for-wms/{erpTid}", method = RequestMethod.GET)
    public void backToAllocate(@PathVariable String erpTid) {
        inboundPlanService.updateStatusByErpTid(erpTid, InboundPlanStatusEnum.ALLOCATED.status());
    }

    @ApiOperation("根据当前location与对应权限的店铺")
    @GetMapping("/get-tiktok-store")
    public List<SelectModel> getTikTokStore() {
        return inboundCreateService.getTikTokStore();
    }

    @ApiOperation("物流渠道")
    @RequestMapping(value = "/logistics-list", method = RequestMethod.GET)
    public List<SelectModel> logisticList() {
        return inboundCreateService.logisticList();
    }

    @ApiOperation("下载装箱清单")
    @RequestMapping(value = "/download-package-info/{planId}", method = RequestMethod.GET)
    public void staShipmentDetailByShipment(@PathVariable Integer planId, HttpServletResponse response) throws Exception {
        inboundIOService.writePackageInfoResponse(planId, response);
    }

    @ApiOperation("上传shipment清单")
    @RequestMapping(value = "/upload-shipment", method = RequestMethod.POST)
    public String uploadShipmentUrls(@RequestBody UploadShipmentRequest uploadShipmentRequest) {
        inboundIOService.uploadShipment(uploadShipmentRequest);
        return "success";
    }

}
