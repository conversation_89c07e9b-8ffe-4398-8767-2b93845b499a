package com.nsy.oms.controller.celebrity;


import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.oms.business.domain.request.celebrity.SaveInternetCelebritySampleOrderItemPostRequest;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <p>
 * 网红样衣订单明细发帖信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-05
 */
@RestController
@RequestMapping("/internet-celebrity-sample-order-item-post")
public class InternetCelebritySampleOrderItemPostController {

    @RequestMapping(value = "/save-or-update", method = RequestMethod.POST)
    @ApiOperation("网红样衣订单-新增发帖信息")
    public void saveOrUpdate(@Valid @RequestBody SaveInternetCelebritySampleOrderItemPostRequest request) {
        throw new BusinessServiceException("网红样衣订单-新增发帖信息已经停用,现在使用插件抓取网红视频");
    }

    @RequestMapping(value = "/delete/{internetCelebritySampleOrderItemPostId}", method = RequestMethod.DELETE)
    @ApiOperation(value = "删除", produces = "application/json")
    public void delete(@Valid @PathVariable Integer internetCelebritySampleOrderItemPostId) {
        throw new BusinessServiceException("网红样衣订单-新增发帖信息已经停用,现在使用插件抓取网红视频");
    }


}
