package com.nsy.oms.controller.points;


import com.nsy.oms.business.domain.request.points.AffirmRefundItemRequest;
import com.nsy.oms.business.domain.request.points.AffirmWaitConfirmRequest;
import com.nsy.oms.business.domain.request.points.B2bPointsRefundPageRequest;
import com.nsy.oms.business.domain.request.points.CancelRefundItemRequest;
import com.nsy.oms.business.domain.request.points.CancelWaitConfirmRequest;
import com.nsy.oms.business.domain.request.points.SyncB2bPointsRefundRequest;
import com.nsy.oms.business.domain.response.base.PageResponse;
import com.nsy.oms.business.domain.response.points.B2bPointsRefundVO;
import com.nsy.oms.business.service.points.IB2bPointsRefundService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * B2B积分退款 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@RestController
@Api(tags = "B2B积分退款")
@RequestMapping("b2b-points-refund")
public class B2bPointsRefundController {

    @Resource
    private IB2bPointsRefundService b2bPointsRefundService;

    @ApiOperation("列表")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public PageResponse<B2bPointsRefundVO> page(@RequestBody B2bPointsRefundPageRequest request) {
        return b2bPointsRefundService.page(request);
    }

    @ApiOperation("商城同步退款数据")
    @RequestMapping(value = "/sync", method = RequestMethod.POST)
    public void syncB2bPointsRefund(@Valid @RequestBody SyncB2bPointsRefundRequest request) {
        b2bPointsRefundService.syncB2bPointsRefund(request.getSyncB2bPointsRefunds());
    }

    @ApiOperation("待业务确认-确认")
    @RequestMapping(value = "/affirm-wait-confirm", method = RequestMethod.POST)
    public void affirmWaitConfirm(@Valid @RequestBody AffirmWaitConfirmRequest request) {
        b2bPointsRefundService.affirmWaitConfirm(request.getRefundIds());
    }

    @ApiOperation("待业务确认-取消")
    @RequestMapping(value = "/cancel-wait-confirm", method = RequestMethod.POST)
    public void cancelWaitConfirm(@Valid @RequestBody CancelWaitConfirmRequest request) {
        b2bPointsRefundService.cancelWaitConfirm(request.getRefundIds());
    }

    @ApiOperation("待财务处理-确认")
    @RequestMapping(value = "/affirm-refund-item", method = RequestMethod.POST)
    public void affirmRefundItem(@Valid @RequestBody AffirmRefundItemRequest request) {
        b2bPointsRefundService.affirmRefundItem(request);
    }

    @ApiOperation("待财务处理-取消")
    @RequestMapping(value = "/cancel-refund-item", method = RequestMethod.POST)
    public void cancelRefundItem(@Valid @RequestBody CancelRefundItemRequest request) {
        b2bPointsRefundService.cancelRefundItem(request);
    }



}
