package com.nsy.oms.controller.sa;

import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.oms.business.domain.request.sa.SaAdAccountRequest;
import com.nsy.oms.business.domain.request.sa.SaMediaAccountRequest;
import com.nsy.oms.business.domain.response.sa.SaAdAccountResponse;
import com.nsy.oms.business.service.sa.SaAdAccountService;
import com.nsy.oms.repository.entity.sa.SaAdAccountEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/18 9:07
 */
@RestController
@Api(tags = "社媒账号-广告账号列表")
@RequestMapping("/sa-ad-account")
public class SaAdAccountController {
    @Autowired
    private SaAdAccountService saAdAccountService;

    @PostMapping("/list")
    @ApiOperation("获取列表")
    public PageResponse<SaAdAccountResponse> list(@RequestBody SaAdAccountRequest request) {
        return saAdAccountService.getList(request);
    }

    @PostMapping("/associate-account_subject")
    @ApiModelProperty("/关联主体")
    public void associateAccountSubject(@RequestBody SaMediaAccountRequest request) {
        saAdAccountService.associateAccountSubject(request);
    }

    @PostMapping("/change-allocate-user")
    @ApiModelProperty("/变更人员")
    public void changeAllocateUser(@RequestBody SaMediaAccountRequest request) {
        saAdAccountService.changeAllocateUser(request);
    }

    @PostMapping("/update")
    @ApiModelProperty("更新/修改")
    public void update(@RequestBody SaAdAccountRequest request) {
        saAdAccountService.update(request);
    }
    @PostMapping("/close-apply")
    @ApiModelProperty("/关店申请")
    public void closeApply(@RequestBody SaMediaAccountRequest request) {
        saAdAccountService.closeApply(request);
    }

    /**
     * 查看详情
     * @return
     */
    @ApiOperation("查看接口")
    @GetMapping("/info/{id}")
    public SaAdAccountResponse info(@PathVariable Integer id) {
        return saAdAccountService.info(id);
    }

    @GetMapping("/select-list")
    @ApiOperation("获取下拉列表")
    public List<SaAdAccountEntity> selectList() {
        return saAdAccountService.selectList();
    }
}
