package com.nsy.oms.controller.external;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.api.core.apicore.util.NsyCollUtil;
import com.nsy.api.omspublish.dto.request.product.ListAmazonSpecsDTO;
import com.nsy.api.omspublish.feign.PublishProductSpecFeignClient;
import com.nsy.oms.business.domain.request.external.CrmOrderPageListRequest;
import com.nsy.oms.business.domain.request.order.OrderPackageTrackingRequest;
import com.nsy.oms.business.domain.response.external.CrmOrderInoResponse;
import com.nsy.oms.business.domain.response.external.CrmOrderPageListResponse;
import com.nsy.oms.business.domain.response.order.OrderTrackingResponse;
import com.nsy.oms.business.manage.amazon.AmazonApiService;
import com.nsy.oms.business.manage.tms.TmsApiService;
import com.nsy.oms.business.manage.tms.request.OrderTrackingChannelRequest;
import com.nsy.oms.business.manage.tms.response.OrderTrackingChannelResponse;
import com.nsy.oms.business.service.order.SaleOrderExtendService;
import com.nsy.oms.business.service.order.SaleOrderService;
import com.nsy.oms.business.service.platform.PlatformOrderService;
import com.nsy.oms.enums.order.OrderTypeEnum;
import com.nsy.oms.enums.platform.PlatformOrderStatusEnum;
import com.nsy.oms.enums.platform.PlatformShippingTypeEnum;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 订单外部接口控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("external")
public class OrderExternalController {
    private static final Logger LOGGER = LoggerFactory.getLogger(OrderExternalController.class);
    private static final String RETURN_STATUS_NAME = "退款";

    @Autowired
    private SaleOrderService saleOrderService;
    @Autowired
    private PlatformOrderService platformOrderService;
    @Autowired
    private PublishProductSpecFeignClient publishProductSpecFeignClient;
    @Autowired
    private SaleOrderExtendService saleOrderExtendService;
    @Autowired
    private TmsApiService tmsApiService;
    @Autowired
    private AmazonApiService amazonApiService;

    @ApiOperation(value = "查询平台电商CRM-订单列表", notes = "查询平台电商CRM-订单列表", produces = "application/json")
    @PostMapping("/crm/order-page-list")
    public PageResponse<CrmOrderPageListResponse> crmOrderPageList(@RequestBody @Valid CrmOrderPageListRequest request) {
        Page<CrmOrderPageListResponse> pageList;
        if (OrderTypeEnum.FBM.getCode().equals(request.getOrderType())) {
            pageList = saleOrderService.crmOrderPageList(request);
        } else {
            pageList = platformOrderService.crmOrderPageList(request);
        }
        if (NsyCollUtil.isNotEmpty(pageList.getRecords())) {
            pageList.getRecords().forEach(order -> order.setOrderStatusName(PlatformOrderStatusEnum.getDescByCode(order.getOrderStatus())));
            // 封装商品标题及图片地址
            buildItemSpecInfo(pageList.getRecords());
            // 获取物流单号跟物流渠道
            Map<String, OrderTrackingResponse> map = getLogisticsInfo(pageList.getRecords().stream().map(CrmOrderPageListResponse::getOrderNo).collect(Collectors.toList()), request.getOrderType());
            pageList.getRecords().forEach(order -> {
                OrderTrackingResponse orderTrackingResponse = map.get(order.getOrderNo());
                if (Objects.isNull(orderTrackingResponse)) {
                    return;
                }
                order.setLogisticsChannel(orderTrackingResponse.getLogisticsChannel());
                order.setLogisticsNo(orderTrackingResponse.getLogisticsNo());
            });
        }
        return PageResponse.of(pageList.getRecords(), pageList.getTotal());
    }


    @ApiOperation(value = "查询平台电商CRM-通过订单号查询订单信息", notes = "查询平台电商CRM-通过订单号查询订单信息", produces = "application/json")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "orderNo", value = "原始订单号", paramType = "String", required = true),
        @ApiImplicitParam(name = "storeIdList", value = "店铺ID", paramType = "List", required = true)
    })
    @GetMapping("/crm/get-order-info")
    public CrmOrderInoResponse getCrmOrderInfo(@RequestParam("orderNo") String orderNo, @RequestParam("storeIdList") List<Integer> storeIdList) {
        CrmOrderInoResponse orderInoResponse = saleOrderService.getCrmOrderInfo(orderNo, storeIdList);
        if (Objects.isNull(orderInoResponse)) {
            orderInoResponse = platformOrderService.getCrmOrderInfo(orderNo, storeIdList);
        }
        if (Objects.isNull(orderInoResponse)) {
            throw new BusinessServiceException(String.format("查询不到订单，订单号：%s", orderNo));
        }
        if (orderInoResponse.getShippingType().contains(PlatformShippingTypeEnum.AFN.name())) {
            orderInoResponse.setOrderType(OrderTypeEnum.FBA.getCode());
        } else {
            orderInoResponse.setOrderType(OrderTypeEnum.FBM.getCode());
        }
        orderInoResponse.setOrderStatusName(PlatformOrderStatusEnum.getDescByCode(orderInoResponse.getOrderStatus()));
        // 封装商品标题及图片地址
        buildSimpleItemSpecInfo(orderInoResponse);

        // 获取物流单号跟物流渠道
        Map<String, OrderTrackingResponse> map = getLogisticsInfo(Collections.singletonList(orderInoResponse.getOrderNo()), orderInoResponse.getOrderType());
        OrderTrackingResponse orderTrackingResponse = map.get(orderInoResponse.getOrderNo());
        if (Objects.nonNull(orderTrackingResponse)) {
            orderInoResponse.setLogisticsChannel(orderTrackingResponse.getLogisticsChannel());
            orderInoResponse.setLogisticsNo(orderTrackingResponse.getLogisticsNo());
        }
        // 查询是否退款
        Boolean isReturn = amazonApiService.isReturnOrder(orderInoResponse.getStoreId(), orderInoResponse.getOrderNo());
        if (isReturn != null && isReturn) {
            orderInoResponse.setOrderStatusName(RETURN_STATUS_NAME);
        }
        return orderInoResponse;
    }


    public void buildItemSpecInfo(List<CrmOrderPageListResponse> orderList) {
        ListAmazonSpecsDTO listAmazonSpecsDTO = new ListAmazonSpecsDTO();
        List<ListAmazonSpecsDTO.ListItem> itemList = new ArrayList<>();

        orderList.forEach(order -> {
            if (NsyCollUtil.isEmpty(order.getItemList())) {
                return;
            }
            ListAmazonSpecsDTO.ListItem listItem = new ListAmazonSpecsDTO.ListItem();
            listItem.setStoreId(order.getStoreId());
            listItem.setMarketCode(order.getMarketCode());

            List<ListAmazonSpecsDTO.AmazonSpec> amazonSpecList = order.getItemList().stream().map(orderItem -> {
                ListAmazonSpecsDTO.AmazonSpec amazonSpec = new ListAmazonSpecsDTO.AmazonSpec();
                amazonSpec.setSellerSku(orderItem.getSellerSku());
                return amazonSpec;
            }).collect(Collectors.toList());

            listItem.setAmazonSpecList(amazonSpecList);
            itemList.add(listItem);
        });

        if (NsyCollUtil.isEmpty(itemList)) {
            return;
        }
        listAmazonSpecsDTO.setList(itemList);
        ListAmazonSpecsDTO amazonSpecsDTO = publishProductSpecFeignClient.listAmazonSpecs(listAmazonSpecsDTO);
        if (Objects.isNull(amazonSpecsDTO) || NsyCollUtil.isEmpty(amazonSpecsDTO.getList())) {
            return;
        }
        orderList.forEach(order -> {
            order.getItemList().forEach(item -> {
                List<ListAmazonSpecsDTO.AmazonSpec> allAmazonSpecList = amazonSpecsDTO.getList().stream().filter(amazonSpec ->
                        amazonSpec.getStoreId().equals(order.getStoreId())
                        && amazonSpec.getMarketCode().equals(order.getMarketCode())
                    )
                    .flatMap(amazonSpec -> amazonSpec.getAmazonSpecList().stream())
                    .collect(Collectors.toList());
                allAmazonSpecList.stream().filter(spec -> spec.getSellerSku().equals(item.getSellerSku())).findFirst().ifPresent(spec -> {
                    item.setImageUrl(spec.getImageUrl());
                    item.setTitle(spec.getTitle());
                });
            });
        });
    }


    public void buildSimpleItemSpecInfo(CrmOrderInoResponse orderInoResponse) {
        if (NsyCollUtil.isEmpty(orderInoResponse.getItemList())) {
            return;
        }
        ListAmazonSpecsDTO listAmazonSpecsDTO = new ListAmazonSpecsDTO();
        ListAmazonSpecsDTO.ListItem listItem = new ListAmazonSpecsDTO.ListItem();
        listItem.setStoreId(orderInoResponse.getStoreId());
        listItem.setMarketCode(orderInoResponse.getMarketCode());

        List<ListAmazonSpecsDTO.AmazonSpec> amazonSpecList = orderInoResponse.getItemList().stream().map(orderItem -> {
            ListAmazonSpecsDTO.AmazonSpec amazonSpec = new ListAmazonSpecsDTO.AmazonSpec();
            amazonSpec.setSellerSku(orderItem.getSellerSku());
            return amazonSpec;
        }).collect(Collectors.toList());

        listItem.setAmazonSpecList(amazonSpecList);
        listAmazonSpecsDTO.setList(Collections.singletonList(listItem));
        ListAmazonSpecsDTO amazonSpecsDTO = publishProductSpecFeignClient.listAmazonSpecs(listAmazonSpecsDTO);
        if (Objects.isNull(amazonSpecsDTO) || NsyCollUtil.isEmpty(amazonSpecsDTO.getList())) {
            return;
        }
        orderInoResponse.getItemList().forEach(item -> {
            List<ListAmazonSpecsDTO.AmazonSpec> allAmazonSpecList = amazonSpecsDTO.getList().stream().filter(amazonSpec ->
                amazonSpec.getStoreId().equals(orderInoResponse.getStoreId())
                && amazonSpec.getMarketCode().equals(orderInoResponse.getMarketCode())
                )
                .flatMap(amazonSpec -> amazonSpec.getAmazonSpecList().stream())
                .collect(Collectors.toList());
            allAmazonSpecList.stream().filter(spec -> spec.getSellerSku().equals(item.getSellerSku())).findFirst().ifPresent(spec -> {
                item.setImageUrl(spec.getImageUrl());
                item.setTitle(spec.getTitle());
            });
        });
    }

    public Map<String, OrderTrackingResponse> getLogisticsInfo(List<String> orderNoList, Integer orderType) {
        Map<String, OrderTrackingResponse> map = new HashMap<>(20);
        if (OrderTypeEnum.FBM.getCode().equals(orderType)) {
            OrderTrackingChannelRequest request = new OrderTrackingChannelRequest();
            request.setOrderNoList(orderNoList);
            List<OrderTrackingChannelResponse> logisticsInfoList = tmsApiService.getLogisticsInfo(request);
            if (NsyCollUtil.isEmpty(logisticsInfoList)) {
                return map;
            }
            logisticsInfoList.forEach(logisticsInfo -> {
                OrderTrackingResponse orderTrackingResponse = new OrderTrackingResponse();
                orderTrackingResponse.setLogisticsChannel(logisticsInfo.getLogisticsChannel());
                orderTrackingResponse.setLogisticsNo(logisticsInfo.getLogisticsNo());
                map.put(logisticsInfo.getOrderNo(), orderTrackingResponse);
            });
        } else {
            // fba 查询物流信息
            orderNoList.forEach(orderNo -> {
                try {
                    OrderPackageTrackingRequest request = new OrderPackageTrackingRequest();
                    request.setPlatformOrderNo(orderNo);
                    OrderTrackingResponse orderTrackingResponse = saleOrderExtendService.getOrderPackageTrackingInfo(request);
                    if (Objects.isNull(orderTrackingResponse)) {
                        return;
                    }
                    map.put(orderNo, orderTrackingResponse);
                } catch (Exception e) {
                    LOGGER.error("crm order fetch logistics error : {}", e.getMessage());
                }
            });
        }
        return map;
    }
}
