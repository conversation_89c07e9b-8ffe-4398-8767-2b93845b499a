package com.nsy.oms.controller.sa;

import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.oms.business.domain.request.base.IdListRequest;
import com.nsy.oms.business.domain.request.sa.AccountApplyReassignmentRequest;
import com.nsy.oms.business.domain.request.sa.SaAccountApplyPageRequest;
import com.nsy.oms.business.domain.request.sa.SaAccountApplyRequest;
import com.nsy.oms.business.domain.request.sa.SaAccountFormRequest;
import com.nsy.oms.business.domain.request.sa.SaAccountModifyRequest;
import com.nsy.oms.business.domain.response.sa.SaAccountApplyResponse;
import com.nsy.oms.business.manage.user.response.SysUserInfo;
import com.nsy.oms.business.service.sa.SaAccountApplyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/29 14:38
 */
@RestController
@Api(tags = "社媒账号-账号通用申请")
@RequestMapping("/sa-account-apply")
public class SaAccountApplyController {

    @Autowired
    private SaAccountApplyService saAccountApplyService;

    @PostMapping("/list")
    @ApiOperation("获取列表")
    public PageResponse<SaAccountApplyResponse> list(@RequestBody SaAccountApplyPageRequest request) {
        return saAccountApplyService.getList(request);
    }

    @PostMapping("/add")
    @ApiOperation("创建申请单")
    public void add(@RequestBody SaAccountApplyRequest request) {
        saAccountApplyService.add(request);
    }

    @PostMapping("/add-account")
    @ApiOperation("填充账号信息")
    public void addAccount(@RequestBody SaAccountFormRequest request) {
        saAccountApplyService.addAccount(request);
    }

    @PostMapping("/update")
    @ApiOperation("修改申请单")
    public void update(@RequestBody SaAccountApplyRequest request) {
        saAccountApplyService.update(request);
    }

    @PostMapping("/modifyApplyStatus")
    @ApiOperation("修改申请单的状态")
    public void modifyApplyStatus(@RequestBody SaAccountModifyRequest request) {
        saAccountApplyService.modifyApplyStatus(request);
    }

    @PostMapping("/allocation-entity")
    @ApiOperation("分配主体")
    public void allocationEntity(@RequestBody SaAccountApplyPageRequest request) {
        saAccountApplyService.allocationEntity(request);
    }

    @PostMapping("/allocation-phone")
    @ApiOperation("分配电话")
    public void allocationPhone(@RequestBody SaAccountApplyPageRequest request) {
        saAccountApplyService.allocationPhone(request);
    }

    @PostMapping("/allocation-email")
    @ApiOperation("分配邮箱")
    public void allocationEmail(@RequestBody SaAccountApplyPageRequest request) {
        saAccountApplyService.allocationEmail(request);
    }

    /**
     * 查看详情
     */
    @ApiOperation("查看接口")
    @GetMapping("/get-apply-info/{id}")
    public SaAccountApplyResponse getApplyInfo(@PathVariable Integer id) {
        return saAccountApplyService.getApplyInfo(id);
    }

    @ApiModelProperty("获取工作流候选人列表")
    @PostMapping("/reassignment-get-users")
    public List<SysUserInfo> getUsers(@RequestBody IdListRequest request) {
        return saAccountApplyService.getUsers(request);
    }

    @ApiModelProperty("任务指派")
    @PostMapping("/reassignment")
    public void reassignment(@RequestBody AccountApplyReassignmentRequest request) {
        saAccountApplyService.reassignment(request);
    }


}
