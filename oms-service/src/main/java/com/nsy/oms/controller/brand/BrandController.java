package com.nsy.oms.controller.brand;

import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.oms.business.domain.request.bd.brand.BdBrandCommonOperateRequest;
import com.nsy.oms.business.domain.request.bd.brand.BdBrandCommonPageRequest;
import com.nsy.oms.business.domain.request.bd.brand.BdBrandQueryRequest;
import com.nsy.oms.business.domain.request.bd.brand.BdBrandSaveRequest;
import com.nsy.oms.business.domain.request.bd.brand.BdBrandStoreListRequest;
import com.nsy.oms.business.domain.request.bd.brand.BdBrandStoreSaveRequest;
import com.nsy.oms.business.domain.request.bd.brand.BdBrandStoreSkcFirstOrderAddRequest;
import com.nsy.oms.business.domain.request.bd.brand.BdBrandStoreSkcQueryRequest;
import com.nsy.oms.business.domain.request.bd.brand.BdBrandStoreSkcSaveRequest;
import com.nsy.oms.business.domain.request.bd.brand.CheckStoreBrandSkcRequest;
import com.nsy.oms.business.domain.response.bd.brand.BdBrand;
import com.nsy.oms.business.domain.response.bd.brand.BdBrandInfoListResponse;
import com.nsy.oms.business.domain.response.bd.brand.BdBrandLocationSkc;
import com.nsy.oms.business.domain.response.bd.brand.BdBrandLocationSkcInfo;
import com.nsy.oms.business.domain.response.bd.brand.BdBrandStore;
import com.nsy.oms.business.domain.response.bd.brand.BdBrandStoreResponse;
import com.nsy.oms.business.domain.response.bd.brand.BdBrandStoreSkc;
import com.nsy.oms.business.domain.response.bd.brand.BrandStoreSkcResponse;
import com.nsy.oms.business.domain.response.bd.brand.CheckStoreSpaceRequest;
import com.nsy.oms.business.domain.response.bd.brand.StoreSkcInfoRequest;
import com.nsy.oms.business.manage.user.request.shared.SelectModel;
import com.nsy.oms.business.service.bd.BdBrandLocationSkcService;
import com.nsy.oms.business.service.bd.BdBrandService;
import com.nsy.oms.business.service.bd.BdBrandStoreService;
import com.nsy.oms.business.service.bd.BdBrandStoreSkcService;
import com.nsy.oms.controller.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.inject.Inject;
import javax.validation.Valid;
import java.util.List;

@RestController
@Api(tags = "品牌管理")
public class BrandController extends BaseController {
    @Inject
    BdBrandService bdBrandService;
    @Inject
    BdBrandStoreService bdBrandStoreService;
    @Inject
    BdBrandStoreSkcService bdBrandStoreSkcService;
    @Inject
    BdBrandLocationSkcService bdBrandLocationSkcService;

    @GetMapping("/bd-brand/select")
    @ApiOperation("品牌-下拉框")
    public List<SelectModel> getBrandSelect() {
        return bdBrandService.getBrandSelect();
    }

    @GetMapping("/bd-brand/name/select")
    @ApiOperation("品牌名称-下拉框（label和value都是品牌名）")
    public List<SelectModel> getBrandNameSelect() {
        return bdBrandService.getBrandNameSelect();
    }

    @GetMapping("/bd-brand/select-ignore-tenant")
    @ApiOperation("品牌-下拉框")
    public List<SelectModel> getBrandSelectIgnoreTenant() {
        return bdBrandService.getBrandSelectIgnoreTenant();
    }

    @GetMapping("/bd-brand/select-all")
    @ApiOperation("所有品牌-下拉框")
    public List<SelectModel> getAllBrandSelect() {
        return bdBrandService.getAllBrandSelect();
    }

    @GetMapping("/bd-brand/select-all-ignore-tenant")
    @ApiOperation("所有品牌-下拉框-忽略区域")
    public List<SelectModel> getAllBrandSelectIgnoreTenant() {
        return bdBrandService.getAllBrandSelectIgnoreTenant();
    }

    @GetMapping("/bd-brand/select-by-location")
    @ApiOperation("品牌-下拉框")
    public List<SelectModel> getBrandSelectByLocation(@RequestParam String location) {
        return bdBrandService.getBrandSelectByLocation(location);
    }

    @GetMapping("/bd-brand/space-select")
    @ApiOperation("品牌-仓库下拉框")
    public List<SelectModel> getSpaceSelect() {
        return bdBrandService.getSpaceSelect();
    }

    @GetMapping("/bd-brand/common-space-select")
    @ApiOperation("普通仓库下拉框")
    public List<SelectModel> commonSpaceSelect() {
        return bdBrandService.commonSpaceSelect();
    }

    @PostMapping("/bd-brand-store-skc/check-store-brand-skc")
    @ApiOperation("确认店铺品牌skc")
    public CheckStoreBrandSkcRequest checkStoreBrandSkc(@RequestBody CheckStoreBrandSkcRequest request) {
        return bdBrandStoreService.checkStoreBrandSkc(request);
    }

    @PostMapping("/bd-brand/page")
    @ApiOperation("品牌-分页查询")
    public PageResponse<BdBrand> brandPage(@Valid @RequestBody BdBrandCommonPageRequest request) {
        return bdBrandService.page(request);
    }

    @PostMapping("/bd-brand/page-ignore-tenant")
    @ApiOperation("品牌-分页查询")
    public PageResponse<BdBrand> brandPageIgnoreTenant(@Valid @RequestBody BdBrandCommonPageRequest request) {
        return bdBrandService.pageIgnoreTenant(request);
    }

    @PostMapping("/bd-brand/save-or-update")
    @ApiOperation("品牌-新增or编辑")
    public Integer saveOrUpdateBrand(@Valid @RequestBody BdBrandSaveRequest request) {
        return bdBrandService.saveOrUpdate(request);
    }

    @PostMapping("/bd-brand-store/page")
    @ApiOperation("品牌店铺-分页查询")
    public PageResponse<BdBrandStore> brandStorePage(@Valid @RequestBody BdBrandCommonPageRequest request) {
        return bdBrandStoreService.page(request);
    }

    @PostMapping("/bd-brand-store/list-brand-store")
    @ApiOperation("品牌店铺-分页查询")
    public BdBrandStoreResponse listBrandStore(@Valid @RequestBody BdBrandStoreListRequest request) {
        return new BdBrandStoreResponse(bdBrandStoreService.listByStoreIds(request));
    }

    @PostMapping("/bd-brand-store/save-or-update")
    @ApiOperation("品牌店铺-新增or编辑")
    public Integer saveOrUpdateBrandStore(@Valid @RequestBody BdBrandStoreSaveRequest request) {
        return bdBrandStoreService.saveOrUpdate(request);
    }

    @PostMapping("/bd-brand-store/delete")
    @ApiOperation("品牌店铺-删除")
    public void deleteBrandStore(@Valid @RequestBody BdBrandCommonOperateRequest request) {
        bdBrandStoreService.delete(request);
    }

    @PostMapping("/bd-brand-store/open-purchase-brand")
    @ApiOperation("品牌店铺-采购挂牌-开启or关闭")
    public void openPurchaseBrand(@Valid @RequestBody BdBrandCommonOperateRequest request) {
        bdBrandStoreService.openPurchaseBrand(request);
    }

    @PostMapping("/bd-brand-store-skc/page")
    @ApiOperation("品牌店铺商品-分页查询")
    public PageResponse<BdBrandStoreSkc> brandStoreSkcPage(@Valid @RequestBody BdBrandCommonPageRequest request) {
        return bdBrandStoreSkcService.page(request);
    }

    @PostMapping("/bd-brand-store-skc/add")
    @ApiOperation("品牌店铺商品-新增")
    public Integer addBdBrandStoreSkc(@Valid @RequestBody BdBrandStoreSkcSaveRequest request) {
        return bdBrandStoreSkcService.addBdBrandStoreSkc(request);
    }

    @PostMapping("/bd-brand-store-skc/open-fba-brand")
    @ApiOperation("品牌店铺商品-FBA挂牌-开启or关闭")
    public void openFbaBrand(@Valid @RequestBody BdBrandCommonOperateRequest request) {
        bdBrandStoreSkcService.openFbaBrand(request);
    }

    @PostMapping("/bd-brand-store-skc/open-purchase-brand")
    @ApiOperation("品牌店铺商品-FBA挂牌-开启or关闭")
    public void brandStoreSkcOpenPurchaseBrand(@Valid @RequestBody BdBrandCommonOperateRequest request) {
        bdBrandStoreSkcService.openPurchaseBrand(request);
    }

    @PostMapping("/bd-brand-store-skc/first-order-add")
    @ApiOperation("SCM调用-亚马逊品牌店铺下新品首申请单自动增加商品到品牌店铺商品列表")
    public void firstOrderAddSkc(@Valid @RequestBody BdBrandStoreSkcFirstOrderAddRequest request) {
        bdBrandStoreSkcService.firstOrderAddSkc(request);
    }

    @PostMapping("/bd-brand-store-skc/query-brand")
    @ApiOperation("SCM调用-根据查询条件查询品牌信息列表")
    public BdBrandInfoListResponse queryBrandInfo(@Valid @RequestBody BdBrandQueryRequest request) {
        return bdBrandStoreSkcService.queryBrandInfo(request);
    }

    @PostMapping("/bd-brand-store-skc/query-brand-ignore-tenant")
    @ApiOperation("SCM调用-根据查询条件查询品牌信息列表")
    public BdBrandInfoListResponse queryBrandInfoIgnoreTenant(@Valid @RequestBody BdBrandQueryRequest request) {
        return bdBrandStoreSkcService.queryBrandInfoIgnoreTenant(request);
    }

    @PostMapping("/bd-brand-store-skc/query-by-store-and-skc")
    @ApiOperation("SCM调用-根据店铺和skc查询品牌店铺商品信息")
    public BrandStoreSkcResponse queryByStoreAndSkc(@Valid @RequestBody BdBrandStoreSkcQueryRequest request) {
        return bdBrandStoreSkcService.queryByStoreAndSkc(request);
    }

    @PostMapping("/check-store-space")
    @ApiOperation(".net调用-判断店铺的品牌是否绑定了品牌仓")
    public CheckStoreSpaceRequest checkStoreSpace(@Valid @RequestBody CheckStoreSpaceRequest request) {
        return bdBrandStoreService.checkStoreSpace(request);
    }

    @PostMapping("/store-skc-brand-space-info/list")
    @ApiOperation("SCM调用-若店铺SKC存在且品牌店铺开启采购挂牌，则返回品牌信息")
    public StoreSkcInfoRequest storeSkcBrandSpaceInfo(@Valid @RequestBody StoreSkcInfoRequest request) {
        return bdBrandStoreService.storeSkcBrandSpaceInfo(request);
    }

    @PostMapping("/store-skc-brand-space-info/list-ignore-tenant")
    @ApiOperation("SCM调用-若店铺SKC存在且品牌店铺开启采购挂牌，则返回品牌信息")
    public StoreSkcInfoRequest storeSkcBrandSpaceInfoIgnoreTenant(@Valid @RequestBody StoreSkcInfoRequest request) {
        return bdBrandStoreService.storeSkcBrandSpaceInfoIgnoreTenant(request);
    }


    @PostMapping("/bd-brand-location/page")
    @ApiOperation("品牌分公司-分页查询")
    public PageResponse<BdBrandLocationSkc> brandLocationPage(@Valid @RequestBody BdBrandCommonPageRequest request) {
        return bdBrandLocationSkcService.brandLocationPage(request);
    }

    @PostMapping("/bd-brand-location/open-purchase-brand")
    @ApiOperation("品牌分公司-挂牌-开启or关闭")
    public void openLocationPurchaseBrand(@Valid @RequestBody BdBrandCommonOperateRequest request) {
        bdBrandLocationSkcService.openLocationPurchaseBrand(request);
    }

    @PostMapping("/bd-brand-location/list-by-skc-location")
    @ApiOperation("品牌分公司-location-skc查询: 有挂牌返回品牌跟仓库")
    public BdBrandLocationSkcInfo listBySkcLocation(@Valid @RequestBody BdBrandLocationSkcInfo request) {
        return bdBrandLocationSkcService.listBySkcLocation(request);
    }

    @GetMapping("/bd-brand/get-brand-by-brand-company-name/{brandCompanyName}")
    @ApiOperation("品牌-通过公司名称获取品牌")
    public BdBrand getBrandByBrandCompanyName(@PathVariable("brandCompanyName") String brandCompanyName) {
        return bdBrandService.getBrandByBrandCompanyName(brandCompanyName);
    }

    @GetMapping("/bd-brand/get-brand-by-brand-name/{brandName}")
    @ApiOperation("品牌-通过公司名称获取品牌")
    public BdBrand getBrandByBrandName(@PathVariable("brandName") String brandName) {
        return bdBrandService.getBrandByBrandName(brandName);
    }

    @GetMapping("/bd-brand/get-brand")
    @ApiOperation("获取运营品牌")
    public List<SelectModel> getBrand() {
        return bdBrandService.getBrand();
    }

    @GetMapping("/bd-brand/get-brand-space-by-department/{department}")
    @ApiOperation("品牌-通过公司名称获取品牌")
    public List<BdBrand> getBrandSpaceByDepartment(@PathVariable("department") String department) {
        return bdBrandService.getBrandSpaceByDepartment(department);
    }

}
