package com.nsy.oms.controller.bd;

import com.nsy.api.core.apicore.base.BaseListResponse;
import com.nsy.oms.business.domain.request.bd.expert.BdStoreExpertPageRequest;
import com.nsy.oms.business.domain.request.bd.expert.BdStoreExpertSaveRequest;
import com.nsy.oms.business.domain.response.bd.expert.BdStoreExpertPageResponse;
import com.nsy.oms.business.service.bd.BdStoreExpertService;
import com.nsy.oms.controller.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;


@RestController
@Api(tags = "店铺达人维护")
public class BdStoreExpertController extends BaseController {
    @Autowired
    private BdStoreExpertService bdStoreExpertService;

    @PostMapping("/bd-store-expert/save")
    @ApiOperation("保存达人信息")
    public void save(@RequestBody BdStoreExpertSaveRequest request) {
        bdStoreExpertService.save(request);
    }

    @PostMapping("/bd-store-expert/page")
    @ApiOperation("查询达人信息")
    public BaseListResponse<BdStoreExpertPageResponse> page(@RequestBody BdStoreExpertPageRequest request) {
        return bdStoreExpertService.page(request);
    }

    @PostMapping("/bd-store-expert/edit-remark")
    @ApiOperation("编辑达人信息备注")
    public void editRemark(@RequestBody BdStoreExpertSaveRequest request) {
        bdStoreExpertService.editRemark(request);
    }
}
