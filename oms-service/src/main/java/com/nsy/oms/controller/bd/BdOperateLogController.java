package com.nsy.oms.controller.bd;

import com.nsy.oms.business.domain.request.bd.BdOperateLogPageRequest;
import com.nsy.oms.business.domain.response.base.PageResponse;
import com.nsy.oms.business.domain.response.bd.BdOperateLogPageResponse;
import com.nsy.oms.business.service.bd.BdOperateLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Api(tags = "日志")
public class BdOperateLogController {
    @Autowired
    private BdOperateLogService bdOperateLogService;

    @ApiOperation(value = "分页列表", notes = "分页列表", produces = "application/json")
    @PostMapping("/bd-operate-log/pageList")
    public PageResponse<BdOperateLogPageResponse> pageList(@Validated @RequestBody BdOperateLogPageRequest request) {
        return bdOperateLogService.pageList(request);
    }
}
