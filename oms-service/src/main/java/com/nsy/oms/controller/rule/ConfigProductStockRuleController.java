package com.nsy.oms.controller.rule;


import com.nsy.oms.business.domain.ao.rule.ConfigProductStockRule;
import com.nsy.oms.business.domain.request.rule.ConfigProductRuleCreateRequest;
import com.nsy.oms.business.domain.request.rule.ConfigProductStockRulePageRequest;
import com.nsy.oms.business.domain.request.rule.GetStockRuleRequest;
import com.nsy.oms.business.domain.response.base.PageResponse;
import com.nsy.oms.business.service.rule.IConfigProductStockRuleService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 同步库存配置 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@RestController
@RequestMapping("/config-product-stock-rule")
public class ConfigProductStockRuleController {

    @Resource
    private IConfigProductStockRuleService configProductStockRuleService;

    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @ApiOperation(value = "列表", notes = "传入ConfigProductStockRulePageRequest")
    public PageResponse<ConfigProductStockRule> page(@Valid @RequestBody ConfigProductStockRulePageRequest request) {
        return configProductStockRuleService.page(request);
    }

    @RequestMapping(value = "/save-or-update", method = RequestMethod.POST)
    @ApiOperation(value = "新增或修改", notes = "传入ConfigProductRuleCreateRequest")
    public void saveOrUpdate(@Valid @RequestBody ConfigProductRuleCreateRequest request) {
        configProductStockRuleService.saveOrUpdate(request);
    }

    @RequestMapping(value = "/detail/{configProductStockRuleId}", method = RequestMethod.GET)
    @ApiOperation(value = "详情", notes = "传入configProductStockRuleId")
    public ConfigProductStockRule detail(@PathVariable("configProductStockRuleId") Integer configProductStockRuleId) {
        return configProductStockRuleService.detail(configProductStockRuleId);
    }

    @RequestMapping(value = "/delete/{configProductStockRuleId}", method = RequestMethod.DELETE)
    @ApiOperation(value = "删除", notes = "传入configProductStockRuleId")
    public void deleteById(@PathVariable("configProductStockRuleId") Integer configProductStockRuleId) {
        configProductStockRuleService.deleteById(configProductStockRuleId);
    }

    @RequestMapping(value = "/get-stock-rule", method = RequestMethod.POST)
    @ApiOperation(value = "查询规则", notes = "传入platformCode")
    public ConfigProductStockRule getStockRule(@Valid @RequestBody GetStockRuleRequest request) {
        return configProductStockRuleService.getStockRule(request);
    }

}
