package com.nsy.oms.controller.celebrity;


import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.oms.business.domain.request.celebrity.InternetCelebrityBindOrderRequest;
import com.nsy.oms.business.domain.request.celebrity.InternetCelebrityOrderRemarkRequest;
import com.nsy.oms.business.domain.request.celebrity.InternetCelebrityOrderRepairRequest;
import com.nsy.oms.business.domain.request.celebrity.InternetCelebritySampleOrderRequest;
import com.nsy.oms.business.domain.request.celebrity.SyncChangeInternetCelebrityOwnerRequest;
import com.nsy.oms.business.domain.response.celebrity.InternetCelebritySampleOrder;
import com.nsy.oms.business.domain.response.celebrity.SyncInternetCelebritySampleOrder;
import com.nsy.oms.business.manage.thirdparty.response.Tracking;
import com.nsy.oms.business.service.celebrity.IInternetCelebritySampleOrderService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 网红样衣订单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
@RestController
@RequestMapping("/internet-celebrity-sample-order")
public class InternetCelebritySampleOrderController {

    @Resource
    private IInternetCelebritySampleOrderService internetCelebritySampleOrderService;


    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @ApiOperation("网红订单")
    public PageResponse<InternetCelebritySampleOrder> getInternetCelebritySampleOrderPage(@Valid @RequestBody InternetCelebritySampleOrderRequest request) {
        return internetCelebritySampleOrderService.getInternetCelebritySampleOrderPage(request);
    }


    @RequestMapping(value = "/rate-page", method = RequestMethod.POST)
    @ApiOperation("网红履约率报表")
    public PageResponse<InternetCelebritySampleOrder> getCelebrityPerformanceRatePage(@Valid @RequestBody InternetCelebritySampleOrderRequest request) {
        return internetCelebritySampleOrderService.getCelebrityPerformanceRatePage(request);
    }


    @RequestMapping(value = "/tracking/{internetCelebritySampleOrderItemId}", method = RequestMethod.GET)
    @ApiOperation(value = "物流轨迹", produces = "application/json")
    public List<Tracking> tracking(@Valid @PathVariable Integer internetCelebritySampleOrderItemId) {
        return internetCelebritySampleOrderService.tracking(internetCelebritySampleOrderItemId);
    }

    @RequestMapping(value = "/erp-order/{platformOrderNo}/{storeRelationId}", method = RequestMethod.GET)
    @ApiOperation(value = "erp订单信息", produces = "application/json")
    public SyncInternetCelebritySampleOrder erpOrder(@PathVariable String platformOrderNo, @PathVariable Integer storeRelationId) {
        return internetCelebritySampleOrderService.erpOrder(platformOrderNo, storeRelationId);
    }


    @RequestMapping(value = "/repair", method = RequestMethod.POST)
    @ApiOperation("补数据接口")
    public void repair(@Valid @RequestBody InternetCelebrityOrderRepairRequest request) {
        internetCelebritySampleOrderService.repair(request);
    }


    @RequestMapping(value = "/bind", method = RequestMethod.POST)
    @ApiOperation("绑定网红")
    public void bind(@Valid @RequestBody InternetCelebrityBindOrderRequest request) {
        internetCelebritySampleOrderService.bind(request);
    }


    @RequestMapping(value = "/sync-unknown-order", method = RequestMethod.GET)
    @ApiOperation("同步未知平台数据")
    public void syncUnknownOrder() {
        internetCelebritySampleOrderService.syncUnknownOrder();
    }


    @RequestMapping(value = "/remark", method = RequestMethod.POST)
    @ApiOperation("添加备注")
    public void remark(@Valid @RequestBody InternetCelebrityOrderRemarkRequest request) {
        internetCelebritySampleOrderService.remark(request);
    }

    @RequestMapping(value = "/sync-change-internet-celebrity-owner", method = RequestMethod.POST)
    @ApiOperation("同步网红变更负责人")
    public void syncChangeInternetCelebrityOwner(@Valid @RequestBody SyncChangeInternetCelebrityOwnerRequest request) {
        internetCelebritySampleOrderService.syncChangeInternetCelebrityOwner(request);
    }

}
