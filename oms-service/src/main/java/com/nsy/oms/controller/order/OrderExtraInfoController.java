package com.nsy.oms.controller.order;

import com.nsy.oms.business.domain.request.order.OrderPackageTrackingRequest;
import com.nsy.oms.business.domain.response.order.OrderTrackingResponse;
import com.nsy.oms.business.service.order.SaleOrderExtendService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 用于订单的额外信息获取
 *
 * <AUTHOR>
 * 2025-02-25
 */
@RestController
@Api(tags = "订单系统-订单额外信息")
@RequestMapping("/order-extra-info")
@Slf4j
public class OrderExtraInfoController {
    @Autowired
    private SaleOrderExtendService saleOrderExtendService;

    @PostMapping("/order-package-tracking")
    @ApiOperation("获取物流信息")
    public OrderTrackingResponse getOrderPackageTrackingInfo(@RequestBody OrderPackageTrackingRequest request) {
        return saleOrderExtendService.getOrderPackageTrackingInfo(request);
    }

}
