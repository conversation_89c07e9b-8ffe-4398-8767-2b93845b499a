package com.nsy.oms.controller.amazon;


import com.nsy.api.core.apicore.base.BaseListResponse;
import com.nsy.oms.business.domain.request.amazon.AmazonOrderProfitMonitoringPageRequest;
import com.nsy.oms.business.domain.request.amazon.AmazonOrderProfitMonitoringSelectRequest;
import com.nsy.oms.business.domain.response.amazon.AmazonOrderProfitMonitoringSkuDto;
import com.nsy.oms.business.domain.response.amazon.AmazonOrderProfitMonitoringSpuResponse;
import com.nsy.oms.business.manage.user.request.shared.SelectModel;
import com.nsy.oms.business.service.amazon.AmazonOrderProfitMonitoringSearchService;
import com.nsy.oms.controller.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@Api(tags = "亚马逊订单利润率监控")
@RestController
public class AmazonOrderProfitMonitoringController extends BaseController {
    @Autowired
    private AmazonOrderProfitMonitoringSearchService amazonOrderProfitMonitoringSearchService;

    @ApiOperation("分页列表")
    @PostMapping("/amazon-order-profit-monitoring/page")
    public BaseListResponse<AmazonOrderProfitMonitoringSpuResponse> page(@RequestBody AmazonOrderProfitMonitoringPageRequest request) {
        return amazonOrderProfitMonitoringSearchService.page(request);
    }

    @ApiOperation("通过spu获取店铺下拉框")
    @PostMapping("/amazon-order-profit-monitoring/store-select")
    public List<SelectModel> getStoreList(@RequestBody AmazonOrderProfitMonitoringSelectRequest request) {
        return amazonOrderProfitMonitoringSearchService.getStoreList(request);
    }

    @ApiOperation("通过spu、storeId获取父asin下拉框")
    @PostMapping("/amazon-order-profit-monitoring/parent-asin-select")
    public List<SelectModel> getParentAsinList(@RequestBody AmazonOrderProfitMonitoringSelectRequest request) {
        return amazonOrderProfitMonitoringSearchService.getParentAsinList(request);
    }

    @ApiOperation("通过spu、storeId、parentAsin获取skc下拉框")
    @PostMapping("/amazon-order-profit-monitoring/skc-select")
    public List<SelectModel> getSkcList(@RequestBody AmazonOrderProfitMonitoringSelectRequest request) {
        return amazonOrderProfitMonitoringSearchService.getSkcList(request);
    }

    @ApiOperation("通过skc、storeId、parentAsin获取sku下拉框")
    @PostMapping("/amazon-order-profit-monitoring/sku-select")
    public List<SelectModel> getSkuList(@RequestBody AmazonOrderProfitMonitoringSelectRequest request) {
        return amazonOrderProfitMonitoringSearchService.getSkuList(request);
    }

    @ApiOperation("获取sku信息")
    @PostMapping("/amazon-order-profit-monitoring/sku-detail")
    public AmazonOrderProfitMonitoringSkuDto getSkuDetail(@RequestBody AmazonOrderProfitMonitoringSelectRequest request) {
        return amazonOrderProfitMonitoringSearchService.getSkuDetail(request);
    }

    @ApiOperation("测算sku")
    @PostMapping("/amazon-order-profit-monitoring/calculate-sku")
    public AmazonOrderProfitMonitoringSkuDto calculateSku(@RequestBody @Valid AmazonOrderProfitMonitoringSkuDto request) {
        return amazonOrderProfitMonitoringSearchService.calculateSku(request);
    }

    @ApiOperation("新款标签下拉框（亚马逊开发季节")
    @GetMapping("/amazon-order-profit-monitoring/amazon-develop-season-select")
    public List<SelectModel> getAmazonDevelopSeasonSelect() {
        return amazonOrderProfitMonitoringSearchService.getAmazonDevelopSeasonSelect();
    }
}
