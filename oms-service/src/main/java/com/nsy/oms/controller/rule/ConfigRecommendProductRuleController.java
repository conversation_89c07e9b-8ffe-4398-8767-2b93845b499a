package com.nsy.oms.controller.rule;

import com.nsy.oms.business.domain.request.rule.ConfigRecommendProductRuleCheckBrandSpiltRequest;
import com.nsy.oms.business.domain.request.rule.ConfigRecommendProductRuleCopyRequest;
import com.nsy.oms.business.domain.request.rule.ConfigRecommendProductRuleCreateRequest;
import com.nsy.oms.business.domain.request.rule.ConfigRecommendProductRulePageRequest;
import com.nsy.oms.business.domain.request.rule.ConfigRecommendProductRuleRequest;
import com.nsy.oms.business.domain.response.base.PageResponse;
import com.nsy.oms.business.domain.response.rule.ConfigRecommendProductRuleResponse;
import com.nsy.oms.business.domain.response.rule.GetConfigRecommendProductRule;
import com.nsy.oms.business.manage.user.request.shared.SelectModel;
import com.nsy.oms.business.service.rule.ConfigRecommendProductRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;


@RestController
@Api(tags = "规则")
public class ConfigRecommendProductRuleController {
    @Autowired
    private ConfigRecommendProductRuleService configRecommendProductRuleService;

    @PostMapping("/config-recommend-product/list")
    @ApiOperation("获取列表数据")
    public GetConfigRecommendProductRule getList(@RequestBody ConfigRecommendProductRuleRequest request) {
        GetConfigRecommendProductRule getConfigRecommendProductRule = new GetConfigRecommendProductRule();
        getConfigRecommendProductRule.setList(configRecommendProductRuleService.getByPlatform(request));
        return getConfigRecommendProductRule;
    }

    @PostMapping("/config-recommend-product/pageList")
    @ApiOperation("获取列表数据")
    public PageResponse<ConfigRecommendProductRuleResponse> getList(@RequestBody ConfigRecommendProductRulePageRequest request) {
        return configRecommendProductRuleService.pageList(request);
    }

    @RequestMapping(value = "/config-recommend-product/{ids}", method = RequestMethod.DELETE)
    @ApiOperation(value = "删除", produces = "application/json")
    public void delete(@Valid @PathVariable List<Integer> ids) {
        configRecommendProductRuleService.deleteBatch(ids);
    }

    @RequestMapping(value = "/config-recommend-product/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "详情", notes = "详情", produces = "application/json")
    public ConfigRecommendProductRuleResponse detail(@PathVariable Integer id) {
        return configRecommendProductRuleService.detail(id);
    }

    @RequestMapping(value = "/config-recommend-product/save", method = RequestMethod.POST)
    @ApiOperation("保存")
    public void save(@RequestBody ConfigRecommendProductRuleCreateRequest request) {
        configRecommendProductRuleService.save(request);
    }

    @RequestMapping(value = "/config-recommend-product/copy", method = RequestMethod.POST)
    @ApiOperation("复制配置")
    public void copy(@RequestBody ConfigRecommendProductRuleCopyRequest request) {
        configRecommendProductRuleService.copy(request);
    }


    @RequestMapping(value = "/config-recommend-product/update", method = RequestMethod.POST)
    @ApiOperation("更新")
    public void update(@RequestBody ConfigRecommendProductRuleCreateRequest request) {
        configRecommendProductRuleService.update(request);
    }


    @RequestMapping(value = "/config-recommend-product/check-brand-spilt", method = RequestMethod.POST)
    @ApiOperation("确认品牌拆标")
    public boolean checkBrandSpilt(@RequestBody @Valid ConfigRecommendProductRuleCheckBrandSpiltRequest request) {
        return configRecommendProductRuleService.checkBrandSpilt(request);
    }

    @RequestMapping(value = "/config-recommend-product/amazon-store-with-brand", method = RequestMethod.GET)
    @ApiOperation("亚马逊店铺-品牌拆")
    public List<SelectModel> getAmazonStoreWithBrand() {
        return configRecommendProductRuleService.getAmazonStoreWithBrand();
    }


    @RequestMapping(value = "/config-recommend-product-old/update", method = RequestMethod.POST)
    @ApiOperation("旧数据处理")
    public void oldUpdate() {
        configRecommendProductRuleService.updateOldDate();
    }


}
