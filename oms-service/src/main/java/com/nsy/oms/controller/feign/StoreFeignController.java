package com.nsy.oms.controller.feign;

import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.api.oms.dto.request.auth.SaAuthRequest;
import com.nsy.api.oms.dto.request.store.SaStoreSkuConvertRequest;
import com.nsy.api.oms.dto.request.store.StoreListRequest;
import com.nsy.api.oms.dto.request.store.StorePageListRequest;
import com.nsy.api.oms.dto.request.store.StorePlatformRequest;
import com.nsy.api.oms.dto.request.store.base.IdListRequest;
import com.nsy.api.oms.dto.response.auth.StoreAuthInfoResponse;
import com.nsy.api.oms.dto.response.auth.WebsiteResponse;
import com.nsy.api.oms.dto.response.base.BdDataSourceRequest;
import com.nsy.api.oms.dto.response.base.SelectModelResponse;
import com.nsy.api.oms.dto.response.store.AlibabaInternationAuthResponse;
import com.nsy.api.oms.dto.response.store.AmazonAuthResponse;
import com.nsy.api.oms.dto.response.store.AmazonStoreListResponse;
import com.nsy.api.oms.dto.response.store.AmazonStoreResponse;
import com.nsy.api.oms.dto.response.store.SaStoreSkuConvertResponse;
import com.nsy.api.oms.dto.response.store.ShopifyStoreListResponse;
import com.nsy.api.oms.dto.response.store.StoreConfigResponse;
import com.nsy.api.oms.dto.response.store.StoreDetailResponse;
import com.nsy.api.oms.dto.response.store.StoreFinancePaymentResponse;
import com.nsy.api.oms.dto.response.store.StoreListResponse;
import com.nsy.api.oms.dto.response.store.StorePageListResponse;
import com.nsy.api.oms.dto.response.store.StoreStaffingResponse;
import com.nsy.api.oms.feign.StoreFeignClient;
import com.nsy.business.base.utils.SelectModel;
import com.nsy.oms.annotation.IgnoreTenant;
import com.nsy.oms.business.service.feign.StoreFeignService;
import com.nsy.oms.business.service.sa.SaStoreExtendService;
import com.nsy.oms.controller.base.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@RestController
public class StoreFeignController extends BaseController implements StoreFeignClient {

    @Autowired
    private StoreFeignService storeFeignService;
    @Autowired
    private SaStoreExtendService saStoreExtendService;

    @Override
    public PageResponse<StorePageListResponse> getStorePageList(StorePageListRequest request) {
        return storeFeignService.getStorePageList(request);
    }

    @Override
    public StoreDetailResponse getStoreByWebsiteId(Integer websiteId) {
        return storeFeignService.getStoreByWebsiteId(websiteId);
    }

    @Override
    public List<StoreDetailResponse> getStoreByWebsiteIds(StoreListRequest request) {
        return storeFeignService.getStoreByWebsiteIds(request);
    }

    @Override
    public List<ShopifyStoreListResponse> getShopifyStoreList() {
        return storeFeignService.getShopifyStoreList();
    }

    @Override
    public boolean storeIsUsedByOMS(Integer storeId, String platformName) {
        return storeFeignService.storeIsUsedByOMS(storeId, platformName);
    }

    @Override
    public List<StoreListResponse> getStoreList(StoreListRequest request) {
        return storeFeignService.getStoreList(request);
    }

    @Override
    @IgnoreTenant
    public List<StoreListResponse> getStoreListIgnoreTenant(StoreListRequest request) {
        return storeFeignService.getStoreList(request);
    }

    @Override
    public List<AmazonStoreListResponse> getAllAmazonStore() {
        return storeFeignService.getAllAmazonStore();
    }

    @Override
    public List<StoreListResponse> getStoreListByUserAccount(String userAccount) {
        return storeFeignService.getStoreListByUserAccount(userAccount);
    }

    @Override
    public StoreDetailResponse getStoreById(Integer id) {
        return storeFeignService.getStoreById(id);
    }

    @Override
    public List<StoreDetailResponse> getStoreListByIds(IdListRequest request) {
        return storeFeignService.getStoreListByIds(request.getIdList());
    }

    @Override
    public List<StoreListResponse> getStoreListByPlatformNames(StorePlatformRequest request) {
        return storeFeignService.getStoreListByPlatformNames(request.getPlatformNameList());
    }

    @Override
    public AlibabaInternationAuthResponse getAliInterAuthByStoreId(Integer storeId) {
        return storeFeignService.getAliInterAuthByStoreId(storeId);
    }

    @Override
    public PageResponse<SelectModelResponse> getStoreSelect(BdDataSourceRequest request) {
        return storeFeignService.getStoreSelect(request);
    }

    @Override
    public AmazonAuthResponse getAmazonAuthByStoreId(Integer storeId) {
        return storeFeignService.getAmazonAuthByStoreId(storeId);
    }

    @Override
    public StoreFinancePaymentResponse getAssociatedStoreInfo(Integer cardId, Integer type) {
        return storeFeignService.getAssociatedStoreInfo(cardId, type);
    }

    @Override
    public StoreAuthInfoResponse getStoreAuthInfo(SaAuthRequest request) {
        return storeFeignService.getStoreAuthInfo(request);
    }

    @Override
    public StoreConfigResponse getStoreConfigInfo(Integer storeId) {
        return storeFeignService.getStoreConfigInfo(storeId);
    }

    @Override
    public void modifyPaymentCreditCardStatus(Integer cardId, Integer status) {
        storeFeignService.modifyPaymentCreditCardStatus(cardId, status);
    }

    @Override
    public void modifyReceivingCreditCardStatus(Integer accountId, Integer status) {
        storeFeignService.modifyReceivingCreditCardStatus(accountId, status);
    }

    @Override
    protected Long getUserId() {
        return super.getUserId();
    }

    @Override
    public List<AmazonAuthResponse> getAmazonAuthList() {
        return storeFeignService.getAmazonAuthList();
    }

    @Override
    public WebsiteResponse getWebsiteByWebsiteKey(SaAuthRequest request) {
        return storeFeignService.getWebsiteByWebsiteKey(request);
    }

    @Override
    public List<Integer> getWebsiteIdListByDepartment(String department, String location) {
        return storeFeignService.getWebsiteIdListByDepartment(department, location);
    }

    @Override
    public List<Integer> getStoreIdList(String platformName) {
        return storeFeignService.getStoreIdList(platformName);
    }

    @Override
    public SaStoreSkuConvertResponse convertLocalSku(SaStoreSkuConvertRequest request) {
        return storeFeignService.convertLocalSku(request);
    }

    @Override
    public Integer getPlatForm(Integer storeId) {
        return storeFeignService.getPlatformIdByStoreId(storeId);
    }

    @Override
    public List<Integer> getStoreIdsByDepartment(String department, String location) {
        return storeFeignService.getStoreIdsByDepartment(department, location);
    }

    @Override
    public List<StoreStaffingResponse> getStoreStaffing(Integer storeId) {
        return storeFeignService.getStoreStaffing(storeId);
    }

    @Override
    public AmazonStoreResponse getAmazonStoreByStoreId(Integer storeId) {
        return storeFeignService.getAmazonStoreByStoreId(storeId);
    }

    @Override
    public List<AmazonStoreResponse> getAmazonStoreByStoreIdList(List<Integer> storeIdList) {
        return storeFeignService.getAmazonStoreByStoreIdList(storeIdList);
    }

    @Override
    public List<SelectModel> amazonStoreList() {
        return saStoreExtendService.amazonStoreList().stream().map(oldSelectModel -> {
            SelectModel selectModel = new SelectModel();
            selectModel.setLabel(oldSelectModel.getLabel());
            selectModel.setValue(oldSelectModel.getValue());
            selectModel.setAttribute(oldSelectModel.getAttribute());
            selectModel.setId(oldSelectModel.getId());
            selectModel.setText(oldSelectModel.getText());
            return selectModel;
        }).collect(Collectors.toList());
    }
}
