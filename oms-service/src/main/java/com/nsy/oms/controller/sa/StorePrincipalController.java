package com.nsy.oms.controller.sa;

import com.nsy.oms.business.domain.request.sa.SaStoreBaseRequest;
import com.nsy.oms.business.domain.response.sa.StorePrincipalDetailResponse;
import com.nsy.oms.business.service.sa.SaStorePrincipalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/8 18:12
 */
@RestController
@Api(tags = "账号系统-店铺负责人")
@RequestMapping("/store-principal")
@Slf4j
public class StorePrincipalController {
    @Autowired
    private SaStorePrincipalService saStorePrincipalService;
    @PostMapping("/list")
    @ApiOperation("获取店铺列表")
    public List<StorePrincipalDetailResponse> list(@RequestBody SaStoreBaseRequest saStoreBaseRequest) {
        return saStorePrincipalService.getBatchList(saStoreBaseRequest);
    }
}
