package com.nsy.oms.controller.order;

import com.nsy.oms.business.domain.response.platform.PlatformOrderResponse;
import com.nsy.oms.business.service.order.SaleOrderService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


/**
 * @description:
 * @author: linCheng
 * @create: 2025-03-12 09:42
 **/
@RestController
@RequestMapping("/order")
public class OrderController {

    @Autowired
    private SaleOrderService saleOrderService;


    @RequestMapping(value = "/get-order-info/{storeId}/{platformOrderNo}", method = RequestMethod.GET)
    @ApiOperation(value = "列表", notes = "传入GetPlatformOrderByPageRequest")
    public PlatformOrderResponse getOrderInfo(@PathVariable("storeId") Integer storeId, @PathVariable("platformOrderNo") String platformOrderNo) {
        return saleOrderService.getOrderInfo(storeId, platformOrderNo);
    }


}
