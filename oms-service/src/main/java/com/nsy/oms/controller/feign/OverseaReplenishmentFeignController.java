package com.nsy.oms.controller.feign;

import com.nsy.api.oms.dto.request.store.OverseaReplenishmentGetGroupStoreRequest;
import com.nsy.api.oms.dto.response.store.StoreInfoResponse;
import com.nsy.api.oms.feign.OverseaReplenishmentFeignClient;
import com.nsy.oms.business.service.bd.BdOverseeReplenishmentStoreGroupService;
import com.nsy.oms.controller.base.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/11/915:01
 */

@RestController
public class OverseaReplenishmentFeignController extends BaseController implements OverseaReplenishmentFeignClient {

    @Autowired
    private BdOverseeReplenishmentStoreGroupService storeGroupService;

    @Override
    public List<StoreInfoResponse> getGroupStoreList(OverseaReplenishmentGetGroupStoreRequest request) {
        return storeGroupService.getGroupStoreList(request);
    }
}
