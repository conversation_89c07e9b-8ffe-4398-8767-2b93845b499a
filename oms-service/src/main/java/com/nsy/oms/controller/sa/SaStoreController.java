package com.nsy.oms.controller.sa;

import com.nsy.api.core.apicore.base.BaseListResponse;
import com.nsy.api.oms.dto.response.linxing.LxMappingResponse;
import com.nsy.oms.business.domain.request.rule.ConfigRecommendProductRuleRequest;
import com.nsy.oms.business.domain.request.sa.B2BStoreSearchRequest;
import com.nsy.oms.business.domain.request.sa.DistributorsStorePageRequest;
import com.nsy.oms.business.domain.request.sa.DistributorsStoreRequest;
import com.nsy.oms.business.domain.request.sa.EtlStoreSearchRequest;
import com.nsy.oms.business.domain.request.sa.QuerySaStoreByPlatformListRequest;
import com.nsy.oms.business.domain.request.sa.SaMediaAccountRequest;
import com.nsy.oms.business.domain.request.sa.SaStorePageRequest;
import com.nsy.oms.business.domain.request.sa.SaStoreRequest;
import com.nsy.oms.business.domain.request.sa.SaStoreSearchRequest;
import com.nsy.oms.business.domain.request.sa.SalesmanStoreRequest;
import com.nsy.oms.business.domain.request.sa.StoreFinanceAccountRequest;
import com.nsy.oms.business.domain.request.sa.StoreFinancePaymentAccountRequest;
import com.nsy.oms.business.domain.request.sa.StoreInfoRequest;
import com.nsy.oms.business.domain.request.sa.StorePermissionSelectRequest;
import com.nsy.oms.business.domain.request.sa.StoreSelectRequest;
import com.nsy.oms.business.domain.response.base.PageResponse;
import com.nsy.oms.business.domain.response.sa.DistributorsStoreResponse;
import com.nsy.oms.business.domain.response.sa.SaStoreDetailByErpResponse;
import com.nsy.oms.business.domain.response.sa.SaStoreDetailResponse;
import com.nsy.oms.business.domain.response.sa.SaStoreListResponse;
import com.nsy.oms.business.domain.response.sa.SaStoreResponse;
import com.nsy.oms.business.domain.response.sa.SaStoreWebsiteResponse;
import com.nsy.oms.business.domain.response.sa.SalesmanStoreResponse;
import com.nsy.oms.business.domain.valid.UpdateGroup;
import com.nsy.oms.business.manage.user.request.shared.SelectModel;
import com.nsy.oms.business.service.sa.SaStoreExtendService;
import com.nsy.oms.business.service.sa.SaStoreFinanceAccountService;
import com.nsy.oms.business.service.sa.SaStoreFinancePaymentAccountService;
import com.nsy.oms.business.service.sa.SaStoreService;
import com.nsy.oms.enums.StatusEnum;
import com.nsy.oms.repository.entity.sa.SaStoreStaffingEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotEmpty;
import javax.validation.groups.Default;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2022/11/29 18:02
 */
@RestController
@Api(tags = "账号系统-店铺")
@RequestMapping("/sa-store")
@Slf4j
public class SaStoreController {
    @Autowired
    private SaStoreService saStoreService;
    @Autowired
    private SaStoreExtendService saStoreExtendService;

    @Autowired
    private SaStoreFinancePaymentAccountService saStoreFinancePaymentAccountService;
    @Autowired
    private SaStoreFinanceAccountService saStoreFinanceAccountService;

    @PostMapping("/list")
    @ApiOperation("获取店铺列表")
    public PageResponse<SaStoreResponse> list(@RequestBody SaStorePageRequest saStorePageRequest) {
        return saStoreService.list(saStorePageRequest);
    }

    @PostMapping("/select-list-by-store-status")
    @ApiOperation("根据店铺状态获取店铺下拉框")
    public BaseListResponse<SelectModel> storePermissionSelectList(@RequestBody StorePermissionSelectRequest request) {
        return saStoreService.storePermissionSelectList(request);
    }

    @PostMapping("/getSaStoreList")
    @ApiOperation("获取店铺列表")
    public List<SaStoreResponse> getSaStoreList(@RequestBody SaStorePageRequest saStorePageRequest) {
        return saStoreService.getSaStoreList(saStorePageRequest);
    }

    @GetMapping("/info/{id}")
    @ApiOperation("获取店铺详情")
    public SaStoreDetailResponse getInfo(@PathVariable Integer id) {
        return saStoreService.getInfo(id);
    }

    @PostMapping("/info")
    @ApiModelProperty("获取完整的店铺信息")
    public SaStoreDetailByErpResponse getInfo(@RequestBody StoreInfoRequest storeInfoRequest) {
        return saStoreService.info(storeInfoRequest);
    }

    @PostMapping("/website-info")
    @ApiModelProperty("获取站点信息")
    public BaseListResponse<SaStoreWebsiteResponse> getWebsiteInfo(@RequestBody @Validated @NotEmpty(message = "站点id不能为空") List<Integer> websiteIds) {
        return saStoreService.getWebsiteInfo(websiteIds);
    }

    @PostMapping("/save-distribution-store")
    @ApiOperation("新增分销店铺信息")
    public void saveDistributionStoreData(@RequestBody @Validated DistributorsStoreRequest saStoreRequest) {
        saStoreService.saveDistributionStore(saStoreRequest);
    }

    @PostMapping("/update-distribution-store")
    @ApiOperation("更新分销店铺信息")
    public void updateDistributionStoreData(@RequestBody @Validated DistributorsStoreRequest saStoreRequest) {
        saStoreService.updateDistributionStore(saStoreRequest);
    }

    @PostMapping("/save")
    @ApiOperation("新增店铺信息")
    public void saveData(@RequestBody @Validated SaStoreRequest saStoreRequest) {
        saStoreService.saveData(saStoreRequest);
    }

    @PostMapping("/update")
    @ApiOperation("更新店铺信息")
    public void updateData(@RequestBody @Validated({UpdateGroup.class, Default.class}) SaStoreRequest saStoreRequest) {
        saStoreService.updateData(saStoreRequest);
    }


    @GetMapping("/checkSku/{sku}")
    @ApiOperation("效验sku是否存在")
    public boolean checkSku(@PathVariable String sku) {
        return saStoreService.checkSku(sku);
    }

    /**
     * 分销商店铺列表
     */
    @PostMapping("/distributors-store-list")
    @ApiOperation("分销商店铺列表")
    public PageResponse<DistributorsStoreResponse> getDistributorsStoreList(@RequestBody DistributorsStorePageRequest distributorsStoreRequest) {
        return saStoreService.getDistributorsStoreList(distributorsStoreRequest);
    }

    @GetMapping("/store-list")
    @ApiOperation("获取所有的店铺数据")
    public List<SaStoreResponse> storeList() {
        return saStoreService.storeList();
    }

    @PostMapping("/store-by-request")
    @ApiOperation("获取店铺数据")
    public List<SaStoreResponse> storeListByRequest(@RequestBody SaStoreSearchRequest request) {
        return saStoreService.storeListByRequest(request);
    }

    @PostMapping("/store-by-platform")
    public SaStoreListResponse getByPlatformName(@RequestBody ConfigRecommendProductRuleRequest request) {
        return saStoreService.getByPlatformName(StatusEnum.NORMAL.getCode(), request.getPlatform());
    }

    @PostMapping("/query-store-by-platform-List")
    public SaStoreListResponse queryStoreByPlatformList(@RequestBody @Validated QuerySaStoreByPlatformListRequest request) {
        return saStoreService.queryStoreByPlatformList(StatusEnum.NORMAL.getCode(), request.getPlatformList());
    }

    @GetMapping("/store-select")
    @ApiOperation("根据当前location获取在售店铺下拉框")
    public List<SelectModel> getOnSaleStoreSelect() {
        return saStoreService.getOnSaleStoreSelect();
    }

    @GetMapping("/get-store-list-by-location")
    @ApiOperation("根据location获取零星的店铺数据")
    public List<LxMappingResponse> getStoreListByLocation(@RequestParam("location") String location, @RequestParam(name = "storeId", required = false) String storeId) {
        return saStoreService.getStoreListByLocation(location, storeId);
    }

    @PostMapping("/salesman-store-select")
    @ApiOperation("获取业务员店铺下拉框，用于PMS新品选款")
    public List<SalesmanStoreResponse> getSalesmanStoreSelect(@RequestBody SalesmanStoreRequest request) {
        return saStoreService.getSalesmanStoreSelect(request);
    }

    @GetMapping("/add-color/require-store-select")
    @ApiOperation("加色需求店铺下拉")
    public List<SaStoreResponse> getRequireStoreSelect() {
        return saStoreService.getRequireStoreSelect();
    }

    @PostMapping("/b2b-store-select")
    @ApiOperation("获取部门或二级部门等于b2b的在营店铺")
    public List<SelectModel> getB2bStoreSelect(@RequestBody B2BStoreSearchRequest request) {
        return saStoreService.getB2bStoreSelect(request);
    }

    @PostMapping("/b2b-website-select")
    @ApiOperation("获取部门或二级部门等于b2b的在营店铺关联站点")
    public List<SelectModel> getB2bWebsiteSelect(@RequestBody B2BStoreSearchRequest request) {
        return saStoreService.getB2bWebsiteSelect(request);
    }

    @PostMapping("/store-select-by-request")
    public List<SelectModel> storeSelectByRequest(@RequestBody StoreSelectRequest request) {
        return saStoreService.storeSelectByRequest(request);
    }

    @PostMapping("/add-payment-card")
    @ApiOperation("付款卡维护")
    public void addPaymentCard(@RequestBody StoreFinancePaymentAccountRequest request) {
        saStoreFinancePaymentAccountService.addPaymentCard(request);
    }

    @PostMapping("/add-finance-account")
    @ApiOperation("收款卡维护")
    public void addFinanceAccount(@RequestBody StoreFinanceAccountRequest request) {
        saStoreFinanceAccountService.addFinanceAccount(request);
    }

    @PostMapping("/etl-store-select")
    @ApiOperation("根据部门或平台ID获取店铺列表")
    public List<SelectModel> getEtlStoreSelect(@RequestBody EtlStoreSearchRequest request) {
        return saStoreService.getEtlStoreSelect(request);
    }

    @PostMapping("/change-allocate-user")
    @ApiModelProperty("/变更人员")
    public void changeAllocateUser(@RequestBody SaMediaAccountRequest request) {
        saStoreService.changeAllocateUser(request);
    }

    @PostMapping("/close-apply")
    @ApiModelProperty("/关店申请")
    public void closeApply(@RequestBody SaMediaAccountRequest request) {
        saStoreService.closeApply(request);
    }

    @ApiModelProperty("/变更前商品通知人员下拉")
    @GetMapping("/store-staffings-list/{storeId}")
    public List<SaStoreStaffingEntity> getStoreStaffingList(@PathVariable Integer storeId) {
        return saStoreService.getStoreStaffingList(storeId);
    }

    @PostMapping("/change-card-info")
    @ApiModelProperty("/付款信用卡变更")
    public void changerCardInfo(@RequestBody SaMediaAccountRequest request) {
        saStoreService.changerCardInfo(request);
    }


    @GetMapping("/amazon-store-list")
    @ApiOperation("获取亚马逊在营的店铺数据")
    public List<SelectModel> amazonStoreList() {
        return saStoreExtendService.amazonStoreList();
    }
}
