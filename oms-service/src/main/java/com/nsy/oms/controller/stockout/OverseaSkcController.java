package com.nsy.oms.controller.stockout;

import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.oms.business.domain.request.stockout.OverseaSkcPageRequest;
import com.nsy.oms.business.domain.response.stockout.OverseaOnTheWayStockResponse;
import com.nsy.oms.business.domain.response.stockout.OverseaSkcPageResponse;
import com.nsy.oms.business.domain.response.stockout.OverseaSkuStoreSpaceStockoutResponse;
import com.nsy.oms.business.service.stockout.OverseaSkcService;
import com.nsy.oms.controller.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/5 15:31
 */
@RestController
@Api(tags = "海外仓库存管理SKC控制器")
@RequestMapping("/oversea-stockout")
public class OverseaSkcController extends BaseController {

    @Autowired
    private OverseaSkcService overseaSkcService;

    @ApiOperation(value = "分页", notes = "分页", produces = "application/json")
    @PostMapping("/page")
    public PageResponse<OverseaSkcPageResponse> page(@RequestBody OverseaSkcPageRequest request) {
        return overseaSkcService.page(request);
    }

    @ApiOperation(value = "sku海外仓在途明细", notes = "sku海外仓在途明细", produces = "application/json")
    @GetMapping("/on-the-way-stock-list")
    public List<OverseaOnTheWayStockResponse> onTheWayStockList(@RequestParam String department, @RequestParam Integer replenishmentGroupId, @RequestParam String skc) {
        return overseaSkcService.onTheWayStockList(department, replenishmentGroupId, Collections.singletonList(skc));
    }

    @ApiOperation(value = "海外仓出库量明细", notes = "海外仓出库量明细", produces = "application/json")
    @GetMapping("/sku-store-space-stockout-list")
    public List<OverseaSkuStoreSpaceStockoutResponse> skuStoreSpaceStockout(@RequestParam String department, @RequestParam Integer replenishmentGroupId, @RequestParam String skc) {
        return overseaSkcService.skuStoreSpaceStockout(department, replenishmentGroupId, skc);
    }
}
