package com.nsy.oms.controller.platform;

import com.nsy.oms.business.domain.request.platform.GetPlatformOrderByPageRequest;
import com.nsy.oms.business.domain.response.base.PageResponse;
import com.nsy.oms.business.domain.response.platform.PlatformOrderResponse;
import com.nsy.oms.business.service.platform.PlatformOrderService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @description:
 * @author: linCheng
 * @create: 2025-03-12 09:42
 **/
@RestController
@RequestMapping("/order")
public class PlatformOrderController {


    @Resource
    private PlatformOrderService platformOrderService;

    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @ApiOperation(value = "列表", notes = "传入GetPlatformOrderByPageRequest")
    public PageResponse<PlatformOrderResponse> page(@Valid @RequestBody GetPlatformOrderByPageRequest request) {
        return platformOrderService.getPlatformOrderByPage(request);
    }


}
