buildscript {
    ext {
        springBootVersion = '2.4.2'
    }
    repositories {
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
        maven { url "https://repo.spring.io/release" }
        maven { url "https://repo.spring.io/milestone" }
        maven { url "https://repo.spring.io/snapshot" }
        gradlePluginPortal()
    }

    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
    }
}
allprojects {
    group 'com.nsy'
    version '0.0.1'
}
//配置所有项目公共内容
subprojects {
    apply plugin: 'java'
    apply plugin: 'eclipse'
    apply plugin: 'org.springframework.boot'
    apply plugin: 'io.spring.dependency-management'
    sourceCompatibility = 17
    
    // 增加编译时的内存限制
//    tasks.withType(JavaCompile) {
//        options.fork = true
//        options.forkOptions.jvmArgs << '-Xmx2048m'
//        options.forkOptions.jvmArgs << '-XX:MaxPermSize=512m'
//    }

    configurations.all {
        resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
        resolutionStrategy.cacheDynamicVersionsFor 0, 'seconds'
    }

    repositories {
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
        maven { url "https://maven.aliyun.com/repository/spring" }
        maven { url "https://repo.spring.io/milestone/" }
        maven {
            credentials {
                username RELEASE_NEXUS_USER
                password RELEASE_NEXUS_PASS
            }
            url REPOSITORY_URL_RELEASE
        }
        maven {
            credentials {
                username getRepositorySnapshotUsername()
                password getRepositorySnapshotPassword()
            }
            url getRepositorySnapshotUrl()
        }
    }

    dependencies {
        // PDFBox 相关库
        implementation 'org.apache.pdfbox:pdfbox:2.0.20'
        implementation 'org.apache.pdfbox:fontbox:2.0.20'
        // ZXing 相关库
        implementation 'com.google.zxing:core:3.4.1'
        implementation 'com.google.zxing:javase:3.4.1'
        implementation 'javax.inject:javax.inject:1'
        implementation 'javax.validation:validation-api:2.0.1.Final'
        implementation 'org.apache.commons:commons-lang3:3.6'
        implementation 'com.fasterxml.jackson.module:jackson-module-jaxb-annotations:2.8.10'
        implementation 'joda-time:joda-time:2.9.9'
        implementation 'com.aliyun.oss:aliyun-sdk-oss:3.0.0'
        testImplementation 'org.springframework.boot:spring-boot-starter-test'
        testImplementation 'org.springframework.integration:spring-integration-test'
        testImplementation 'com.h2database:h2:1.4.196'
        implementation('io.springfox:springfox-swagger2:2.8.0')
        implementation('io.springfox:springfox-swagger-ui:2.8.0')
        implementation('io.micrometer:micrometer-registry-prometheus')
        implementation("com.nsy.base:nsy-base-feign") {
            changing = true
        }
        implementation('com.nsy.base:nsy-base-core') {
            changing = true
            exclude group: 'mysql', module: 'mysql-connector-java'
        }
        implementation('mysql:mysql-connector-java:8.0.33')
        implementation ('org.projectlombok:lombok:1.18.24')
        annotationProcessor group: 'org.projectlombok', name: 'lombok', version: '1.18.24'
        implementation('com.nsy.business.base:nsy-business-base:0.6.3')
        implementation('com.nsy.permission:permission-starter:1.2.5') {
            exclude group: 'com.nsy.api', module: 'core'
        }
    }

    test {
        useJUnitPlatform()
    }

    ext {
        nsyBomVersion = '0.1.17'
        snapshotGroupEnd = getSnapshotGroupEnd()
    }

    dependencyManagement {
        imports {
            mavenBom "com.nsy:nsy-bom:${nsyBomVersion}"

        }
    }
}
apply from: file("${rootDir}/gradle/quality.gradle")

def getSnapshotGroupEnd() {
    if ('production'.equals(getProjectEnv()) || 'dev'.equals(getProjectEnv()) || 'test'.equals(getProjectEnv())) {
        return ""
    }
    return "." + getProjectEnv()
}

def getProjectEnv() {
    return project.hasProperty('env') ? env : PROJECT_ENV
}

def getRepositorySnapshotUrl() {
    return project.hasProperty('nexues_url') ? nexues_url : REPOSITORY_URL_SNAPSHOT_DEV
}

def getRepositorySnapshotUsername() {
    return project.hasProperty('nexues_user') ? nexues_user : SNAPSHOT_NEXUS_USER
}

def getRepositorySnapshotPassword() {
    return project.hasProperty('nexues_pass') ? nexues_pass : SNAPSHOT_NEXUS_PASS
}