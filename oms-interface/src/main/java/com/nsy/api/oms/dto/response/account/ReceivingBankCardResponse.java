package com.nsy.api.oms.dto.response.account;

import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/2/28 14:23
 */
public class ReceivingBankCardResponse {
    @ApiModelProperty("收款信用卡id")
    private Integer id;
    @ApiModelProperty("卡号")
    private String cardNum;
    @ApiModelProperty("店铺id")
    private Integer storeId;
    @ApiModelProperty("开户行")
    private String bankName;
    @ApiModelProperty("开户行id")
    private Integer bankId;
    @ApiModelProperty("持卡人姓名")
    private String holderName;
    @ApiModelProperty("绑定收款平台")
    private String bindPlatformName;
    @ApiModelProperty("绑定收款平台id")
    private String bindPlatformId;
    @ApiModelProperty("账号id")
    private Integer accountId;
    @ApiModelProperty("账号")
    private String accountName;
    @ApiModelProperty("持卡人手机号码")
    private String phone;
    @ApiModelProperty("创建人")
    private String createBy;
    @ApiModelProperty("创建时间")
    private Date createDate;
    @ApiModelProperty("状态")
    private Integer enable;
    @ApiModelProperty("是否激活")
    private String enableName;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCardNum() {
        return cardNum;
    }

    public void setCardNum(String cardNum) {
        this.cardNum = cardNum;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public Integer getBankId() {
        return bankId;
    }

    public void setBankId(Integer bankId) {
        this.bankId = bankId;
    }

    public String getHolderName() {
        return holderName;
    }

    public void setHolderName(String holderName) {
        this.holderName = holderName;
    }

    public String getBindPlatformName() {
        return bindPlatformName;
    }

    public void setBindPlatformName(String bindPlatformName) {
        this.bindPlatformName = bindPlatformName;
    }

    public String getBindPlatformId() {
        return bindPlatformId;
    }

    public void setBindPlatformId(String bindPlatformId) {
        this.bindPlatformId = bindPlatformId;
    }

    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getEnable() {
        return enable;
    }

    public void setEnable(Integer enable) {
        this.enable = enable;
    }

    public String getEnableName() {
        return enableName;
    }

    public void setEnableName(String enableName) {
        this.enableName = enableName;
    }
}
