package com.nsy.api.oms.dto.response.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel("样品订单")
public class SampleOrderResponse implements Serializable {
    private static final long serialVersionUID = 3230720540770923666L;

    /**
     * 店铺id
     */
    @ApiModelProperty("store_id")
    private Integer storeId;

    /**
     * 店铺名称
     */
    @ApiModelProperty("store_name")
    private String storeName;

    /**
     * 平台订单号
     */
    @ApiModelProperty("platform_order_no")
    private String platformOrderNo;

    /**
     * 平台原始订单号
     */
    @ApiModelProperty("platform_original_order_no")
    private String platformOriginalOrderNo;

    /**
     * 订单发货时间
     */
    @ApiModelProperty("order_deliver_date")
    private Date orderDeliverDate;

    /**
     * 订单创建时间
     */
    @ApiModelProperty("订单创建时间")
    private Date orderCreateDate;

    /**
     * 买家昵称
     */
    @ApiModelProperty("buyer_nick")
    private String buyerNick;

    /**
     * 地区
     */
    @ApiModelProperty("location")
    private String location;

    @ApiModelProperty("buyer_uid")
    private String buyerUid;

    /**
     * 样品订单详情
     */
    @ApiModelProperty("orderItems")
    private List<SampleOrderItemResponse> orderItems;

    public String getBuyerUid() {
        return buyerUid;
    }

    public void setBuyerUid(String buyerUid) {
        this.buyerUid = buyerUid;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getPlatformOrderNo() {
        return platformOrderNo;
    }

    public void setPlatformOrderNo(String platformOrderNo) {
        this.platformOrderNo = platformOrderNo;
    }

    public String getPlatformOriginalOrderNo() {
        return platformOriginalOrderNo;
    }

    public void setPlatformOriginalOrderNo(String platformOriginalOrderNo) {
        this.platformOriginalOrderNo = platformOriginalOrderNo;
    }

    public Date getOrderDeliverDate() {
        return orderDeliverDate;
    }

    public void setOrderDeliverDate(Date orderDeliverDate) {
        this.orderDeliverDate = orderDeliverDate;
    }

    public String getBuyerNick() {
        return buyerNick;
    }

    public void setBuyerNick(String buyerNick) {
        this.buyerNick = buyerNick;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public List<SampleOrderItemResponse> getOrderItems() {
        return orderItems;
    }

    public void setOrderItems(List<SampleOrderItemResponse> orderItems) {
        this.orderItems = orderItems;
    }

    public Date getOrderCreateDate() {
        return orderCreateDate;
    }

    public void setOrderCreateDate(Date orderCreateDate) {
        this.orderCreateDate = orderCreateDate;
    }
}
