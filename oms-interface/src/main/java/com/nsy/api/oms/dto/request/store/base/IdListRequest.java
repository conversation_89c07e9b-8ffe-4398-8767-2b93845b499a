package com.nsy.api.oms.dto.request.store.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * id请求
 * <AUTHOR>
 * */
@ApiModel(value = "IdListRequest", description = "id集合")
public class IdListRequest implements Serializable {
    private static final long serialVersionUID = 8257082276969440565L;
    @Size(min = 1, message = "idList不能为空")
    @ApiModelProperty(value = "要操作的id集合", name = "idList", required = true)
    private List<Integer> idList;

    public List<Integer> getIdList() {
        return idList;
    }

    public void setIdList(List<Integer> idList) {
        this.idList = idList;
    }
}
