package com.nsy.api.oms.dto.response.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ApiModel("TK平台样品订单详情")
public class SampleOrderItemResponse implements Serializable {
    private static final long serialVersionUID = -2256335221477859949L;
    /**
     * sku
     */
    @ApiModelProperty("sku")
    private String sku;

    /**
     * 卖方SKU
     */
    @ApiModelProperty("seller_sku")
    private String sellerSku;

    /**
     * 数量
     */
    @ApiModelProperty("qty")
    private Integer qty;

    @ApiModelProperty("seller_sku_id")
    private String sellerSkuId;
    @ApiModelProperty("seller_product_id")
    private String sellerProductId;

    public String getSellerProductId() {
        return sellerProductId;
    }

    public void setSellerProductId(String sellerProductId) {
        this.sellerProductId = sellerProductId;
    }

    public String getSellerSkuId() {
        return sellerSkuId;
    }

    public void setSellerSkuId(String sellerSkuId) {
        this.sellerSkuId = sellerSkuId;
    }

    public Integer getQty() {
        return qty;
    }

    public void setQty(Integer qty) {
        this.qty = qty;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getSellerSku() {
        return sellerSku;
    }

    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }
}
