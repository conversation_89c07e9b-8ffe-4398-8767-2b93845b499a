package com.nsy.api.oms.feign;

import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.api.oms.dto.request.auth.SaAuthRequest;
import com.nsy.api.oms.dto.request.store.SaStoreSkuConvertRequest;
import com.nsy.api.oms.dto.request.store.StoreListRequest;
import com.nsy.api.oms.dto.request.store.StorePageListRequest;
import com.nsy.api.oms.dto.request.store.StorePlatformRequest;
import com.nsy.api.oms.dto.request.store.base.IdListRequest;
import com.nsy.api.oms.dto.response.auth.StoreAuthInfoResponse;
import com.nsy.api.oms.dto.response.auth.WebsiteResponse;
import com.nsy.api.oms.dto.response.base.BdDataSourceRequest;
import com.nsy.api.oms.dto.response.base.SelectModelResponse;
import com.nsy.api.oms.dto.response.store.AlibabaInternationAuthResponse;
import com.nsy.api.oms.dto.response.store.AmazonAuthResponse;
import com.nsy.api.oms.dto.response.store.AmazonStoreListResponse;
import com.nsy.api.oms.dto.response.store.AmazonStoreResponse;
import com.nsy.api.oms.dto.response.store.SaStoreSkuConvertResponse;
import com.nsy.api.oms.dto.response.store.ShopifyStoreListResponse;
import com.nsy.api.oms.dto.response.store.StoreConfigResponse;
import com.nsy.api.oms.dto.response.store.StoreDetailResponse;
import com.nsy.api.oms.dto.response.store.StoreFinancePaymentResponse;
import com.nsy.api.oms.dto.response.store.StoreListResponse;
import com.nsy.api.oms.dto.response.store.StorePageListResponse;
import com.nsy.api.oms.dto.response.store.StoreStaffingResponse;
import com.nsy.business.base.utils.SelectModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;


/**
 * <AUTHOR>
 * @description: 对外-店铺接口
 * @date 2024/15/11:22
 */
@Api(tags = "对外-店铺接口")
@FeignClient(name = "api-oms", contextId = "api-oms-store")
public interface StoreFeignClient {

    @ApiOperation(value = "店铺分页列表", notes = "店铺分页列表")
    @PostMapping("/store-feign/get-store-page-list")
    PageResponse<StorePageListResponse> getStorePageList(@RequestBody StorePageListRequest request);

    @ApiOperation(value = "通过站点ID查询店铺详情", notes = "通过站点ID查询店铺详情")
    @GetMapping("/store-feign/get-store-by-website-id")
    StoreDetailResponse getStoreByWebsiteId(Integer websiteId);

    @ApiOperation(value = "通过站点ID数组查询店铺详情", notes = "通过站点ID列表查询店铺详情")
    @PostMapping("/store-feign/get-store-by-website-ids")
    List<StoreDetailResponse> getStoreByWebsiteIds(@RequestBody StoreListRequest request);

    @ApiOperation(value = "获取shopify|shoplazy|shopyy店铺列表", notes = "获取shopify|shoplazy|shopyy店铺列表")
    @GetMapping("/store-feign/get-shopify-store-list")
    List<ShopifyStoreListResponse> getShopifyStoreList();

    @ApiOperation(value = "通过店铺ID、平台判店铺是否被刊登系统使用（站点配置表中是否存在）", notes = "通过店铺ID、平台判店铺是否被刊登系统使用（站点配置表中是否存在）")
    @GetMapping("/store-feign/store-is-used-by-oms")
    boolean storeIsUsedByOMS(@RequestParam Integer storeId, @RequestParam String platformName);

    @ApiOperation(value = "店铺列表", notes = "店铺列表")
    @PostMapping("/store-feign/get-store-list")
    List<StoreListResponse> getStoreList(@RequestBody StoreListRequest request);

    @ApiOperation(value = "店铺列表", notes = "店铺列表")
    @PostMapping("/store-feign/get-store-list-ignore-tenant")
    List<StoreListResponse> getStoreListIgnoreTenant(@RequestBody StoreListRequest request);

    @ApiOperation(value = "获取所有启用的亚马逊店铺列表", notes = "获取所有启用的亚马逊店铺列表")
    @GetMapping("/store-feign/get-all-amazon-store")
    List<AmazonStoreListResponse> getAllAmazonStore();

    @ApiOperation(value = "通过用户账号查询店铺列表", notes = "通过用户账号查询店铺列表")
    @GetMapping("/store-feign/get-store-list-by-user-account")
    List<StoreListResponse> getStoreListByUserAccount(String userAccount);

    @ApiOperation(value = "通过店铺ID查询店铺详情", notes = "通过店铺ID查询店铺详情")
    @GetMapping("/store-feign/get-store-by-id")
    StoreDetailResponse getStoreById(Integer id);

    @ApiOperation(value = "通过店铺ID集合查询店铺详情列表", notes = "通过店铺ID集合查询店铺详情列表")
    @PostMapping("/store-feign/get-store-list-by-ids")
    List<StoreDetailResponse> getStoreListByIds(@RequestBody @Valid IdListRequest request);

    @ApiOperation(value = "通过平台名称集合获取店铺列表", notes = "通过平台名称集合获取店铺列表")
    @PostMapping("/store-feign/get-store-list-by-platform-names")
    List<StoreListResponse> getStoreListByPlatformNames(@RequestBody @Valid StorePlatformRequest request);

    @ApiOperation(value = "通过店铺ID查询阿里国际授权信息", notes = "通过店铺ID查询阿里国际授权信息")
    @GetMapping("/store-feign/get-ali-inter-auth-by-store-id")
    AlibabaInternationAuthResponse getAliInterAuthByStoreId(Integer storeId);

    @ApiOperation(value = "通过店铺ID查询亚马逊授权信息", notes = "通过店铺ID查询亚马逊授权信息")
    @GetMapping("/store-feign/get-amazon-auth-by-store-id")
    AmazonAuthResponse getAmazonAuthByStoreId(Integer storeId);


    @ApiOperation(value = "查询亚马逊全部授权信息", notes = "查询亚马逊全部授权信息")
    @GetMapping("/store-feign/get-amazon-auth-list")
    List<AmazonAuthResponse> getAmazonAuthList();

    @ApiOperation(value = "查询绑定付款信用卡的店铺", notes = "查询绑定付款信用卡的店铺")
    @GetMapping("/store-feign/get-associated-store")
    StoreFinancePaymentResponse getAssociatedStoreInfo(@RequestParam("cardId") Integer cardId, @RequestParam("type") Integer type);

    @PostMapping("/store-feign/get-store-select")
    @ApiOperation(value = "获取店铺下拉数据", notes = "获取店铺下拉数据", produces = "application/json")
    PageResponse<SelectModelResponse> getStoreSelect(@RequestBody BdDataSourceRequest request);

    @PostMapping("/store-feign/get-store-auth-info")
    @ApiOperation(value = "获取店铺授权信息", notes = "获取店铺授权信息", produces = "application/json")
    StoreAuthInfoResponse getStoreAuthInfo(@RequestBody SaAuthRequest request);

    @GetMapping("/store-feign/get-store-config-info")
    @ApiOperation(value = "获取店铺配置信息", notes = "获取店铺配置信息", produces = "application/json")
    StoreConfigResponse getStoreConfigInfo(Integer storeId);

    @RequestMapping(value = "/store-feign/modify-payment-credit-card-status", method = RequestMethod.GET)
    void modifyPaymentCreditCardStatus(@RequestParam("cardId") Integer cardId, @RequestParam("status") Integer status);

    @RequestMapping(value = "/store-feign/modify-receiving-card-status", method = RequestMethod.GET)
    void modifyReceivingCreditCardStatus(@RequestParam("accountId") Integer accountId, @RequestParam("status") Integer status);


    @PostMapping("/store-feign/get-website-by-website-key")
    @ApiOperation(value = "根据websiteKey获取站点ID", notes = "根据websiteKey获取站点ID", produces = "application/json")
    WebsiteResponse getWebsiteByWebsiteKey(@RequestBody SaAuthRequest request);

    @ApiOperation(value = "通过部门id获取站点id", notes = "通过部门id获取站点id")
    @GetMapping("/store-feign/get-website-id-by-department/{department}/{location}")
    List<Integer> getWebsiteIdListByDepartment(@PathVariable("department") String department, @PathVariable("location") String location);

    @ApiOperation(value = "通过部门id获取站点id", notes = "通过部门id获取站点id")
    @GetMapping("/store-feign/get-store-ids-by-department/{department}/{location}")
    List<Integer> getStoreIdsByDepartment(@PathVariable("department") String department, @PathVariable("location") String location);

    @ApiOperation(value = "根据平台名称获取启用的店铺数据", notes = "根据平台名称获取启用的店铺数据")
    @GetMapping("/store-feign/get-store-id-list/{platformName}")
    List<Integer> getStoreIdList(@PathVariable("platformName") String platformName);

    @PostMapping("/store-feign/convert-local-sku")
    @ApiOperation(value = "批量平台sku转换本地sku", notes = "根据店铺ID、sellerSku批量转换本地sku")
    SaStoreSkuConvertResponse convertLocalSku(@Valid @RequestBody SaStoreSkuConvertRequest request);

    @ApiOperation(value = "根据店铺Id获取平台ID", notes = "根据店铺Id获取平台ID")
    @GetMapping("/store-feign/get-platform/{storeId}")
    Integer getPlatForm(@PathVariable("storeId") Integer storeId);

    @ApiOperation(value = "根据店铺id获取商品负责人", notes = "根据店铺id获取商品负责人")
    @GetMapping("/store-feign/get-store-staffing/{storeId}")
    List<StoreStaffingResponse> getStoreStaffing(@PathVariable("storeId") Integer storeId);

    @ApiOperation(value = "通过店铺ID查询亚马逊信息", notes = "通过店铺ID查询亚马逊信息")
    @GetMapping("/store-feign/get-amazon-store-by-store-id")
    AmazonStoreResponse getAmazonStoreByStoreId(@RequestParam Integer storeId);

    @ApiOperation(value = "通过店铺ID查询亚马逊信息", notes = "通过店铺ID查询亚马逊信息")
    @GetMapping("/store-feign/get-amazon-store-by-store-ids")
    List<AmazonStoreResponse> getAmazonStoreByStoreIdList(@RequestParam List<Integer> storeIdList);

    @GetMapping("/store-feign/amazon-store-list")
    @ApiOperation("获取亚马逊在营的店铺数据")
    List<SelectModel> amazonStoreList();

}
