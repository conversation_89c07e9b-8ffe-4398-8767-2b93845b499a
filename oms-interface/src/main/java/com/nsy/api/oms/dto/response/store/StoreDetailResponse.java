package com.nsy.api.oms.dto.response.store;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2022/12/7 17:34
 */
@ApiModel("店铺详情响应实体")
public class StoreDetailResponse implements Serializable {

    private static final long serialVersionUID = 2566928362449999208L;
    @ApiModelProperty("店铺id")
    private Integer id;

    @ApiModelProperty("erp店铺名称")
    private String erpStoreName;

    @ApiModelProperty("平台店铺名称")
    private String storeName;

    @ApiModelProperty("平台id(数据字典id)")
    private Integer platformId;

    @ApiModelProperty("平台名称(数据字典名称)")
    private String platformName;

    @ApiModelProperty("扩展属性1")
    private String extendedAttributes0ne;

    @ApiModelProperty("扩展属性2")
    private String extendedAttributesTwo;

    @ApiModelProperty("扩展属性3")
    private String extendedAttributesThree;

    @ApiModelProperty("扩展属性4")
    private String extendedAttributesFour;

    @ApiModelProperty("直营/分销id")
    private Integer retailId;

    @ApiModelProperty("直营/分销名称")
    private String retailName;

    @ApiModelProperty("账号键")
    private String accountKey;

    @ApiModelProperty("账号秘钥")
    private String accountSecret;

    @ApiModelProperty("市场列表")
    private List<StoreMarketResponse> markets;

    @ApiModelProperty("店铺性质-数值： 1-主营电 2-备用店 3-站群线 4-长期线 5-品牌孵化项目")
    private Integer nature;

    @ApiModelProperty("店铺性质-名称： 1-主营电 2-备用店 3-站群线 4-长期线 5-品牌孵化项目")
    private String natureName;

    @ApiModelProperty("店铺状态-数值: 0-关店 1-在营")
    private Integer status;

    @ApiModelProperty("店铺状态-名称: 0-关店 1-在营")
    private String statusName;

    @ApiModelProperty("分销二级平台id（对接平台）")
    private Integer secondPlatformId;

    @ApiModelProperty("分销二级平台（对接平台）")
    private String secondPlatformName;

    @ApiModelProperty("关联店铺对应的部门ID")
    private Integer associatedStoreDepartmentId;

    @ApiModelProperty("关联店铺对应的部门")
    private String associatedStoreDepartment;

    @ApiModelProperty("店铺站点映射mapping集合")
    private List<StoreWebsiteMappingResponse> websites;

    @ApiModelProperty("sku规则配置")
    private String skuRule;

    @ApiModelProperty("店铺类型-数值")
    private Integer storeType;

    @ApiModelProperty("店铺类型名称-中文")
    private String storeTypeName;

    @ApiModelProperty("店铺URL")
    private String storeUrl;

    @ApiModelProperty("部门ID")
    private Integer departmentId;

    @ApiModelProperty("部门")
    private String department;

    @ApiModelProperty("二级部门ID")
    private Integer secondDepartmentId;

    @ApiModelProperty("二级部门")
    private String secondDepartment;

    @ApiModelProperty("地区")
    private String location;

    public List<String> allPlatform() {
        return new ArrayList<>(Arrays.asList(platformName, secondPlatformName));
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getErpStoreName() {
        return erpStoreName;
    }

    public void setErpStoreName(String erpStoreName) {
        this.erpStoreName = erpStoreName;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public Integer getPlatformId() {
        return platformId;
    }

    public void setPlatformId(Integer platformId) {
        this.platformId = platformId;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public String getExtendedAttributes0ne() {
        return extendedAttributes0ne;
    }

    public void setExtendedAttributes0ne(String extendedAttributes0ne) {
        this.extendedAttributes0ne = extendedAttributes0ne;
    }

    public String getExtendedAttributesTwo() {
        return extendedAttributesTwo;
    }

    public void setExtendedAttributesTwo(String extendedAttributesTwo) {
        this.extendedAttributesTwo = extendedAttributesTwo;
    }

    public String getExtendedAttributesThree() {
        return extendedAttributesThree;
    }

    public void setExtendedAttributesThree(String extendedAttributesThree) {
        this.extendedAttributesThree = extendedAttributesThree;
    }

    public String getExtendedAttributesFour() {
        return extendedAttributesFour;
    }

    public void setExtendedAttributesFour(String extendedAttributesFour) {
        this.extendedAttributesFour = extendedAttributesFour;
    }

    public Integer getRetailId() {
        return retailId;
    }

    public void setRetailId(Integer retailId) {
        this.retailId = retailId;
    }

    public String getRetailName() {
        return retailName;
    }

    public void setRetailName(String retailName) {
        this.retailName = retailName;
    }

    public String getAccountKey() {
        return accountKey;
    }

    public void setAccountKey(String accountKey) {
        this.accountKey = accountKey;
    }

    public String getAccountSecret() {
        return accountSecret;
    }

    public void setAccountSecret(String accountSecret) {
        this.accountSecret = accountSecret;
    }

    public List<StoreMarketResponse> getMarkets() {
        return markets;
    }

    public void setMarkets(List<StoreMarketResponse> markets) {
        this.markets = markets;
    }

    public Integer getNature() {
        return nature;
    }

    public void setNature(Integer nature) {
        this.nature = nature;
    }

    public String getNatureName() {
        return natureName;
    }

    public void setNatureName(String natureName) {
        this.natureName = natureName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public Integer getSecondPlatformId() {
        return secondPlatformId;
    }

    public void setSecondPlatformId(Integer secondPlatformId) {
        this.secondPlatformId = secondPlatformId;
    }

    public String getSecondPlatformName() {
        return secondPlatformName;
    }

    public void setSecondPlatformName(String secondPlatformName) {
        this.secondPlatformName = secondPlatformName;
    }

    public Integer getAssociatedStoreDepartmentId() {
        return associatedStoreDepartmentId;
    }

    public void setAssociatedStoreDepartmentId(Integer associatedStoreDepartmentId) {
        this.associatedStoreDepartmentId = associatedStoreDepartmentId;
    }

    public String getAssociatedStoreDepartment() {
        return associatedStoreDepartment;
    }

    public void setAssociatedStoreDepartment(String associatedStoreDepartment) {
        this.associatedStoreDepartment = associatedStoreDepartment;
    }

    public List<StoreWebsiteMappingResponse> getWebsites() {
        return websites;
    }

    public void setWebsites(List<StoreWebsiteMappingResponse> websites) {
        this.websites = websites;
    }

    public String getSkuRule() {
        return skuRule;
    }

    public void setSkuRule(String skuRule) {
        this.skuRule = skuRule;
    }

    public Integer getStoreType() {
        return storeType;
    }

    public void setStoreType(Integer storeType) {
        this.storeType = storeType;
    }

    public String getStoreTypeName() {
        return storeTypeName;
    }

    public void setStoreTypeName(String storeTypeName) {
        this.storeTypeName = storeTypeName;
    }

    public String getStoreUrl() {
        return storeUrl;
    }

    public void setStoreUrl(String storeUrl) {
        this.storeUrl = storeUrl;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public Integer getSecondDepartmentId() {
        return secondDepartmentId;
    }

    public void setSecondDepartmentId(Integer secondDepartmentId) {
        this.secondDepartmentId = secondDepartmentId;
    }

    public String getSecondDepartment() {
        return secondDepartment;
    }

    public void setSecondDepartment(String secondDepartment) {
        this.secondDepartment = secondDepartment;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }
}
