package com.nsy.api.oms.dto.response.store;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ApiModel("Shopify店铺列表响应实体")
public class ShopifyStoreListResponse implements Serializable {
    private static final long serialVersionUID = -3623795138683805202L;

    @ApiModelProperty("店铺id")
    private Integer id;

    @ApiModelProperty("erp店铺名称")
    private String erpStoreName;

    @ApiModelProperty("平台店铺名称")
    private String storeName;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getErpStoreName() {
        return erpStoreName;
    }

    public void setErpStoreName(String erpStoreName) {
        this.erpStoreName = erpStoreName;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }
}
