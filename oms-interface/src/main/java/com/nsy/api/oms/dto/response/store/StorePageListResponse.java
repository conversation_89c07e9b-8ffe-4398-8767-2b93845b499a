package com.nsy.api.oms.dto.response.store;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 2022/12/7 17:34
 */
@ApiModel("店铺分页列表响应实体")
public class StorePageListResponse implements Serializable {

    private static final long serialVersionUID = -574566185221701031L;
    @ApiModelProperty("店铺id")
    private Integer id;
    /**
     * erp店铺名称
     */
    @ApiModelProperty("erp店铺名称")
    private String erpStoreName;
    /**
     * 平台店铺名称
     */
    @ApiModelProperty("平台店铺名称")
    private String storeName;

    /**
     * sku规则配置
     */
    @ApiModelProperty("sku规则配置")
    private String skuRule;

    /**
     * 店铺类型-数值
     */
    @ApiModelProperty("店铺类型-数值")
    private Integer storeType;

    /**
     * 店铺类型名称-中文
     */
    @ApiModelProperty("店铺类型名称-中文")
    private String storeTypeName;

    private String department;

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getErpStoreName() {
        return erpStoreName;
    }

    public void setErpStoreName(String erpStoreName) {
        this.erpStoreName = erpStoreName;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getSkuRule() {
        return skuRule;
    }

    public void setSkuRule(String skuRule) {
        this.skuRule = skuRule;
    }

    public Integer getStoreType() {
        return storeType;
    }

    public void setStoreType(Integer storeType) {
        this.storeType = storeType;
    }

    public String getStoreTypeName() {
        return storeTypeName;
    }

    public void setStoreTypeName(String storeTypeName) {
        this.storeTypeName = storeTypeName;
    }
}
