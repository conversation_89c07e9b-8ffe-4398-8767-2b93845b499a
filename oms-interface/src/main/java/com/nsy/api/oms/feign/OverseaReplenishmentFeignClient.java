package com.nsy.api.oms.feign;

import com.nsy.api.oms.dto.request.store.OverseaReplenishmentGetGroupStoreRequest;
import com.nsy.api.oms.dto.response.store.StoreInfoResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/11/915:01
 */

@FeignClient(name = "api-oms", contextId = "api-oms-oversea-replenishment")
public interface OverseaReplenishmentFeignClient {

    @PostMapping("/oversea-replenishment/get-group-store-list")
    List<StoreInfoResponse> getGroupStoreList(@RequestBody @Valid OverseaReplenishmentGetGroupStoreRequest request);
}
