package com.nsy.api.oms.dto.request.amazon;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-03-03
 */
public class CalculateGrossProfitRateRequest {
    @ApiModelProperty("计算类型")
    @NotBlank(message = "计算类型不能为空")
    private String type;
    @ApiModelProperty("亚马逊市场编码")
    @NotBlank(message = "市场不能为空")
    private String countryCode;
    @ApiModelProperty("折前价")
    @NotNull(message = "折前价不能为空")
    private BigDecimal priceBeforeDiscount;
    @ApiModelProperty("折后价")
    @NotNull(message = "折后价不能为空")
    private BigDecimal priceAfterDiscount;
    @ApiModelProperty("实际长（CM）")
    @NotNull(message = "实际长（CM）不能为空")
    private BigDecimal length;
    @ApiModelProperty("实际宽（CM）")
    @NotNull(message = "实际宽（CM）不能为空")
    private BigDecimal width;
    @ApiModelProperty("实际高（CM）")
    @NotNull(message = "实际高（CM）不能为空")
    private BigDecimal height;
    @ApiModelProperty("实际重量（克）")
    @NotNull(message = "实际重量（克）不能为空")
    private BigDecimal weight;
    @ApiModelProperty("商品成本")
    @NotNull(message = "商品成本不能为空")
    private BigDecimal productCost;
    @ApiModelProperty("退货率")
    @NotNull(message = "退货率不能为空")
    private BigDecimal returnRate;
    @ApiModelProperty("品类退货不可售比例")
    @NotNull(message = "品类退货不可售比例不能为空")
    private BigDecimal returnNotForSaleRate;
    @ApiModelProperty("广告费率")
    @NotNull(message = "广告费率不能为空")
    private BigDecimal advertisingRate;
    @ApiModelProperty("包裹类型")
    @NotNull(message = "包裹类型不能为空")
    private String standard;
    @ApiModelProperty("商品折扣率")
    @NotNull(message = "商品折扣率不能为空")
    private BigDecimal discountRate;

    private BigDecimal couponRate;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public BigDecimal getPriceBeforeDiscount() {
        return priceBeforeDiscount;
    }

    public void setPriceBeforeDiscount(BigDecimal priceBeforeDiscount) {
        this.priceBeforeDiscount = priceBeforeDiscount;
    }

    public BigDecimal getPriceAfterDiscount() {
        return priceAfterDiscount;
    }

    public void setPriceAfterDiscount(BigDecimal priceAfterDiscount) {
        this.priceAfterDiscount = priceAfterDiscount;
    }

    public BigDecimal getLength() {
        return length;
    }

    public void setLength(BigDecimal length) {
        this.length = length;
    }

    public BigDecimal getWidth() {
        return width;
    }

    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    public BigDecimal getHeight() {
        return height;
    }

    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public BigDecimal getProductCost() {
        return productCost;
    }

    public void setProductCost(BigDecimal productCost) {
        this.productCost = productCost;
    }

    public BigDecimal getReturnRate() {
        return returnRate;
    }

    public void setReturnRate(BigDecimal returnRate) {
        this.returnRate = returnRate;
    }

    public BigDecimal getReturnNotForSaleRate() {
        return returnNotForSaleRate;
    }

    public void setReturnNotForSaleRate(BigDecimal returnNotForSaleRate) {
        this.returnNotForSaleRate = returnNotForSaleRate;
    }

    public BigDecimal getAdvertisingRate() {
        return advertisingRate;
    }

    public void setAdvertisingRate(BigDecimal advertisingRate) {
        this.advertisingRate = advertisingRate;
    }

    public String getStandard() {
        return standard;
    }

    public void setStandard(String standard) {
        this.standard = standard;
    }

    public BigDecimal getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(BigDecimal discountRate) {
        this.discountRate = discountRate;
    }

    public BigDecimal getCouponRate() {
        return couponRate;
    }

    public void setCouponRate(BigDecimal couponRate) {
        this.couponRate = couponRate;
    }

    // 低价减免物流费：如果折前价<=9.99，则低价减免物流费=0.77；否则低价减免物流费=0
    public BigDecimal getReducedLogisticsCost() {
        return this.priceBeforeDiscount.compareTo(new BigDecimal("9.99")) > 0 ? BigDecimal.ZERO : new BigDecimal("0.77");
    }
}
