package com.nsy.api.oms.feign;

import com.nsy.api.oms.dto.response.linxing.LxMappingResponse;
import com.nsy.business.base.dto.group.BusinessGroup;
import com.nsy.business.base.dto.request.lingxing.LxMappingQueryRequest;
import com.nsy.business.base.dto.request.lingxing.LxMappingSaveRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/11/22 15:58
 */
@Api(tags = "api-etl")
@FeignClient(name = "api-etl", contextId = "api-etl-1")
public interface EtlFeignClient {
    @ApiOperation(value = "获取站点数据", notes = "获取站点数据")
    @PostMapping("/lx/mapping/save")
    void lxMappingSave(@RequestBody LxMappingSaveRequest request);

    @ApiOperation(value = "通过多个领星单号及类型查询业务单号", notes = "通过多个领星单号及类型查询业务单号")
    @GetMapping("/lx/mapping/getListByType")
    List<LxMappingResponse> getBusinessNoList(@RequestParam("businessType") Integer businessType, @RequestParam("location") String location);

    @ApiOperation(value = "通过业务单号集合及类型查询领星单号", notes = "通过业务单号集合及类型查询领星单号")
    @PostMapping("/lx/mapping/getExternalNoList")
    List<LxMappingResponse> getExternalNoList(@Validated(BusinessGroup.class) @RequestBody LxMappingQueryRequest request);
}
