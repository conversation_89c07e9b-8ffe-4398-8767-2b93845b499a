package com.nsy.api.oms.dto.response.store;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 2022/12/7 17:34
 */
@ApiModel("店铺市场响应实体")
public class StoreMarketResponse implements Serializable {
    private static final long serialVersionUID = 4320334907449761886L;
    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("店铺id")
    private Integer storeId;

    @ApiModelProperty("市场编码")
    private String marketCode;

    @ApiModelProperty("市场名称")
    private String marketName;

    @ApiModelProperty("状态: 0-删除 1-正常")
    private Integer status;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getMarketCode() {
        return marketCode;
    }

    public void setMarketCode(String marketCode) {
        this.marketCode = marketCode;
    }

    public String getMarketName() {
        return marketName;
    }

    public void setMarketName(String marketName) {
        this.marketName = marketName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
