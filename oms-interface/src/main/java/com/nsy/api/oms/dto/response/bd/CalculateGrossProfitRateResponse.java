package com.nsy.api.oms.dto.response.bd;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-03-03
 */
public class CalculateGrossProfitRateResponse {
    @ApiModelProperty("计算类型")
    private String type;
    @ApiModelProperty("计费重量(磅)")
    private BigDecimal chargingWeight;
    @ApiModelProperty("FBA配送费")
    private BigDecimal fbaDeliveryCost;
    @ApiModelProperty("FBA退货处理费")
    private BigDecimal fbaReturnCost;
    @ApiModelProperty("FBA头程邮费")
    private BigDecimal fbaFirstPostageCost;
    @ApiModelProperty("仓租费")
    private BigDecimal storageCost;
    @ApiModelProperty("废弃成本")
    private BigDecimal disposalCost;
    @ApiModelProperty("平台拥金率及兑换手续费")
    private BigDecimal handlingCost;
    @ApiModelProperty("广告费")
    private BigDecimal advertisingCost;
    @ApiModelProperty("测评费用+秒杀COUPON等费用")
    private BigDecimal couponCost;
    @ApiModelProperty("退货平台扣除佣金")
    private BigDecimal returnPlatformDeductsCost;
    @ApiModelProperty("净毛利润率")
    private BigDecimal grossProfitRate;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public BigDecimal getChargingWeight() {
        return chargingWeight;
    }

    public void setChargingWeight(BigDecimal chargingWeight) {
        this.chargingWeight = chargingWeight;
    }

    public BigDecimal getFbaDeliveryCost() {
        return fbaDeliveryCost;
    }

    public void setFbaDeliveryCost(BigDecimal fbaDeliveryCost) {
        this.fbaDeliveryCost = fbaDeliveryCost;
    }

    public BigDecimal getFbaReturnCost() {
        return fbaReturnCost;
    }

    public void setFbaReturnCost(BigDecimal fbaReturnCost) {
        this.fbaReturnCost = fbaReturnCost;
    }

    public BigDecimal getFbaFirstPostageCost() {
        return fbaFirstPostageCost;
    }

    public void setFbaFirstPostageCost(BigDecimal fbaFirstPostageCost) {
        this.fbaFirstPostageCost = fbaFirstPostageCost;
    }

    public BigDecimal getStorageCost() {
        return storageCost;
    }

    public void setStorageCost(BigDecimal storageCost) {
        this.storageCost = storageCost;
    }

    public BigDecimal getDisposalCost() {
        return disposalCost;
    }

    public void setDisposalCost(BigDecimal disposalCost) {
        this.disposalCost = disposalCost;
    }

    public BigDecimal getHandlingCost() {
        return handlingCost;
    }

    public void setHandlingCost(BigDecimal handlingCost) {
        this.handlingCost = handlingCost;
    }

    public BigDecimal getAdvertisingCost() {
        return advertisingCost;
    }

    public void setAdvertisingCost(BigDecimal advertisingCost) {
        this.advertisingCost = advertisingCost;
    }

    public BigDecimal getCouponCost() {
        return couponCost;
    }

    public void setCouponCost(BigDecimal couponCost) {
        this.couponCost = couponCost;
    }

    public BigDecimal getReturnPlatformDeductsCost() {
        return returnPlatformDeductsCost;
    }

    public void setReturnPlatformDeductsCost(BigDecimal returnPlatformDeductsCost) {
        this.returnPlatformDeductsCost = returnPlatformDeductsCost;
    }

    public BigDecimal getGrossProfitRate() {
        return grossProfitRate;
    }

    public void setGrossProfitRate(BigDecimal grossProfitRate) {
        this.grossProfitRate = grossProfitRate;
    }
}
