package com.nsy.api.oms.dto.response.bd;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-03-03
 */
public class IncreaseProfitResponse {
    @ApiModelProperty("佣金率(%)")
    private BigDecimal commissionRate;
    @ApiModelProperty("售价范围($)")
    private String range;
    @ApiModelProperty("试算折后价($)")
    private BigDecimal discountPrice;
    @ApiModelProperty("利润率(%)")
    private BigDecimal profitRate;
    @ApiModelProperty("盈亏平衡价($)")
    private BigDecimal balancePrice;
    @ApiModelProperty("利润增加额($)")
    private BigDecimal increaseProfit;
    @ApiModelProperty("是否命中当前档")
    private boolean hit;
    @ApiModelProperty("档序号")
    private int level;

    public BigDecimal getCommissionRate() {
        return commissionRate;
    }

    public void setCommissionRate(BigDecimal commissionRate) {
        this.commissionRate = commissionRate;
    }

    public String getRange() {
        return range;
    }

    public void setRange(String range) {
        this.range = range;
    }

    public BigDecimal getDiscountPrice() {
        return discountPrice;
    }

    public void setDiscountPrice(BigDecimal discountPrice) {
        this.discountPrice = discountPrice;
    }

    public BigDecimal getProfitRate() {
        return profitRate;
    }

    public void setProfitRate(BigDecimal profitRate) {
        this.profitRate = profitRate;
    }

    public BigDecimal getBalancePrice() {
        return balancePrice;
    }

    public void setBalancePrice(BigDecimal balancePrice) {
        this.balancePrice = balancePrice;
    }

    public BigDecimal getIncreaseProfit() {
        return increaseProfit;
    }

    public void setIncreaseProfit(BigDecimal increaseProfit) {
        this.increaseProfit = increaseProfit;
    }

    public boolean isHit() {
        return hit;
    }

    public void setHit(boolean hit) {
        this.hit = hit;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }
}
