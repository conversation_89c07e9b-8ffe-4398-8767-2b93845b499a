技术架构：
-----------------------------------
#### 开发环境

- 语言：Java 8

- IDE(JAVA)： IDEA / Eclipse安装lombok插件

- 依赖管理：Gradle7.0

- 数据库：MySQL8.0.28

- 缓存：Redis


#### 后端
- 基础框架：Spring Boot 2.4.1

- 微服务框架：Spring Cloud (2020.0.0) && Spring Cloud Alibaba  2020.0.RC1

- 持久层框架：JPA -->Mybatis Plus 3.4.3

- 消息中间件：KAFKA

- 调度引擎：elastic-job

- 微服务技术栈：Spring Cloud Alibaba、Nacos、Gateway、Sentinel、Skywalking(持续更新)

- 缓存框架：redis

- 日志打印：logback && 阿里日志服务

- 单元测试： junit5

- 搜索引擎： elasticsearch 7.8.1(持续更新)

- 其他：jackson，Swagger-ui，checkstyle，pmd，spotbugs，lombok（简化代码）等。


#### 开发规范

##### Redis规范
- Key值不要超过10KB
- Key值中分割符使用":"
- 业务系统:Redis类型:关键字， 比如OAUTH2:H:USER_INFO，其中OAUTH2表示业务系统，H表示redis类型Hash, USER_INFO表示关键字

##### MBP规范
- 表关联需要写别名，不然最后解析 多租户字段（location）
- 表关联别名取值避开关键字

##### 异常处理规范
| 序号 | httpstatus | 异常 | 日志是否可忽略记录 | 备注 |
| ---- | ---- | ---- | ---- | ---- |
|1|200|无|-|-|
|2|201|-|忽略|也属于正常|
|3|202|-|忽略|也属于正常|
|4|-|NoneDataException|忽略|Job专用，如果没有处理到任何数据，抛此异常|
|5|400|InvalidRequestException|参数错误|
|6|400|BindException|-|表单提供绑定错误，比如设置notnull，但传进来参数为null|
|7|400|MethodArgumentNotValidException|-|也是表单绑定错误|
|8|401|-|忽略|只控制未登录，统一处理，没加异常|
|9|403|UserAuthorizationException|忽略|没有权限，比如用户没有该菜单权限|
|10|404|ResourceNotFoundException|-|url找不到异常|
|11|409|DuplicateSubmitException|忽略|重复提交异常|
|12|500|BusinessServiceException|忽略|业务异常，用来提示框|
|13|500|InternalServiceException|-|内部错误|
|14|500|RuntimeIOException|-|内部运行时出错|
|15|500|其他未知异常|-|	其他|


##### JOB规范
- 在处理多条数据时，如遇到问题，可以记下错误，并继续往下执行，直到全部处理完，再统一抛异常出来;
- 多租户模式下，JOB根据租户ID配置多个，每个JOB的job-parameters为租户ID，例如仓库系统，job-parameters 可以配置为QUANZHOU 
或 GUANGZHOU;

# API-OMS 项目

## 项目简介
这是一个基于Spring Boot的多模块项目，主要用于订单管理系统(OMS)的API服务。

## 技术架构
### 开发环境
- 语言：Java 8
- IDE(JAVA)：IDEA / Eclipse安装lombok插件
- 依赖管理：Gradle 7.0
- 数据库：MySQL 8.0.28
- 缓存：Redis

### 后端技术栈
- 基础框架：Spring Boot 2.4.1
- 微服务框架：Spring Cloud (2020.0.0) && Spring Cloud Alibaba 2020.0.RC1
- 持久层框架：Mybatis Plus 3.4.3
- 消息中间件：KAFKA
- 调度引擎：elastic-job
- 微服务技术栈：Spring Cloud Alibaba、Nacos、Gateway、Sentinel、Skywalking
- 缓存框架：redis
- 日志打印：logback && 阿里日志服务
- 单元测试：junit5
- 搜索引擎：elasticsearch 7.8.1
- 其他：jackson，Swagger-ui，checkstyle，pmd，spotbugs，lombok等

## 项目结构
项目采用多模块设计，主要包含以下模块：

### 根目录
```
.
├── oms-interface/          # 接口模块
├── oms-service/           # 服务实现模块
├── build.gradle           # Gradle构建文件
├── settings.gradle        # Gradle设置文件
├── gradle.properties      # Gradle属性文件
└── README.md              # 项目说明文档
```

### oms-service模块结构
```
oms-service/src/main/java/com/nsy/oms/
├── ApiOmsApplication.java    # 应用启动类
├── annotation/              # 自定义注解
├── aspect/                 # AOP切面
├── business/               # 业务逻辑
│   ├── service/           # 业务服务实现
│   ├── domain/            # 领域模型
│   ├── manage/            # 业务管理
│   ├── factory/           # 工厂类
│   ├── external/          # 外部服务调用
│   └── converter/         # 数据转换器
├── config/                # 配置类
├── constants/             # 常量定义
├── controller/            # 控制器
├── elasticjob/           # 定时任务
├── enums/                # 枚举类
├── enumstable/           # 枚举表
├── exception/            # 异常处理
├── filter/               # 过滤器
├── interceptor/          # 拦截器
├── mq/                   # 消息队列
├── repository/           # 仓储层
│   ├── entity/          # 实体类
│   ├── mongo/           # MongoDB相关
│   ├── sql/             # SQL相关
│   ├── dao/             # 数据访问对象
│   └── cache/           # 缓存相关
└── utils/                # 工具类
```

## 开发规范

### Redis规范
- Key值不要超过10KB
- Key值中分割符使用":"
- 业务系统:Redis类型:关键字， 比如OAUTH2:H:USER_INFO，其中OAUTH2表示业务系统，H表示redis类型Hash, USER_INFO表示关键字

### MBP规范
- 表关联需要写别名，不然最后解析 多租户字段（location）
- 表关联别名取值避开关键字

### 异常处理规范
| 序号 | httpstatus | 异常 | 日志是否可忽略记录 | 备注 |
| ---- | ---- | ---- | ---- | ---- |
|1|200|无|-|-|
|2|201|-|忽略|也属于正常|
|3|202|-|忽略|也属于正常|
|4|-|NoneDataException|忽略|Job专用，如果没有处理到任何数据，抛此异常|
|5|400|InvalidRequestException|参数错误|
|6|400|BindException|-|表单提供绑定错误，比如设置notnull，但传进来参数为null|
|7|400|MethodArgumentNotValidException|-|也是表单绑定错误|
|8|401|-|忽略|只控制未登录，统一处理，没加异常|
|9|403|UserAuthorizationException|忽略|没有权限，比如用户没有该菜单权限|
|10|404|ResourceNotFoundException|-|url找不到异常|
|11|409|DuplicateSubmitException|忽略|重复提交异常|
|12|500|BusinessServiceException|忽略|业务异常，用来提示框|
|13|500|InternalServiceException|-|内部错误|
|14|500|RuntimeIOException|-|内部运行时出错|
|15|500|其他未知异常|-|其他|

### JOB规范
- 在处理多条数据时，如遇到问题，可以记下错误，并继续往下执行，直到全部处理完，再统一抛异常出来;
- 多租户模式下，JOB根据租户ID配置多个，每个JOB的job-parameters为租户ID，例如仓库系统，job-parameters 可以配置为QUANZHOU 
或 GUANGZHOU;

## 开发环境要求
- JDK 1.8+
- Gradle 6.x+
- MySQL 5.7+
- IDE: IntelliJ IDEA

## 构建和运行
1. 克隆项目
2. 配置数据库连接
3. 执行 `./gradlew bootRun`
